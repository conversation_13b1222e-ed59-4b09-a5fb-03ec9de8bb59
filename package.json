{"name": "website-reconsitution", "author": "", "version": "1.0.0", "private": true, "scripts": {"dev": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"@smallwei/avue": "^2.10.4", "@sscfaith/avue-form-design": "^1.5.6", "ali-oss": "^6.3.1", "angular-expressions": "^1.2.1", "axios": "^0.24.0", "cos-js-sdk-v5": "^1.4.22", "docxtemplater": "^3.46.0", "docxtemplater-image-module-free": "^1.1.1", "echarts": "^5.4.3", "echarts-wordcloud": "^2.1.0", "element-ui": "^2.12.0", "file-saver": "^2.0.5", "html2canvas": "^1.4.1", "jspdf": "^2.5.1", "jszip-utils": "^0.1.0", "k-form-design": "^3.7.4", "medsci-avue-form-design": "^0.4.6", "ms-form": "^2.4.6", "pizzip": "^3.1.6", "prismjs": "^1.18.0", "screenfull": "^4.2.0", "sortablejs": "^1.10.0-rc3", "vue": "^2.5.17", "vue-prism-editor": "^0.3.0", "vue-router": "3.0.1", "vuedraggable": "^2.24.3", "vuex": "3.0.1"}, "devDependencies": {"@vue/cli-plugin-babel": "^3.0.0", "@vue/cli-plugin-eslint": "^3.0.0", "@vue/cli-service": "^3.8.4", "ajv": "^6.0.0", "ajv-keywords": "^3.2.0", "babel-eslint": "^10.0.1", "compression-webpack-plugin": "^3.0.1", "eslint": "^5.8.0", "eslint-plugin-vue": "^5.0.0-0", "node-sass": "^4.9.2", "sass-loader": "^7.0.3", "svg-sprite-loader": "^4.1.3", "vue-cli-plugin-element": "^1.0.0", "vue-template-compiler": "^2.5.17"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "rules": {"no-console": "off"}, "parserOptions": {"parser": "babel-es<PERSON>"}}, "postcss": {"plugins": {"autoprefixer": {}}}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"]}