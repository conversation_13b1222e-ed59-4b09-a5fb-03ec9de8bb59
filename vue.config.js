const CompressionPlugin = require("compression-webpack-plugin")

const isProduction = process.env.NODE_ENV === 'production';

const cdn = {
  css: [
    "https://static.medsci.cn/public-css/elementUI/2.13.2/index.css"
  ],
  js: [
    "https://static.medsci.cn/public-js/vue/2.6.10/vue.min.js",
    "https://static.medsci.cn/public-js/elementUI/2.13.2/index.js",
    "https://static.medsci.cn/public-js/vue-router/3.1.3/vue-router.min.js",
    "https://static.medsci.cn/public-js/axios/0.19.2/axios.min.js",
    "https://static.medsci.cn/public-js/vuex/3.1.1/vuex.min.js.js",
    // "https://static.medsci.cn/public-js/k-form-design/k-form-design-o.umd.min.js"
  ]
}

module.exports = {
  publicPath: '/',
  outputDir: '../k8s-front-medsciback/public', // 根据本地项目位置自行修改
  runtimeCompiler: true,
  lintOnSave: true,
  transpileDependencies: [],
  chainWebpack: config => {
    config.plugins.delete('prefetch')
    // build打包才使用CDN
    if (isProduction) {
      config.plugin('html')
        .tap(args => {
          args[0].cdn = cdn;
          return args;
        })
    }
  },
  configureWebpack: config => {
    if (isProduction) {
      config.externals = {
        vue: 'Vue',
        'vue-router': 'VueRouter',
        'vuex': 'Vuex',
        'axios': 'axios',
        'element-ui': 'ELEMENT',
        // 'k-form-design': 'KFormDesign'
      }
      return {
        plugins: [
          new CompressionPlugin({
            test: /\.js$|\.html$|\.css/,
            threshold: 10240,
            deleteOriginalAssets: false
          })
        ]
      }
    }
  },
  productionSourceMap: false,
  css: {
    extract: true,
    sourceMap: false,
    loaderOptions: {
      sass: {
        data: `@import "@/styles/index.scss";`
      }
    },
    modules: false
  },
  parallel: require('os').cpus().length > 1,
  pwa: {},
  devServer: {
    open: true,
    host: '0.0.0.0',
    port: 8080,
    // port: 8080,
    https: false,
    hot: true,
    disableHostCheck: true,
    proxy: {
      // 中心服务 tag、分类服务
      '/tag-center': {
        ws: false,
        // target: 'http://***************:9582/rest/tag-center/V2/',
        target: 'https://medsci-gateway.medon.com.cn/api/mg/tag/', //测试环境
        // target: 'https://medsci-security.medon.com.cn/api/mg/tag/',
        // target: 'http://medsci-gateway.ms-site.svc.cluster.local:8080/api/mg/tag/', //松江环境
        // target: 'http://**********:10081/',

        changeOrigin: true,
        pathRewrite: {
          '^/tag-center': ''
        }
      },
      // 中心服务 用户服务
      '/mg-user-service': {
        ws: false,
        // target: 'http://***************:9582/rest/mg-user-service/V2/',
        target: 'https://medsci-gateway.medon.com.cn/api/mg/user/',//测试环境
        // target: 'https://medsci-security.medon.com.cn/api/mg/user',
        // target: 'https://medsci-gateway.medon.com.cn/api/mg/user',
        // target: 'https://medsci-gateway.medon.com.cn/api/mg/user',
        // target: 'http://medsci-gateway.ms-site.svc.cluster.local:8080/api/mg/user/', //松江环境
        // target: 'https://medsci-gateway.medon.com.cn/api/mg/user/',
        changeOrigin: true,
        pathRewrite: {
          '^/mg-user-service': ''
        }
      },
      // 中心服务 资讯服务
      '/info-center': {
        ws: false,
        // target: 'http://***************:9582/rest/info-center/V2/',
        target: 'https://medsci-gateway.medon.com.cn/api/mg/information/',//测试环境
        // target: 'https://medsci-security.medon.com.cn/api/mg/information/',
        // target: 'https://medsci-gateway.medon.com.cn/api/mg/information/',
        // target: 'http://medsci-gateway.ms-site.svc.cluster.local:8080/api/mg/information/', //松江环境
        changeOrigin: true,
        pathRewrite: {
          '^/info-center': ''
        }
      },
      // 中心服务 搜索服务
      '/search-center': {
        ws: false,
        // target: 'http://***************:9582/rest/search-center/V2/',
        target: 'https://medsci-gateway.medon.com.cn/api/mg/search/',//测试环境
        // target: 'https://medsci-security.medon.com.cn/api/mg/search/',
        // target: 'http://medsci-gateway.ms-site.svc.cluster.local:8080/api/mg/search/', //松江环境
        changeOrigin: true,
        pathRewrite: {
          '^/search-center': ''
        }
      },
      // 中心服务 paas-mgr-center-service
      '/paas-mgr-center-service': {
        ws: false,
        // target: 'http://***************:9582/rest/paas-mgr-center-service/V2/',
        target: 'https://medsci-gateway.medon.com.cn/api/mg/paas-mgr/',//测试环境
        // target: 'https://medsci-security.medon.com.cn/api/mg/paas-mgr/',
        // target: 'https://medsci-gateway.medon.com.cn/api/mg/paas-mgr/',
        // target: 'http://medsci-gateway.ms-site.svc.cluster.local:8080/api/mg/paas-mgr/', //松江环境
        changeOrigin: true,
        pathRewrite: {
          '^/paas-mgr-center-service': ''
        }
      },
      // 中心服务 medsci-mgr-center-service
      '/medsci-mgr-center-service': {
        ws: false,
        // target: 'http://***************:9582/rest/medsci-mgr-center-service/V2/',
        target: 'https://medsci-gateway.medon.com.cn/api/mg/medsci-mgr/',//测试环境
        // target: 'https://medsci-security.medon.com.cn/api/mg/medsci-mgr/',
        // target: 'http://medsci-gateway.ms-site.svc.cluster.local:8080/api/mg/medsci-mgr/', //松江环境
        changeOrigin: true,
        pathRewrite: {
          '^/medsci-mgr-center-service': ''
        }
      },
      // 直播服务
      '/live-center': {
        ws: false,
        // target: 'http://***************:10015/',
        target: 'https://medsci-gateway.medon.com.cn/api/live/',//测试环境
        // target: 'https://medsci-security.medon.com.cn/api/live/',
        // target: 'http://medsci-gateway.ms-site.svc.cluster.local:8080/api/live/', //松江环境
        changeOrigin: true,
        pathRewrite: {
          '^/live-center': ''
        }
      },
      // cms服务
      '/material': {
        ws: false,
        // target: 'http://***************:10016/',
        target: 'https://medsci-gateway.medon.com.cn/api/material/',
        // target: 'https://medsci-security.medon.com.cn/api/material/',
        // target: 'http://medsci-gateway.ms-site.svc.cluster.local:8080/api/material/', //松江环境
        // target: 'https://medsci-gateway.medon.com.cn/api/material/',//测试环境
        // target : 'http://material-service.ms-site.svc.cluster.local/',
        pathRewrite: {
          '^/material': ''
        },
        changeOrigin: true
      },
      // 考试服务
      '/exam-service': {
        ws: false,
        // target: 'http://***************:9582/rest/exam-service/V2/',
        target: 'https://medsci-gateway.medon.com.cn/api/mg/exam/',//测试环境
        // target: 'http://medsci-gateway.ms-site.svc.cluster.local:8080/api/mg/exam/', //松江环境
        // target: 'http://exam-test.cn.utools.club/',
        
        changeOrigin: true,
        pathRewrite: {
          '^/exam-service': ''
        }
      },
      // 视频课程
      '/video': {
        ws: false,
        // target: 'http://***************:8861/video/',
        target: 'https://medsci-gateway.medon.com.cn/api/mg/video/',
        // target: 'https://medsci-gateway.medon.com.cn/api/mg/video/',//测试环境
        // target: 'http://medsci-gateway.ms-site.svc.cluster.local:8080/api/mg/video/',   //松江环境
        changeOrigin: true,
        pathRewrite: {
          '^/video': ''
        }
      },
      // 付费会员
      '/member': {
        ws: false,
        // target: 'http://***************:8860/member/',
        target: 'https://medsci-gateway.medon.com.cn/api/mg/member/member/',
        // target: 'http://10.0.1.188:8860/member/', //肖庭本地
        // target: 'https://medsci-gateway.medon.com.cn/api/mg/member/member/', //肖庭本地
        // target: 'http://medsci-gateway.ms-site.svc.cluster.local:8080/api/mg/member/member/',   //松江环境
        // target: 'https://medsci-gateway.medon.com.cn/api/mg/member/member/',  
        changeOrigin: true,
        pathRewrite: {
          '^/member': ''
        }
      },
      // 订单管理
      '/medsciOrder': {
        ws: false,
        // target: 'http://***************:8860/medsciOrder/',
        target: 'https://medsci-gateway.medon.com.cn/api/mg/member/medsciOrder/',
        // target: 'http://10.0.1.188:8860/medsciOrder/', //肖庭本地
        // target: 'https://backend.editor.medsci.cn/medsciOrder/', //ceshi
        // target: 'https://medsci-gateway.medon.com.cn/medsciOrder/', //ceshi//测试环境
        // target: 'http://medsci-gateway.ms-site.svc.cluster.local:8080/medsciOrder/', //ceshi //松江环境
        changeOrigin: true,
        pathRewrite: {
          '^/medsciOrder': ''
        }
      },
      // 表单
      '/form-service': {
        ws: false,
        target: 'http://medsci-gateway.medon.com.cn/api/mid/gateway/api/',
        // target: 'https://mid.medsci.cn/api/',
        changeOrigin: true,
        pathRewrite: {
          '^/form-service': ''
        }
      },
      '/report-service': {
        ws: false,
        target: 'https://medsci-gateway.medon.com.cn/api/report/',
        // target: 'https://mid.medsci.cn/api/',
        changeOrigin: true,
        pathRewrite: {
          '^/report-service': ''
        }
      },
      // 中心服务 medsci-mgr-center-service
      // '/medsciSJ-mgr-center-service': {
      //   ws: false,
      //   // target: 'http://***************:9582/rest/medsci-mgr-center-service/V2/',
      //   target: 'https://medsci-gateway.medon.com.cn/api/mg/medsci-mgr/',
      //   changeOrigin: true,
      //   pathRewrite: {
      //     '^/medsciSJ-mgr-center-service': ''
      //   }
      // },
    },
    overlay: {
      warnings: false,
      errors: true
    }
  },
  pluginOptions: {}
}
