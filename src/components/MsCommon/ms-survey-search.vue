<template>
  <div :class="{'input-label': label}">
    <label v-show="label">{{label}}</label>
    <el-select size="mini"
               v-model="result" 
               clearable 
               filterable 
               :style="{width: width || '100%'}"
               remote
               :remote-method="filterMethod"
               :allow-create="allowCreate"
               :placeholder="placeholder || '请选择'"
               :loading="loading"
               @change="change">
        <el-option v-for="(item,index) in optionSearch"
                   :key="index"
                   :label="item.templateName"
                   :value="index">
        </el-option>
    </el-select>
  </div>
</template>

<script>
  export default {
    name: "ms-survey-search",
    props: [
      "model",
      "label",
      "width",
      "placeholder",
      "allowCreate", //自定义添加
      'isOpenResult'
    ],
    data() {
      return {
        loading: false,
        result: null,
        optionSearch: []
      }
    },
    watch: {
      model: function(val) {
        this.result = val;
      }
    },
    methods: {
      filterMethod(val) {
        if(val && val.length >= 1) {
          this.getOptionData(val)
        }
      },
      change(val) {
        let selectVal = this.optionSearch[val] ? this.optionSearch[val] : '' //自定义添加判断
        this.result = selectVal ? selectVal.templateName : val //自定义添加判断
        this.$emit('update:model', this.result) 
        this.$emit('update:modelId', selectVal.id )
        this.$emit('bindData', {model: selectVal, operation: 'survey'})
      },
      getOptionData(val) {
        this.loading = true;
        // 接口获取数据
        let params = {
          value : val ? val : '',
          pageIndex: 1,
          approvalStatus: 1,
          pageSize: 20,
          isOpenResult:this.isOpenResult
        }
        this.api.getFormMaterialList(params).then(response => {
          this.loading = false
          if (response.status === 200) {
            this.optionSearch = response.data || []
          }
        })
      },
    }
  }
</script>
