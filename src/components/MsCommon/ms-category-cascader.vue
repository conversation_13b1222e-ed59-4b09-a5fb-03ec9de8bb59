<template>
  <div :class="{'input-label': label}">
    <label v-show="label">{{label}}</label>
    <el-cascader v-model="result"
                 ref="cascaderRef"
                 :options="optionSearch"
                 size="mini"
                 :placeholder="placeholder || '请选择'"
                 :clearable="!multiple"
                 :style="{width: width || '100%'}"
                 separator="-"
                 :show-all-levels="false"
                 :props="{children: level == 1 ? 'childrens' : 'children', label: 'titleCn',value: 'categoryId', emitPath: false, multiple: multiple}"
                 @change="change">
    </el-cascader>
  </div>
</template>

<script>
  export default {
    name: "ms-category-cascader",
    props: [
      "model",
      "modelName",
      "label",
      "width",
      "disabled",
      "config",
      "multiple",
      "type",
      "level",
      "placeholder"
    ],
    data() {
      return {
        loading: false,
        result: null,
        optionSearch: [],
      }
    },
    watch: {
      model: function(val) {
        if(this.multiple && val) {
          this.result = val.map(v => {
            return v.categoryId
          })
        } else {
          this.result = val;
        }
        
      }
    },
    created() {
      this.init()
      if(!this.multiple && this.model) {
        this.result = this.model;
      }
    },
    methods: {
      init() {
         if(this.config && this.config.moduleName=='shop'){
        let params = {
          module: this.config && this.config.moduleName || 'article'
        }
        this.loading = true;
        this.api.getProjectModuleAndCategoryList(params).then( response => {
          if(response.status === 200) {
           this.optionSearch = response.data.length>2?[response.data[2]]:[]
            if(this.type == 'all') {
              this.optionSearch.unshift({
                categoryId: 9999,
                titleCn: '全部',
                children:[{
                  categoryId: -1,
                  titleCn: '全部',
                }]
              })
            }
            // 期刊新增 未分类选项 2021/2/24 柏
            if(this.type == 'journal') {
              this.optionSearch.push({
                categoryId: 0,
                titleCn: '未分配专题',
                children:[{
                  categoryId: -1,
                  titleCn: '未分配专题',
                }]
              })
            }
          }
          this.loading = false;
        })
        }else{
          let params = {
          module: this.config && this.config.moduleName || 'article'
        }
        this.loading = true;
        this.api.getProjectModuleAndCategoryList(params).then( response => {
          if(response.status === 200) {
            if (response.data.length === 1) {
              this.optionSearch = response.data && response.data[0] && response.data[0].children || []
            } else {
              this.optionSearch = response.data || []
            }
            if(this.type == 'all') {
              this.optionSearch.unshift({
                categoryId: 9999,
                titleCn: '全部',
                children:[{
                  categoryId: -1,
                  titleCn: '全部',
                }]
              })
            }
            // 期刊新增 未分类选项 2021/2/24 柏
            if(this.type == 'journal') {
              this.optionSearch.push({
                categoryId: 0,
                titleCn: '未分配专题',
                children:[{
                  categoryId: -1,
                  titleCn: '未分配专题',
                }]
              })
            }
          }
          this.loading = false;
        })
        }
      }, 
      change(val) {
        let nodeContent = []
        let params = {}
        if (this.multiple) {
          let changeVal = val || []
          if (this.$refs['cascaderRef'].getCheckedNodes()) {
            this.$refs['cascaderRef'].getCheckedNodes().forEach(v => {
              if (changeVal.indexOf(v.value) !== -1) {
                nodeContent.push({
                  categoryId: v.value,
                  categoryName: v.label
                })
              }
            });
          }
          params = nodeContent
          this.$emit('update:model', params)
          this.$emit('bindData', {model: params, operation: 'department'})
        } else {
          nodeContent = this.$refs['cascaderRef'].getCheckedNodes()
          this.$emit('update:model', val)
          this.$emit('update:modelName', nodeContent.length > 0 ? nodeContent[0].label : '')
          this.$emit('getNodeData', nodeContent[0])
        }
      }
    }
  }
</script>
