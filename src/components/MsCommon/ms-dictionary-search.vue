<template>
  <div :class="{'input-label': label}">
    <label v-show="label">{{label}}</label>
    <el-select size="mini"
               v-model="result" 
               :style="{width: width || '100%'}"
               :loading="loading"
               :clearable="clearable"
               @change="change"
               value-key="userId">
        <el-option v-for="(item,index) in optionSearch"
                   :key="index"
                   :label="item.label"
                   :value="item.id"
                   :disabled="item.disabled">
        </el-option>
    </el-select>
  </div>
</template>

<script>
  export default {
    name: "ms-dictionary-search",
    props: [
      "model",
      "modelName",
      "label",
      "width",
      "type",
      "clearable",
      "disabled"
    ],
    data() {
      return {
        loading: false,
        result: null,
        optionSearch: []
      }
    },
    watch: {
      model: function(val) {
        if (val) {
          this.result = val
        } else {
          this.result = null
        }
      }
    },
    created() {
      this.result = this.model
      this.init()
    },
    methods: {
      init() {
        this.loading = true;
        let params = {
          "type": this.type
        }
        this.api.getValuesByType(params).then( response => {
          this.loading = false;
          if(response.status === 200) {
            this.optionSearch = response.data.map(v => {
              return {
                id: v.value,
                label: v.displayName,
                disabled: this.disabled && (v.value === 'meeting' || v.value === 'nsfc') ? true : false
              }
            })
          }
        })
      }, 
      change(val) {
        this.$emit('messageSearch')
        this.$emit('update:model', val)
        this.optionSearch.some(v => {
          if (v.id === val) {
            this.$emit('update:modelName', v.label)
            return 
          }
        })
      }
    }
  }
</script>
