<template>
    <div :class="{'input-label': label}">
        <label v-show="label">{{label}}</label>
        <el-select size="mini"
                v-model="result" 
                clearable 
                filterable 
                :style="{width: width || '100%'}"
                :loading="loading"
                remote 
                :remote-method="getList"
                @change="change"
                value-key="id"
                ref="speakerRef">
            <el-option v-for="item in optionSearch"
                    :key="item.id"
                    :label="item.name"
                    :value="item">
            </el-option>
        </el-select>
    </div>
</template>

<script>
    export default {
        name: "ms-speaker-search",
        props: [
            "model",
            // "modelName",
            "label",
            "width"
        ],
        data() {
            return {
                loading: false,
                result: null,
                optionSearch: []
            }
        },
        watch: {
            model: function(val) {
                this.result = val;
            }
        },
        created() {
            this.result = this.model;
        },
        methods: {
          getList(val) {
            this.optionSearch = []
            if(!val) {
              return
            }
            this.api.getAssociatedUserLecturer({pageIndex: 1, pageSize: 100, userIdNotNull: false, name:val}).then(response => {
              if (response.status === 200 && response.data) {
                this.optionSearch = response.data;
                this.optionSearch.forEach(element => {
                  element.name= `${element.name} ${element.unit}`
                });
              }
            })
          },
          change(val) {
            this.$nextTick(() => {
                this.$emit('update:model', val.name)
                this.$emit('update:modelId', val.id)
            })
          }
        }
    }
</script>
