<template>
  <section class="category-check" :key="symbolKey">
    <template v-if="type =='question'">
      <div class="category-btn" v-for="(item,index) in checkOptions" :key="index">
        <input type="checkbox" :id="`cate_${index}`" :disabled="disabled" v-model="checkModel" :value="item" />
        <label :for="`cate_${index}`" :style="disabled ? {cursor: 'not-allowed'}: ''">{{item.categoryName}}</label>
      </div>
    </template>
    <template v-else>
      <div v-for="(item,index) in checkOptions" :key="index">
        <div :ref="'categoryId' + item.categoryId" class="flex" style="margin-top: 8px;margin-bottom: 6px;overflow: hidden;" :style="{height: item.titleEn === 'activity' && !show ? '56px' : 'auto', marginBottom: (item.categoryName === '专科科室' && config.moduleName == 'article')? heightTag + 'px' : '6px'}" >
          <template v-if="item.categoryName === '专科科室'">
            <span ref="otherDept" v-show="hasLabel" class="category-check-label">专科科室</span>
          </template>
          <template v-else>
            <span v-show="hasLabel" class="category-check-label">
              <!-- <span v-show="item.categoryName === '横向分类'" class="error-label">*</span> -->
              {{item.categoryName}}
            </span>
          </template>

          <div class="category-check-group">
            <div class="category-btn" 
                v-for="(itemC, indexC) in item.children"
                :key="indexC"
                >
              <input type="checkbox" :id="`cate_${index}_${indexC}`" :disabled="disabled" v-model="checkModel" :value="itemC" />
              <label :for="`cate_${index}_${indexC}`" :style="disabled ? {cursor: 'not-allowed'}: ''">{{itemC.categoryName}}</label>
            </div>
          </div>
        </div>
        <div class="arrows" v-if="item.titleEn === 'activity'" :style="{transform: show ? 'rotate(180deg)' : 'rotate(0deg)'}" @click="show = !show"><i class="el-icon-arrow-down"></i></div>
      </div>
      <!-- 引流项目 -->
      <div v-if="drainageModel&&drainageModel.length>0">
        <div class="flex">
          <span class="category-check-label">
              引流项目
          </span>
          <div class="category-check-group">
            <div class="category-btn" 
                v-for="(itemC, indexC) in drainageModel"
                :key="indexC"
                >
              <input type="checkbox" :id="`cate_${indexC}`" :disabled="drainagecheckModel.length>0 && drainagecheckModel[0].categoryId != itemC.categoryId" v-model="drainagecheckModel" :value="itemC" />
              <label :for="`cate_${indexC}`" :style="disabled ? {cursor: 'not-allowed'}: ''">{{itemC.categoryName}}</label>
            </div>
          </div>
        </div>
      </div>
    </template>
  </section>
</template>

<script>
  export default {
    name: 'ms-category-check',
    data() {
      return {
        checkOptions: [],
        checkModel: [],
        hasLabel: this.config.notLabel ? false : true,
        hasBorder: this.config.hasBorder ? true : false,
        show: false,
        top: 0,
        symbolKey: '',
        oldCheckModel: [],
        drainageModel: [],
        drainagecheckModel :[]
      }
    },
    props: {
      config: {
        type: Object,
        default: () => {
          return {}
        }
      },
      model: {
        type: Array,
        default: () => {
          return []
        }
      },
      disabled: {
        type: Boolean,
        default: false
      },
      type: {
        type: String,
        default: ''
      },
      heightTag: {
        type: Number,
        default: 150
      },
      drainageList: {
        type: Array,
        default: () => {
          return []
        }
      },
    },
    watch: {
      model: function(val) {
        console.log(val,3);
        this.checkModel = val
      },
      checkModel: function(val) {
        let resfresh = false;
        let diff = {}
        if(this.oldCheckModel.length > val.length) {
          diff = this.oldCheckModel.filter(value => val.indexOf(value) == -1)[0]
          // let Arr = this.checkOptions.filter(item=>item.categoryId == 20353)
          if(this.drainageModel.length != 0){
            this.drainageModel.forEach(e => {
                 if(e.categoryId == diff.categoryId){
                  this.drainageModel.splice(this.drainageModel.indexOf(e),1)
                  if(this.drainagecheckModel != 0 && diff.categoryId == this.drainagecheckModel[0].categoryId){
                    this.drainagecheckModel.splice(this.drainageModel.indexOf(e),1)
                  }
                 }
            })
          }
        } else {
          diff = val[val.length-1]
          let Arr = this.checkOptions.filter(item=>item.categoryId == 20353)
          if(Arr.length != 0){
            Arr[0].children.forEach(e => {
              // 是选的项目则添加
                 if(e.categoryId == diff.categoryId){
                  this.api.getProjectDetailByCategoryIds({categoryIds:[e.categoryId]}).then(res => {
                    // console.log(res,'kkk');
                    // console.log(res.data[0].disparkState,'sss');
                    if(res.status == 200 && res.data.length>0){
                      // 判断项目是否是  开发和半开发式，需要引流，是APOC , 未删除状态 ，正在进行中
                      if(res.data[0].disparkState !== 0 && res.data[0].drainage === '1' && res.data[0].possessor === 'APOC' && res.data[0].deleted === 0 && res.data[0].state ===1){
                        // 去重
                        // console.log(this.drainageModel.findIndex(e => e.categoryId == item.categoryId) == -1);
                        if(this.drainageModel.findIndex(e => e.categoryId == res.data[0].categoryId) == -1){
                          this.drainageModel.push({url:res.data[0].domain, categoryId:res.data[0].categoryId,categoryName:res.data[0].name})
                        }
                      }
                    }
                  })
                 }
            })
          }
        }
        this.checkOptions.forEach((item) => {
          item.children.forEach((child) => {
            if(child.categoryId == diff.categoryId) {
              if(item.categoryName == '内科' || item.categoryName == '外科' || item.categoryName == '其它科室') {
                resfresh = true
              }
            }
          })
        })
        this.oldCheckModel=val;
        this.$emit('changeCategoryT', resfresh)
        this.$emit('update:model', val)
        this.$emit('changeCategory', val)
      },
      drainagecheckModel:function(){
          // console.log(this.drainagecheckModel,'hhhh');
          if(this.drainagecheckModel.length == 0){
            this.$emit('changeDrainage', [])
          }else{
            this.$emit('changeDrainage', this.drainagecheckModel)
          }
      }
    },
   async created() {
      this.symbolKey = Symbol(new Date().toString())
      if (this.model.length > 0) {
        this.checkModel = this.model
      }
      await this.init()

      setTimeout(() => {
        // if(this.drainageList)
        let Arr = this.checkOptions.filter(item=>item.categoryId == 20353)
        if(Arr.length != 0 && this.model.length > 0){
          let params = []
          Arr[0].children.forEach(e => {
            let a = this.checkModel.filter(item=>item.categoryId == e.categoryId)
            if(a.length != 0 ){
             return params.push(a[0].categoryId)
            }
          })
          this.api.getProjectDetailByCategoryIds({categoryIds:params}).then(res => {
            if(res.status == 200 && res.data.length>0){
              // 判断项目是否是  开发和半开发式，需要引流，是APOC , 未删除状态 ，正在进行中
              res.data.forEach(item=>{
                if(item.disparkState !== 0 && item.drainage === '1' && item.possessor === 'APOC' && item.deleted === 0 && item.state ===1){
                  if(this.drainageModel.findIndex(e => e.categoryId == item.categoryId) == -1){
                    this.drainageModel.push({
                      url:item.domain,
                      categoryId:item.categoryId,
                      categoryName:item.name
                    })
                  }
                }
              })
              if(this.drainageList.length != 0 && this.drainageList[0].projectName){
                this.drainagecheckModel = this.drainageModel.filter(item=>item.categoryName == this.drainageList[0].projectName)
              }
            }
          })
        }
      }, 1500);

    },
    mounted() {
    },
    updated() {
      // console.log(window.sessionStorage.getItem('otherDeptTop'), 'tttttttttt')
      // console.log(this.$refs.otherDept[0].offsetTop, 'tttttttttt')
      // console.log(this.$refs.otherDept[0].clientHeight, 'tttttttttt')
      // let otherTop = Number(window.sessionStorage.getItem('otherDeptTop'));
      // if(otherTop < 714) {
      //   otherTop = 714
      // }
      // if(otherTop > 750) {
      //   otherTop = 750
      // }
      // let top = otherTop + Number(this.$refs.otherDept[0].offsetTop) + Number(this.$refs.otherDept[0].clientHeight)
      // console.log(top, 'top')
      // this.$emit('passTop', top)
      var that = this;
      setTimeout(function() {
        // top = Number(window.sessionStorage.getItem('otherDeptTop')) + Number(that.$refs.otherDept[0].offsetTop) + Number(that.$refs.otherDept[0].clientHeight)
        let top
        if(that.$refs.otherDept){
           top = Number(window.sessionStorage.getItem('otherDeptTop')) + Number(that.$refs.otherDept[0].offsetTop) + Number(that.$refs.otherDept[0].clientHeight)
        }else{
           top = Number(window.sessionStorage.getItem('otherDeptTop'))
        }
        
        that.$emit('passTop', top)
      }, 1100)
    },
    destroyed() {
    },
    methods: {
     async init() {
       if(this.config && this.config.moduleName=='shop'){
         let params = {
          module: this.config && this.config.moduleName || 'article'
        }
       await this.api.getProjectModuleAndCategoryList(params).then(response => {
          let arr  = []
          if (response.status === 200) {
            let data = response.data.length>2?[response.data[2]]:[]
            for (let i = 0; i< data.length; i++) {
              let dataArr = {categoryId: data[i].categoryId, categoryName: data[i].titleCn, titleEn: data[i].titleEn,children: []}
              let children = data[i].children || []
              for (let j = 0; j < children.length; j++) {
                dataArr.children.push({categoryId: children[j].categoryId, categoryName: children[j].titleCn, tenant: children[j].tenant})
                // if(data[i].titleCn == '内科' || data[i].titleCn == '外科' || data[i].titleCn == '专科科室') {
                //   console.log('内科')
                //   dataArr.children.push({categoryId: children[j].categoryId, categoryName: children[j].titleCn, tenant: children[j].tenant,parentId: data[i].categoryId})
                // } else {
                //   dataArr.children.push({categoryId: children[j].categoryId, categoryName: children[j].titleCn, tenant: children[j].tenant})
                // }
              }
              arr.push(dataArr)
            }
            this.checkOptions = arr
            // console.log(this.checkOptions,1111);
          }
        })
       }else{
         let params = {
          module: this.config && this.config.moduleName || 'article'
        }
       await this.api.getProjectModuleAndCategoryList(params).then(response => {
          let arr  = []
          if (response.status === 200) {
            let data = response.data
            for (let i = 0; i< data.length; i++) {
              let dataArr = {categoryId: data[i].categoryId, categoryName: data[i].titleCn, titleEn: data[i].titleEn,children: []}
              let children = data[i].children || []
              for (let j = 0; j < children.length; j++) {
                dataArr.children.push({categoryId: children[j].categoryId, categoryName: children[j].titleCn, tenant: children[j].tenant})
                // if(data[i].titleCn == '内科' || data[i].titleCn == '外科' || data[i].titleCn == '专科科室') {
                //   console.log('内科')
                //   dataArr.children.push({categoryId: children[j].categoryId, categoryName: children[j].titleCn, tenant: children[j].tenant,parentId: data[i].categoryId})
                // } else {
                //   dataArr.children.push({categoryId: children[j].categoryId, categoryName: children[j].titleCn, tenant: children[j].tenant})
                // }
              }
              arr.push(dataArr)
            }
            this.checkOptions = arr
            // console.log(this.checkOptions,1111);
          }
        })
       }
        
      }
    }
  }
</script>

<style lang="scss" scoped>
  .arrows {
    cursor: pointer;
    margin-bottom: 16px;
    text-align: center;
    i {
      font-size: 14px;
    }
  }
  .category-check {
    &-label {
      width: 70px;
      font-size: 12px;
      // color: #4B74FF;
      color: #409EFF;
      text-align: right;
      margin-right: 20px;
      position: relative;
      top: 3px;
      font-weight: 600;
      .error-label {
        color: #F56C6C;
      }
    }
    &-group {
      width: 100%;
      text-align: left;
    }
    .category-btn {
      display: inline-block;
      margin-right: 20px;
      margin-bottom: 6px;
      position:relative;
      input[type="checkbox"] {
        position:absolute;
        width:0;
        height:0;
        opacity: 0;
      }
      input[type="radio"] {
        position:absolute;
        width:0;
        height:0;
        opacity: 0;
      }
      label{
        height: 24px;
        line-height: 24px;
        padding: 0 15px;
        border-radius: 5px;
        background-color:#F0F2F5;
        font-size: 11px;
        font-weight: 400;
        display:inline-block;
        cursor: pointer;
      }
      input:checked+label {
        // background-color: #4B74FF;
        background-color: #409EFF;
        color: #fff;
      }
    }
  }
</style>
