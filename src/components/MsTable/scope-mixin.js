// 列表模块展示
import tableScopeBase from './scope/table-scope-base'

// 自定义列表头
import tableScopeHeader from './scope/table-scope-header'

// 列表操作按钮
import operationScopeButton from './operation/operation-scope-button'
// 列表操作按钮
import operationScopeButtonInfo from './operation/operation-scope-button-info'

// 列表扩展字段展示
import tableScopeExpand from './scope/table-scope-expand'

// 分页组件
import tablePageScroll from './paging/table-page-scroll'
import tablePageDefault from './paging/table-page-default'

export default {
  components: {
    tableScopeBase,
    tableScopeHeader,
    operationScopeButton,
    operationScopeButtonInfo,
    tablePageScroll,
    tablePageDefault,
    tableScopeExpand
  }
}
