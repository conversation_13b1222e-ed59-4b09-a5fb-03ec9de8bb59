<template>
  <div class="table-scope">
    <!-- 展示图标 -->
    <template v-if="config.type === 'icon'">
      <svg-icon :icon-class="config.icon" :style="{color: config.color,position: 'relative',top: '-1px'}" v-if="scope.row[index] === config.showVal"></svg-icon>
    </template>
    <!-- 图片展示 -->
    <template v-else-if="config.type === 'image'">
      <div style="cursor: pointer" v-if="typeof(scope.row[index])=='object'&&scope.row[index].constructor==Array" @click="scope_click(config.config)">[查看图片]</div>
      <el-image v-else-if="typeof(scope.row[index])=='string'&&scope.row[index].constructor==String&&scope.row[index]!=''" @click="scope_click(config.config)" :src="scope.row[index]" fit="cover" style="width: 120px; height: 80px;padding: 6px 0 0;text-align: left;cursor: pointer;"/>
    </template>
    <!-- 订单-发放凭证 -->
    <template v-else-if="config.type === 'vouchers'">
      <div style="cursor: pointer" v-if="scope.row[index]&&scope.row[index].length" @click="scope_click(config.config)">[查看图片]</div>
    </template>
    <!-- 视频图片展示 -->
    <template v-else-if="config.type === 'video'">
      <el-image @click="scope_click(config.config)" :src="scope.row[index]" fit="cover" style="width: 120px; height: 80px;padding: 6px 0 0;text-align: left; cursor: pointer;"/>
      <i class="el-icon-video-play" @click="scope_click(config.config)" style="position:absolute; left: 38%; top: 36%;color: rgba(255,255,255,.7);font-size: 30px;cursor: pointer;"></i>
    </template>
    <!-- 状态展示 -->
    <template v-else-if="config.type === 'status'">
      <template v-if="config.rule[scope.row[index]]">
        <label class="status-label" :style="{background: config.rule[scope.row[index]].background}"></label>
        <span v-text="config.rule[scope.row[index]].label"></span>
      </template>
    </template>
    <!-- 状态文字展示 -->
    <template v-else-if="config.type === 'status-char'">
      <template v-if="config.rule[scope.row[index]]">
        {{ config.rule[scope.row[index]].label }}
      </template>
    </template>
    <!-- 状态按钮展示 -->
    <template v-else-if="config.type === 'status-button'">
      <template v-if="config.rule[scope.row[index]]">
        <span style="display: inline-block;border-radius: 4px;color: #ffffff;line-height: 22px;padding: 0 8px;" :style="{background: config.rule[scope.row[index]].background}">{{ config.rule[scope.row[index]].label }}</span>
      </template>
    </template>
    <!-- 编码展示 -->
    <template v-else-if="config.type === 'code'">
      <template v-if="config.rule[scope.row[index]]">
        <span>{{config.rule[scope.row[index]].label}}</span>
      </template>
    </template>
    <!-- 多个字段展示 -->
    <template v-else-if="config.type === 'fidd'">
      <span v-for="(itemLink, indexLink) in config.fields"
            :key="indexLink">
        <span v-show="indexLink !== 0">{{config.connector?config.connector:'/'}}</span>
        <template v-if="itemLink.way === 'page'">
          <el-link style="vertical-align:top" @click="scope_click(itemLink)" type="primary">{{scope.row[itemLink.name]}}</el-link>
        </template>
        <template v-else>
          <span>{{scope.row[itemLink.name]!=null?scope.row[itemLink.name]:'-'}}</span>
        </template>
      </span>
    </template>
    <!-- 链接跳转 -->
    <template v-else-if="config.type === 'link'">
      <el-link :href="config.url" type="primary" :target="config.target || '_blank'">{{scope.row[index]}}</el-link>
    </template>
    <!-- 文章预览 -->
    <template v-else-if="config.type === 'preview'">
      <el-link type="primary" @click="scope_click(config.config, config.type)">{{scope.row[index]}}</el-link>
    </template>
    <!-- 时间格式化 -->
    <template v-else-if="config.type === 'formatTime'">
      <p style="overflow:hidden;text-overflow:ellipsis;white-space:nowrap;">{{scope.row[index] | parseTime(config.cFormat || '')}}</p>
    </template>
    <!-- switch滑块 -->
    <template v-else-if="config.type === 'switch'">
      <el-switch
        v-model="scope.row[index]"
        :active-value="config.activeVal"
        :inactive-value="config.inactiveVal"
        @change="swithchChange($event,config.config)">
      </el-switch>
    </template>
    <!-- 数字比较 -->
    <template v-else-if="config.type === 'number'">
      <span v-if="parseInt(scope.row[index]) > 0" style="color: #259B24">+{{scope.row[index]}}</span>
      <span v-else style="color: #B30F14">{{scope.row[index]}}</span>
    </template>
    <!-- 查询 -->
    <template v-else-if="config.type === 'query'">
      <el-link @click="scope_click(config.config)" class="link-scope" v-text="scope.row[index]"></el-link>
      &nbsp;
      <el-link v-if="config.config.jumpObj" class="link-scope" style="font-weight: bold; position: relative;top: 2px;" @click="scope_click(config.config.jumpObj)"><i class="el-icon-search"></i></el-link>
    </template>
    <!-- 链接跳转 -->
    <template v-else-if="config.type === 'webLink'">
      <el-link @click="scope_click(config.config)" type="primary" class="link-scope" v-text="scope.row[index]"></el-link>
    </template>
    <!-- 查看 -->
    <template v-else-if="config.type === 'showMore'">
      <el-link @click="scope_click(config.config)" type="primary" class="link-scope">查看</el-link>
    </template>
    <!-- 项目名称展示 -->
    <template v-else-if="config.type === 'projectName'">
      <!-- <el-link @click="scope_click(config.config)" type="primary" class="link-scope" v-text="scope.row[index]"></el-link> -->
      <div>
        <el-tooltip
        popper-class="tips-con"
        placement="top"
        :content="JSON.parse(scope.row[index]).map(item=>item.categoryName).join(',')"
        >
        <span style="cursor:pointer;">{{JSON.parse(scope.row[index])[0].categoryName}}</span>
        </el-tooltip>
      </div>   
    </template>
    <!-- 用户名称 -->
    <template v-else-if="config.type === 'nick'">
      <div>{{scope.row.customData.nick||''}}</div>
    </template>
    <!-- 其他操作 -->
    <template v-else-if="config.type === 'other'">
      <el-link :style="{color: config.config.color?config.config.color:''}" @click="scope_click(config.config)" type="primary" class="link-scope" v-html="scope.row[index]"></el-link>
    </template>
    <!-- 展示信息，换行 -->
    <!-- <template></template> -->
    <!-- <el-tooltip class="item" effect="dark" :content="String(scope.row[index])" placement="top-start" v-else> -->
    <p v-else-if="config.type === 'copy'" v-text="scope.row[index]" v-copy style="cursor: pointer;"></p>
    <!-- 复制 -->
    <template v-else-if="config.type === 'copyLink'">
      <div v-if="config.hiddenText">
        <svg-icon style="cursor: pointer; margin-left: 20px" icon-class="icon-copy" v-copy :data-text="config.subStr ? scope.row[index].substring(32): scope.row[index]"></svg-icon>
      </div>
      <div v-else>
        <span v-if="config.noLink">{{scope.row[index]}}</span>
        <a v-else :href="scope.row[index]" target="_blank" style="color: #0099ff;">{{scope.row[index]}}</a>
        <svg-icon style="cursor: pointer; margin-left: 20px" icon-class="icon-copy" v-copy :data-text="config.subStr ? scope.row[index].substring(32): scope.row[index]"></svg-icon>
      </div>
    </template>
    <!-- render -->
    <template v-else-if="config.type === 'render'">
      <ms-render :scope="scope" :render="config.render"></ms-render>
    </template>
    <p v-else v-text="scope.row[index]"></p>
    <!-- </el-tooltip> -->
  </div>
</template>

<script>
  export default {
    name: 'table-scope-base',
    data() {
      return {
        config: {}
      }
    },
    props: {
      index: String, // => 列字段属性
      scopeConfig: { // => 列字段显示配置
        type: Object,
        default: () => {
          return {}
        }
      },
      scope: { // => 行内值
        type: Object,
        default: () => {
          return {}
        }
      }
    },
    created() {
      if (this.scopeConfig[this.index]) {
        this.config = this.scopeConfig[this.index](this.scope.row) ? this.scopeConfig[this.index](this.scope.row) : {};
      }
    },
    methods: {
      scope_click(config, way) {
        this.$emit('scope-click', {model: this.scope.row, operation: {...config, way: config.way || way} })
      },
      swithchChange($event, config) {
        this.$emit('scope-click', {model: this.scope.row, operation: {...config, way: config.way, valName: $event}})
      }
    }
  }
</script>

<style>
.link-scope {
  width: auto;
  justify-content: left !important;
  max-height: 115px;
  font-size: 12px !important;
  align-items: flex-start !important;
}
.link-scope span {
  width: 100%;
  display: inline-block;
  text-align: left;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
</style>

