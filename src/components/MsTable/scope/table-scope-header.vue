<template>
  <span class="table-header">
    <!-- 列表头部下拉菜单 -->
    <template v-if="config.type === 'dropdown'">
      <el-dropdown trigger="click" size="medium" @command="header_operation">
        <span class="el-dropdown-link" style="cursor: pointer;" :class="{'drop-ch': (selectVal || selectVal === 0) && selectVal !== -1 ? true : false}">
          <span v-text="label" class="table-header-span"></span>
          <svg-icon v-if="config.icon" class-name="table-header-icon" :icon-class="config.icon"></svg-icon>
        </span>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item v-for="(item,index) in config.options"
                            :key="index"
                            :command="item.value"
                            v-text="item.label"
                            :style="{color: item.value === selectVal ? '#409EFF': ''}">
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </template>
    <template v-else-if="config.type === 'multi'">
      <el-dropdown trigger="click" size="medium" @command="header_operation" class="header-dropdown">
        <span class="el-dropdown-link" style="cursor: pointer;">
          <span v-text="label" class="table-header-span"></span>
          <svg-icon v-if="config.icon" class-name="table-header-icon" :icon-class="config.icon"></svg-icon>
        </span>
        <el-dropdown-menu slot="dropdown">
          <div class="multi_area" style="padding: 0 15px;">
            <el-checkbox-group v-model="multiVal" >
              <div v-for="(item,index) in config.options || []"
                   style="margin-top: 4px;" 
                   :key="index"><el-checkbox :label="item.value">{{item.label}}</el-checkbox>
              </div>
            </el-checkbox-group>
            <el-button type="text" @click="header_operation(multiVal)">筛选</el-button>
            <el-button type="text" @click="header_operation([])">重置</el-button>
          </div>
        </el-dropdown-menu>
      </el-dropdown>
    </template>
    <template v-else-if="config.type === 'tip'">
      <span v-text="label" class="table-header-span"></span>
      <el-tooltip class="item" effect="light" :content="config.text" placement="right"><i class="el-icon-info"></i></el-tooltip>
    </template>
     <!-- 列表头部说明 -->
    <template v-else-if="config.type === 'tooltip'">
      <el-tooltip class="item" popper-class="tips-con" :content="config.text" placement="top">
        <span v-text="label" class="table-header-span" style="color: #f00;"></span>
      </el-tooltip>
    </template>
     <!-- 指定列表头部样式 -->
     <template v-else-if="config.type === 'styleColor'">
        <span v-text="label" class="table-header-span" :style="{'color': config.text}"></span>
    </template>
    <!-- 基础头部标签展示 -->
    <template v-else><span v-text="label" class="table-header-span"></span></template>
  </span>
</template>

<script>
  import {mapGetters} from 'vuex';
  export default {
    name: 'table-scope-header',
    data() {
      return {
        config: {},
        selectVal: null,
        multiVal: []
      }
    },
    props: {
      index: String, // => 列字段属性
      label: String, // => 列头展示标签
      scopeHeader: { // => 列字段显示配置
        type: Object,
        default: () => {
          return {}
        }
      },
      scope: { // => 行内值
        type: Object,
        default: () => {
          return {}
        }
      }
    },
    computed: {
      ...mapGetters(['tableDropId']),
    },
    watch: {
      tableDropId: function(val) {
        this.selectVal = val
      }
    },
    created() {
      if (this.scopeHeader[this.index]) {
        this.config = this.scopeHeader[this.index](this.scope.row) ? this.scopeHeader[this.index](this.scope.row) : {};
      }
    },
    methods: {
      header_operation(val) {
        if (this.PUBLIC_Methods.isArrayFn(val)) {
          this.multiVal = val
        } else {
          this.selectVal = val
        }
        let res = {
          index: this.index,
          operation: this.config.operation,
          value: val,
          config: this.config
        }
        this.$emit('change', res)
      }
    }
  }
</script>

<style>
  .table-header .el-dropdown{
    padding-left: 0 !important;
    top: -1px !important;
  }
</style>

<style lang="scss" scoped>
.table-header {
  text-align: center;
  display: inline-block;
  /deep/ div {
    line-height: 26px;
    overflow: inherit;
  }
  .drop-ch {
    span {
      color: #409EFF !important;
    }
    .table-header-icon {
      color: #409EFF !important;
    }
  }
  &-span {
    font-size: 12px;
    color: #999;
  }
  &-icon {
    width: 16px !important;
    height: 16px !important;
    position: relative;
    top: -2px;
  }
}
</style>
