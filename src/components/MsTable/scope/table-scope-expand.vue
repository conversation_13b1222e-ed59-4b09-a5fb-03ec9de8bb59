<template>
  <el-col :span="colSpan || 24" class="expand-col">
    <!-- 链接跳转 -->
    <template v-if="type === 'link' && scope.row[index]">
      <el-link :href="scope.row[index]" target="__blank">{{label}}: {{scope.row[index]}}</el-link>
    </template>
    <p  v-else-if="type === 'htm'" class="word-break flex height-line" style="font-size: 14px;color: #606266;"><span class="label">{{label}}: </span> <span v-html="scope.row[index]"></span></p>
    <p v-else class="word-break" style="font-size: 14px;color: #606266;">{{label}}: {{scope.row[index]}}</p>
  </el-col>
</template>

<script>
  export default {
    name: 'table-scope-expand',
    props: {
      index: String, // => 列字段属性
      label: String, // => 字段标题
      type: String, // => 展示字段属性
      colSpan: Number, // => 展示区域
      scope: { // => 行内值
        type: Object,
        default: () => {
          return {}
        }
      }
    }
  }
</script>

<style scoped >
.expand-col {
  margin-bottom: 6px;
  font-size: 12px;
  color: #333;
}
.expand-col .label {
  display: inline-block;
  width: 60px;
}
.expand-col .height-line {
  line-height: 16px;
}
</style>
