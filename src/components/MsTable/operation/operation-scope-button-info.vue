<template>
  <div class="table-operation" style="margin: auto;">
    <span v-for="(item, index) in model"
          :key="index">
      <!-- 普通按钮 -->
      <!-- v-if="scope.row.status != 1" -->
      <!-- <el-button 
        :type="item.type"
        @click="operation_click(item)"
        :disabled="item.disabled ? item.disabled(scope.row) : false"
        :style="!(item.showField && !scope.row[item.showField]) && (item.showCallback ? item.showCallback(scope.row) : true) ? {} : {'display': 'none'}"
        size="mini"
        class="scope-btn"
        plain>审核
      </el-button> -->
      <el-button
        :type="item.rule[scope.row[item.field]].type"
        v-show="!item.roleDisabled && (item.showCallback ? item.showCallback(scope.row) : true)"
        :disabled="item.rule[scope.row[item.field]].disabled"
        @click="operation_click(item, item.rule[scope.row[item.field]].operation)"
        size="mini"
        class="scope-btn"
        plain>{{item.rule[scope.row[item.field]].label}}
      </el-button>
    </span>
  </div>
</template>

<script>
export default {
	name: "operation-scope-button-info",
	data () {
		return {
      domConfig: null, 
    }
	},
	props: {
		scope: { // => 行内值
      type: Object,
      default: () => {
        return {}
      }
    },
		model: { // => 操作按钮数组
      type: Array,
      default: () => {
        return []
      }
    }
  },
	methods: {
		operation_click (val, operation) {
			this.$emit('change', { model: this.scope.row, operation: {...val, operation: operation || val.operation} })
    },
    operation_drop(val) {
      this.$emit('change', { model: this.scope.row, operation: val })
    }
	}
}
</script>
