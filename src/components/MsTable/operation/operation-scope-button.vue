<template>
  <div class="table-operation" style="margin: auto;">
    <span v-for="(item, index) in model"
          :key="index">
      <!-- 图标按钮 -->
      <el-tooltip effect="dark"
                  :content="item.label"
                  v-if="item.icon">
        <label class="table-operation-icon"
               @click="operation_click(item)">
          <svg-icon :icon-class="item.icon"></svg-icon>
        </label>
      </el-tooltip>
      <!-- 下拉按钮 -->
      <el-dropdown trigger="click" size="medium" @command="operation_drop"
                   v-else-if="item.operation === 'more'"
                   placement="bottom-start">
        <span class="el-dropdown-link">
          <el-button size="mini" class="scope-btn" :type="item.type" plain v-show="item.showCallback ? item.showCallback(scope.row) : true">{{item.label}}</el-button>
        </span>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item v-for="(itemC,indexC) in item.children"
                            :key="indexC"
                            :command="itemC"
                            v-show="!itemC.roleDisabled && (itemC.showCallback ? itemC.showCallback(scope.row) : true)"
                            v-text="itemC.label || itemC.rule[scope.row[itemC.field]].label">
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
      <!-- 状态按钮 -->
      <el-button v-else-if="item.rule && item.rule[scope.row[item.field]]"
                 :type="item.rule[scope.row[item.field]].type"
                 v-show="!item.roleDisabled && (item.showCallback ? item.showCallback(scope.row) : true)"
                 :disabled="item.rule[scope.row[item.field]].disabled"
                 @click="operation_click(item, item.rule[scope.row[item.field]].operation)"
                 size="mini"
                 class="scope-btn"
                 plain>{{item.rule[scope.row[item.field]].label}}
      </el-button>
      <!-- 普通按钮 -->
      <el-button v-else-if="item.label && !item.roleDisabled"
                 :type="item.type"
                 @click="operation_click(item)"
                 :disabled="item.disabled ? item.disabled(scope.row) : false"
                 :style="!(item.showField && !scope.row[item.showField]) && (item.showCallback ? item.showCallback(scope.row) : true) ? {} : {'display': 'none'}"
                 size="mini"
                 class="scope-btn"
                 plain>{{item.label}}
      </el-button>
    </span>
  </div>
</template>

<script>
export default {
	name: "operation-scope-buttons",
	data () {
		return {
      domConfig: null, 
    }
	},
	props: {
		scope: { // => 行内值
      type: Object,
      default: () => {
        return {}
      }
    },
		model: { // => 操作按钮数组
      type: Array,
      default: () => {
        return []
      }
    }
  },
	methods: {
		operation_click (val, operation) {
			this.$emit('change', { model: this.scope.row, operation: {...val, operation: operation || val.operation} })
    },
    operation_drop(val) {
      this.$emit('change', { model: this.scope.row, operation: val })
    }
	}
}
</script>
