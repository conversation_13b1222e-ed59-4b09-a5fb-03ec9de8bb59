<template>
  <el-pagination :current-page="currentPage"
                 :page-size="pageSize"
                 :page-sizes="pageSizes"
                 :total="total"
                 @current-change="current_change"
                 @size-change="size_change"
                 layout="total, sizes, prev, pager, next, jumper"
      ></el-pagination>
</template>

<script>
// import { mapGetters } from "vuex";
export default {
	name: "table-page-scroll",
	data () {
		return {
      // currentPageX: 1,
    }
  },
  // computed: {
  //   ...mapGetters(["listSearchParams"])
  // },
	props: [
    "total",
    "page-size",
    "page-sizes",
    "current-page"
  ],
  // watch: {
  //   "listSearchParams.pageIndex": function(val) {
  //     if (val) {
  //       this.currentPageX = val;
  //     }
  //   }
  // },
  // created() {
  //   this.currentPageX = this.listSearchParams.pageIndex ? this.listSearchParams.pageIndex : this.currentPage
  // },
  methods: {
    size_change (val) {
      this.$emit("size-change", val);
    },
    current_change(val) {
      this.$emit("current-change", val);
    }
  }
}
</script>
