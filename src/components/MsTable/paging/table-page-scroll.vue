<template>
  <div style="text-align: center">
    <el-button plain
               v-text="needLoad ? '加载更多' : '暂无更多数据'"
               :disabled="!needLoad"
               @click="btn_click">
    </el-button>
  </div>
</template>

<script>
export default {
	name: "table-page-default",
	data () {
		return {
      needLoad: false 
    }
	},
	props: [
    "total",
    "page-size",
    "current-page"
  ],
  computed: {
    btnStatus() {
      const { total, currentPage } = this
      return { total, currentPage }
    }
  },
  watch: {
    btnStatus: {
      handler: function(val) {
        this.needLoad = val.total > this.pageSize * (val.currentPage) ? true : false
      },
      deep: true
    }
  },
	methods: {
		btn_click () {
      if(this.needLoad) {
        this.$emit('current-change', { operation: 'scroll', currentPage: this.currentPage })
      }
		}
	}
}
</script>
