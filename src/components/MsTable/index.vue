<template>
	<div class="ms-table">
		<div class="clearfix ms-table-search">
			<slot name="ms-table-header"></slot>
		</div>
		<el-table
			:data="tableData"
			:header-cell-style="headerCellStyle"
			:header-row-style="headerRowStyle"
      :row-style="rowStyle"
			highlight-current-row
      size="mini"
			style="width: 100%;"
      :height="elTableHeight"
			ref="singleTable"
      :lazy="lazy"
      :load="load"
      :row-key="rowKey"
      :tree-props="treeProps"
      :row-class-name="rowColor"
      :expand-row-keys="expandRowKeys"
      class="table-style"
			@selection-change="handleSelectionChange"
			v-loading="loading"
      @row-click="rowClick">
      <!-- 列表多选 -->
      <el-table-column type="selection"
                       v-if='showSelection'
                       :selectable="msSelectable"
                       width="55"
                       align="center">
      </el-table-column>
        <!-- 列表序号 -->
			<el-table-column type="index"
                       v-if="showIndex"
                       width="80"
                       label="序号">
      </el-table-column>
      <!-- 展开行 -->
      <el-table-column type="expand"
                       v-if="expandList && expandList.length > 0">
        <template slot-scope="props">
          <!-- 列表扩展字段 -->
          <el-row class="expand-area">
            <table-scope-expand v-for="(item, index) in expandList"
                                :key="index"
                                :index="item.property"
                                :label="item.label"
                                :type="item.type"
                                :scope="props"
                                :colSpan="item.colSpan || 24"/>
          </el-row>
        </template>
      </el-table-column>
      <!-- 列表组件 -->
      <template v-for="(item, index) in tableHeader">
        <el-table-column :column-key="item.property"
                         :key="index"
                         :label="item.label"
                         :property="item.property"
                         :sortable="item.sortable"
                         :min-width="item.width || 100"
                         align="left"
                         :fixed="item.fixed ? item.fixed : false">
          <!-- 自定义列表头 -->
          <template slot="header" slot-scope="scope">
            <component :is="item.componentHead || 'tableScopeHeader'"
                       :scope="scope"
                       :label="item.label || ''"
                       :scopeHeader="scopeHeader"
                       :index="item.property || ''"
                       @change="header_operation">
            </component>
          </template>
          <!-- 列表信息展示 -->
          <template slot-scope="scope">
            <component :is="item.component || 'tableScopeBase'"
                       :index="item.property || ''"
                       :scope="scope"
                       :scopeConfig="scopeConfig"
                       @scope-click="scope_click">
            </component>
          </template>
        </el-table-column>
      </template>
      <!-- 列表行内按钮 -->
			<el-table-column :width="operationWidth"
                       column-key="column.operation"
                       fixed="right"
                       label="操作"
                       v-if="operationButtons.length"
                       align="center">
				<template slot-scope="scope">
					<component :is="operation"
                     :model="operationButtons"
                     :scope="scope"
                     @change="operation_change">
          </component>
				</template>
			</el-table-column>
		</el-table>
    <!-- 列表分页 -->
    <div class="ms-table-pagination" v-if="pageSize > 0">
        <component :is="pageComponent"
                   :total="total"
                   :page-size="pageSize"
                   :page-sizes="pageSizes"
                   :current-page="currentPage"
                   @current-change="current_change"
                   @size-change="size_change">
        </component>
    </div>

		<el-dialog :visible.sync="dialog" closeable width="40%">
			<component :is="dialogComponent"
                 :model="scopeInfo"
                 @close="dialog = !dialog"
                 @up-date="up_date"
                 v-if="dialog"></component>
		</el-dialog>
	</div>
</template>
<script>
import scopeMixin from "./scope-mixin";

export default {
  name: "ms-table",
  mixins: [scopeMixin],
  data () {
    return {
      tableDataShow: [],
      dialog: false,
      dialogComponent: "",
      scopeInfo: {},
      headerCellStyle: {
        "background-color": "#EBEEF5"
      },
      headerRowStyle: {
        "background-color": "#EBEEF5"
      }
    };
  },
  props: {
    elTableHeight: {
      type: String,
    },
    arraySpanMethod: {
      type: Function
    },
    msSelectable: {
      type: Function
    },
    tableData: { // => 列表数据
      type: Array,
      default: () => {
        return []
      }
    },
    tableHeader: { // => 列表
      type: Array,
      default: () => {
        return []
      }
    },
    expandList: { // => 列表展开行数组
      type: Array,
      default: () => {
        return []
      }
    },
    scopeConfig: { // => 列表显示配置
      type: Object,
      default: () => {
        return {}
      }
    },
    scopeHeader: { // => 自定义表头配置
      type: Object,
      default: () => {
        return {}
      }
    },
    rowStyle: {
      type: Object,
      default: () => {
        return { height: 'auto' }
      }
    },
    lazy: { // => 列表是否懒加载
      type: Boolean,
      default: false
    },
    showIndex: { // => 列表是否展示序号
      type: Boolean,
      default: false
    },
    showSelection: { // => 列表是否可多选
			type: Boolean,
			default: false
    },
    loading: { // => 异步请求数据加载显示
      type: Boolean,
      default: false
    },
    treeProps: {
      type: Object,
      default: () => {
        return {children: 'children'}
      }
    },
    rowKey: { // => 树形懒加载key值
      type: String,
      default: 'id'
    },
    expandRowKeys: {
      type: Array,
      default: () => {
        return []
      }
    },
    operation: { // => 列表操作组件name
      type: String,
      default: 'operationScopeButton'
    },
    operationButtons: { // => 列表操作按钮数组
      type: Array,
      default: () => {
        return []
      }
    },
    pageComponent: { // => 分页组件
      type: String,
      default: 'tablePageDefault'
    },
    currentPage: { // => 当前页数
      type: Number,
      default: 1
    },
    pageSizes: { // => 每页显示条数控制
      type: Array,
      default: () => {
        return [5, 10, 20, 30, 40, 50, 100];
      }
    },
    pageSize: { // => 列表每页条数
      type: Number,
      default: 10
    },
    total: { // => 列表总条数
      type: Number,
      default: 0
    },
    rowColor: {
      type: Function
    }
  },
  computed: {
    operationWidth() {
      let btn = this.operationButtons;
      let width = 0;
      for (let i = 0; i<btn.length; i++) {
        width += parseInt(btn[i].tdWidth) || 60
      }
      width += 16
      return width
    }
  },
  methods: {
    // tableRowClassName({row,rowIndex}) {
    //   console.log(row)
    // },
    /*
     * @explain 分页操作
     */
    size_change (val) {
      this.$emit("size-change", val);
    },
    current_change (val) {
      this.$emit("current-change", val);
    },
    /*
     * @explain 列表按钮操作
     */
    operation_change (val) {
      this.$emit("operation-change", val);
    },
    /*
     * @explain 头部点击操作
     */
    header_operation(val) {
      this.$emit('header-operation', val)
    },
    /*
     * @explain 弹框操作
     */
    up_date () {
      this.$emit("up-date");
    },
    scope_click (val) {
      this.$emit("operation-change", val);
    },
    load(tree, treeNode, resolve) {
      this.$emit('loadChild', {tree, treeNode, resolve})
    },
    // 列表多选获取选中的值
    handleSelectionChange(val) {
      this.$emit('handleSelectionChange', val)
    },
    //点击当前行触发
    rowClick(row) {
      this.$emit("rowClick",row)
    }
  }
};
</script>
<style>
  .el-table .recommend-row {
    background: #fef0f0 !important;
  }

  .el-table .sticky-row {
    background: #fdf6ec !important;
  }

  .el-table .pc-visible {
    background: #ecf8ff !important;
  }
  .el-table .success-row {
    background: rgb(225, 243, 216) !important;
  }
</style>

<style scoped lang="scss">
.ms-table {
  text-align: left;
  &-search {
    margin-bottom: 10px;
  }
  &-pagination {
    margin-top: 15px;
    text-align: right;
  }
  /deep/.el-table__expanded-cell {
    padding: 10px 60px;
  }
  /deep/.el-table-column--selection {
    .cell {
      display: block;
    }
  }
  .expand-area {
    min-height: 80px;
    // background-color: #F8F8F9;
    width: calc(100vw - 280px);
    padding: 12px 140px 12px 80px;
  }
}
</style>
