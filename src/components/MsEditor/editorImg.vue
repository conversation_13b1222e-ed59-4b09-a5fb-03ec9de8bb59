<template>
  <div>
    <el-tooltip effect="dark" content="选择图片" placement="bottom">
      <el-button icon="el-icon-picture" size="mini" circle @click=" dialogVisible=true" :disabled="disabled"></el-button>
    </el-tooltip>
    <msImageDialog :dialog="dialogVisible" :isAppendBody="false" @close="dialogVisible=false" @getImgUrl="getImgUrl" v-if="dialogVisible" :isTip="false"></msImageDialog>
  </div>
</template>

<script>
import msImageDialog from '../UpFile/ms-image-dialog'
export default {
  name: 'EditorImg',
  data() {
    return {
      dialogVisible: false
    }
  },
  props: {
    disabled: {
      type: Boolean,
      default: false
    }
  },
  components: {
    msImageDialog
  },
  methods: {
    getImgUrl(data) {
      const arr = Object.keys(data).map(v => data[v])
      this.$emit('successCBK', arr)
    }
  }
}
</script>

