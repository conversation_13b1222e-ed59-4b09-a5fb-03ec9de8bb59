<template>
  <div>
    <el-dialog title="发起投票" :visible.sync="visible" width="60%" height="50%">
      <el-row>
        <el-col :span="22" style="margin-top: 10px">
          <p style="margin-bottom: 4px">
            仅限选择已审核通过、且在有效期内的投票
          </p>
          <el-table
            ref="refsTable"
            :data="optionSearch"
            border
            style="width: 100%;margin-top:20px"
            :row-class-name="tableRowClassName"
          >
            <!-- <el-table-column type="index" width="50"> </el-table-column> -->
            <el-table-column label="操作" width="127">
              <template slot-scope="scope">
                <el-radio
                  v-model="radio"
                  :label="scope.$index"
                  @input="selectRowData(scope.row)"
                >
                  {{ "" }}
                </el-radio>
              </template>
            </el-table-column>
            <el-table-column property="templateName" label="调研名称" width="330">
            </el-table-column>
            <el-table-column property="openStartTime" label="投放时间" width="330">
            </el-table-column>
          </el-table>
        </el-col>
        <el-col :span="22" style="margin-top: 30px;text-align:center">
          <el-button @click="visible = false">取消</el-button>
          <el-button @click="getFormUrl()" type="primary">确定</el-button>
        </el-col>
      </el-row>
    </el-dialog>
    <el-tooltip
      effect="dark"
      content="发起投票"
      placement="bottom"
      slot="reference"
    >
      <el-button
        @click="addSurvey"
        icon="el-icon-plus"
        size="mini"
        circle
        :disabled="disabled"
      ></el-button>
    </el-tooltip>
  </div>
</template>

<script>
export default {
  name: "EditorForm",
  data() {
    return {
      radio: "",
      tableData: [],
      currentRow: null,
      formSubmit: {
        url: "",
        name: "",
      },
      visible: false,
      loading: false,
      optionSearch: [],
      formRules: {
        url: [{ required: true, message: "请选择调研表单", trigger: "blur" }],
      },
    };
  },
  props: {
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  created() {
    this.getFormList();
  },
  methods: {
    tableRowClassName({ row, rowIndex }) {
      console.log(row);
      console.log(rowIndex);
    },
    selectRowData(val) {
      //   this.$nextTick(() => {
      //     this.formSubmit.name = this.$refs.formRef.selectedLabel;
      //   });
      console.log(val);
    },
    addSurvey() {
      this.visible = !this.visible;
    },
    getFormList(val) {
      this.loading = true;
      this.optionSearch = [];
      let parmas = {
        pageIndex: 1,
        pageSize: 20,
        value: val,
        approvalStatus: 1,
      };
      this.api.getFormMaterialList(parmas).then((response) => {
        let newdate = new Date().getTime();
        let newArrData = response.data.filter(item=>new Date(item.openStartTime).getTime() < newdate && new Date(item.openEndTime).getTime() >= newdate && item.isOpenResult == 1)
        this.loading = false;
        if (response.status == 200) {
          this.optionSearch = newArrData;
        }
      });
    },
    // formChange() {
    //   this.$nextTick(() => {
    //     this.formSubmit.name = this.$refs.formRef.selectedLabel;
    //   });
    // },
    getFormUrl() {
      console.log(this.optionSearch[this.radio]['url'],'urllllll');
      console.log(this.optionSearch[this.radio]['templateName'],'nameeeee');
      this.formSubmit.url = this.optionSearch[this.radio]['url'];
      this.formSubmit.name = this.optionSearch[this.radio]['templateName'];
      this.visible = false;
      this.$emit("successCBK", this.formSubmit);
          // this.formSubmit.url = "";
          // this.formSubmit.name = "";
      //   this.$refs.ruleForm.validate((valid) => {
      //     if (valid) {
      //       this.$emit("successCBK", this.formSubmit);
      //       this.formSubmit.url = "";
      //       this.formSubmit.name = "";
      //       this.visible = false;
      //     }
      //   });
    },
  },
};
</script>
