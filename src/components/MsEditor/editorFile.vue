<template>
    <el-popover
        placement="bottom"
        width="400"
        v-model="visible"
        trigger="click">
        <template v-for="(item,index) in submitData.fileList">
            <el-row :gutter="10" :key="index" style="margin-bottom: 10px;">
                <el-col :span="12">
                    <el-input v-model="item.url" style="width: 100%;" placeholder="输入资源地址"></el-input>
                </el-col>
                <el-col :span="9">
                    <el-input v-model="item.name" style="width: 100%;" placeholder="输入资源名"></el-input>
                </el-col>
                <template v-if="index === 0">
                    <el-col :span="3">
                        <el-button icon="el-icon-plus" size="mini" circle @click="plusFileList()"></el-button>
                    </el-col>
                </template>
                <template v-if="index > 0">  
                    <el-col :span="3">
                        <el-button icon="el-icon-minus" size="mini" circle @click="closeFileList(index)"></el-button>
                    </el-col>
                </template>
            </el-row>
        </template>
        <el-row>
            <el-col :span="18" style="margin-top: 10px">
                <ms-file-upload ref="fileUpload" v-model="submitData.attachmentList" buttonLabel="手动上传资源" :isPrivate="false"></ms-file-upload>
            </el-col>
            <el-col :span="6" style="margin-top: 10px;text-align: right;">
                <el-button @click="getFileUrl()" type="primary">确定</el-button>
            </el-col>
        </el-row>
        <el-tooltip effect="dark" content="添加文件" placement="bottom" slot="reference">
            <el-button icon="el-icon-folder" size="mini"  circle :disabled="disabled"></el-button>
        </el-tooltip>
        
    </el-popover>
</template>

<script>
export default {
    name: 'EditorFile',
    data() {
        return {
            submitData: {
                fileList: [{
                    url: '',
                    name: ''
                }],
                attachmentList: []
            },
            visible: false
        }
    },
    props: {
        disabled: {
            type: Boolean,
            default: false
        }
    },
    methods: {
        plusFileList() {
            this.submitData.fileList.push({
                url: '',
                name: ''
            })
        },
        closeFileList(index) {
            this.submitData.fileList.splice(index, 1)
        },
        getFileUrl() {
            if (this.submitData.fileList[0].url || this.submitData.attachmentList.length > 0) {
                let fileList = []
                this.submitData.fileList.forEach(v => {
                    if (v.url) {
                        fileList.push({
                            url: v.url,
                            name: v.name || v.url 
                        })
                    }
                })
                this.$emit('successCBK', [...fileList, ...this.submitData.attachmentList])
                this.submitData = {
                    fileList: [{
                        url: '',
                        name: ''
                    }],
                    attachmentList: []
                }
                this.$refs['fileUpload'].fileLi = []
                this.visible = false
            } else {
                this.$message.warning('请输入文件资源地址或者上传文件');
            }
        }
    }
}
</script>
