<template>
    <el-popover
        placement="bottom"
        width="400"
        v-model="visible"
        trigger="click"
        @show="popverShow">
        <el-tabs v-model="videoAction" >
            <el-tab-pane label="视频上传" name="first">
                <el-button plain icon="el-icon-upload" @click="goUpload">视频上传</el-button>
                <!-- <ms-video-upload v-model="submitData.videoKey" buttonLabel="视频上传" :fileSuffix="videoSpace" @bindData="videoDataDeal" upFileFormat="video"></ms-video-upload> -->
            </el-tab-pane>
            <el-tab-pane label="视频素材选择" name="second">
                <msVideoSearch :model.sync="videoUrlSearch" :modelCover.sync="videoCover"></msVideoSearch>
            </el-tab-pane>
            <el-tab-pane label="地址输入" name="third">
                <el-input v-model="videoUrlInput" placeholder="请输入视频地址"></el-input>
            </el-tab-pane>
        </el-tabs>
        <el-row style="margin-top: 30px; text-align: right">
            <el-button @click="visible = false" >取消</el-button>
            <el-button @click="getVideoFilrUrl()" type="primary">确定</el-button>
        </el-row>
        <el-tooltip effect="dark" content="选择视频" placement="bottom" slot="reference">
            <el-button icon="el-icon-s-platform" size="mini"  circle :disabled="disabled"></el-button>
        </el-tooltip>
        
    </el-popover>
</template>

<script>
import msVideoSearch from '../MsCommon/ms-materialVideo-search'
export default {
    name: 'EditorVideo',
    data() {
        return {
            submitData: {
                videoKey: '',
                cover: '',
                duration: '',
                title: ''
            },
            videoUrlInput: '',
            videoUrlSearch: '',
            videoCover: '', //视频封面
            videoSpace: `/up/${new Date().getFullYear()}-${new Date().getMonth() + 1 < 10 ? '0'+(new Date().getMonth() + 1): new Date().getMonth() + 1}`,

            visible: false,
            videoAction: 'zero',
            
        }
    },
    props: {
        disabled: {
            type: Boolean,
            default: false
        }
    },
    components: {
        msVideoSearch
    },
    methods: {
        popverShow() {
            if (this.videoAction === 'zero') {
                this.videoAction = 'first'
            }
        },
        videoDataDeal (data) {
            if (!data.key) {return false}
            // let videoUrl = `https://ali-video.medsci.cn/${data.key}`
            // let videoUrl = `https://${data.key}`
            // let videoUrl = 'https://' + this.PUBLIC_Methods.dealCosUrl(data.key).m3u8Url
            let videoUrl = 'https://ali-video.medsci.cn/' + this.PUBLIC_Methods.dealCosUrl(data.key).m3u8Url
            console.log(videoUrl, 'videourl');
            this.submitData.videoKey = data.key
            this.submitData.title = data.name
            if (!this.submitData.cover) {
                // this.submitData.cover = `${videoUrl}?x-oss-process=video/snapshot,t_7000,f_jpg,w_0,h_0,m_fast`
                // this.submitData.cover = 'https://' + this.PUBLIC_Methods.dealCosUrl(data.key).snapshotUrl
                this.submitData.cover = 'https://ali-video.medsci.cn/' + this.PUBLIC_Methods.dealCosUrl(data.key).snapshotUrl
            }
            
            // 获取视频时长
            let videoEle = document.createElement('video');
            videoEle.setAttribute('src', videoUrl);
            videoEle.onloadedmetadata = ( () => {
                if (videoEle.duration) {
                    let v_hour = Math.floor(videoEle.duration/(60*60)%24);
                    let v_minute = Math.floor(videoEle.duration/60%60);
                    let v_second = Math.floor(videoEle.duration%60);

                    var vH = v_hour >= 10 ?  v_hour : '0'+ v_hour;
                    var vM = v_minute >= 10 ?  v_minute : '0'+ v_minute;
                    var vS = v_second >= 10 ?  v_second : '0'+ v_second;

                    this.submitData.duration = `${vH}:${vM}:${vS}`
                    videoEle = null;
                }
                
            })
            if (this.$store.getters.info.roleLevel !== 3) {
                this.api.saveVideoMaterial({...this.submitData,userId: this.$store.getters.info.userId,username: this.$store.getters.info.userName})
            }
        },
        getVideoFilrUrl () {
            let videoUrlList = []
            if (this.videoAction === 'first') {
                if (this.submitData.videoKey) {
                    videoUrlList.push({
                        url: `https://ali-video.medsci.cn/${this.submitData.videoKey}`
                    })
                    this.submitData = {
                        videoKey: '',
                        cover: '',
                        duration: '',
                        title: ''
                    }
                }
            } else if (this.videoAction === 'second'){
                if (this.videoUrlSearch) {
                    videoUrlList.push({
                        url: this.videoUrlSearch,
                        cover: this.videoCover
                    })
                    this.videoUrlSearch = ''
                    this.videoCover = ''
                }
            } else if (this.videoAction === 'third') {
                if (this.videoUrlInput) {
                    videoUrlList.push({
                        url: this.videoUrlInput
                    })
                    this.videoUrlInput = ''
                }
            }
            this.visible = false
            this.$emit('successCBK', videoUrlList)
        },
        goUpload() {
          this.$router.push('/material-operation-video?operation=created&component=video-operation')
        }
    }
}
</script>
