<template>
  <div :class="{fullscreen:fullscreen}" class="tinymce-container" v-loading="imgReplace" element-loading-text="图片上传...">
    <textarea :id="tinymceId" class="tinymce-textarea"/>
    <div class="editor-custom-btn-container">
      <!-- <editorPoll class="editor-upload-btn"  @successCBK="formSuccessPoll" :disabled="disabled" style="margin-right: 20px;"></editorPoll> -->
      <editorForm class="editor-upload-btn" @successCBK="formSuccessCBK" :disabled="disabled" style="margin-right: 20px;"></editorForm>
      <editorFile class="editor-upload-btn" @successCBK="fileSuccessCBK" :disabled="disabled" style="margin-right: 20px;"></editorFile>
      <editorImg class="editor-upload-btn" @successCBK="imageSuccessCBK" :disabled="disabled" style="margin-right: 20px;"/>
      <editorVideo class="editor-upload-btn" @successCBK="videoSuccessCBK" :disabled="disabled" style="margin-right: 20px;"/>
      <editorAudio class="editor-upload-btn" @successCBK="audioSuccessCBK" :disabled="disabled" v-show="audioShow" style="margin-right: 20px;"/>
      <div class="editor-upload-btn">
        <el-tooltip effect="dark" content="一键排版" placement="bottom">
          <el-button icon="el-icon-menu" size="mini" circle @click="formatContent"></el-button>
        </el-tooltip>
      </div>
    </div>
  </div>
</template>

<script>
import plugins from './plugins'
import toolbar from './toolbar'
import load from './dynamicLoadScript'
import editorImg from './editorImg'
import editorFile from './editorFile'
import editorVideo from './editorVideo'
import editorForm from './editorForm'
import editorAudio from './editorAudio'
import editorPoll from './editorPoll'
// const tinymceCDN = 'https://cdn.jsdelivr.net/npm/tinymce-all-in-one@4.9.3/tinymce.min.js'
const tinymceCDN = 'https://static.medsci.cn/public-js/tinymce/@4.9.3/tinymce.min.js'
// const tinymceCDN = 'https://static.medsci.cn/public-js/tinymce/@5.x/tinymce.min.js'
import COS from "cos-js-sdk-v5";
export default {
  name: 'Tinymce',
  props: {
    id: {
      type: String,
      default: function() {
        return 'vue-tinymce-' + +new Date() + ((Math.random() * 1000).toFixed(0) + '')
      }
    },
    value: {
      type: String,
      default: ''
    },
    toolbar: {
      type: Array,
      required: false,
      default() {
        return []
      }
    },
    menubar: {
      type: String,
      default: 'file edit insert view format table'
    },
    height: {
      type: [Number, String],
      required: false,
      default: 220
    },
    disabled: {
      type: Boolean,
      default: false
    },
    audioShow: {
      type: Boolean,
      default: false
    },
    isBlock: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      hasChange: false,
      hasInit: false,
      tinymceId: this.id,
      fullscreen: false,
      imgReplace: false,
      imgUploadFlag: true,

      ossData: {},
      uploadYear: new Date().getFullYear(),
      uploadMouth: new Date().getMonth() < 10 ? '0' + (new Date().getMonth() + 1) : new Date().getMonth() + 1,
      uploadDate: new Date().getDate() < 10 ? '0' + new Date().getDate() : new Date().getDate(),
      userId: ''
    }
  },
  components: { editorImg,editorFile,editorVideo,editorForm,editorAudio,editorPoll },
  watch: {
    value(val) {
      if (!this.hasChange && this.hasInit) {
        this.$nextTick(() => window.tinymce.get(this.tinymceId).setContent(val || ''))
        this.setEditorImg(val)
      }
    }
  },
  mounted() {
    this.init()

    this.getOssToken()
    this.userId = this.$store.getters.info.userId
  },
  activated() {
    if (window.tinymce) {
      this.initTinymce()
    }
  },
  deactivated() {
    this.destroyTinymce()
  },
  destroyed() {
    this.destroyTinymce()
    this.$store.dispatch('SetEditorImg', [])
  },
  methods: {
    init() {
      // dynamic load tinymce from cdn
      load(tinymceCDN, (err) => {
        if (err) {
          this.$message.error(err.message)
          return
        }
        this.initTinymce()
      })
    },
    initTinymce() {
      const _this = this
      window.tinymce.init({
        selector: `#${this.tinymceId}`,
        forced_root_block : this.isBlock ? 'p' : '',
        language: 'zh_CN',
        height: this.height,
        body_class: 'panel-body ',
        object_resizing: false,
        toolbar: this.toolbar.length > 0 ? this.toolbar : toolbar,
        menubar: this.menubar,
        plugins: plugins,
        end_container_on_empty_block: true,
        paste_webkit_styles: "color",
        paste_retain_style_properties: "color",
        paste_data_images: true,
        paste_convert_word_fake_lists: false,
        powerpaste_word_import: 'merge',
        code_dialog_height: 450,
        code_dialog_width: 1000,
        advlist_bullet_styles: 'square',
        advlist_number_styles: 'default',
        imagetools_cors_hosts: ['www.tinymce.com', 'codepen.io'],
        default_link_target: '_blank',
        link_title: false,
        fontsize_formats: "12px 14px 16px 18px 20px 22px 24px 30px 36px",
        // emoticons_database_url: './emojis.js',
        readonly: this.disabled,
        nonbreaking_force_tab: true, // inserting nonbreaking space &nbsp; need Nonbreaking Space Plugin
        init_instance_callback: editor => {
          if (_this.value) {
            editor.setContent(_this.dealImg(_this.value))
            this.getEditorImg(_this.value)
          }
          _this.hasInit = true
          editor.on('NodeChange Change KeyUp SetContent', () => {
            this.hasChange = true
            this.$emit('input', editor.getContent())
          })
          editor.on('blur', () => {
            this.setEditorImg(editor.getContent())
          })
        },
        setup(editor) {
          editor.on('FullscreenStateChanged', (e) => {
            _this.fullscreen = e.state
          }),

          editor.addButton('clearContentLink', {
            icon: 'mce-ico mce-i-format-painter',
            tooltip: '清除文本超链接',
            onclick: function () {
              editor.setContent(_this.dealImg(editor.getContent().replace(/<a(.*?)<\/a>/g, '<span$1</span>')))
            }
          });
        }
      })
    },
    destroyTinymce() {
      const tinymce = window.tinymce.get(this.tinymceId)
      if (this.fullscreen) {
        tinymce.execCommand('mceFullScreen')
      }

      if (tinymce) {
        tinymce.destroy()
      }
    },
    // setContent(value) {
    //   window.tinymce.get(this.tinymceId).setContent(value)
    // },
    // getContent() {
    //   window.tinymce.get(this.tinymceId).getContent()
    // },
    dealImg(value){
      // 获取包含img标签的文本
      var htmlString = value;
      // 创建一个DOM解析器
      var parser = new DOMParser();
      var doc = parser.parseFromString(htmlString, 'text/html');
      // 获取所有的img标签
      var imgTags = doc.getElementsByTagName('img');
      // 遍历所有的img标签，去除crossorigin属性
      for (var i = 0; i < imgTags.length; i++) {
        imgTags[i].removeAttribute('crossorigin');
      }
      // 获取修改后的HTML字符串
      var modifiedHtmlString = doc.body.innerHTML;
      // 输出修改后的HTML字符串
      return modifiedHtmlString
    },
    setEditorImg(value) {
      let imgArr = []
      // let newImg = []
      // let newValue = ''
      // let repIndex = -1
      if (value) {
        value.replace(/<img [^>]*src=['"]([^'"]+)[^>]*>/g, function (match, capture) {
          if (capture.substring(0,4) !== 'http' && capture.substring(0,4) !== 'data') {
            imgArr.push(`http:${capture}`)
          } else {
            imgArr.push(capture)
          }
        });
      }
      if (imgArr.length > 0) {
        this.imgReplace = true
        // 循环执行，分批执行上传， 原因： 图片较多后台处理较慢
        this.uplaodImg(imgArr)
      }
    },
    async uplaodImg(imgArr) {
      let newImg = []
      let newValue = ''
      let repIndex = -1
      const promises = imgArr.map(async (url, index) => {
        if (url.indexOf('img.medsci.cn') !== -1 || url.indexOf('msimg.bioon.com') !== -1) {
          newImg[index] = url
        } else if (url.substring(0,4) == 'data') {
          let aliOssFile = this.aliOssBase64Upload(url);
          let fileType = aliOssFile.type.replace('image/', '')
          // let  nDate = new Date()
          // let timestamp = nDate.getTime()

          //   let fileName = `${timestamp}.${fileType}`
            let fileName = ''
            if(window.location.href.includes('Bioon')){
              fileName = `bioon-com/${this.uploadYear}${this.uploadMouth}${this.uploadDate}/${new Date().getTime()}_${this.userId}.${fileType}`
            }else {
              fileName = `${this.uploadYear}${this.uploadMouth}${this.uploadDate}/${new Date().getTime()}_${this.userId}.${fileType}`
            }
             const cos = new COS({
                SecretId: this.ossData.accessKeyId,
                SecretKey: this.ossData.accessKeySecret,
                XCosSecurityToken: this.ossData.securityToken,
              });
             await new Promise((resolve, reject) => {
                cos.uploadFile(
                  {
                    Bucket: this.ossData.bucket,
                    Region: "ap-shanghai",
                    Key: fileName,
                    Body:  aliOssFile,
                  },
                  (err, data) => {
                    if (err) {
                       this.imgUploadFlag = false
                      reject(err);
                    } else {
                      let imgUrl= "https://" + data.Location.replace(data.Location.split('/')[0], this.cosUrl);
                      // let imgUrl= `https://${data.Location}`;

                      newImg[index] = imgUrl
                      resolve();
                    }
                  }
                );
              });
        } else {
          await this.api.transferPictureUrl({transferData: [url]}).then( response => {
            if (response.status === 200) {
              newImg[index] = response.data[0].transferUrl 
            }
          }).catch(() =>{this.imgUploadFlag = false})
        }
        
      })

      await Promise.all(promises)
      if (!this.imgUploadFlag) {
        this.$message({
          message: '部分图片粘贴上传失败，请手动上传',
          type: 'warning'
        });
        this.imgUploadFlag = true
      }
      this.imgReplace = false;
      newValue = window.tinymce.get(this.tinymceId).getContent().replace(/<img [^>]*src=['"]([^'"]+)[^>]*>/gi, function (match) {
        repIndex++
        // eslint-disable-next-line no-useless-escape
        return match.replace(/\bsrc\b\s*=\s*[\'\"]?([^\'\"]*)[\'\"]?/g, `src="${newImg[repIndex]}"`)
      });
      this.$emit('input', newValue)
      window.tinymce.get(this.tinymceId).setContent(this.dealImg(newValue))
      this.$store.dispatch('SetEditorImg', newImg)
    },
    getEditorImg(value) {
      let imgArr = []
      if (value) {
        value.replace(/<img [^>]*src=['"]([^'"]+)[^>]*>/g, function (match, capture) {
            imgArr.push(capture)
        });
      }
      this.$store.dispatch('SetEditorImg', imgArr)
    },
    formSuccessCBK(data) {
      const _this = this
      window.tinymce.get(_this.tinymceId).insertContent(`<a href="${data.url}" data-src="${data.url}" style="color:#2f92ee;">${data.name}</a>`)
    },
    // formSuccessPoll(data) {
    //   const _this = this
    //   window.tinymce.get(_this.tinymceId).insertContent(`<a href="${data.url}" data-src="${data.url}" target="_blank" style="color:#2f92ee;">${data.name}</a>`)
    // },
    // 音频
    audioSuccessCBK(arr) {
      const _this = this
      arr.forEach(v => {
        window.tinymce.get(_this.tinymceId).insertContent(`<audio src="${v.url}" controls="controls">`)
      })
    },
    videoSuccessCBK(arr) {
      const _this = this
      arr.forEach(v => {
        window.tinymce.get(_this.tinymceId).insertContent(`<div><video controls="controls" data-cover="${v.cover}" poster="${v.cover}" width="100%" height="auto"><source src="${v.url}" /></video></div>`)
      })
    },
    fileSuccessCBK(arr) {
      const _this = this
      arr.forEach(v => {
        window.tinymce.get(_this.tinymceId).insertContent(`<a href="${v.url}" class="ms-editor-file" data-src="${v.url}" style="color:#2f92ee;">${v.name}</a>`)
      })
    },
    imageSuccessCBK(arr) {
      const _this = this
      arr.forEach(v => {
        window.tinymce.get(_this.tinymceId).insertContent(`<img class="wscnph" src="${v.url}" >`)
      })
    },

    getOssToken () {
      this.api.getToken({type:1}).then(response => {
          this.ossData = {
              accessKeyId: response.data.accessKeyId,
              accessKeySecret: response.data.accessKeySecret,
              bucket: response.data.publicBucketName,
              securityToken: response.data.securityToken,
              timeout: 10 * 1000
          }
      })
    },

    aliOssBase64Upload (url) {
      var arr = url.split(',');
      var mime = arr[0].match(/:(.*?);/)[1];
      var bstr = atob(arr[1]);
      var n = bstr.length; 
      var u8arr = new Uint8Array(n);
      while(n--){
          u8arr[n] = bstr.charCodeAt(n);
      }
      //转换成file对象
      return new File([u8arr], 'test', {type:mime}) ;
    },
    // 一键排版
    formatContent(){
      // 获取编辑器内容
      var content = window.tinymce.activeEditor.getContent();

      // 去除空标签
      content = content.replace(/<(\w+)\s*\/?>/g, function(match, tag) {
        if (tag === "br" || tag === "hr" || tag === "img") {
          return match; // 保留 br、hr、img 标签
        } else {
          return ""; // 去除其他空标签
        }
      });

      // 将内容重新设置到编辑器中
      window.tinymce.activeEditor.setContent(this.dealImg(content));
    }
  }
}

</script>

<style scoped>
.tinymce-container {
  position: relative;
  line-height: normal;
}
/* .tinymce-container>>>.mce-tinymce {
  width: 100% !important;
} */
.tinymce-container>>>.mce-fullscreen {
  z-index: 10000;
}
.tinymce-textarea {
  visibility: hidden;
  z-index: -1;
}
.editor-custom-btn-container {
  position: absolute;
  right: 4px;
  top: 4px;
  max-width: 220px
}
.editor-custom-btn-container>span{
  width: 28px;
}
.editor-custom-btn-container>div{
  width: 28px;
}
.fullscreen .editor-custom-btn-container {
  z-index: 10000;
  position: fixed;
}
.editor-upload-btn {
  display: inline-block;
}
.tinymce-container /deep/ #mceu_50-body{
  width: 100%;
  white-space: pre-wrap;
}
.tinymce-container /deep/ #mceu_47-body{
  width: 100%;
  white-space: pre-wrap;
}
.tinymce-container /deep/ .mce-flow-layout-item.mce-last >div{
  width: 100%;
  white-space: pre-wrap;
}
/* .tinymce-container /deep/ .mce-container .mce-flow-layout-item .mce-first .mce-btn-group >div{
  width: 100%;
  white-space: pre-wrap;
} */

</style>
