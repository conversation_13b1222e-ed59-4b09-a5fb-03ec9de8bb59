<template>
  <el-popover placement="bottom" width="300" v-model="visible" trigger="click">
    <el-row>
      <el-col :span="22" style="margin-top: 10px">
        <p style="margin-bottom: 4px">请选择调研问卷</p>
        <el-form :model="formSubmit" :rules="formRules" ref="ruleForm">
          <el-form-item prop="url">
            <el-select
              size="mini"
              v-model="formSubmit.url"
              clearable
              filterable
              remote
              style="width: 100%"
              :remote-method="getFormList"
              :loading="loading"
              @change="formChange"
              ref="formRef"
            >
              <el-option
                v-for="(item, index) in optionSearch"
                :key="index"
                :label="item.templateName"
                :value="item.url"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </el-col>
      <el-col :span="18" style="margin-top: 30px">
        <el-button @click="visible = false">取消</el-button>
        <el-button @click="getFormUrl()" type="primary">确定</el-button>
      </el-col>
    </el-row>
    <el-tooltip
      effect="dark"
      content="添加调研问卷"
      placement="bottom"
      slot="reference"
    >
      <el-button
        icon="el-icon-s-data"
        size="mini"
        circle
        :disabled="disabled"
      ></el-button>
    </el-tooltip>
  </el-popover>
</template>

<script>
export default {
  name: "EditorForm",
  data() {
    return {
      formSubmit: {
        url: "",
        name: "",
      },
      visible: false,
      loading: false,
      optionSearch: [],
      formRules: {
        url: [{ required: true, message: "请选择调研表单", trigger: "blur" }],
      },
    };
  },
  props: {
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  created() {
    this.getFormList();
  },
  methods: {
    getFormList(val) {
      this.loading = true;
      this.optionSearch = [];
      let parmas = {
        pageIndex: 1,
        pageSize: 20,
        value: val,
        approvalStatus: 1,
      };
      this.api.getFormMaterialList(parmas).then((response) => {
        this.loading = false;
        if (response.status == 200) {
          this.optionSearch = response.data;
        }
      });
    },
    formChange() {
      this.$nextTick(() => {
        this.formSubmit.name = this.$refs.formRef.selectedLabel;
      });
    },
    getFormUrl() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.$emit("successCBK", this.formSubmit);
          this.formSubmit.url = "";
          this.formSubmit.name = "";
          this.visible = false;
        }
      });
    },
  },
};
</script>
