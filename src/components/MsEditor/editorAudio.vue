<template>
  <div>
    <el-tooltip effect="dark" content="上传音频" placement="bottom">
      <el-button icon="el-icon-service" size="mini" circle @click=" dialogVisible=true" :disabled="disabled"></el-button>
    </el-tooltip>
    <msAudioDialog :dialog="dialogVisible" :isAppendBody="false" @close="dialogVisible=false" @getAudioUrl="getAudioUrl" v-if="dialogVisible" :isTip="false"></msAudioDialog>
  </div>
</template>

<script>
import msAudioDialog from '../UpFile/ms-audio-dialog'
export default {
  name: 'EditorImg',
  data() {
    return {
      dialogVisible: false
    }
  },
  props: {
    disabled: {
      type: Boolean,
      default: false
    }
  },
  components: {
    msAudioDialog
  },
  methods: {
    getAudioUrl(data) {
      const arr = Object.keys(data).map(v => data[v])
      this.$emit('successCBK', arr)
    }
  }
}
</script>

