import Vue from "vue";
import MsTable from "./MsTable/index.vue";
import MsSingleImage from "./UpFile/ms-single-image.vue";
import MsFileUpload from "./UpFile/ms-file-upload"

import MsSelectFilter from "./Msselect/ms-select-filter";
import MsSelectLocal from "./Msselect/ms-select-local";

import MsInput from "./Msselect/ms-input";
import MsInputNumber from "./Msselect/ms-input-number";
import MsPicker from "./Msselect/ms-picker";
import MsCheckbox from "./Msselect/ms-checkbox";
import MsRadio from "./Msselect/ms-radio";
import MsSwitch from "./Msselect/ms-switch";

import MsCategoryCascader from './MsCommon/ms-category-cascader'
import MsCreatebySearch from './MsCommon/ms-createby-search'
import MsDepartmentSearch from './MsCommon/ms-department-search'

import Ms<PERSON>ender from "./ms-render/index.js"

// 函数式组件
import MsOperationDialog from "./MsDialog/ms-operation-dialog";
import MsRightDialog from "./MsDialog/ms-right-dialog";
import MsRightContent from "./MsDialog/ms-right-content";

let components = [
  MsRender,
  MsTable,
  MsSingleImage,
  MsFileUpload,
  MsSelectLocal,
  MsInput,
  MsInputNumber,
  MsPicker,
  MsCheckbox,
  MsRadio,
  MsSwitch,
  MsSelectFilter,
  MsOperationDialog,
  MsCategoryCascader,
  MsCreatebySearch,
  MsDepartmentSearch,
  MsRightDialog,
  MsRightContent
]

components.map(component => {
  Vue.component(component.name, component);
});
