<template>
  <div :class="{'input-label': label}">
    <label v-if="label && label !== ' '">{{label}}</label>
    <el-select size="mini"
               v-model="result" 
               clearable 
               filterable 
               :multiple="multiple"
               :style="{width: width || '100%'}"
               :disabled="disabled" 
               :placeholder="placeholder"
               :loading="loading"
               @change="change"
               @visible-change="visible_change">
        <el-option v-for="(item,index) in options || code[operation] || []"
                   :key="index"
                   :label="item.name"
                   :value="item.label">
        </el-option>
    </el-select>
  </div>
</template>

<script>
  import {mapGetters} from 'vuex';
  export default {
    name: "ms-select-filter",
    props: [
      "multiple", // => 是否可多选
      "label", // => 输入框label
      "index", // => 回填name
      "placeholder", // => 占位符
      "operation",
      "model",
      "options",  // => 选择项
      "disabled",  // => 选择项
      "width", // => 输入框宽度
      "loading", // => 选择数据获取加载
      'tacitlyApprove' // => 默认值
    ],
    data() {
      return {
        result: this.multiple ? [] : this.tacitlyApprove ? this.tacitlyApprove : '',
        optionsSearch: []
      }
    },
    computed: {
    ...mapGetters(["code"])
    },
    watch: {
      model: function(val) {
        this.result = val;
      },
      code: function(val) {
        this.optionsSearch = val[this.operation] || []
      },
      result:function() {
        this.$emit('update:model', this.result)
      }
    },
    created(){
      if(this.tacitlyApprove){
        this.$emit('update:model', this.result)
      }
    },
    methods: {
      change(val) {
        this.optionsSearch = [...this.options]
        let selectVal = this.optionsSearch[val]
        this.$emit('update:model', selectVal.name)
        this.$emit('more-operation', {model: selectVal, operation: this.operation})
      },
      visible_change(bool) {
        if(bool) {
          if (this.optionsSearch.length === 0 || !this.optionsSearch) {
            this.$emit('getCodeInfo', {operation: this.operation})
          }
        }
      }
    }
  }
</script>
