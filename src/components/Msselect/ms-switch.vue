<template>
  <div :class="{'input-label': label}">
    <label v-if="label && label !== ' '">{{label}}</label>
    <el-switch v-model="switchVal"
               :active-value="active"
               :inactive-value="inactive"
               :disabled="disabled"
               @change="change">
  </el-switch>
  </div>
</template>

<script>
  export default {
    name: 'ms-switch',
    data() {
      return {
        switchVal: this.inactive
      }
    },
    props: [
      "label", // => 输入框label
      "model",
      "active",
      "inactive",
      "disabled"
    ],
    watch: {
      model: function(val) {
        this.switchVal = val;
      }
    },
    created() {
      this.switchVal = this.model
    },
    methods: {
      change(val) {
        this.$emit('update:model', val)
      }
    }
  }
</script>
