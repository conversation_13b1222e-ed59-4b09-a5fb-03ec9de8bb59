<template>
  <div :class="{'input-label': label}">
    <label v-if="label && label !== ' '">{{label}}</label>
    <el-checkbox-group v-model="checkModel" @change="change">
      <el-checkbox v-for="(item,index) in options || code[operation] || []" 
                   :key="index"
                   :disabled="disabled || false"
                   :label="item.label">{{item.name?item.name:item.label}}</el-checkbox>
    </el-checkbox-group>
  </div>
</template>

<script>
  import {mapGetters} from 'vuex';
  export default {
    name: 'ms-checkbox',
    data() {
      return {
        checkModel: this.type === 'option' ? (this.model?this.model:[]) : false,
        optionsSearch: []
      }
    },
    props: [
      "label", // => 输入框label
      "model",
      "options",
      "disabled",
      "operation",
      "type" // => status || option
    ],
    computed: {
    ...mapGetters(["code"])
    },
    watch: {
      model: function(val) {
        if (val) {
          this.check = val 
        }
      },
      code: function(val) {
        this.optionsSearch = val[this.operation]
      }
    },
    methods: {
      change(val) {
        this.$emit('update:model', val)
      }
    }
  }
</script>
