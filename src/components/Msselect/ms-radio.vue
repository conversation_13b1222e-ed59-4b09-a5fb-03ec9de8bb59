<template>
  <div :class="{'input-label': label}" class="ms-radio">
    <label v-if="label && label !== ' '">{{label}}</label>
    <el-radio-group v-model="radio">
      <el-radio v-for="(item, index) in options" 
                :key="index"
                :disabled="disabled || false"
                :label="item.value"
                @change="change">{{item.label}}</el-radio>
    </el-radio-group>
  </div>
</template>

<script>
  export default {
    name: 'ms-radio',
    data() {
      return {
        radio: this.model
      }
    },
    props: [
      "label", // => 输入框label
      "model",
      "disabled",
      "options"
    ],
    watch: {
      model: function(val) {
        this.radio = val;
      }
    },
    methods: {
      change(val) {
        this.$emit('update:model', val)
      }
    }
  }
</script>
