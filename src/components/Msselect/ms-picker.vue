<template>
  <div :class="{'input-label': label}">
    <label v-if="label && label !== ' '" :style="labelWidth ? {'display': 'inline-block', width: labelWidth} : ''">{{label}}</label>
    <el-date-picker @change="change"
                    size="mini"
                    v-model="picker"
                    :type="type||'date'"
                    clearable
                    :disabled="disabled"
                    :style="{width: width || '100%'}"
                    unlink-panels
                    value-format="yyyy-MM-dd HH:mm:ss"
                    range-separator="-"
                    :start-placeholder="startPlaceholder || '开始日期'"
                    :end-placeholder="endPlaceholder || '结束日期'"
                    :placeholder="placeholder || '请选择'"
                    class="suffix-inner"
                    :picker-options="pickerOptions"
                    :default-time="defaultTime">
    </el-date-picker>
  </div>
</template>

<script>
  const daterange = [
    "daterange",
    "monthrange",
    "datetimerange"
  ]
  export default {
    name: "ms-picker",
    data() {
      return {
        picker: daterange.indexOf(this.type) !== -1 ? [] : '',
        pickerOptions: null
      };
    },
    props: [
      "startPlaceholder",
      "endPlaceholder",
      "label", // => 输入框label
      "index", // => 回填name
      "placeholder", // => 占位符
      "operation",
      "model",
      "disabled",  // => 选择项
      "width", // => 输入框宽度
      "type",
      "labelWidth",
      "config",
      "defaultTime"
    ],
    watch: {
      model: function(val) {
        this.picker = val;
      }
    },
    created() {
      this.picker = this.model
      this.pickerOptions = this.config && this.config.pickerOptions || {}
    },
    methods: {
      change(val) {
        let params;
        if (!val) {
          params = daterange.indexOf(this.type) !== -1 ? [] : ''
        } else {
          params = val
        }
        this.$emit('update:model', params)
      }
    }
  };
</script>

<style>
.suffix-inner .el-input__inner{
  padding-right: 25px !important;
}
</style>
