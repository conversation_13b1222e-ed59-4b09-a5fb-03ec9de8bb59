<template>
  <div :class="{'input-label': label}">
    <label v-if="label && label !== ' '">{{label}}</label>
    <el-input-number size="mini" 
              clearable
              v-model="input" 
              :disabled="disabled"
              style="display: inline-block;"
              :style="{width: width}"
              :placeholder="placeholder"
              :type="type"
              :controls-position="position || 'right'"
              :min="min || 0"
              :max="max"
              :step="step"
              :step-strictly="stepStrictly||false"
              :precision="precision"
              @change="change">
    </el-input-number>
  </div>
</template>

<script>
  export default {
    name: 'ms-input-number',
    data() {
      return {
        input: this.model
      }
    },
    props: [
      "label", // => 输入框label
      "index", // => 回填name
      "placeholder", // => 占位符
      "operation",
      "model",
      "disabled",  // => 选择项
      "width", // => 输入框宽度
      "type",
      "position", // => 控制按钮位置
      "min", // => 最小值
      "max", // => 最大值
      "step", // => 步数
      "stepStrictly", // => 严格步数
      "precision", // => 精度
    ],
    watch: {
      model: function(val) {
        if(val === null){
            val = undefined
        }
        this.input = val;
      }
    },
    methods: {
      change(val) {
        this.$emit('update:model', val)
      }
    }
  }
</script>
