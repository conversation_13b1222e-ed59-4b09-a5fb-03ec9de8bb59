<template>
  <div :class="{'input-label': label}">
    <label v-if="label && label !== ' '">{{label}}</label>
    <el-input size="mini" 
              clearable
              v-model="input" 
              :disabled="disabled"
              :style="{'width': width,'display': slotName?'inline-table':'inline-block'}"
              :placeholder="placeholder || '请输入'"
              :type="type"
              :maxlength="maxlength"
              :show-word-limit="showWordLimit"
              :autosize="{ minRows: minRows ? minRows : 2, maxRows: maxRows ? maxRows : 2}"
              @change="change">
              <template v-if="slotName" slot="prepend">{{ slotName }}</template>    
    </el-input>
  </div>
</template>

<script>
  export default {
    name: 'ms-input',
    data() {
      return {
        input: this.model
      }
    },
    props: [
      "label", // => 输入框label
      "index", // => 回填name
      "placeholder", // => 占位符
      "operation",
      "model",
      "disabled",  // => 选择项
      "width", // => 输入框宽度
      "type",
      "maxlength",
      "showWordLimit",
      "minRows",
      "maxRows", // => 文本域最大高度
      "slotName", 
    ],
    watch: {
      model: function(val) {
        this.input = val;
      }
    },
    methods: {
      change(val) {
        this.$emit('update:model', val)
      }
    }
  }
</script>
