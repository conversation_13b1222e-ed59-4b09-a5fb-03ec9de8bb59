<template>
  <div :class="{'input-label': label}">
    <label v-if="label && label !== ' '">{{label}}</label>
    <el-select size="mini"
               v-model="result" 
               :clearable="clearable == 'clearable' ? false : true"
               filterable 
               :multiple="multiple"
               :style="{width: width || '100%'}"
               :disabled="disabled" 
               :placeholder="placeholder"
               @change="change">
        <el-option v-for="(item,index) in localOptions"
                   :key="index"
                   :label="item.label || item.name"
                   :value="item.value">
        </el-option>
    </el-select>
  </div>
</template>

<script>
  import { mapGetters } from "vuex";
  export default {
    name: "ms-select-local",
    props: [
      "multiple", // => 是否可多选
      "label", // => 输入框label
      "index", // => 回填name
      "placeholder", // => 占位符
      "operation",
      "model",
      "options",  // => 选择项
      "disabled",  // => 选择项
      "width", // => 输入框宽度
      "code",
      "clearable"
    ],
    data() {
      return {
        result: this.multiple ? this.model || [] : this.model,
        localOptions: []
      }
    },
    computed: {
      ...mapGetters(["localCode"])
    },
    watch: {
      model: function(val) {
        this.result = val;
      }
    },
    mounted() {
      this.localOptions =  this.options && this.options.length > 0 ? this.options : this.localCode[this.code]
    },
    methods: {
      change(val) {
        this.$emit('update:model', val)
      }
    }
  }
</script>
