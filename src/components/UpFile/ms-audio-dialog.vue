<template>
  <el-dialog
    :visible.sync="dialog"
    width="50%"
    title="选择音频"
    @close="close"
    :before-close="close"
    :before-upload="beforeUpload"
    :append-to-body="isAppendBody"
    class="img-dialog"
    v-if="dialog"
  >
    <el-scrollbar>
      <section class="upload-img">
        <el-tabs v-model="activeName" type="card" class="upload-img-com" @tab-click="search">
          <el-tab-pane label="手动上传" name="manual">
            <el-form-item label="标题" style="width: 80%;">
              <el-input v-model="audioTitle"></el-input>
            </el-form-item>
            <el-form-item label="关键字" style="width: 80%;">
              <el-input v-model="audioExplain"></el-input>
            </el-form-item>
            <el-form-item label="音频内容" >
              <el-upload
                :http-request="upLoad"
                :on-remove="handleRemove"
                :before-upload="beforeUpload"
                :show-file-list="false"
                action=""
              >
                <el-progress
                  type="circle"
                  v-show="showProgress"
                  :percentage="imgprogress"
                  class="avatar-progress"
                  :width="110"
                ></el-progress>
                <el-button style="background-color: #f2f2f2;" size="medium">上传音频</el-button>
                <span v-if="audioContext && audioContext.length != 0"> {{audioContext[0].url}} </span>
              </el-upload>
            </el-form-item>
          </el-tab-pane>
          <el-tab-pane
            label="公共音频库"
            name="library"
          >
            <div class="search-wrap">
              <el-input v-model="title" clearable placeholder="输入标题关键字搜索音频素材"></el-input>
              <el-button @click="search"  style="margin: 0 2px 0 20px;">查 询</el-button>
              <el-button @click="reset"  style="margin: 0 22px 0 2px;">重 置</el-button>
            </div>


            <el-table
              ref="multipleTable"
              :data="projectAudioList"
              tooltip-effect="dark"
              class="audio-table">
              <el-table-column
                label="音频ID"
                width="120">
                <template slot-scope="scope">{{ scope.row.id }}</template>
              </el-table-column>
        
              <el-table-column
                prop="audioTitle"
                label="音频名称">
              </el-table-column>
              <el-table-column
                label="时长"
                width="120">
                <template slot-scope="scope">{{ scope.row.duration }}</template>
              </el-table-column>
              <el-table-column
                label="操作"
                width="120">
                <template slot-scope="scope">
                  <el-popover
                    placement="top"
                    width="400"
                    @hide="outAudio"
                    @show="auditionUrl=scope.row.audioUrl"
                    trigger="click">
                    <!-- 音频试听 -->
                    <div style="text-align: center;"><audio :src="auditionUrl" controls="controls" ref="audio"></audio></div>
                    <span slot="reference" style="margin-right: 10px;"><i class="el-icon-headset"></i></span>
                  </el-popover>
                  <input type="checkbox" @click="handleSelectionChange(scope.row,$event)" :checked="selectAudio.some(item => item.id == scope.row.id) ? true:false">
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
        </el-tabs>
      </section>
    </el-scrollbar>
    <div class="tip" v-show="activeName === 'manual'">
      <ul>
        <li>格式支持mp3、wma、wav、amr、m4a</li>
        <li>文件大小不超过100M，时长不超过2小时</li>
      </ul>
     
    </div>
    <div v-show="activeName === 'library'">
      <el-pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="projectAudioPage"
      :page-sizes="[5, 10]"
      :page-size="projectAudioSize"
      layout="prev, pager, next, sizes, jumper"
      :total="projectAudioTotle"
      >
      </el-pagination>
      <div>已选择 {{selectAudio.length}}/1 个音频</div>
    </div>
    <span slot="footer">
      <el-button @click="close" size="medium">取 消</el-button>
      <el-button type="primary" size="medium" @click="confirmImg" :loading="buttonLoading">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { mapGetters } from "vuex";
import COS from "cos-js-sdk-v5";
export default {
  name: "ms-image-dialog",
  // mixins: [upload],
  data() {
    return {
      activeName: "manual",
      audioLi: [], // 上传图片url
      chooseImgUrl: "", // 富文本框选择图片url
      audioContext: [], //上传音频的内容
      isPrivate: false,
      imgLibrary: [],
      showProgress: false,
      imgprogress: 0,
      audioTitle:'',//音频名称
      audioExplain:'', //音频说明
      selectAudio:[], //勾选中的音频
      auditionUrl:'',//试听的音频
      audioDuration: 0,//音频时长
      audioRes:{},//上传音频内容
      audioSize:0,//上传音频大小
      //项目素材
      projectAudioTotle: 0,
      projectAudioSize: 5,
      projectAudioPage: 1,//页数
      projectAudioList: [],

      proImgLoading: false,

      // 图片上传限制
      upFileFormat: "audio",
      upFileSize: 100,
      uploadTimeout: 30 * 1000,
      title: '',
      dataObj: {},
      buttonLoading: false,
     
    };
  },
  props: {
    dialog: {
      type: Boolean,
      default: false,
    },
    isAppendBody: {
      type: Boolean,
      default: true,
    },
    isTip: {
      type: Boolean,
      default: true,
    },
  },
  computed: {
    ...mapGetters(["serverInfo"]),
    editorImg: function () {
      var arr = this.$store.getters.editorImgArr;
      return arr;
    },
    libImg: function () {
      return this.$store.getters.libraryImg || [];
    },
  },
  watch: {
    libImg: function (val) {
      this.imgLibrary = val.slice(0, 30);
      if (!this.imgLibrary || this.imgLibrary.length == 0) {
        this.imgLibrary = val.slice(0, 30);
      }
    },
  },
  mounted() {
    this.imgLibrary = this.$store.getters.libraryImg.slice(0, 30);

    // 获取项目素材
    this.getProjectImage();
  },
  methods: {
    handleRemove() {
      // 因为目前都是单图上传，所以直接清空图片list
      this.audioLi = [];
    },
    progressOpe(p) {
      this.showProgress = true;
      this.imgprogress = Math.floor(p * 100);
    },
    uploadSuccess(res, name, size) {
      var imgUrl
      if(window.location.href.includes('Bioon')){
        imgUrl = `https://${res.name}`;
      }else {
        imgUrl = `https://${res.name}`;
      }
      this.audioLi[0] = { url: imgUrl, name : name};

      // 上传成功，添加到音频素材库 **接口
      if (this.$store.getters.info.roleLevel !== 3) {
        let params = {
          userId: this.$store.getters.info.userId,
          username: this.$store.getters.info.userName,
          audioKey: res.name,
          audioSize: size,
          audioUrl: imgUrl,
          audioTitle: name,
          duration : this.audioDuration,
        };
        this.api.saveAudioMaterial({...params,keyword:this.audioExplain});
        this.api.saveToolImpactFactorAttachment({...params,id:this.$route.query.id ? this.$route.query.id : 0});
      }
    },
    uploadFiled(err) {
      this.$message.error(err || "上传失败");
    },
    // 上传弹框确定上传
    confirmImg() {
      if (this.activeName === "manual") {
        this.uploadSuccess(this.audioRes, this.dataObj, this.audioTitle, this.audioSize,)
        // this.audioLi = [199]
        this.$emit("getAudioUrl", this.audioLi);
        this.audioLi = [];
        this.$emit("close", false);
      } else {
        if (!this.selectAudio || this.selectAudio.length == 0) {
          return this.PUBLIC_Methods.apiNotify("请选择音频", "warning");
        }
        let params = {
          userId: this.$store.getters.info.userId,
          username: this.$store.getters.info.userName,
          audioKey: this.selectAudio[0].audioKey,
          audioSize: this.selectAudio[0].audioSize,
          audioUrl: this.selectAudio[0].audioUrl,
          audioTitle: this.selectAudio[0].audioTitle,
          duration : this.selectAudio[0].duration,
          id : this.$route.query.id ? this.$route.query.id : 0
        };
        this.api.saveToolImpactFactorAttachment(params);
        this.$emit("getAudioUrl", [{ url: this.selectAudio[0].audioUrl, name : this.selectAudio[0].audioTitle }]);
        this.$emit("close", false);
      }
    },
    close() {
      this.audioLi = [];
      this.$emit("close", false);
    },
    // 搜索
    getProjectImage() {
      this.proImgLoading = true;
      this.api
        .getAudioMaterialListByProject({
          pageIndex: this.projectAudioPage,
          pageSize: this.projectAudioSize,
          keyword: this.title,
        })
        .then((res) => {
          this.proImgLoading = false;
          if (Number(res.status) === 200) {
            this.projectAudioList = res.data
            this.projectAudioTotle = res.totalSize;
          }
        });
    },
    // 查询音频库
    search() {
      this.projectAudioPage = 1
      this.getProjectImage();
    },
    reset(){
      this.title = ''
      this.search()
    },
    // 音频文件时长
    getAudioDuration(file){
      return new Promise(function(resolve){
        const url = URL.createObjectURL(file);
        const filelement = new Audio(url);
        let duration = 0
        filelement.addEventListener("loadedmetadata",function () {
          duration = filelement.duration; // 得到视频或音频的时长，单位秒
          resolve(duration)
        })
      })
    },

    beforeUpload(file) {
      let fileSuffix = file.name.substr(file.name.lastIndexOf('.')).toLowerCase()
      // 危险文件类型校验
      if (fileSuffix === '.bat' || fileSuffix === '.sh') {
          this.$message({
              message: '警告！您上传的文件存在风险',
              type: 'warning'
          })
          return false;
      }
       // 校验音频格式
      if (this.upFileFormat === 'audio' && !/\.(mp3|wma|wav|amr|m4a)$/.test(fileSuffix)) {
          this.$message({
              message: '请上传MP3、wma、wav、amr、m4a类型文件',
              type: 'warning'
          })
          return false;
      }
      // 校验文件大小
      if (this.upFileSize > 0 && file.size / 1024 / 1024 > this.upFileSize) {
          this.$message({
              message: `上传文件大小不能超过${this.upFileSize}MB`,
              type: 'warning'
          })
          return false;
      }
      return new Promise(async (resolve, reject) => {
         // 校验音频时长
         this.audioDuration = await this.getAudioDuration(file)
          if(this.audioDuration > 7200){
            this.$message({
              message: '音频时长不能超过2小时',
              type: 'warning'
            })
            return reject()
          }
          //getToken 获取OSS秘钥的接口地址
          this.buttonLoading = true;
          this.api.getToken({ type:  1 }).then(response => {
              this.dataObj = {
                  region: 'oss-cn-shanghai',
                  accessKeyId: response.data.accessKeyId,
                  accessKeySecret: response.data.accessKeySecret,
                  bucket: response.data.publicBucketName,
                  stsToken: response.data.securityToken,
                  timeout: this.uploadTimeout || 50 * 1000
              }
              resolve(true)
          }).catch(() => {
              reject(false)
          })
      })
    },
    async upLoad(file) {
      // let files = file.file
      //   let  fileSize = files.size
      //   let   fileName = files.name
      let files = file.file,
          fileSize = files.size,
          point = files.name.lastIndexOf('.'),
          suffix = files.name.substr(point),
          fileName = files.name,
          nDate = new Date(),
          year = nDate.getFullYear(),
          mouth = nDate.getMonth() < 10 ? '0' + (new Date().getMonth() + 1) : new Date().getMonth() + 1,
          date = nDate.getDate() < 10 ? '0' + new Date().getDate() : new Date().getDate(),
          timestamp = nDate.getTime(),
          fileNames = ''
          if(window.location.href.includes('Bioon')){
            fileNames = this.fileSuffix ? `bioon-com/${this.fileSuffix}/${timestamp}${suffix}` : `bioon-com/${year}${mouth}${date}/${timestamp}${suffix}`;
          }else {
            fileNames = this.fileSuffix ? `${this.fileSuffix}/${timestamp}${suffix}` : `${year}${mouth}${date}/${timestamp}${suffix}`;
          }

          
          const cos = new COS({
                SecretId: this.dataObj.accessKeyId,
                SecretKey: this.dataObj.accessKeySecret,
                XCosSecurityToken: this.dataObj.stsToken,
              });
             await new Promise((resolve, reject) => {
                cos.uploadFile(
                  {
                    Bucket: this.dataObj.bucket,
                    Region: "ap-shanghai",
                    Key: fileNames,
                    Body:  files,
                  },
                  (err, data) => {
                    if (err) {
                       this.imgUploadFlag = false
                      reject(err);
                    } else {
                      this.buttonLoading = false;
                     let result = {
                          name:data.Location.replace(data.Location.split('/')[0], this.cosUrl)
                     }
                    //  let result = {
                    //    name:data.Location
                    //  }
                    this.audioRes = result
                    // this.audioName = fileName
                    this.audioSize = fileSize
                    if(this.audioTitle == ''){
                      this.audioTitle = fileName
                    }
                    this.audioContext[0] = { url: fileName }
                    this.showProgress = false;
                    this.imgprogress = 0;
                      resolve();
                    }
                  }
                );
              });

    },
    handleSelectionChange(a,e){
       if(e.target.checked){
        // 只支持一个的情况
         this.selectAudio = [a]
         // 支持多个
        //  this.selectAudio.push(a)
       }else{
        this.selectAudio = this.selectAudio.filter(item => item.id != a.id) 
       }
    },
    handleCurrentChange(val){
      this.projectAudioPage = val
      this.getProjectImage()
    },
    handleSizeChange(e){
      this.projectAudioPage = 1
      this.projectAudioSize = e
      this.getProjectImage();
    },
    outAudio(){
      this.auditionUrl = ''
    }

  },
};
</script>
<style>
.upload-img {
  min-height: 250px;
}
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.avatar-uploader .avatar-progress {
  position: absolute;
  left: 7px;
  top: 7px;
  background: #ffffff;
}
.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 124px;
  height: 124px;
  line-height: 122px !important;
  text-align: center;
}
.avatar {
  width: 124px;
  height: 124px;
  display: block;
}
.single-action-d {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  cursor: default;
  text-align: center;
  color: #fff;
  opacity: 0;
  font-size: 24px;
  background-color: rgba(0, 0, 0, 0.5);
  transition: opacity 0.3s;
  line-height: 114px;
}
.single-action-d:hover {
  opacity: 1;
}
.single-action-d span {
  display: inline-block;
  cursor: pointer;
}
</style>
<style lang="scss" scoped>
.tip {
  margin-left: 50px;
  font-size: 14px;
  position: relative;
  top: 10px;
  color: #d7d7d7;
  li{
    list-style-type: disc;
  }
}
.img-dialog {
  /deep/ .el-dialog__body {
    padding: 0 20px 0;
  }
  /deep/ .el-scrollbar__wrap {
    overflow-x: hidden;
  }
  /deep/ .el-dialog {
    min-width: 660px;
  }
}
::v-deep{
  .audio-table{
    width: 100%; 
    height: 300px;    
    overflow-y: scroll;padding-bottom: 15px;
  }
}
.img-choose {
  &-item {
    width: 194px;
    height: 124px;
    border: 1px solid #c0ccda;
    border-radius: 6px;
    margin: 0 8px 8px 0;
    overflow: hidden;
    float: left;
    position: relative;
    cursor: pointer;
    input[type="radio"] {
      position: absolute;
      width: 0;
      height: 0;
      opacity: 0;
    }
    label {
      width: 100%;
      height: 100%;
      display: inline-block;
      background-size: cover;
      background-repeat: no-repeat;
    }
    input:checked + label {
      .choose-radio {
        display: block;
      }
    }
    .choose-radio {
      position: absolute;
      right: -15px;
      top: -6px;
      width: 40px;
      height: 24px;
      background: #13ce66;
      text-align: center;
      transform: rotate(45deg);
      box-shadow: 0 0 1pc 1px rgba(0, 0, 0, 0.2);
      display: none;
      i {
        font-size: 12px;
        margin-top: 11px;
        transform: rotate(-45deg);
        color: #fff;
      }
    }
  }
}
.tag-wrap{
  position: absolute;
  bottom: 0;
  right: 0;
  font-size: 12px;
  padding: 2px 6px;
  background: #fff;
  box-shadow: 10px 10px 10px 10px#888888
}
.search-wrap{
  padding-bottom: 15px;
  display: flex;
}
</style>
