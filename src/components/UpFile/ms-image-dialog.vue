<template>
  <el-dialog
    :visible.sync="dialog"
    width="50%"
    title="图片上传"
    @close="close"
    :before-close="close"
    :before-upload="beforeUpload"
    :append-to-body="isAppendBody"
    class="img-dialog"
    v-if="dialog"
  >
    <el-scrollbar style="height: 350px">
      <div class="search-wrap">
        <el-input v-model="title" clearable></el-input>
        <el-button @click="search" type="primary" style="margin: 0 22px 0 20px;">搜 索</el-button>
      </div>
      <section class="upload-img">
        <el-tabs v-model="activeName" type="card" class="upload-img-com" @tab-click="search">
          <el-tab-pane label="手动上传" name="manual">
            <el-upload
              class="avatar-uploader"
              :http-request="upLoad"
              :on-remove="handleRemove"
              :before-upload="beforeUpload"
              :show-file-list="false"
              action=""
            >
              <el-progress
                type="circle"
                v-show="showProgress"
                :percentage="imgprogress"
                class="avatar-progress"
                :width="110"
              ></el-progress>
              <img v-if="imgLi.length > 0" :src="imgLi[0].url" class="avatar" />
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
              <span
                class="single-action-d"
                @click.stop=""
                v-if="imgLi.length > 0"
              >
                <span @click="handleRemove()"
                  ><i class="el-icon-delete"></i
                ></span>
              </span>
            </el-upload>
          </el-tab-pane>
          <!-- v-if="libImg && libImg.length > 0" -->
          <el-tab-pane
            label="公共图片库"
            name="library"
          >
            <div class="img-choose clearfix">
              <div
                class="img-choose-item"
                v-for="(item, index) in imgLibrary"
                :key="index"
              >
                <input
                  type="radio"
                  :id="`libIMG_${index}`"
                  :value="item.fileUrl"
                  name="libChoose"
                  v-model="chooseImgUrl"
                />
                <label :for="`libIMG_${index}`">
                  <el-image
                    style="width: 100%; height: 100%"
                    :src="item.fileUrl"
                    fit="cover"
                  ></el-image>
                  <span class="choose-radio">
                    <i class="el-icon-upload-success el-icon-check"></i>
                  </span>
                  <span class="tag-wrap" v-if="item.tagName">{{item.tagName}}</span>
                </label>
              </div>
            </div>
          </el-tab-pane>

          <!-- 项目图片库 -->
          <!-- v-if="libImg && libImg.length > 0" -->
          <el-tab-pane
            label="项目图片库"
            name="projectLibrary"
          >
            <div class="img-choose clearfix" v-loading="proImgLoading">
              <div
                class="img-choose-item"
                v-for="(item, index) in projectImgList"
                :key="index"
              >
                <input
                  type="radio"
                  :id="`proIMG_${index}`"
                  :value="item.url"
                  name="libChoose"
                  v-model="chooseImgUrl"
                />
                <label :for="`proIMG_${index}`">
                  <el-image
                    style="width: 100%; height: 100%"
                    :src="item.url"
                    fit="cover"
                  ></el-image>
                  <span class="choose-radio">
                    <i class="el-icon-upload-success el-icon-check"></i>
                  </span>
                  <span class="tag-wrap" v-if="item.tagName">{{item.tagName}}</span>
                </label>
              </div>
            </div>
          </el-tab-pane>

          <el-tab-pane
            label="内容图片选择"
            name="editor"
            v-if="editorImg && editorImg.length > 0"
          >
            <div class="img-choose clearfix">
              <div
                class="img-choose-item"
                v-for="(item, index) in editorImg"
                :key="index"
              >
                <input
                  type="radio"
                  :id="`editorIMG_${index}`"
                  :value="item"
                  name="imgChoose"
                  v-model="chooseImgUrl"
                />
                <label :for="`editorIMG_${index}`">
                  <el-image
                    style="width: 100%; height: 100%"
                    :src="item"
                    fit="cover"
                  ></el-image>
                  <span class="choose-radio">
                    <i class="el-icon-upload-success el-icon-check"></i>
                  </span>
                  <span class="tag-wrap" v-if="item.tagName">{{item.tagName}}</span>
                </label>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </section>
    </el-scrollbar>
    <span slot="footer">
      <div class="tip" v-show="isTip && activeName === 'manual'">
        图片仅支持 jpg/png 格式
      </div>
      <div style="float: left" v-show="activeName === 'library'">
        <el-pagination
          :page-size="30"
          layout="total, prev, pager, next"
          @current-change="handleCurrentChange"
          :total="libImg.length"
        >
        </el-pagination>
      </div>
      <div style="float: left" v-show="activeName === 'projectLibrary'">
        <el-pagination
          :page-size="projectImgSize"
          layout="total, prev, pager, next"
          @current-change="getProjectImage"
          :total="projectImgTotle"
        >
        </el-pagination>
      </div>
      <el-button type="primary" @click="confirmImg" :loading="buttonLoading"
        >确 定</el-button
      >
      <el-button @click="close">取 消</el-button>
    </span>
  </el-dialog>
</template>

<script>
import upload from "./upload";
import { mapGetters } from "vuex";
export default {
  name: "ms-image-dialog",
  mixins: [upload],
  data() {
    return {
      activeName: "manual",
      imgLi: [], // 上传图片url
      chooseImgUrl: "", // 富文本框选择图片url
      isPrivate: false,
      imgLibrary: [],
      showProgress: false,
      imgprogress: 0,

      //项目素材
      projectImgTotle: 0,
      projectImgSize: 30,
      projectImgList: [],
      proImgLoading: false,

      // 图片上传限制
      upFileFormat: "image",
      uploadTimeout: 30 * 1000,
      title: '',
    };
  },
  props: {
    dialog: {
      type: Boolean,
      default: false,
    },
    isAppendBody: {
      type: Boolean,
      default: true,
    },
    isTip: {
      type: Boolean,
      default: true,
    },
    upFileSize: {
      type: Number,
      default: 2,
    },
  },
  computed: {
    ...mapGetters(["serverInfo"]),
    editorImg: function () {
      var arr = this.$store.getters.editorImgArr;
      return arr;
    },
    libImg: function () {
      console.log(1)
      return this.$store.getters.libraryImg || [];
    },
  },
  watch: {
    libImg: function (val) {
      console.log(2)
      this.imgLibrary = val.slice(0, 30);
      if (!this.imgLibrary || this.imgLibrary.length == 0) {
        this.imgLibrary = val.slice(0, 30);
      }
    },
  },
  mounted() {
    this.imgLibrary = this.$store.getters.libraryImg.slice(0, 30);

    // 获取项目素材
    this.getProjectImage(1);
  },
  methods: {
    handleCurrentChange(val) {
      let length = this.libImg.length;
      this.imgLibrary = [];
      if (val * 30 < length) {
        this.imgLibrary = this.libImg.slice(val * 30 - 30, val * 30);
      } else {
        this.imgLibrary = this.libImg.slice(val * 30 - 30 - length);
      }
    },
    handleRemove() {
      // 因为目前都是单图上传，所以直接清空图片list
      this.imgLi = [];
    },
    progressOpe(p) {
      this.showProgress = true;
      this.imgprogress = Math.floor(p * 100);
    },
    uploadSuccess(res, arg,name, size) {
      this.showProgress = false;
      this.imgprogress = 0;
      var imgUrl
      if(window.location.href.includes('Bioon')){
        imgUrl = `https://msimg.bioon.com/${res.bioonUrl}`;
      }else {
        imgUrl = `https://${res.name}`;
      }
      this.imgLi[0] = { url: imgUrl };

      // 上传成功，添加到素材库
      if (this.$store.getters.info.roleLevel !== 3) {
        let params = {
          userId: this.$store.getters.info.userId,
          username: this.$store.getters.info.userName,
          fileKey: res.name,
          fileSize: size,
          fileUrl: imgUrl,
          title: name,
        };
        this.api.saveImageMaterial(params);
      }
    },
    uploadFiled(err) {
      this.$message.error(err || "上传失败");
    },
    confirmImg() {
      console.log(this.chooseImgUrl,1111);
      if (this.activeName === "manual") {
        this.$emit("getImgUrl", this.imgLi);
        this.imgLi = [];
        this.$emit("close", false);
      } else {
        if (!this.chooseImgUrl) {
          return this.PUBLIC_Methods.apiNotify("请选择图片", "warning");
        }
       
        this.$emit("getImgUrl", [{ url: this.chooseImgUrl }]);
        this.$emit("close", false);
      }
    },
    close() {
      this.imgLi = [];
      this.$emit("close", false);
    },
    getProjectImage(val) {
      this.proImgLoading = true;
      this.api
        .getImageMaterialListByProject({
          pageIndex: val,
          pageSize: this.projectImgSize,
          title: this.title,
        })
        .then((res) => {
          this.proImgLoading = false;
          if (Number(res.status) === 200) {
            this.projectImgList = res.data.map((v) => {
              return {
                url: v.fileUrl,
                tagName: v.tagName,
              };
            });
            this.projectImgTotle = res.totalSize;
          }
        });
    },
    search() {
      console.log(this.activeName, 'activeName')
      if(this.activeName == 'library') {
        this.api.getImageMaterialList({
          pageIndex: 1,
          pageSize: 1000,
          title: this.title
        })
        .then((res) => {
          console.log(res, 'rr')
          if (Number(res.status) === 200) {
            this.$store.dispatch('SetLibraryImg', res.data)
            // this.libImg = res.data.length;
            // this.imgLibrary = res.data.slice(0, 30);
          }
        });
      }
      if(this.activeName == 'projectLibrary') {
        this.getProjectImage(1);
      }
    }
  },
};
</script>
<style>
.upload-img {
  min-height: 250px;
}
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.avatar-uploader .avatar-progress {
  position: absolute;
  left: 7px;
  top: 7px;
  background: #ffffff;
}
.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 124px;
  height: 124px;
  line-height: 122px !important;
  text-align: center;
}
.avatar {
  width: 124px;
  height: 124px;
  display: block;
}
.single-action-d {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  cursor: default;
  text-align: center;
  color: #fff;
  opacity: 0;
  font-size: 24px;
  background-color: rgba(0, 0, 0, 0.5);
  transition: opacity 0.3s;
  line-height: 114px;
}
.single-action-d:hover {
  opacity: 1;
}
.single-action-d span {
  display: inline-block;
  cursor: pointer;
}
</style>
<style lang="scss" scoped>
.tip {
  float: left;
  font-size: 14px;
  position: relative;
  top: 10px;
  color: #f56c6c;
}
.img-dialog {
  /deep/ .el-dialog__body {
    padding: 0 20px 0;
  }
  /deep/ .el-scrollbar__wrap {
    overflow-x: hidden;
  }
  /deep/ .el-dialog {
    min-width: 660px;
  }
}
.img-choose {
  &-item {
    width: 194px;
    height: 124px;
    border: 1px solid #c0ccda;
    border-radius: 6px;
    margin: 0 8px 8px 0;
    overflow: hidden;
    float: left;
    position: relative;
    cursor: pointer;
    input[type="radio"] {
      position: absolute;
      width: 0;
      height: 0;
      opacity: 0;
    }
    label {
      width: 100%;
      height: 100%;
      display: inline-block;
      background-size: cover;
      background-repeat: no-repeat;
    }
    input:checked + label {
      .choose-radio {
        display: block;
      }
    }
    .choose-radio {
      position: absolute;
      right: -15px;
      top: -6px;
      width: 40px;
      height: 24px;
      background: #13ce66;
      text-align: center;
      transform: rotate(45deg);
      box-shadow: 0 0 1pc 1px rgba(0, 0, 0, 0.2);
      display: none;
      i {
        font-size: 12px;
        margin-top: 11px;
        transform: rotate(-45deg);
        color: #fff;
      }
    }
  }
}
.tag-wrap{
  position: absolute;
  bottom: 0;
  right: 0;
  font-size: 12px;
  padding: 2px 6px;
  background: #fff;
  box-shadow: 10px 10px 10px 10px#888888
}
.search-wrap{
  padding-bottom: 15px;
  display: flex;
}
</style>
