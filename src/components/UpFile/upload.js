import COS from "cos-js-sdk-v5";
import store from '@/store'
export default {
    name: 'Upload',
    data() {
        return {
            dataObj: {},
            buttonLoading: false
        };
    },
    methods: {
        /**
         * [beforeUpload 上传图片前执行获取秘钥]
         * @return {[type]} [description]
         */
        beforeUpload(file) {
            let fileSuffix = file.name.substr(file.name.lastIndexOf('.')).toLowerCase()
            // 危险文件类型校验
            if (fileSuffix === '.bat' || fileSuffix === '.sh') {
                this.$message({
                    message: '警告！您上传的文件存在风险',
                    type: 'warning'
                })
                return false;
            }
            // 校验图片格式
            if (this.upFileFormat === 'image' && !/\.(gif|jpg|jpeg|png|svg|tiff|bmp|webp)$/.test(fileSuffix)) {
                this.$message({
                    message: '请上传图片类型文件',
                    type: 'warning'
                })
                return false;
            }
            // 校验视频格式
            if (this.upFileFormat === 'video' && !/\.(mp4|mkv)$/.test(fileSuffix)) {
                this.$message({
                    message: '请上传MP4、MKV类型文件',
                    type: 'warning'
                })
                return false;
            }
            // 校验文件大小
            if (this.upFileSize > 0 && file.size / 1024 / 1024 > this.upFileSize) {
                this.$message({
                    message: `上传文件大小不能超过${this.upFileSize}MB`,
                    type: 'warning'
                })
                return false;
            }

            return new Promise((resolve, reject) => {
                //getToken 获取OSS秘钥的接口地址
                this.buttonLoading = true;
                this.api.getToken({ type: this.upFileFormat == 'video' ? 3 : 1 }).then(response => {
                    this.dataObj = {
                        region: 'oss-cn-shanghai',
                        accessKeyId: response.data.accessKeyId,
                        accessKeySecret: response.data.accessKeySecret,
                        bucket: response.data.publicBucketName,
                        stsToken: response.data.securityToken,
                        timeout: this.uploadTimeout || 50 * 1000
                    }
                    resolve(true)
                }).catch(() => {
                    reject(false)
                })
            })
        },
        /**
         * [upLoad 自定义上传图片]
         * @param  {[type]} file [上传值]
         * @return {[type]}      [description]
         */
        async upLoad(file) {
            let files = file.file,
                fileSize = files.size,
                point = files.name.lastIndexOf('.'),
                suffix = files.name.substr(point),
                fileName = files.name,
                nDate = new Date(),
                year = nDate.getFullYear(),
                mouth = nDate.getMonth() < 10 ? '0' + (new Date().getMonth() + 1) : new Date().getMonth() + 1,
                date = nDate.getDate() < 10 ? '0' + new Date().getDate() : new Date().getDate(),
                timestamp = nDate.getTime(),
                userId = store.getters.info.userId,
                fileNames = ''
                if(window.location.href.includes('Bioon')){
                  fileNames = this.fileSuffix ? `bioon-com/${this.fileSuffix}/${timestamp}_${userId}${suffix}` : `bioon-com/${year}${mouth}${date}/${timestamp}_${userId}${suffix}`;
                }else {
                  fileNames = this.fileSuffix ? `${this.fileSuffix}/${timestamp}_${userId}${suffix}` : `${year}${mouth}${date}/${timestamp}_${userId}${suffix}`;
                }

            const cos = new COS({
                SecretId: this.dataObj.accessKeyId,
                SecretKey: this.dataObj.accessKeySecret,
                XCosSecurityToken: this.dataObj.stsToken,
            });
            await new Promise((resolve, reject) => {
                cos.uploadFile(
                    {
                        Bucket: this.dataObj.bucket,
                        Region: "ap-shanghai",
                        Key: fileNames,
                        Body: file.file,
                    },
                    (err, data) => {
                        if (err) {
                            let message = err.message
                            if (err.code == 'ConnectionTimeoutError') {
                                message = "上传超时，请检查网络"
                            }
                            this.buttonLoading = false;
                            this.uploadFiled(message)
                            reject(err);
                        } else {
                            this.buttonLoading = false;
                            // let result = {
                            //     name: data.Location
                            // }
                            let result = {
                                name:this.upFileFormat !== 'video' ? data.Location.replace(data.Location.split('/')[0], this.cosUrl) : data.Location,
                                videoCopyUrl: this.upFileFormat !== 'video' ? '' : this.PUBLIC_Methods.dealCosUrl(data.Location).m3u8Url,
                                bioonUrl:data.Location.replace(data.Location.split('/')[0], this.cosUrl).replace('img.medsci.cn/','')
                            }
                            this.uploadSuccess(result, this.dataObj, fileName, fileSize)
                            resolve();
                        }
                    }
                );
            });

        }
    }
};