<template>
    <el-upload action=""
                :http-request="upLoad"
                :before-upload="beforeUpload"
                :file-list="fileLi"
                :show-file-list="false"
                :multiple="false">
        <el-button :loading="buttonLoading" icon="el-icon-upload">{{buttonLabel}}</el-button>
    </el-upload>
</template>

<script>
import upload from "./upload"
export default {
    name: 'ms-single-file',
    mixins: [upload],
    data() {
        return {
        fileLi: []
        }
    },
    props: ["model","buttonLabel"],
    methods: {
        uploadSuccess(res) {
            this.$emit('update:model', `https://${res.name}`)
        },
        uploadFiled(err) {
            this.$message.error(err.message || '上传失败');
        },
    }
}
</script>
