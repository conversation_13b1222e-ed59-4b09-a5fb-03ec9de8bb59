<template>
    <div>
        <el-upload action=""
             :on-remove="handleRemove"
             :http-request="upLoad"
             :before-upload="beforeUpload"
             :on-preview="onPreview"
             :disabled="buttonLoading"
             :file-list="fileLi"
             :limit="1">
            <el-button plain :loading="buttonLoading" icon="el-icon-upload">{{buttonLabel}}</el-button>
        </el-upload>
        <el-progress v-show="showProgress" :percentage="progress"></el-progress>
    </div>
</template>

<script>
import upload from "./upload"
export default {
    name: 'ms-video-upload',
    mixins: [upload],
    data() {
        return {
            fileLi: [],
            isPrivate: true,
            showProgress: false,
            progress: 0,
            type: 3,
            videoCommandUrl: ''
        }
    },
    props: {
        value: {
            type: String,
            default: ''
        },
        buttonLabel: {
            type: String,
            default: '附件上传'
        },
        fileSuffix: {
            type: String,
            default: ''
        },
        upFileFormat: {
            type: String,
            default: ''
        }
    },
    // watch: {
    //     value: function (val) {
    //         if (val) {
    //             this.fileLi = [{name: val}]
    //         }
    //     }
    // },
    created() {
        if (this.value) {
            this.fileLi = [{name: this.value}]
            this.videoCommandUrl = this.value
        }
    },
    methods: {
        handleRemove() {
            this.fileLi = []
            this.$emit('bindData', {
                name: '',
                key: ''
            })
        },
        uploadSuccess(res,arg,name) {
            this.showProgress = false;
            this.progress = 0;
            this.videoCommandUrl = res.videoCopyUrl;
            this.$emit('bindData', {
                name: name,
                key: res.videoCopyUrl
            })
        },
        uploadFiled(err) {
            this.showProgress = false;
            this.progress = 0;
            this.$message.error(err.message || '上传失败');
        },
        progressOpe(p) {
            this.showProgress = true;
            this.progress = Math.floor(p * 100);
        },
        onPreview() {
            let url = `https://ali-video.medsci.cn/${this.videoCommandUrl}`
            let oInput = document.createElement('input')
            oInput.value = url
            document.body.appendChild(oInput)
            oInput.select() // 选择对象
            document.execCommand("Copy") // 执行浏览器复制命令
            this.$message({
                message: '复制成功',
                type: 'success'
            })
            oInput.remove()
        }
    }
}
</script>
