<template>
  <el-upload :on-remove="handleRemove"
             action=""
             :http-request="upLoad"
             :before-upload="beforeUpload"
             :on-preview="onPreview"
             :disabled="buttonLoading"
             :file-list="fileLi"
             :limit="limit"
             :accept="accept"
             :on-exceed="handleExceed"
             multiple>
    <el-button plain :loading="buttonLoading" icon="el-icon-upload">{{buttonLabel}}</el-button>
  </el-upload>
</template>

<script>
import upload from "./upload"
import { mapGetters } from "vuex";
export default {
  name: 'ms-file-upload',
  mixins: [upload],
  data() {
    return {
      fileLi: []
      // isPrivate: true
    }
  },
  computed: {
    ...mapGetters(["serverInfo"])
  },
  props: {
    upFileSize: {
      type: Number,
      default: 0
    },
    limit: {
      type: Number,
      default: 10000
    },
    accept: {
      type: String,
      default: ''
    },
    value: {
      type: Array,
      default: () => {
        return []
      }
    },
    buttonLabel: {
      type: String,
      default: '附件上传'
    },
    isPrivate: {
      type: Boolean,
      default: true
    },
    fileSuffix: {
      type: String,
      default: ''
    }
  },
  watch: {
    value: {
      handler: function (val) {
        if (val.length > 0) {
          this.fileLi = val
        }
      },
      immediate: true
    },
  },
  methods: {
    handleRemove(file, fileList) {
      this.fileLi = fileList
      this.$emit('input', fileList)
    },
    uploadSuccess(res,arg,filename) {
      this.fileLi.push({
        name: filename,
        url: `https://${res.name}`,
        fileKey: res.name
      })
      this.$emit('input', this.fileLi)
    },
    uploadFiled(err) {
      this.$message.error(err.message || '上传失败');
    },
    onPreview(file) {
      let url = file.url
      let oInput = document.createElement('input')
      oInput.value = url
      document.body.appendChild(oInput)
      oInput.select() // 选择对象
      document.execCommand("Copy") // 执行浏览器复制命令
      this.$message({
        message: '复制成功',
        type: 'success'
      })
      oInput.remove()
    },
    handleExceed() {
      this.$message.warning(`当前限制选择${this.limit}个文件 请删除后 重新上传`);
    }
  }
}
</script>
