<template>
  <el-upload
    list-type="picture-card"
    :http-request="upLoad"
    :on-remove="handleRemove"
    :before-upload="beforeUpload"
    action=""
    :limit="1"
    :class="uploadDisable ? 'ms-image-upload ms-image-none' : 'ms-image-upload'"
    :file-list="fileLi"
  >
    <i class="el-icon-plus"></i>
  </el-upload>
</template>

<script>
import upload from "./upload";
export default {
  name: "ms-image-upload",
  mixins: [upload],
  data() {
    return {
      fileLi: [],
      uploadDisable: false,
      upFileFormat: "image",
      upFileSize: 2,
      uploadTimeout: 30 * 1000,
    };
  },
  props: {
    imageUrl: {
      type: String,
      default: "",
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  created() {
    if (this.imageUrl) {
      this.fileLi.push({
        url: this.imageUrl,
      });
      this.uploadDisable = this.disabled;
    }
  },
  methods: {
    uploadSuccess(res, arg,name, size) {
      console.log(size, 'warning');
      var imgUrl
      if(window.location.href.includes('Bioon')){
        imgUrl = `https://msimg.bioon.com/${res.bioonUrl}`;
      }else {
        imgUrl = `https://${res.name}`;
      }
      this.$emit("bindData", {
        name: name,
        key: res.name,
        size: size,
        url: imgUrl,
      });
    },
    uploadFiled(err) {
      this.$message.error(err || "上传失败");
    },
    handleRemove() {},
  },
};
</script>

<style>
.ms-image-upload .el-upload {
  width: 120px;
  height: 120px;
  line-height: 118px;
}
.ms-image-upload .el-upload-list__item {
  width: 120px;
  height: 120px;
}
.ms-image-upload .el-upload-list__item img {
  object-fit: contain;
}

.ms-image-none .el-upload {
  display: none;
}
.ms-image-none .el-upload-list__item-actions {
  display: none;
}
.ms-image-none .el-upload-list__item-status-label {
  display: none !important;
}
</style>
