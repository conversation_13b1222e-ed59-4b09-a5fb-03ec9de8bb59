<template functional>
  <div class="right-dialog">
    <el-scrollbar wrap-class="right-dialog-scroll">
      <slot />
    </el-scrollbar>
    <footer class="right-dialog-footer">
      <slot name="footer"></slot>
    </footer>
  </div>
</template>

<script>
export default {
  name: 'ms-right-content'
}
</script>

<style scope lang="scss">
.right-dialog {
  height: 100%;
  position: relative;
  &-scroll {
    max-height: calc(100vh - 55px - 60px);
    min-height: calc(100vh - 155px);
    padding-bottom: 40px;
  }
  &-footer {
    position: relative;
    width: 100%;
    bottom: 0;
    text-align: center;
    margin-top: 20px;
  }
}
</style>
