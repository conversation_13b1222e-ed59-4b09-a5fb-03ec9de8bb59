<template functional>
  <section class="ms-dialog">
    <header class="ms-dialog-header" v-if="props.title">
      <span v-text="props.title" />
    </header>
    <div class="ms-dialog-line" v-else></div>
    <el-scrollbar class="dialog-scrollbar">
    <div class="ms-dialog-main" :style="{padding: props.title ? '15px 0' : '20px 0'}">
      <slot name="content"></slot>
    </div>
    </el-scrollbar>
    <footer class="ms-dialog-footer">
      <slot name="footer"></slot>
    </footer>
  </section>
</template>

<script>
export default {
  name: 'ms-operation-dialog'
}
</script>

<style>
.dialog-scrollbar .el-scrollbar__wrap{
  max-height: 65vh;
}
</style>

<style scope lang="scss">
.ms-dialog {
  &-line {
    padding: 0 20px;
    height: 1px;
    background: #E2E4E7;
  }
  &-header {
    font-size: 14px;
    padding: 15px 0 0;
    text-align: left;
  }
  &-main {
    text-align: left;
    // max-height: 66vh;
    padding-bottom: 16px
  }
  &-footer {
    margin-top: 10px;
    text-align: center;
  }
}
</style>
