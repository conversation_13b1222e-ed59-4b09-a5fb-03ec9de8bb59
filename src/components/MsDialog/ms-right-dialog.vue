<template>
  <div ref="rightDialog" :class="{show: visible}" class="ms-right-dialog">
    <div class="right-background" @click.self="handleWrapperClick" />
    <div class="right-section" :style="style"> 
      <div class="right-header">
        <span>{{title}}</span>
        <button class="right-header-btn" @click="handleClose">
          <i class="el-icon el-icon-close"></i>
        </button>
      </div>
      <div class="right-content">
        <el-scrollbar>
            <div class="right-content-main">
                <slot />
            </div>
        </el-scrollbar>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ms-right-dialog',
  props: {
    title: String,
    clickNotClose: {  // => 是否点击关闭modal
      default: false,
      type: Boolean
    },
    appendToBody: {
      type: Boolean,
      default: true
    },
    width: {
      type: String,
      default: '40%'
    },
    visible: {
      type: <PERSON>olean,
      default: false
    },
    beforeClose: Function,
  },
  data() {
    return {
      closed: false
    }
  },
  computed: {
    style() {
      let style = {}
      style.width = this.width
      return style
    }
  },
  watch: {
    visible(value) {
      if (value) {
        this.closed = false;
        this.$emit('open')
      } else {
        if (!this.closed) {
          this.$emit('close')
        }
      }
    }
  },
  mounted() {
    if (this.appendToBody) {
      this.insertToBody()
    }
  },
  destroyed() {
    if (this.appendToBody && this.$el && this.$el.parentNode) {
      this.$el.parentNode.removeChild(this.$el);
    }
  },
  methods: {
    handleWrapperClick () {
      if (this.clickNotClose) return;
      this.handleClose()
    },
    handleClose() {
      if (typeof this.beforeClose === 'function') {
        this.beforeClose(this.hide);
      } else {
        this.hide();
      }
    },
    hide() {
      this.$emit('update:visible', false);
      this.$emit('close');
      this.closed = true;
    },
    insertToBody() {
      const el = this.$refs.rightDialog
      const body = document.querySelector('body')
      body.insertBefore(el, body.firstChild)
    }
  }
}
</script>

<style lang="scss" scoped>
  .right-background {
    position: fixed;
    top: 0;
    left: 0;
    opacity: 0;
    transition: opacity .3s cubic-bezier(.7, .3, .1, 1);
    background: rgba(0, 0, 0, .2);
    z-index: -1;
  }
  .right-section {
    height: 100vh;
    // bottom: 0;
    position: fixed;
    top: 0;
    right: 0;
    box-shadow: 0px 0px 15px 0px rgba(0, 0, 0, .05);
    transition: all .25s cubic-bezier(.7, .3, .1, 1);
    transform: translate(100%);
    background: #fff;
    z-index: 2000;
    padding: 0 20px;
  }
  .right-header {
    height: 55px;
    line-height: 55px;
    border-bottom: 1px solid rgba(81, 90, 110, 0.25);
    position: relative;
    span {
      color: #303133;
      font-size: 16px;
    }
    &-btn {
      position: absolute;
      right: 10px;
      top: 17px;
      padding: 0;
      background: transparent;
      border: none;
      outline: none;
      cursor: pointer;
      font-size: 16px;
      .el-icon {
        font-size: 18px;
        color: #909399;
      }
    }
  }
  .right-content {
    height: calc(100vh - 55px);
    padding: 20px 10px;
    overflow-y: auto;
    &-mian {
        max-height: calc(100vh - 55px - 20px);
    }
  }
  .show {
    transition: all .3s cubic-bezier(.7, .3, .1, 1);
    .right-background {
      z-index: 1000;
      opacity: 1;
      width: 100%;
      height: 100%;
    }
    .right-section {
      transform: translate(0);
    }
  }
</style>
