<template>
  <div @click="openScreen" class="screenfull">
    <el-tooltip class="item" effect="dark" :content="isFullscreen?'退出全屏':'全屏'" placement="bottom">
      <svg-icon :icon-class="isFullscreen?'icon-tuichuquanping':'icon-quanping'" class-name="screenfull-svg"/>
    </el-tooltip>
  </div>
</template>

<script>
import screenfull from 'screenfull'

export default {
  name: 'TopScreenfull',
  data() {
    return {
      isFullscreen: false
    }
  },
  mounted() {
    this.init()
  },
  beforeDestroy() {
    this.destroy()
  },
  methods: {
    openScreen() {
      if (!screenfull.enabled) {
        this.$message({
          message: 'you browser can not work',
          type: 'warning'
        })
        return false
      }
      screenfull.toggle()
    },
    change() {
      this.isFullscreen = screenfull.isFullscreen
    },
    init() {
      if (screenfull.enabled) {
        screenfull.on('change', this.change)
      }
    },
    destroy() {
      if (screenfull.enabled) {
        screenfull.off('change', this.change)
      }
    }
  }
}
</script>

<style scoped>
.screenfull {
  display: inline-block;
  margin-right: 20px;
  position: relative;
  cursor: pointer;
}
.screenfull-svg {
  display: inline-block;
  cursor: pointer;
  fill: #5a5e66;
  width: 25px;
  height: 25px;
}
</style>
