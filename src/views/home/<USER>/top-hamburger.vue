<template>
  <div @click="toggleCollapse" :class="!collapse ? '' : 'is-active'">
    <svg-icon icon-class="icon-zhankai" class-name="collapse-svg"></svg-icon>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
export default {
  name: 'Hamburger',
  computed: {
    ...mapGetters(["collapse"])
  },
  methods: {
    toggleCollapse() {
      this.$store.dispatch("SetCollapse")
    },
  }
}
</script>

<style scoped>
  .is-active {
    transform: rotate(90deg);
  }
  .collapse-svg {
    width: 25px;
    height: 25px;
    position: relative;
    top: 3px;
  }
</style>