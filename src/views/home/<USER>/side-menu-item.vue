<template>
  <div v-if="!item.hidden">
    <template v-if="item.childMenu && item.childMenu.length > 0">
      <el-submenu :index="item.menuChineseName">
      <template slot="title">
        <svg-icon v-if="item.menuCron" :icon-class="item.menuCron||''"></svg-icon>
        <span slot="title">{{item.menuChineseName}}</span>
      </template>
      <side-menu-item v-for="(child, childKey) in item.childMenu"
                        :key="childKey"
                        :is-nest="true"
                        :item="child"
                        class="nest-menu"/>
      </el-submenu>
    </template>
    <template v-else>
      <el-menu-item v-if="item.menuRoute"
                    :index="item.menuRoute"
                    @click="menu_click(item.menuRoute)"
                    :disabled="+item.status === 1 ? false : true">
          <svg-icon v-if="item.menuCron" :icon-class="item.menuCron||''"></svg-icon>
          <span slot="title">{{item.menuChineseName}}</span>
      </el-menu-item>
    </template>
  </div>
</template>

<script>
export default {
  name: 'side-menu-item',
  props: {
    item: {
      type: Object,
      required: true
    },
    isNest: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {}
  },
  methods: {
    menu_click(val) {
      this.$store.dispatch('SetSide', val);
      this.$store.dispatch("SetActive", val);
      this.$store.dispatch('SetSearchParams', {})
    }
  }
}
</script>
