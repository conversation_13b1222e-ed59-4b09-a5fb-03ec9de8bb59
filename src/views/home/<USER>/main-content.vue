<template>
  <el-container class="main">
    <el-main style="height:100%;overflow-x: hidden;">
      <transition name="fade-transform" mode="out-in">
        <!-- <keep-alive :include="['ms-article-manage']"> -->
          <router-view :key="key"></router-view>
        <!-- </keep-alive> -->
      </transition>
    </el-main>
  </el-container>
</template>

<script>
import { mapGetters } from "vuex";

export default {
  name: "main-content",
  data () {
    return {
      fullPath: this.$route.fullPath,
      clearKeep: "refresh"
    };
  },
  watch: {
    $route: function (newVal) {
      this.fullPath = newVal.fullPath;
      if (this.clearKeep === "refresh" && this.$route.meta.keepAlive) {
        this.clearKeep = "keep";
      }
    }
  },
  computed: {
    ...mapGetters(["side", "update"]),
    key() {
      return this.$route.path;
    }
  }
};
</script>

<style lang="scss" scoped>
.main {
  height: 100%;
  &-header {
    text-align: left;
    padding: 2px 0 2px 5px;
    line-height: 40px;
    background: #d3dce657;
    white-space: nowrap;
    overflow-x: auto;
    width: 100%;
    border: none;
    &::-webkit-scrollbar {
      width: 7px;
      height: 7px;
    }
    &::-webkit-scrollbar-thumb {
      border-radius: 3px;
      background: #eae9ec;
    }
    &-bread {
      margin-left: 15px;
    }
  }
}
</style>
