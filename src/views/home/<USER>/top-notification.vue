<template>
    <div class="notification">
        <el-badge :is-dot="hasMess" class="item" style="height: 25px;">
            <i class="el-icon-bell" @click="openRightDialog()"></i>
        </el-badge>
        
        <ms-right-dialog :visible.sync="dialogVisible"
                         :width="dialogWidth"
                         :title="dialogTitle">
            <el-table :data="recentList" 
                      v-loading="getLoading" 
                      style="width: 100%">
                <el-table-column label="标题" align="left">
                    <template slot-scope="scope">
                        <el-link @click="openRecent(scope.row)">{{scope.row.calendarTitle}}</el-link>
                    </template>
                </el-table-column>
                <el-table-column label="开始时间" align="right">
                    <template slot-scope="scope">
                        <span>{{scope.row.startAt | parseTime('{m}-{d}')}}</span>
                    </template>
                </el-table-column>
            </el-table>
        </ms-right-dialog>
        <el-dialog :visible.sync="dialog" 
                   closeable 
                   show-close
                   :close-on-click-modal="true"
                   :append-to-body="true"
                   width="60%"
                   title="事件内容查看"
                   class="model-dialog">
            <ms-calendar-preview
                       :model="scopeInfo" 
                       @close="dialog = !dialog" 
                       @up-date="init" 
                       v-if="dialog"></ms-calendar-preview>
        </el-dialog>
    </div>
  
</template>

<script>
// export { parseTime } from '@/utils'
import msCalendarPreview from '@/components/MsCommon/ms-calendar-preview'

export default {
  name: 'TopNotification',
  data() {
    return {
        dialogWidth: '40%',
        dialogTitle: '近期事件',
        dialogVisible: false,
        hasMess: false,
        recentList: [],
        scopeInfo: {},
        dialog: false,
        getLoading: false
    }
  },
  components: {
      'ms-calendar-preview': msCalendarPreview
  },
  created() {
    this.init()
  },
  methods: {
    init() {
      this.api.getOperatingCalendarRecentCount().then(response => {
        if (response.status === 200 && response.data) {
            if (response.data.recentCount && +response.data.recentCount > 0) {
                this.hasMess = true
            }
        }
      }).catch(() => { })
    },
    openRightDialog() {
        this.dialogVisible = true
        this.getData()
    },
    getData() {
        this.getLoading = true;
        this.api.getOperatingCalendarRecent().then(response => {
            if (response.status === 200 && response.data && response.data.length > 0) {
                this.recentList = response.data
            }
            this.getLoading = false
        }).catch(() => { this.getLoading = false })
    },
    openRecent(item) {
        this.dialog = true;
        this.scopeInfo = item;
    }
  }
}
</script>
<style>
    .model-dialog .el-dialog__body {
        padding: 0 20px 30px;
    }
</style>
<style scoped>
.notification {
    display: inline-block;
    margin-right: 20px;
    position: relative;
}
.notification i {
    font-size: 24px;
    position: relative;
    top: -16px;
    cursor: pointer;
}
</style>
