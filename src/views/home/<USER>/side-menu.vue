<template>
  <div class="side" :style="{background: menuStyle.background}">
    <div class="side-logo" :style="menuStyle">
      <!-- <img src="../../../assets/stage_images/timg.png"/> -->
      <span class="side-logo-title" >Backstage</span>
    </div>
    <el-scrollbar style="height: 100%;">
      <el-menu router 
              class="side-menu" 
              :background-color="menuStyle.background" 
              :text-color="menuStyle.color" 
              :default-openeds="opends" 
              @open="menu_open" 
              @close="close_open" 
              :default-active="menuActive" 
              :collapse="collapse">
        <side-menu-item v-for="(item,key) in sideMenu" :key="key" :item="item"></side-menu-item>
      </el-menu>
    </el-scrollbar>
  </div>
</template>

<script>
import { mapGetters, mapActions } from "vuex";
import SideMenuItem from '../tools/side-menu-item'
export default {
  name: "side-menu",
  data () {
    return {
      opends: [],
      menuActive: "",
      menu: [],
      menuStyle: {
        background: '#304156',
        color: '#fff'
      }
    };
  },
  components: {
    SideMenuItem
  },
  computed: {
    ...mapGetters([
      "collapse",
      "sideMenu",
      "side",
      "open",
      "active",
      "permissions"
    ])
  },
  watch: {
    'active':function(val) {
      if (val.indexOf("-") !== -1) {
        this.menuActive = val.split("-")[0]
      } else {
        this.menuActive = val
      }
    }
  },
  created () {
    this.init();
  },
  methods: {
    ...mapActions(["up_date"]),
    init () {
      this.opends = Array.isArray(this.open) ? this.open : [this.open];
      this.menuActive = this.active;
    },
    menu_open (val) {
      if (this.opends.indexOf(val) === -1) {
        this.opends.push(val);
        this.$store.dispatch("SetOpen", this.opends);
      }
    },
    close_open (val) {
      if (this.opends.indexOf(val) !== -1) {
        this.opends.splice(this.opends.indexOf(val), 1);
        this.$store.dispatch("SetOpen", this.opends);
      }
    }
  }
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.side {
  height: 100%;
  padding-bottom: 60px;
  border-right: solid 1px #e4e4e4;
  overflow: hidden;
  &-logo {
    height: 60px;
    img {
      width: 40px;
      height: auto;
      position: relative;
      top: 10px;
    }
    &-title {
      display: inline-block;
      font-size: 18px;
      padding-left: 15px;
      font-weight: 700;
      line-height: 60px;
      
    }
    &-icon {
      display: inline-block;
      vertical-align: middle;
      width: 60px;
      height: 60px;
      text-align: center;
      line-height: 60px;
    }
  }
  &-menu {
    text-align: left;
    width: 100%;
    /deep/ .el-menu-item {
      border-right: none !important;
      min-width: 0 !important;
    }
  }
}
</style>
<style>
.side-menu .active-item {
  font-weight: 700;
}
.side-logo-icon i {
  color: #fff;
  font-size: 20px;
}
</style>
