<template>
  <el-breadcrumb separator="/">
    <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
      <template v-for="(item, index) in breadCrumbArr">
        <el-breadcrumb-item :key="index" :to="{ path: item.path }">
          <span :class="{'no-redirect': index === breadCrumbArr.length - 1}">{{item.title}}</span>
        </el-breadcrumb-item>
      </template>
    </el-breadcrumb>
</template>

<script>
import { mapGetters } from "vuex";
export default {
  name: 'BreadCrumb',
  data() {
    return {
      breadCrumbArr: []
    }
  },
  computed: {
    ...mapGetters(["sideMenu"])
  },
  watch: {
    $route: function(newRoute) {
      this.getBreadcrumb(newRoute).then(res => {
        this.breadCrumbArr = res
      }) 
    }
  },
  created() {
    this.getBreadcrumb(this.$route).then(res => {
        this.breadCrumbArr = res
      }) 
  },
  methods: {
    getBreadcrumb(val) {
      return new Promise((resolve) => {
        let route = val
        let res = []
        if (route.path !== '/' && route.path !== '/homePage') {
          if (route.path.indexOf("-") !== -1) {
            res.push({
              path: route.path.split("-")[0],
              title: route.meta.title.split("-")[0]
            }) 
            res.push({
              path: route.path,
              title: route.meta.title.split("-")[1]
            })
          } else {
            res.push({
              path: route.path,
              title: route.meta.title
            })
          }
        }
        resolve(res)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.no-redirect {
  color: #97a8be;
  cursor: text;
}
</style>
