<template>
  <div @click="openScreen" class="otherscreen">
    <el-tooltip class="item" effect="dark" content="打开新页面" placement="bottom">
      <i class="el-icon-circle-plus-outline otherscreen-icon"></i>
    </el-tooltip>
  </div>
</template>

<script>

export default {
  name: 'TopOtherscreen',
  data() {
    return {
    }
  },
  methods: {
    openScreen() {
      window.open(window.location.href)
    },
  }
}
</script>

<style scoped>
.otherscreen {
  display: inline-block;
  margin-right: 15px;
  position: relative;
  cursor: pointer;
}
.otherscreen-icon {
  font-size: 24px;
  position: relative;
  top: 3px;
  cursor: pointer;
}
</style>
