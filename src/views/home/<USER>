<template>
    <el-container class="ms-container">
        <el-aside :style="styleObj" :class="classObj">
            <side-menu v-if="!topDone"></side-menu>
        </el-aside>
        <el-container class="ms-container" :style="styleCon">
            <el-header class="ms-header">
                <top-menu @success="topDone = false"></top-menu>
            </el-header>
            <el-main class="ms-main" v-loading="topDone">
              <el-scrollbar tag="div" 
                            style="height: 100%;">
                <main-content v-if="!topDone"></main-content>
              </el-scrollbar>
            </el-main>
        </el-container>
    </el-container>
</template>

<script>
  import topMenu from './components/top-menu'
  import sideMenu from './components/side-menu'
  import mainContent from './components/main-content'
  import {mapGetters} from 'vuex';

  export default {
    name: 'home',
    data() {
      return {
        topDone: true
      }
    },
    components: {
      topMenu,
      sideMenu,
      mainContent
    },
    computed: {
      ...mapGetters(['collapse']),
      styleObj() {
        return {
          'overflow-x': 'hidden',
          'width': this.collapse ? '60px' : '180px',
          'transition': 'width 0.3s'
        }
      },
      classObj() {
        return {
          hideSidebar: this.collapse
        }
      },
      styleCon() {
        return {
          'width': `calc(100% - ${this.collapse ? 60 : 180}px)`
        }
      }
    }
  }
</script>

<style lang="scss" scoped>
  .ms-container {
    height: 100%;
    width: 100%;
  }
  .ms-header {
    padding: 0;
    z-index: 99;
  }
  .ms-main {
    padding:0px;
    height:100%;
  }
</style>
