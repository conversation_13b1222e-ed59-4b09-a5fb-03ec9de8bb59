<template>
  <div class="navbar">
    <top-hamburger class="navbar-hamburger"></top-hamburger>
    <top-breadcrumb class="navbar-breadcrumb"></top-breadcrumb>
    <div class="navbar-user" v-loading="loading">
      <top-notification></top-notification>
      <top-screenfull></top-screenfull>
      <top-otherscreen></top-otherscreen>
      <top-project></top-project>
      <el-dropdown trigger="click" size="medium" @command="drop_role" class="top-user-drop">
        <span class="el-dropdown-link">
          <span class="user-name">{{info.userName}},{{info.roleName}}</span>
          <svg-icon icon-class="icon-xiala1" class-name="user-icon"></svg-icon>
        </span>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item v-for="(item, index) in info.getRoleLoginResponses || []"
                        :key="index"
                        :command="item">{{item.roleName}}</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
      <el-dropdown trigger="click" size="medium" @command="drop_command" class="top-user-drop">
        <span class="el-dropdown-link">
          <div class="user-img">
            <svg-icon icon-class="icon-guanlizhe" class-name="user-img-svg"></svg-icon>
          </div>
          <svg-icon icon-class="icon-xiala1" class-name="user-icon"></svg-icon>
        </span>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item command="workbench">我的工作台</el-dropdown-item>
          <el-dropdown-item command="ChangePassword" divided>修改密码</el-dropdown-item>
          <el-dropdown-item command="LogOut" divided>退出登录</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
      
    </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import TopHamburger from '../tools/top-hamburger'
import TopBreadcrumb from '../tools/top-breadcrumb'
import TopScreenfull from '../tools/top-screenfull'
import TopOtherscreen from '../tools/top-otherscreen'
import TopProject from '../tools/top-project'
import TopNotification from '../tools/top-notification'

export default {
  name: "top-menu",
  data () {
    return {
      loading: false,
    };
  },
  components: {
    TopHamburger,
    TopBreadcrumb,
    TopScreenfull,
    TopOtherscreen,
    TopProject,
    TopNotification
  },
  computed: {
    ...mapGetters(["info", "project"])
  },
  created () {
    this.init();
  },
  methods: {
    async init () {
      this.loading = true;
      await this.$store.dispatch("GetInfo");
      this.loading = false;
      this.$emit('success');
    },
    drop_role (val) {
      let params = {
        projectId: this.info.projectId,
        roleId: val.roleId 
      }
      this.api.getMedsciPlatformMenuList(params).then(response => {
        if (response.status === 200 && response.data) {
          this.$store.dispatch('GetMenu', response.data.getMedsciRoleAndMenuResponses || [])
          let menuPermission = response.data.getMedsciMenusInMenuRouteResponses || []
          let arr = []
          let roleMenu = {}
          menuPermission.forEach(v => {
            if (v.menuRoute) {
              arr.push(`/${v.menuRoute}`)
              roleMenu[`/${v.menuRoute}`] = v.id 
            }
          });
          this.$store.dispatch('SetPermissionMenuId', roleMenu)
          this.$store.dispatch('SetPermissionMenu', arr)
          this.$store.dispatch('TabRole',val)
          this.$store.dispatch('clearPermissionBtn')
        } else {
          this.$store.dispatch('GetMenu', [])
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
        this.$router.push('/');
      }).catch(() => { this.$store.dispatch('GetMenu', []) })
    },
    drop_command (val) {
      if (val === "LogOut") {
        this.$store.dispatch("LogOut").then(resp => {
          this.$router.push(resp);
        });
      } else if (val === "ChangePassword") {
        this.$router.push('userpassword')
      } else if (val === 'workbench') {
        this.$router.push('workbenchArticle')
      }
    }
  }
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.navbar {
  height: 60px;
  width: 100%;
  overflow: hidden;
  background: #fff;
  position: relative;
  box-shadow: 0 -4px 23px 10px rgba(0, 21, 41, 0.08);
  &-hamburger {
    line-height: 56px;
    height: 100%;
    float: left;
    cursor: pointer;
    padding: 0 20px;
  }
  &-breadcrumb {
    float: left;
    line-height: 60px;
    margin-left: 10px;
    font-size: 14px;
    height: 100%;
  }
  &-user {
    float: right;
    line-height: 60px;
    height: 100%;
    margin-right: 16px;
    .user-name {
      font-size: 14px;
      cursor: pointer;
    }
    .user-icon {
      width: 14px;
      margin-left: 4px;
      position: relative;
      top: -1px;
    }
    .user-img {
      width: 35px;
      height: 35px;
      cursor: pointer;
      border-radius: 50%;
      box-shadow: 0px 5px 8px -1px #ccc;
      position: relative;
      top: -4px;
      margin-left: 14px;
      display: inline-block;
      text-align: center;
      line-height: 30px;
      &-svg {
        width: 24px;
        height: 24px;
        position: relative;
        top: 2px;
        left: 2px;
      }
    }
  }
}
</style>
