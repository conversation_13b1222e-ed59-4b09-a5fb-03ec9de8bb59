<template>
  <el-dropdown trigger="click" size="medium" @command="drop_project" class="project-drop">
    <span class="el-dropdown-link">
      <span class="project-name">{{info.projectName || '请选择项目'}}</span>
      <svg-icon icon-class="icon-xiala1" class-name="project-icon"></svg-icon>
    </span>
    <el-dropdown-menu slot="dropdown">
      <el-dropdown-item v-for="(item, index) in project"
                        :key="index"
                        :command="item">{{item.projectName}}</el-dropdown-item>
    </el-dropdown-menu>
  </el-dropdown>
</template>

<script>
import { mapGetters } from "vuex";
export default {
  name: 'Hamburger',
  computed: {
    ...mapGetters(["info","project"])
  },
  created() {
    if (this.info && this.info.projectId && this.info.getRoleLoginResponses) {
      this.init(this.info)
    }
    // 获取图片素材库
    this.api.getImageMaterialList({pageIndex: 1,pageSize: 1000}).then(res => {
      if (res.status === 200 && res.data) {
        this.$store.dispatch('SetLibraryImg', res.data)
      }
    })
    // 获取讲师库信息
    this.api.getMedsciSpeakePage({pageIndex: 1,pageSize: 400}).then(response => {
      if (response.status === 200 && response.data) {
        this.$store.dispatch('SetSpeakerList', response.data)
      }
    })
    // 获取创建人信息
    this.api.getProjectIdAndRoleListDicList({"roleIds": null}).then( response => {
      if(response.status === 200) {
        this.$store.dispatch('SetCreatebyList', response.data)
      }
    })
  },
  methods: {
    init(val) {
      let params = {
        projectId: val.projectId,
        roleId: val.roleId || val.getRoleLoginResponses && val.getRoleLoginResponses[0] && val.getRoleLoginResponses[0].roleId
      }
      this.api.getMedsciPlatformMenuList(params).then(response => {
        if (response.status === 200 && response.data) {this.$store.dispatch('GetMenu', response.data.getMedsciRoleAndMenuResponses || [])
          let menuPermission = response.data.getMedsciMenusInMenuRouteResponses || []
          let arr = []
          let roleMenu = {}
          menuPermission.forEach(v => {
            if (v.menuRoute) {
              arr.push(`/${v.menuRoute}`)
              roleMenu[`/${v.menuRoute}`] = v.id 
            }
          });
          this.$store.dispatch('SetPermissionMenuId', roleMenu)
          this.$store.dispatch('SetPermissionMenu', arr)
        } else {
          this.$store.dispatch('GetMenu', [])
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
      }).catch(() => { this.$store.dispatch('GetMenu', []) })
    },
    drop_project(val) {
      this.$store.dispatch('TabProject', val).then(response => {
        this.init(val)
        this.$store.dispatch('clearPermissionBtn')
        this.$router.push(response);
      })
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
  .project-drop {
    margin: 0 12px;
  }
  .project-name {
    font-size: 14px;
    cursor: pointer;
  }
  .project-icon {
    width: 14px;
    margin-left: 4px;
    position: relative;
    top: -1px;
  }
</style>
