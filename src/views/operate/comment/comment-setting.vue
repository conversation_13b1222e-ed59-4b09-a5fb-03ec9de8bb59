<template>
	<ms-table
		:currentPage="searchParams.pageIndex"
		:loading="loading"
		:operationButtons="domConfig.tableButtons"
		:pageSize="searchParams.pageSize"
		:tableData="list"
		:tableHeader="domConfig.tableHeader"
    :scopeConfig="scopeConfig.show"
		:total="total"
		@current-change="current_change"
		@operation-change="operation_change"
		@size-change="size_change"
    @header-operation="header_operation"
    @handleSelectionChange="handleSelectionChange"
    class="table-svg"
	>
    <!-- 列表搜索去区域插槽 -->
		<template slot="ms-table-header">
			<div class="slot-search"></div>
      <div class="slot-button">
        <template v-for="(item, index) in domConfig.soltButtons">
          <el-button :key="index" v-show="!item.roleDisabled" :type="item.type" :icon="item.icon" @click="operation_change({operation: item})" plain>{{ item.label }}</el-button>
        </template>
      </div>
      <ms-right-dialog :visible.sync="dialog" :width="dialogWidth" :title="dialogTitle">
        <component
          :is="dialogComponent"
          :model="scopeInfo"
          :operation="dialogOperation"
          @close="dialog = !dialog"
          @up-date="init"
          v-if="dialog"
        ></component>
      </ms-right-dialog>
		</template>
	</ms-table>
</template>

<script>
import tableMixins  from "../../common/mixins/table"
import domConfig from "./data/dom-config"
import scopeConfig from "./data/scope-config"
import commentOperate from './tool/comment-operate'
export default {
  name: "comment-setting",
  mixins: [tableMixins],
  data () {
    return {
      domConfig: domConfig,
      scopeConfig: scopeConfig
    }
  },
  components: {
    commentOperate
  },
  methods: {
    apiInit (params) {
      let searchParams = {...params}
      this.api.settingList(searchParams).then(response => {
        this.loading = false
        this.total = response.totalSize || 0;
        this.list = response.data.map( v => {
          return {
            ...v
          }
        })
        if (response.status !== 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
      }).catch(() => this.loading = false)
    },
    operation_change_module (val) {
      if (val.operation.way === 'update') {
          let params = {
              userId: this.$store.getters.info.userId,
              username: this.$store.getters.info.userName,
              id: val.model.id,
              openImage: val.model.openImage
          }
          this.api.updateSetting(params).then(response => {
              if(response.status === 200) {
                  this.init()
              } else {
                  this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
              }
          }).catch()
      }
    },
  }
};
</script>
