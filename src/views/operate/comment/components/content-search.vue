<template>
    <div>
        <el-select size="mini"
                v-model="result" 
                clearable 
                filterable 
                remote
                :remote-method="getOptionData"
                ref="searchRef"
                :loading="loading"
                @change="change">
            <el-option v-for="(item,index) in optionSearch"
                    :key="index"
                    :label="item.name"
                    :value="item.id">
            </el-option>
        </el-select>
        <el-table :data="tableData" style="width: 100%">
            <el-table-column prop="objectTitle" label="已选择："></el-table-column>
            <el-table-column align="center" width="60px" fixed="right">
                <template slot-scope="scope">
                <el-tooltip effect="dark" content="删除" placement="bottom">
                    <span @click="deleteRow(scope.row, scope.$index)">
                        <svg-icon class-name="drag-handler" icon-class="icon-shanchu" />
                    </span>
                </el-tooltip>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            :page-size="30"
            layout="total, prev, pager, next"
            @current-change="currentChange"
            :total="total">
        </el-pagination>
    </div>
</template>

<script>
  export default {
    name: "content-search",
    props: [
      "type",
      "id"
    ],
    data() {
      return {
        loading: false,
        result: null,
        optionSearch: [],
        searchName: '',
        pageSize: 10,
        pageIndex: 1,
        total: 0,
        tableData: []
      }
    },
    mounted() {
      this.init()
      this.getList()
    },
    methods: {
        async init () {
            if (this.type === 'topic') {
                const res = await this.api.getTopicKeywordsPage({pageSize: 20,pageIndex: 1, topicName: this.searchName})
                this.optionSearch = res.data.map(v => {return {id: v.id,name: v.topicName}})
            }   
        },
        async getList () {
            const res = await this.api.settingBlacklist({pageSize: 20,pageIndex: 1,settingId: this.id})
            this.tableData = res.data || []
        },
        getOptionData (val) {
            this.searchName = val;
            this.init()
        },
        deleteRow (row,index) {
            this.api.deleteBlacklist({settingId: this.id,objectIds: [row.id]}).then(res => {
                if (res.status === 200) {
                    this.tableData.splice(index, 1)
                }
            })
        },
        change (val) {
            this.$nextTick(() => {
                let params = {
                    "settingId": this.id,
                    "objectId": val,
                    "objectTitle": this.$refs.searchRef.selectedLabel,
                    "userId": this.$store.getters.info.userId,
                    "username": this.$store.getters.info.userName
                }
                this.api.saveBlacklist(params).then(res => {
                    if (res.status === 200) {
                        this.getList()
                    }
                })
            })
        }
    }
  }
</script>

<style scoped>
.drag-handler {
  cursor: pointer;
  color: rgba(31, 38, 62, 0.31);
}
</style>
