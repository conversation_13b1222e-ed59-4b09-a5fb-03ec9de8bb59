<template>
  <ms-right-content>
    <el-form
        ref="submitRef"
        class="rule-form comment-form"
        label-width="100px"
        v-loading="getLoading"
      >
        <el-row>
          <el-col :span="24">
            <el-form-item label="限已认证用户">
              <el-switch
                v-model="submitData.imageNeedAuth"
                :active-value="1"
                :inactive-value="0">
              </el-switch>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="限积分数量">
              <el-input-number v-model="submitData.imageNeedIntegralBase" style="width:200px"></el-input-number>
            </el-form-item>
          </el-col>
        </el-row>
        <!-- <el-row>
          <el-col :span="24">
            <el-form-item label="不允许的分类维度">
              <ms-category-cascader :model.sync="submitData.categoryList" :multiple="true" ></ms-category-cascader>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="不允许的具体内容">
              <content-search :type="submitData.objectType" :id="submitData.id" v-if="!getLoading"></content-search>
            </el-form-item>
          </el-col>
        </el-row> -->
    </el-form>
    <template slot="footer">
      <el-button @click="submitForm"
                :loading="loading"
                size="mini"
                type="primary">确 定</el-button>
      <el-button @click="$emit('close')"
                size="mini">取 消</el-button>
    </template>
  </ms-right-content>
</template>

<script>
import MsRightContent from "@/components/MsDialog/ms-right-content";
// import contentSearch from "../components/content-search"
export default {
  name: "comment-operate",
  data() {
    return {
      getLoading: false,
      submitData: {},
      loading: false
    };
  },
  props: {
    model: Object,
    operation: String
  },
  components: {
    MsRightContent,
    // contentSearch
  },
  created() {
    this.getDetail();
  },
  methods: {
    getDetail() {
      this.getLoading = true;
      this.api.settingDetail({id: this.model.id}).then(response => {
        this.getLoading = false;
        if (response.status === 200 && response.data) {
          this.submitData = {...response.data}
        }
      })
    },
    submitForm () {
      this.loading = true
      let params = {
        userId: this.$store.getters.info.userId,
        username: this.$store.getters.info.userName,
        ...this.submitData
      }
      this.api.updateSetting(params).then(response => {
        this.loading = false
        if(response.status === 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
          this.$emit('up-date')
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
      }).catch(()=> { this.loading = false })
    }
  }
};
</script>

<style>
.comment-form .el-form-item__label {
  color: #8b949d;
}
.comment-form .comment-info {
  color: #606266;
}
</style>
