<template>
    <ms-right-content>
        <el-form :model="moduleForm" ref="submitRef" label-width="100px">
            <template v-for="(item, index) in moduleForm.list">
                <el-card class="box-module-card" :key="index" shadow="never">
                    <div slot="header" class="clearfix">
                        <span>运营位 {{index+1}}</span>
                        <el-button style="float: right; padding: 3px 0" type="text" @click="deleteOpera(index)">删除运营位</el-button>
                    </div>
                    <el-row :gutter="20">
                        <el-col :span="8">
                            <el-form-item
                                label="直播ID"
                                :prop="`list.${index}.liveId`" 
                                :rules="[
                                    {
                                        required: true,
                                        message: '请选择直播ID',
                                        trigger: 'blur',
                                    },
                                ]"
                            >
                                <el-select size="mini"
                                        v-model="item.liveId" 
                                        clearable 
                                        filterable 
                                        remote
                                        style="width:100%;"
                                        :remote-method="filterMethod"
                                        :loading="liveLoading"
                                        @change="(val) => {liveChange(val, item)}">
                                    <el-option v-for="(t_item,t_index) in optionSearch"
                                            :label="t_item.id"
                                            :key="t_index"
                                            :value="t_item.id">
                                        <span v-text="`${t_item.id} ---- ${t_item.name}`"></span>
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item
                                label="直播名称"
                                :prop="`list.${index}.liveName`"
                                :rules="[
                                    {
                                        required: true,
                                        message: '请输入标题',
                                        trigger: 'blur',
                                    }
                                ]"
                            >
                                <el-input
                                    placeholder="字数≤15个汉字"
                                    v-model="item.liveName"
                                />
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="上线时间">
                                <ms-picker :model.sync="item.time" type="datetimerange" width="100%" :defaultTime="['00:00:00', '23:59:59']"></ms-picker>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    
                </el-card>
            </template>
        </el-form>
        <template slot="footer">
            <el-button @click="addOpera"
                        :loading="loading"
                        size="mini"
                        type="primary"
                        :disabled="moduleForm.list.length >= 3">添加运营位</el-button>
            <el-button @click="submitForm"
                        :loading="loading"
                        size="mini"
                        type="primary">确 定</el-button>
            <el-button @click="$emit('close')"
                        size="mini">取 消</el-button>
        </template>
    </ms-right-content>
</template>

<script>
import { mapGetters } from "vuex";
import MsRightContent from "@/components/MsDialog/ms-right-content";
// import {ms_rule_url} from "@/utils/form-rule.js";
export default {
	name: "operate-activity",
	data () {
		return {
            loading: false,
            getLoading: false,
            liveLoading: false,
            optionSearch: [],
            // ms_rule_url: ms_rule_url,
            moduleForm: {
                list: []
            }
		}
	},
	props: {
		model: Object,
		operation: String
    },
    computed: {
        ...mapGetters(["info"])
    },
    components: {
        MsRightContent
    },
    created() {
        this.init()
    },
	methods: {
        init() {
            this.getLoading = true
            this.api.getMedsciOperationLiveList({}).then( response => {
                if (response.status === 200) {
                    this.moduleForm.list = response.data.map( v => {
                        return {
                            ...v,
                            time: v.upTime && v.downTime ? [v.upTime, v.downTime] : null
                        }
                    })
                } else {
                    this.moduleForm.list = [{liveName: ''},{liveName: ''},{liveName: ''}]
                }
                this.getLoading = false
            }).catch(() => this.getLoading = false)
        },
        
        submitForm() {
            this.$refs["submitRef"].validate( valid => {
                if (valid) {
                    this.loading = true
                    let list = this.moduleForm.list.map( v => {
                        return {
                            ...v,
                            upTime: v.time && v.time.length > 0 ? v.time[0] : '',
                            downTime: v.time && v.time.length > 0 ? v.time[1] : '',
                        }
                    })
                    let params = {
                        userId: this.info.userId,
                        username: this.info.userName,
                        operationLiveRequests: list
                    }

                    this.api.updateMedsciOperationLive(params).then(response => {
                        this.loading = false
                        if(response.status === 200) {
                            this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
                            this.$emit('up-date')
                        } else {
                            this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
                        }
                    }).catch(()=> { this.loading = false })
                    
                }
            })
        },

        filterMethod (val) {
            if (val) {
                this.liveLoading = true;
                // 接口获取数据
                let params = {
                    searchTitle : val
                }
                this.api.medsciLiveList(params).then(response => {
                    this.liveLoading = false
                    if (response.status === 200) {
                        this.optionSearch = response.data
                    }
                })
            }
        },
        liveChange(val,item) {
            this.optionSearch.some(v => {
                if (v.id === val) {
                    item.liveName = v.name
                    return 
                }
            })
        },
        addOpera() {
            this.moduleForm.list.push({liveName: ''})
        },
        deleteOpera(index) {
            this.moduleForm.list.splice(index, 1)
        }
	}
}
</script>

<style lang="scss">
.box-module-card {
    margin-bottom: 10px;
    .el-card__header {
        padding: 10px 14px;
        font-size: 16px;
    }
    .el-card__body {
        padding: 16px 20px 0;
    }
    .el-form-item__label {
        font-weight: inherit;
    }
}
</style>
