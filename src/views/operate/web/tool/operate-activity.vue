<template>
    <ms-right-content>
      <el-collapse v-model="activeNames" accordion>
          <el-collapse-item title="通栏A" name="1">
            <el-form :model="moduleForm" ref="submitRef" label-width="100px">
                <template v-for="(item, index) in moduleForm.listA">
                    <el-card class="box-module-card" :key="index" shadow="never">
                        <div slot="header" class="clearfix">
                            <span>运营位 {{index+1}}</span>
                        </div>
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <el-form-item
                                    label="运营位名称"
                                    :prop="`listA.${index}.name`" 
                                    :rules="[
                                        {
                                            required: true,
                                            message: '请输入运营位名称',
                                            trigger: 'blur',
                                        },
                                        {
                                            max: 8,
                                            message: '字数≤8个',
                                            trigger: 'blur'
                                        }
                                    ]"
                                >
                                    <el-input
                                        placeholder="字数≤8个"
                                        v-model="item.name"
                                    />
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item
                                    label="跳转链接"
                                    :prop="`listA.${index}.url`" 
                                    :rules="[
                                        {
                                            required: true,
                                            message: '请输入跳转链接',
                                            trigger: 'blur',
                                        }
                                    ]"
                                >
                                    <el-input
                                        placeholder="请输入有效URL"
                                        v-model="item.url"
                                    />
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="上线时间">
                                    <ms-picker :model.sync="item.time" type="datetimerange" width="100%" :defaultTime="['00:00:00', '23:59:59']"></ms-picker>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        
                    </el-card>
                </template>
            </el-form>
          </el-collapse-item>  
          <el-collapse-item title="通栏B" name="2">
            <el-form :model="moduleForm" ref="submitRef" label-width="100px">
                <template v-for="(item, index) in moduleForm.listB">
                    <el-card class="box-module-card" :key="index" shadow="never">
                        <div slot="header" class="clearfix">
                            <span>运营位 {{index+1}}</span>
                        </div>
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <el-form-item
                                    label="运营位名称"
                                    :prop="`listB.${index}.name`" 
                                    :rules="[
                                        {
                                            required: true,
                                            message: '请输入运营位名称',
                                            trigger: 'blur',
                                        },
                                        {
                                            max: 8,
                                            message: '字数≤8个',
                                            trigger: 'blur'
                                        }
                                    ]"
                                >
                                    <el-input
                                        placeholder="字数≤8个"
                                        v-model="item.name"
                                    />
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item
                                    label="跳转链接"
                                    :prop="`listB.${index}.url`" 
                                    :rules="[
                                        {
                                            required: true,
                                            message: '请输入跳转链接',
                                            trigger: 'blur',
                                        }
                                    ]"
                                >
                                    <el-input
                                        placeholder="请输入有效URL"
                                        v-model="item.url"
                                    />
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="上线时间">
                                    <ms-picker :model.sync="item.time" type="datetimerange" width="100%" :defaultTime="['00:00:00', '23:59:59']"></ms-picker>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        
                    </el-card>
                </template>
            </el-form>
          </el-collapse-item>
          <el-collapse-item title="通栏C" name="3">
            <el-form :model="moduleForm" ref="submitRef" label-width="100px">
                <template v-for="(item, index) in moduleForm.listC">
                    <el-card class="box-module-card" :key="index" shadow="never">
                        <div slot="header" class="clearfix">
                            <span>运营位 {{index+1}}</span>
                        </div>
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <el-form-item
                                    label="运营位名称"
                                    :prop="`listC.${index}.name`" 
                                    :rules="[
                                        {
                                            required: true,
                                            message: '请输入运营位名称',
                                            trigger: 'blur',
                                        },
                                        {
                                            max: 8,
                                            message: '字数≤8个',
                                            trigger: 'blur'
                                        }
                                    ]"
                                >
                                    <el-input
                                        placeholder="字数≤8个"
                                        v-model="item.name"
                                    />
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item
                                    label="跳转链接"
                                    :prop="`listC.${index}.url`" 
                                    :rules="[
                                        {
                                            required: true,
                                            message: '请输入跳转链接',
                                            trigger: 'blur',
                                        }
                                    ]"
                                >
                                    <el-input
                                        placeholder="请输入有效URL"
                                        v-model="item.url"
                                    />
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="上线时间">
                                    <ms-picker :model.sync="item.time" type="datetimerange" width="100%" :defaultTime="['00:00:00', '23:59:59']"></ms-picker>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        
                    </el-card>
                </template>
            </el-form>
          </el-collapse-item>  
      </el-collapse>


        <template slot="footer">
            <el-button @click="submitForm"
                        :loading="loading"
                        size="mini"
                        type="primary">确 定</el-button>
            <el-button @click="$emit('close')"
                        size="mini">取 消</el-button>
        </template>
    </ms-right-content>
</template>

<script>
import { mapGetters } from "vuex";
import MsRightContent from "@/components/MsDialog/ms-right-content";
// import {ms_rule_url} from "@/utils/form-rule.js";
export default {
	name: "operate-activity",
	data () {
		return {
            loading: false,
            getLoading: false,
            moduleForm: {
                listA: [],
                listB: [],
                listC: []
            },
            activeNames: ''
		}
	},
	props: {
		model: Object,
		operation: String
    },
    computed: {
        ...mapGetters(["info"])
    },
    components: {
        MsRightContent
    },
    created() {
        this.init(1)
        this.init(2)
        this.init(3)
    },
	methods: {
        init(type) {
            this.getLoading = true
            this.api.aConfigList({type:type}).then( response => {
                if (response.status === 200) {
                    if(type == 1) {
                      this.moduleForm.listA = response.data.map( v => {
                        return {
                            ...v,
                            time: v.upTime && v.downTime ? [v.upTime, v.downTime] : null
                        }
                      })
                    }
                    if(type == 2) {
                      this.moduleForm.listB = response.data.map( v => {
                        return {
                            ...v,
                            time: v.upTime && v.downTime ? [v.upTime, v.downTime] : null
                        }
                      })
                    }
                    if(type == 3) {
                      this.moduleForm.listC = response.data.map( v => {
                        return {
                            ...v,
                            time: v.upTime && v.downTime ? [v.upTime, v.downTime] : null
                        }
                      })
                    }
                }
                this.getLoading = false
            }).catch(() => this.getLoading = false)
        },
        submitForm() {
            this.$refs["submitRef"].validate( valid => {
                if (valid) {
                    this.loading = true
                    let listA = this.moduleForm.listA.map( v => {
                        return {
                            ...v,
                            upTime: v.time && v.time.length > 0 ? v.time[0] : '',
                            downTime: v.time && v.time.length > 0 ? v.time[1] : '',
                        }
                    })
                    let listB = this.moduleForm.listB.map( v => {
                        return {
                            ...v,
                            upTime: v.time && v.time.length > 0 ? v.time[0] : '',
                            downTime: v.time && v.time.length > 0 ? v.time[1] : '',
                        }
                    })
                    let listC = this.moduleForm.listC.map( v => {
                        return {
                            ...v,
                            upTime: v.time && v.time.length > 0 ? v.time[0] : '',
                            downTime: v.time && v.time.length > 0 ? v.time[1] : '',
                        }
                    })
                    let params = {
                        userId: this.info.userId,
                        username: this.info.userName,
                        columnA: listA,
                        columnB: listB,
                        columnC: listC
                    }
                    this.api.aUpdateConfig(params).then(response => {
                        this.loading = false
                        if(response.status === 200) {
                            this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
                            this.$emit('up-date')
                        } else {
                            this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
                        }
                    }).catch(()=> { this.loading = false })
                }
            })
        
        }
	}
}
</script>

<style lang="scss">
.box-module-card {
    margin-bottom: 10px;
    .el-card__header {
        padding: 10px 14px;
        font-size: 16px;
    }
    .el-card__body {
        padding: 16px 20px 0;
    }
    .el-form-item__label {
        font-weight: inherit;
    }
}
</style>
