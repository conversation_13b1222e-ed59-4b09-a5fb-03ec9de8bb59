<template>
    <ms-right-content>
        <el-form :model="moduleForm" ref="submitRef" label-width="90px">
            <template v-for="(item, index) in moduleForm.list">

                    <el-row :key="index" class="box-module-card ms-card">
                        <div class="card-number">{{index+1}}</div>
                        <el-col :span="8">
                            <el-form-item
                                label="话题ID"
                                :prop="`list.${index}.topicId`" 
                                :rules="[
                                    {
                                        required: true,
                                        message: '请选择话题ID',
                                        trigger: 'blur',
                                    },
                                ]"
                            >
                                <el-select size="mini"
                                        v-model="item.topicId" 
                                        clearable 
                                        filterable 
                                        remote
                                        style="width:100%;"
                                        :remote-method="filterMethod"
                                        :loading="topicLoading"
                                        @change="(val) => {topicChange(val, item)}">
                                    <el-option v-for="(t_item,t_index) in optionSearch"
                                            :label="t_item.topicId"
                                            :key="t_index"
                                            :value="t_item.topicId">
                                        <span v-text="`${t_item.topicId} ---- ${t_item.topicName}`"></span>
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item
                                label="标题"
                                :prop="`list.${index}.topicName`" 
                                :rules="[
                                    {
                                        required: true,
                                        message: '请输入标题',
                                        trigger: 'blur',
                                    }
                                ]"
                            >
                                <el-input
                                    placeholder="字数≤15个汉字"
                                    v-model="item.topicName"
                                />
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="标签：">
                                <el-radio-group v-model="item.tag">
                                    <el-radio :label="1">沸</el-radio>
                                    <el-radio :label="2">荐</el-radio>
                                    <el-radio :label="3">热</el-radio>

                                    <el-button type="text" v-if="item.tag" @click="item.tag = 0">取消标签</el-button>
                                </el-radio-group>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    
            </template>
        </el-form>
        <template slot="footer">
            <el-button @click="submitForm"
                        :loading="loading"
                        size="mini"
                        type="primary">确 定</el-button>
            <el-button @click="$emit('close')"
                        size="mini">取 消</el-button>
        </template>
    </ms-right-content>
</template>

<script>
import { mapGetters } from "vuex";
import MsRightContent from "@/components/MsDialog/ms-right-content";
export default {
	name: "operate-topic",
	data () {
		return {
            loading: false,
            getLoading: false,
            moduleForm: {
                list: [{},{},{},{}]
            },

            topicLoading: false,
            optionSearch: []
		}
	},
	props: {
		model: Object,
		operation: String
    },
    computed: {
        ...mapGetters(["info"])
    },
    components: {
        MsRightContent
    },
    created() {
        this.init()
    },
	methods: {
        init() {
            this.getLoading = true
            this.api.getHotTopicList({}).then( response => {
                if (response.status === 200) {
                    this.moduleForm.list = response.data
                }
                this.getLoading = false
            }).catch(() => this.getLoading = false)
        },
        
        submitForm() {
            this.$refs["submitRef"].validate( valid => {
                if (valid) {
                    this.loading = true
                    let params = {
                        userId: this.info.userId,
                        username: this.info.userName,
                        saveList: this.moduleForm.list
                    }
                    this.api.saveHotTopic(params).then(response => {
                        this.loading = false
                        if(response.status === 200) {
                            this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
                            this.$emit('up-date')
                        } else {
                            this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
                        }
                    }).catch(()=> { this.loading = false })
                    
                }
            })
        
        },
    
        filterMethod (val) {
            if (val) {
                this.topicLoading = true;
                // 接口获取数据
                let params = {
                    searchContent : val
                }
                this.api.searchHotTopic(params).then(response => {
                    this.topicLoading = false
                    if (response.status === 200) {
                        this.optionSearch = response.data
                    }
                })
            }
        },
        topicChange(val,item) {
            this.optionSearch.some(v => {
                if (v.topicId === val) {
                    item.topicName = v.topicName
                    return 
                }
            })
        }
    }
}
</script>

<style lang="scss">
.box-module-card {
    margin-bottom: 10px;
    .el-card__header {
        padding: 10px 14px;
        font-size: 16px;
    }
    .el-card__body {
        padding: 16px 20px 0;
    }
    .el-form-item__label {
        font-weight: inherit;
    }
}
</style>
<style lang="scss" scoped>
    .ms-card {
        position: relative;
        .card-number {
            position: absolute;
            width: 20px;
            height: 20px;
            border: 1px solid #b2b2b2;
            color: #b2b2b2;
            border-radius: 999px;
            top: 5px;
            left: 0px;
            font-size: 12px;
            font-weight: bold;
            text-align: center;
            line-height: 21px;
        }
    }
</style>
