<template>
    <ms-right-content>
        <div class="brand-manage">
            <div class="brand-header">
                <h3>品牌栏目管理</h3>
                <el-button 
                    type="primary" 
                    size="small" 
                    icon="el-icon-plus" 
                    @click="addBrandItem"
                    class="add-btn"
                >
                    添加品牌项
                </el-button>
            </div>
            
            <el-form :model="brandForm" ref="brandFormRef" label-width="100px" v-loading="getLoading">
                <template v-for="(item, index) in brandForm.brandList">
                    <el-card class="brand-card" :key="index" shadow="never">
                        <div slot="header" class="card-header">
                            <span>品牌项 {{ index + 1 }}</span>
                            <el-button 
                                v-if="brandForm.brandList.length > 1"
                                type="text" 
                                size="small" 
                                icon="el-icon-delete" 
                                @click="removeBrandItem(index)"
                                class="delete-btn"
                            >
                                删除
                            </el-button>
                        </div>
                        
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <el-form-item
                                    label="标题"
                                    :prop="`brandList.${index}.title`"
                                    :rules="[
                                        {
                                            required: true,
                                            message: '请输入标题',
                                            trigger: 'blur',
                                        },
                                        {
                                            max: 20,
                                            message: '标题长度不能超过20个字符',
                                            trigger: 'blur'
                                        }
                                    ]"
                                >
                                    <el-input
                                        v-model="item.title"
                                        placeholder="请输入标题（最多20个字符）"
                                        clearable
                                    />
                                </el-form-item>
                            </el-col>
                            
                            <el-col :span="12">
                                <el-form-item
                                    label="链接"
                                    :prop="`brandList.${index}.url`"
                                    :rules="[
                                        {
                                            required: true,
                                            message: '请输入链接',
                                            trigger: 'blur',
                                        },
                                        {
                                            type: 'url',
                                            message: '请输入有效的URL地址',
                                            trigger: 'blur'
                                        }
                                    ]"
                                >
                                    <el-input
                                        v-model="item.url"
                                        placeholder="请输入有效的URL地址"
                                        clearable
                                    />
                                </el-form-item>
                            </el-col>
                        </el-row>
                        
                        <el-row>
                            <el-col :span="12">
                                <el-form-item
                                    label="封面图片"
                                    :prop="`brandList.${index}.cover`"
                                    :rules="[
                                        {
                                            required: true,
                                            message: '请上传封面图片',
                                            trigger: 'change',
                                        }
                                    ]"
                                >
                                    <ms-single-image
                                        v-model="item.cover"
                                        :upFileSize="2"
                                    />
                                    <div class="upload-tip">建议尺寸：400x300px，支持jpg/png格式，大小不超过2MB</div>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-card>
                </template>
            </el-form>
        </div>

        <template slot="footer">
            <el-button 
                @click="submitForm"
                :loading="loading"
                size="mini"
                type="primary"
            >
                确 定
            </el-button>
            <el-button 
                @click="$emit('close')"
                size="mini"
            >
                取 消
            </el-button>
        </template>
    </ms-right-content>
</template>

<script>
import { mapGetters } from "vuex";
import MsRightContent from "@/components/MsDialog/ms-right-content";
import MsSingleImage from "@/components/UpFile/ms-single-image";

export default {
    name: "operate-brand",
    data() {
        return {
            loading: false,
            getLoading: false,
            brandForm: {
                brandList: []
            }
        }
    },
    props: {
        model: Object,
        operation: String
    },
    computed: {
        ...mapGetters(["info"])
    },
    components: {
        MsRightContent,
        MsSingleImage
    },
    created() {
        this.initBrandData();
    },
    methods: {
        // 初始化品牌数据
        initBrandData() {
            this.getLoading = true;
            this.api.getBrandBuildingList({}).then(response => {
                if (response.status === 200 && response.data && response.data.length > 0) {
                    // 如果有数据，使用返回的数据
                    this.brandForm.brandList = response.data.map(item => ({
                        id: item.id,
                        title: item.title || '',
                        url: item.url || '',
                        cover: item.cover || ''
                    }));
                } else {
                    // 如果没有数据，初始化一个空的数据框
                    this.brandForm.brandList = [
                        {
                            title: '',
                            url: '',
                            cover: ''
                        }
                    ];
                }
                this.getLoading = false;
            }).catch(() => {
                // 接口调用失败，使用默认数据
                this.brandForm.brandList = [
                    {
                        title: '',
                        url: '',
                        cover: ''
                    }
                ];
                this.getLoading = false;
                this.PUBLIC_Methods.apiNotify('获取品牌数据失败，请刷新重试', 'warning');
            });
        },
        
        // 添加品牌项
        addBrandItem() {
            this.brandForm.brandList.push({
                title: '',
                url: '',
                cover: ''
            });
        },
        
        // 删除品牌项
        removeBrandItem(index) {
            if (this.brandForm.brandList.length > 1) {
                this.$confirm('确定要删除这个品牌项吗？', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.brandForm.brandList.splice(index, 1);
                    this.$message.success('删除成功');
                }).catch(() => {
                    // 用户取消删除
                });
            }
        },

        
        // 提交表单
        submitForm() {
            this.$refs.brandFormRef.validate(valid => {
                if (valid) {
                    this.loading = true;

                    // 准备提交数据 - 根据是否有id判断是新增还是更新
                    const submitData = this.brandForm.brandList.map(item => {
                        const data = {
                            title: item.title,
                            url: item.url,
                            cover: item.cover
                        };

                        // 如果有id，说明是更新操作，需要传id
                        if (item.id) {
                            data.id = item.id;
                        }

                        return data;
                    });

                    // 调用保存/更新接口
                    this.api.saveBrandBuilding(submitData).then(response => {
                        this.loading = false;
                        if (response.status === 200) {
                            this.PUBLIC_Methods.apiNotify(response.message || '品牌栏目保存成功', 'success');
                            this.$emit('up-date');
                        } else {
                            this.PUBLIC_Methods.apiNotify(response.message || '保存失败', 'warning');
                        }
                    }).catch(() => {
                        this.loading = false;
                        this.PUBLIC_Methods.apiNotify('网络错误，请重试', 'error');
                    });
                } else {
                    this.PUBLIC_Methods.apiNotify('请完善必填信息', 'warning');
                }
            });
        }
    }
}
</script>

<style scoped>
.brand-manage {
    .brand-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        
        h3 {
            margin: 0;
            color: #303133;
            font-size: 18px;
            font-weight: 500;
        }
        
        .add-btn {
            background: #409EFF;
            border-color: #409EFF;
        }
    }
    
    .brand-card {
        margin-bottom: 20px;
        
        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            
            .delete-btn {
                color: #F56C6C;
                
                &:hover {
                    color: #F56C6C;
                }
            }
        }
        
        .upload-tip {
            font-size: 12px;
            color: #909399;
            margin-top: 8px;
            line-height: 1.4;
        }
    }
}

/* 全局样式 */
.brand-card >>> .el-card__header {
    padding: 15px 20px;
    background-color: #fafafa;
    border-bottom: 1px solid #ebeef5;
}

.brand-card >>> .el-card__body {
    padding: 20px;
}

.brand-card >>> .el-form-item__label {
    font-weight: 500;
    color: #606266;
}
</style>
