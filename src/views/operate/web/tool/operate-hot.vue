<template>
    <ms-right-content>
        <el-form :model="moduleForm" ref="submitRef" label-width="90px">
            <template v-for="(item, index) in moduleForm.list">

                    <el-row :key="index" class="box-module-card ms-card">
                        <div class="card-number">{{index+1}}</div>
                        <el-col :span="8">
                            <el-form-item
                                label="标题"
                                :prop="`list.${index}.title`" 
                                :rules="[
                                    {
                                        required: true,
                                        message: '请输入运营位名称',
                                        trigger: 'blur',
                                    },
                                ]"
                            >
                                <el-input
                                    placeholder="字数≤17个汉字"
                                    v-model="item.title"
                                />
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item
                                label="跳转链接"
                                :prop="`list.${index}.url`" 
                                :rules="[
                                    {
                                        required: true,
                                        message: '请输入跳转链接',
                                        trigger: 'blur',
                                    }
                                ]"
                            >
                                <el-input
                                    placeholder="请输入有效URL"
                                    v-model="item.url"
                                />
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="标签：">
                                <el-radio-group v-model="item.tag">
                                    <el-radio :label="1">沸</el-radio>
                                    <el-radio :label="2">荐</el-radio>
                                    <el-radio :label="3">新</el-radio>

                                    <el-button type="text" v-if="item.tag" @click="item.tag = 0">取消标签</el-button>
                                </el-radio-group>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    
            </template>
        </el-form>
        <template slot="footer">
            <el-button @click="submitForm"
                        :loading="loading"
                        size="mini"
                        type="primary">确 定</el-button>
            <el-button @click="$emit('close')"
                        size="mini">取 消</el-button>
        </template>
    </ms-right-content>
</template>

<script>
import { mapGetters } from "vuex";
import MsRightContent from "@/components/MsDialog/ms-right-content";
// import {ms_rule_url} from "@/utils/form-rule.js";
export default {
	name: "operate-hot",
	data () {
		return {
            loading: false,
            getLoading: false,
            // ms_rule_url: ms_rule_url,
            moduleForm: {
                list: [{},{},{},{},{},{},{},{},{},{}]
            }
		}
	},
	props: {
		model: Object,
		operation: String
    },
    computed: {
        ...mapGetters(["info"])
    },
    components: {
        MsRightContent
    },
    created() {
        this.init()
    },
	methods: {
        init() {
            this.getLoading = true
            this.api.hConfigList({}).then( response => {
                if (response.status === 200) {
                    this.moduleForm.list = response.data
                }
                this.getLoading = false
            }).catch(() => this.getLoading = false)
        },
        
        submitForm() {
            this.$refs["submitRef"].validate( valid => {
                if (valid) {
                    this.loading = true
                    let params = {
                        userId: this.info.userId,
                        username: this.info.userName,
                        topList: this.moduleForm.list
                    }
                    this.api.hUpdateConfig(params).then(response => {
                        this.loading = false
                        if(response.status === 200) {
                            this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
                            this.$emit('up-date')
                        } else {
                            this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
                        }
                    }).catch(()=> { this.loading = false })
                    
                }
            })
        
        }
	}
}
</script>

<style lang="scss">
.box-module-card {
    margin-bottom: 10px;
    .el-card__header {
        padding: 10px 14px;
        font-size: 16px;
    }
    .el-card__body {
        padding: 16px 20px 0;
    }
    .el-form-item__label {
        font-weight: inherit;
    }
}
</style>
<style lang="scss" scoped>
    .ms-card {
        position: relative;
        .card-number {
            position: absolute;
            width: 20px;
            height: 20px;
            border: 1px solid #b2b2b2;
            color: #b2b2b2;
            border-radius: 999px;
            top: 5px;
            left: 0px;
            font-size: 12px;
            font-weight: bold;
            text-align: center;
            line-height: 21px;
        }
        &:nth-child(1) {
            .card-number {
                color: #F43A49;
                border-color: #F43A49;
            }
        }
        &:nth-child(2) {
            .card-number {
                color: #FF6700;
                border-color: #FF6700;
            }
        }
        &:nth-child(3) {
            .card-number {
                color: #FFAB28;
                border-color: #FFAB28;
            }
        }
    }
</style>
