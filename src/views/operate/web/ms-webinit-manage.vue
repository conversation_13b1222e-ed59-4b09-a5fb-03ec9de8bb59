<template>
    <ms-table
        :operationButtons="domConfig.tableButtons"
        :tableData="list"
        :tableHeader="domConfig.tableHeader"
        @operation-change="operation_change"
        class="table-simple"
    >
      <!-- 列表搜索去区域插槽 -->
      <template slot="ms-table-header">
        <ms-right-dialog :visible.sync="dialog" :width="dialogWidth" :title="dialogTitle">
          <component
            :is="dialogComponent"
            :model="scopeInfo"
            :operation="dialogOperation"
            @close="dialog = !dialog"
            @up-date="init"
            v-if="dialog"
          ></component>
        </ms-right-dialog>
      </template>
    </ms-table>
</template>

<script>
import operateMixin from "./operate-mixin"
import tableMixins  from "../../common/mixins/table"
export default {
  name: "ms-webinit-manage",
  mixins: [tableMixins,operateMixin],
  data () {
    return {
    }
  },
  methods: {
    apiInit () {
      this.list = [
          {id: 1, title: '活动运营位', componentName: 'operate-activity', componentTitle: '运营位列表', dialogWidth: '70%'},
          // {id: 2, title: '首页一周热榜', componentName: 'operate-hot', componentTitle: '热榜列表', dialogWidth: '88%'},
          // {id: 3, title: '首页热门话题', componentName: 'operate-topic', componentTitle: '话题列表', dialogWidth: '88%'},
          // {id: 4, title: '直播通道', componentName: 'operate-live', componentTitle: '运营位列表', dialogWidth: '70%'}
          {id: 2, title: '首页热门话题', componentName: 'operate-topic', componentTitle: '话题列表', dialogWidth: '88%'},
          {id: 3, title: '直播通道', componentName: 'operate-live', componentTitle: '运营位列表', dialogWidth: '70%'},
          {id: 4, title: '品牌栏目', componentName: 'operate-brand', componentTitle: '品牌栏目管理', dialogWidth: '80%'}
      ]
    },
    operation_change_module (val) {
      switch (val.operation.way) {
        case "dynamic_dialog":
          this.dialog = true;
          this.scopeInfo = val.model ? val.model : {};
          this.dialogOperation = val.operation.operation;
          this.dialogComponent = val.model.componentName;
          this.dialogTitle = val.model.componentTitle || val.operation.title;
          this.dialogWidth = val.model.dialogWidth || val.operation.width || '50%';
          break;
        default: break;
      }
    },
  }
};
</script>
