const scopeConfig = {
  show: {
    createdTime: () => {
      return {
        type: 'formatTime',
        cFormat: '{y}-{m}-{d} {h}:{i}'
      }
    },
    status: () => {
      return {
        type: 'status',
        rule: {
          0: { label: '禁用', background: '#A7ADBD' },
          1: { label: '启用', background: '#40A23F' }
        }
      }
    },
    realName: () => {
      return {
        type: 'fidd',
        fields: [
          {
            way: 'page',
            path: 'lecture-show',
            name: 'realName',
            params: [{ keyName: 'userId', valName: 'userId' }, { keyName: 'userName', valName: 'userName' }]
          }
        ]
      }
    }
  },
  headerShow: {
    status: () => {
      return {
        type: 'dropdown',
        icon: 'icon-funnel',
        options: [
          { label: '全部', value: null },
          { label: '禁用', value: 0 },
          { label: '启用', value: 1 }
        ],
        operation: 'query'
      }
    }
  }
}

export default scopeConfig
