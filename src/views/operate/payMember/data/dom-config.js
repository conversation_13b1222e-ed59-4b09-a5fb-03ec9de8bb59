const domConfig = {
  // 搜索配置
  listSearch: [
  ],

  // 表头配置
  tableHeader: [
    { label: '会员ID', property: 'id', width: '80' },
    { label: '会员名称', property: 'cardName', sortable: false, width: '150' },
    { label: '价格', property: 'cardPrice', sortable: false, width: '120' },
    { label: '时长', property: 'validTime', sortable: false, width: '120' },
    { label: '创建时间', property: 'createdTime', sortable: true, width: '150' },
    { label: '创建人', property: 'createdName', sortable: false, width: '150' },
    // { label: '发布时间', property: 'createdTime', sortable: true, width: '150' },
    { label: '状态', property: 'status', sortable: false, width: '120' }
  ],

  // 行内列表按钮配置
  tableButtons: [
    {
      label: '编辑',
      icon: '',
      role: '',
      operation: 'edit',
      component: 'ms-member-add',
      way: 'dialog',
      path: 'lecture-edit',
      params: ['id'],
      type: 'primary',
      title: '编辑',
      position: 'right',
      width: '50%'
    },
    {
      icon: '',
      role: '',
      operation: 'editStatus',
      component: 'msMemberStatus',
      way: 'dialog',
      field: 'status',
      rule: {
        0: { label: '启用', type: 'success' },
        1: { label: '禁用', type: 'info' }
      }
    },
    {
      label: '删除',
      icon: '',
      role: '',
      operation: 'delete',
      component: '',
      way: 'delete',
      type: 'danger',
      title: '删除评论',
      showCallback: (val) => {
        if (val.status == 1) {
          return false
        } else {
          return true
        }
      },
      width: '45%'
    }
  ],

  // 新建项目按钮
  soltButtons: [
    {
      label: '添加会员',
      icon: 'el-icon-plus',
      type: 'primary',
      operation: 'created',
      way: 'dialog',
      component: 'ms-member-add',
      title: '添加会员',
      position: 'right',
      width: '50%'
    }
  ]
}
export default domConfig
