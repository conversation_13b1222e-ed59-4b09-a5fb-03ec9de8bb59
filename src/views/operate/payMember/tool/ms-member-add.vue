<template>
  <div class="lecture" v-loading="even()">
    <el-form
          label-width="100px"
          class="rule-form"
          ref="submitRef"
          :rules="rule"
          :model="submitData"
        >
      <el-row>
        <el-col :span="24">
          <el-form-item
            label="会员名称:"
            prop="cardName"
          >
            <el-input
              v-model.trim="submitData.cardName"
              placeholder="请输入20个字以内的名称"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="天数（天）:" prop="validTime">
            <el-input
              v-model.trim="submitData.validTime"
              type="number"
              placeholder="请输入"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="价格（元）:" clearable prop="cardPrice">
            <el-input
              v-model.trim="submitData.cardPrice"
              placeholder="请输入"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="活动价（元）:">
            <el-input
              v-model.trim="submitData.activityPrice"
              placeholder="请输入"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="活动时间:" clearable prop="activityTime">
            <el-date-picker style="width: 100%;" @change="changeActivityTime"
              v-model="submitData.activityTime"
              format="yyyy-MM-dd HH:mm:ss"
              value-format="yyyy-MM-dd HH:mm:ss"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期">
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="会员详情:" clearable prop="cardImage">
            <!-- <ms-image-upload :imageUrl.sync="submitData.cardImage" :key="submitData.cardImage" @bindData="bindData" :disabled="!!submitData.cardImage"></ms-image-upload> -->
            <ms-single-image v-model="submitData.cardImage"></ms-single-image>
            <p class="hint">支持扩展名：jpg,jpeg,png</p>
            <p class="hint">数量：1张</p>
            <p class="hint">尺寸：940*480</p>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="描述:">
            <el-input
              v-model="submitData.remark"
              type="textarea"
              :rows="4"
              placeholder="请输入100个字以内的说明"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div class="buttons">
      <el-button type="primary" @click="submit">保存</el-button>
      <el-button type="info" @click="close">返回</el-button>
    </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex"
// import msImageUpload from "@/components/UpFile/ms-image-upload"

export default {
  name: "ms-member-add",
  data() {
    var validatePass = (rule, value, callback) => {
      if (this.submitData.activityPrice != '' && this.submitData.activityPrice != null && !this.submitData.activityTime) {
        callback(new Error('请选择'));
      } else {
        callback();
      }
    };
    return {
      show: false,
      submitData: {
        // projectId: "", // 项目id
        // userId: "", // 用户id
        // username: "", // 用户名称
        cardPrice: "", // 会员价格
        activityPrice: "", // 活动价格
        activityTime: null, // 活动时间
        activityStartTime: "", // 活动开始时间
        activityEndTime: "", // 活动结束时间
        cardImage: "", // 上传图片
        cardName: "", // 会员名称
        validTime: "", // 天数
        remark: "", // 描述
      },
      imgUploadTip: '',
      rule: {
        cardName: [
          { required: true, message: "请输入", trigger: "change" },
        ],
        validTime: [
          { required: true, message: "请输入", trigger: "change" },
        ],
        cardPrice: [
          { required: true, message: "请输入", trigger: "change" },
        ],
        activityTime: [
          // { required: true, message: "请选择", trigger: "change" },
          { required: true, validator: validatePass, trigger: 'change' }
        ],
        cardImage: [
          { required: true, message: "请选择", trigger: "change" },
        ],
      },
    }
  },
  components: {
    // msImageUpload
  },
  props: {
    model: Object,
    operation: String,
  },
  computed: {
    ...mapGetters(["info"]),
  },
  created() {
    if (this.operation === "edit") {
      this.show = true
      this.getMemberInfo()
    }
  },
  methods: {
    even() {
      if (this.operation === "edit") {
        return this.show
      } else {
        return false
      }
    },
    getMemberInfo() {
      let params = {
        id: this.model.id
      };
      this.api.memberCardDetail(params).then(response => {
        this.show = false
        if(response.data) {
          this.submitData = {
            id: response.data.id, 
            cardPrice: response.data.cardPrice, 
            activityPrice: response.data.activityPrice,
            activityStartTime: response.data.activityStartTime || '',
            activityEndTime: response.data.activityEndTime || '',
            cardImage: response.data.cardImage,
            cardName: response.data.cardName,
            validTime: response.data.validTime,
            remark: response.data.remark,
            activityTime: null
          }
          if(response.data.activityStartTime) {
            this.submitData.activityTime = [response.data.activityStartTime, response.data.activityEndTime];
          }
        }
        if (response.status !== 200) {
          this.PUBLIC_Methods.apiNotify(
            response.message || "请求出错",
            "warning"
          )
        }
      }).catch(() => (this.show = false))
    },
    changeActivityTime() {
      if(this.submitData.activityTime) {
        this.submitData.activityStartTime = this.submitData.activityTime[0];
        this.submitData.activityEndTime = this.submitData.activityTime[1];
      } else {
        this.submitData.activityStartTime = '';
        this.submitData.activityEndTime = '';
      }
    },
    bindData (params) {
      this.submitData.cardImage = params.url;
    },
    submit() {
      this.$refs["submitRef"].validate(valid => {
        if (valid) {
          let userInfo = this.$store.getters.info || {}
          this.submitData.projectId = userInfo.projectId;
          this.submitData.userId = userInfo.userId;
          this.submitData.username = userInfo.userName;
          this.api[
            this.operation === "edit"
              ? "memberCardModifyInfo"
              : "memberCardSaveInfo"
          ](this.submitData).then(response => {
            if (response.status === 200) {
              this.PUBLIC_Methods.apiNotify(
                response.message || "添加成功",
                "success"
              )
              this.close()
              this.$emit("up-date")
            } else {
              this.PUBLIC_Methods.apiNotify(
                response.message || "添加失败",
                "warning"
              )
            }
          })
        }
      })
    },
    close() {
      this.$emit("close");
      // if (this.operation === "edit") {
      //   this.$emit("close")
      // } else {
      //   window.history.go(-1)
      // }
    },
    get_countryList(val) {
      this.submitData.countryId = val.model.id
      this.submitData.countryName = val.model.name
    },
    get_cityList(val) {
      this.submitData.provinceId = val.model[0].id || 0
      this.submitData.provinceName = val.model[0].name || ''
      this.submitData.cityName = val.model[1] ? val.model[1].name : ''
      this.submitData.cityId = val.model[1] ? val.model[1].id : 0
      this.submitData.districtId = val.model[2] ? val.model[2].id : 0
      this.submitData.districtName = val.model[2] ? val.model[2].name : ''
    },
    get_professional(val) {
      this.submitData.professionalCatid = val.model[0].id
      this.submitData.professionalCatName = val.model[0].name
      this.submitData.professionalCatEname = val.model[0].en
      this.submitData.professionalId = val.model[1] ? val.model[1].id : 0
      this.submitData.professionalName = val.model[1] ? val.model[1].name : ""
      this.submitData.professionalEname = val.model[1] ? val.model[1].en : ""
    },
  },
}
</script>

<style lang="scss" scoped>
.buttons {
  position: fixed;
  bottom: 20px;
  right: 20px;
}
.hint{
  color: rgba(0,0,0,0.4);
}
</style>
