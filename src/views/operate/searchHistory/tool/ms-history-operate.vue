<template>
  <ms-operation-dialog :title="`是否要删除搜索历史内容`">
    <template slot="content">
      <el-tag v-for="(id, index) in arr" 
              :key="index" 
              type="info" 
              style="margin: 0 5px 5px 0">{{id}}</el-tag>
    </template>
    <template slot="footer">
      <el-button @click="submitForm"
                 :loading="loading"
                 size="mini"
                 type="primary">确 定</el-button>
      <el-button @click="$emit('close')"
                 size="mini">取 消</el-button>
    </template>
  </ms-operation-dialog>
</template>

<script>
export default {
	name: "ms-article-operation",
	data () {
		return {
        loading: false,
        userInfo: {},
        arr: [],
		}
	},
	props: [
		"model",
		"operation"
  ],
  created() {
    this.userInfo = this.$store.getters.info || {}
    let arr = []
    if (this.PUBLIC_Methods.isArrayFn(this.model)) {
      this.model.forEach(item => {
        arr.push(item.content)
      });
    } else {
      arr.push(this.model.content)
    }
    this.arr = arr
  },
	methods: {
		submitForm () {
            this.loading = true;
            this.api.deletMedsciSearchHistoryBatch(this.arr).then(response => {
                if(response.status === 200) {
                this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
                } else {
                this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
                }
                this.$emit('up-date')
                this.loading = false
            }).catch( () => {
                this.loading = false;
                this.$emit('close')
            })
		}
	}
}
</script>
