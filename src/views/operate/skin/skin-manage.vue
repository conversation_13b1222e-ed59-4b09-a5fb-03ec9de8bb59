<template>
    <el-row :gutter="20">
        <template v-for="(item, index) in skinArr">
            <el-col :key="index" :span="8">
                <el-card class="box-card" >
                    <div slot="header" class="clearfix">
                        <span style="float: left; font-size: 14px;">{{item.displayName}}</span>
                        <el-button 
                            style="float: right; padding: 3px 0; font-size: 14px" type="text" @click="operaSkin(item)"
                            :disabled="item.value == '0' && submitData.value == '0'"
                        >
                            {{item.value === submitData.value ? '关闭' : '开启'}}
                        </el-button>
                    </div>
                    <el-image
                        style="width: 300px; height: 200px"
                        :src="item.imgUrl"
                        fit="cover"
                        :preview-src-list="[item.imgUrlPreview]"
                    />
                    <div class="choose-skin" v-if="item.value === submitData.value">
                        <i class="el-icon-upload-success el-icon-check"></i>
                    </div>
                </el-card>
            </el-col>
        </template>
    </el-row>
</template>

<script>
export default {
    name: "skin-manage",
    data () {
        return {
            skinArr: [],
            submitData: {
                id: 0,
                value: '0'
            }
        }
    },
    mounted() {
        this.init()
    },
    methods: {
        async init() {
            const res = await  this.api.getValuesByType({"type": "skin_type"})
            const resCh = await  this.api.getValuesByType({"type": "site_skin"})
            if (res.data) {
                this.skinArr = res.data.map(v => {
                    return {
                        ...v,
                        imgUrl: `https://static.medsci.cn/product/medsci-site/portal/img/site_skin_${v.value}.png`,
                        imgUrlPreview: `https://static.medsci.cn/product/medsci-site/portal/img/site_skin_preview${v.value}.png`
                    }
                })
            }
            if (resCh.data) {
                this.submitData.id = resCh.data[0].id;
                this.submitData.value = resCh.data[0].value;
            }
            
        },
        operaSkin(item) {
            let comfirmText = '';
            let params = null;
            if (item.value == this.submitData.value) { // 关闭皮肤
                comfirmText = `是否关闭${item.displayName}`
                params = {
                    id: this.submitData.id,
                    value: '0',
                    userId: this.$store.getters.info.userId,
                    username: this.$store.getters.info.userName
                }
            } else {
                comfirmText = `是否启用${item.displayName}`
                params = {
                    id: this.submitData.id,
                    value: item.value,
                    userId: this.$store.getters.info.userId,
                    username: this.$store.getters.info.userName
                }
            }
            this.$confirm(`${comfirmText}?`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.api.updateValuesByType(params).then( res=> {
                    if (Number(res.status) === 200) {
                        this.init()
                    } else {
                        this.PUBLIC_Methods.apiNotify(res.message || '请求出错', 'warning')
                    }
                })
            }).catch(() => {});
        }
    }
};
</script>

<style lang="scss" scoped>
    .box-card {
        /deep/ .el-card__header {
            padding: 10px 20px;
        }
        /deep/ .el-card__body {
            position: relative;
        }
        .choose-skin {
            position: absolute;
            right: -16px;
            bottom: -25px;
            width: 38px;
            height: 55px;
            background: #13ce66;
            text-align: center;
            transform: rotate(45deg);
            box-shadow: 0 0 1pc 1px rgba(0, 0, 0, 0.2);
            i {
                font-size: 16px;
                margin-top: 17px;
                margin-right: 23px;
                transform: rotate(-45deg);
                color: #fff;
            }
        }
    }
</style>
