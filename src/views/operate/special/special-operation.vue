<template>
  <section class="form-tab" v-loading="getLoading">
    <el-tabs v-model="activeName">
      <el-tab-pane label="内容详情" name="content">
        <ms-special-content ref="content" @changeTab="changeTab" :submitData.sync="submitData"></ms-special-content>
      </el-tab-pane>
      <el-tab-pane label="显示栏目" name="category">
        <ms-info-setting :categoryModel.sync="submitData.categoryList" :categoryConfigChild="{moduleName: 'specialTopic'}" headerShow></ms-info-setting>
      </el-tab-pane>
    </el-tabs>
    <!-- 表单内容 -->
    
    <!-- 提交按钮 -->
    <footer-tool-bar v-loading="buttonLoading">
      <template slot="tool-content">
        <el-button type="primary" @click="info_operation('save')">保存</el-button>
        <el-button type="info" @click="info_operation('back')">返回</el-button>
      </template>
    </footer-tool-bar>
  </section>
</template>

<script>
import FooterToolBar from '@/components/ToolBar/footer-tool-bar'
import msSpecialContent from './tool/ms-special-content.vue'
import msInfoSetting from '@/components/MsCommon/ms-info-setting'
import { mapGetters } from "vuex";
export default {
  name: "special-operation",
	data () {
		return {
      buttonLoading: false,
      getLoading: false,
      dataId: this.$route.query.id ? this.$route.query.id : 0,
      submitData: {
        categoryList:[],
        contentList: [],
        h5Cover: "",
        pcCover: "",
        shareCover: "",
        guideDownload: 0,
        specialTopicTitle: "",
        summary: "",
        tagList: [],
        classifyList: [],
        skipUrl: ''
      },
      activeName: 'content',
		}
  },
  computed: {
    ...mapGetters(["info"]),
  },
  components: {
    FooterToolBar,
    msSpecialContent,
    msInfoSetting,
  },
  created() {
    this.init()
  },
  methods: {
    init() {
      let id = this.dataId
      if(id !== 0) {
        this.getLoading = true;
        this.submitData.id = id;
        this.api.getMedsciSpecialTopicById({id: id}).then( response => {
          this.getLoading = false;
          if(response.status === 200) {
            let res = response.data
            if(!res.classifyList) {
              res.classifyList = []
            }
            this.submitData = {
              ...this.submitData,
              ...res
            }
            this.$nextTick(()=>{
              this.$refs.content.setSort()
            })
          } else {
            this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
          }
        }).catch(() => {
          this.getLoading = false;
        })
      } 
    },
    info_operation(val) {
      switch (val) {
        case 'save': 
          try {
             Promise.all([
              this.$refs['content'].validateData()
            ]).then(() => {
              this.dataId ? this.updateForm() : this.createForm();
            });
          } catch (error) {
            return;
          }
          break;
        case 'back':
          this.$router.back();
          break;
        default: break;
      }
    },
    changeTab(val) {
      this.activeName = val
    },
    createForm() {
      this.buttonLoading = true;
      let params = {
        ...this.submitData,
        userId: this.info.userId,
        username: this.info.userName,
      }
      this.api.saveMedsciSpecialTopic(params).then(response => {
        this.buttonLoading = false
        if(response.status === 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
          this.$router.back()
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
      }).catch(() => this.buttonLoading = false)
    },
    updateForm() {
      this.buttonLoading = true;
      let params = {
        userId: this.info.userId,
        username: this.info.userName,
        ...this.submitData
      }
      this.api.updateMedsciSpecialTopic(params).then(response => {
        this.buttonLoading = false
        if(response.status === 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
          this.$router.back()
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
      }).catch(() => this.buttonLoading = false)
    },
  }
}
</script>
