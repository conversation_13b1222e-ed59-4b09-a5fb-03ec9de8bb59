<template>
  <section>
    <el-form :model="submitData"
      ref="submitRef"
      class="rule-form info-form"
      :rules="rules"
      label-width="100px">
        <el-row :gutter="10"> 
          <el-col :span="18">
            <el-row>
              <el-col :span="24">
                <el-form-item label="专题标题" prop="specialTopicTitle">
                  <el-input v-model="submitData.specialTopicTitle" style="width: 100%" maxlength="64" placeholder="请输入≤64字的标题" show-word-limit></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <el-form-item label="创建专题标签" class="flex-item">
                  <el-input v-model.trim="tagValue" style="width: 100%" maxlength="64" placeholder="请输入" show-word-limit></el-input>
                  <el-button style="flex:1;margin-left:10px" type="primary" :disabled="tagValue === ''" @click="addTag">添加</el-button>
                </el-form-item>
                <div class="tag-wrap">
                  <div class="tag-btn" v-for="(item,index) in submitData.classifyList" :key="'tag'+index">
                    <label>{{item.classifyName}}</label>
                    <i class="el-icon-close" @click="delTag(index)"></i>
                  </div>
                </div>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="16">
                <el-form-item label="选择内容" prop="contentList" class="flex-item">
                  <el-select style="width:100%;margin-right:10px;" v-model="tags" value-key="contentId" :loading="getLoading" filterable remote :remote-method="remoteContent" placeholder="请选择标签" clearable multiple>
                    <el-option
                      v-for="item in submitData.classifyList"
                      :key="item.classifyName"
                      :label="item.classifyName"
                      :value="item.classifyName">
                    </el-option>
                  </el-select>
                  <ms-dictionary-search v-model="keywords" :model.sync="keywords" :modelName.sync='search.contentType' type="special_topic" :clearable="true" style="width: 100%;"></ms-dictionary-search>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label-width="10px" prop="content" class="flex-item">
                  <el-select style="width:100%" v-model="contentData" value-key="contentId" :loading="getLoading" filterable remote :remote-method="remoteContent" placeholder="输入标题关键字搜索" clearable>
                    <el-option
                      v-for="item in contentList"
                      :key="item.contentId"
                      :label="item.contentTitle"
                      :value="item">
                    </el-option>
                  </el-select>
                  <el-button style="flex:1;margin-left:10px" type="primary" :disabled="JSON.stringify(contentData) === '{}'" @click="addContent">添加</el-button>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <el-form-item label="已选择">
                  <el-table ref="dragTable" 
                    v-loading="getLoading" 
                    row-key="additionId" 
                    :data="submitData.contentList" 
                    :header-cell-style="headerCellStyle" 
                    :header-row-style="headerRowStyle" 
                    style="width: 100%;border-left: 1px solid #EBEEF5;border-right: 1px solid #EBEEF5">
                    <el-table-column align="center" label="序号" min-width="50px">
                      <template slot-scope="scope">
                        <span>{{ scope.row.sort }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column label="专题标签" min-width="200px">
                      <template slot-scope="scope">
                        <div class="tag-btn" v-for="(item,index) in scope.row.classifyList" :key="index">
                          <label>{{item.classifyName}}</label>
                          <i class="el-icon-close" @click="delTagV(scope, index)"></i>
                        </div>

                      </template>
                    </el-table-column>
                    <el-table-column label="内容类型" min-width="100px">
                      <template slot-scope="scope">
                        <span>{{ scope.row.contentType }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column align="center" label="内容标题" min-width="300px">
                      <template slot-scope="scope">
                        <span>{{ scope.row.contentTitle }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column align="center" label="操作" min-width="80px">
                      <template slot-scope="scope">
                        <el-tooltip effect="dark" content="删除" placement="bottom">
                          <span @click="deleteRow(scope.$index)">
                            <svg-icon class-name="drag-handler" icon-class="icon-shanchu" />
                          </span>
                        </el-tooltip>
                      </template>
                    </el-table-column>
                  </el-table>
                  <div class="tips">说明：上下拖动记录，调整展示顺序</div>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
               <el-col :span="24">
                <el-form-item label="引导下载" prop="guideDownload">
                  <el-switch v-model="submitData.guideDownload" :active-value="1" :inactive-value="0"></el-switch>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <el-form-item label="PC封面图片（1920*303）" prop="pcCover">
                  <ms-single-image v-model="submitData.pcCover"></ms-single-image>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="H5封面图片（750*370）" prop="h5Cover">
                  <ms-single-image v-model="submitData.h5Cover"></ms-single-image>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="分享图片（100*100）" prop="shareCover">
                  <ms-single-image v-model="submitData.shareCover"></ms-single-image>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <el-form-item label="跳转链接" prop="skipUrl">
                  <el-input v-model.trim="submitData.skipUrl" style="width: 100%" placeholder="请输入(链接需以http或https开头)"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                  <el-form-item label="摘要" prop="summary">
                  <el-input v-model="submitData.summary" type="textarea" :rows="3" :placeholder="'选填；字数≤20；转发至微信时作为副标题展示'" maxlength="20" show-word-limit></el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </el-col>
        </el-row>
      </el-form>
  </section>
</template>

<script>
import Sortable from 'sortablejs'
import msDictionarySearch from '@/components/MsCommon/ms-dictionary-search'
import { mapGetters } from "vuex"
import {ms_rule_url_http} from "@/utils/form-rule.js";
export default {
	name: "ms-article-content",
	data () {
		return {
      rules: {
        specialTopicTitle: [
          { required: true, message: "标题不能为空且长度不能超过64字", trigger: 'blur' }
        ],
        contentList: [
          { required: true, message: "必须选择内容，请选择后重试", trigger: 'blur' },
        ],
        pcCover: [
          { required: true, message: "请上传PC封面，尺寸：1920*303", trigger: 'blur' }
        ],
        h5Cover: [
          { required: true, message: "请上传H5封面，尺寸：750*303", trigger: 'blur' }
        ],
        skipUrl: [
          { validator: ms_rule_url_http, trigger: 'blur' }
        ]
      },
      pickerConfig: {
        pickerOptions: {
          disabledDate: (time) => {
            return time.getTime() < new Date().getTime() - 8.64e7;
          }
        }
      },
      contentList: [],
      getLoading: false,
      headerCellStyle: {
        "background-color": "#EBEEF5"
      },
      headerRowStyle: {
        "background-color": "#EBEEF5"
      },
      search: {
        contentTitle: "",
        contentType: ""
      },
      contentData: {},
      keywords: null,
      // tagsList: ['资讯', '课程', '课程3课程3课程3', '课程4', '课程5', '课程6', '课程7', '课程8', '课程9', '课程10'],
      tags:[],
      tagValue: '',
		}
  },
  components: {
    msDictionarySearch
  },
  props:["submitData"],
  computed: {
    ...mapGetters(["info"]),
  },
  watch: {
    keywords: {
      handler: function() {
        this.changeType()
      },
      deep: true,
      immediate: true
    }
  },
  mounted() {},
	methods: {
    addTag() {
      if(this.submitData.classifyList.length > 9) {
        this.PUBLIC_Methods.apiNotify("最多可以创建10个标签", "warning")
        return false
      }
      if(this.submitData.classifyList.includes(this.tagValue)) {
        this.PUBLIC_Methods.apiNotify("标签已经存在", "warning")
      } else {
        this.submitData.classifyList.push({
          classifyName: this.tagValue
        });
        this.tagValue = '';
      }
    },
    delTag(index) {
      let value = this.submitData.classifyList[index].classifyName;
      this.submitData.classifyList.splice(index, 1)
      this.submitData.contentList.forEach((el) => {
        if(el.classifyList && el.classifyList.length) {
          el.classifyList.forEach((els, elsIndex) => {
            if(els.classifyName == value) {
              el.classifyList.splice(elsIndex, 1)
            }
          })
        }
      })
    },
    delTagV(scope, index) {
      this.submitData.contentList[scope.$index].classifyList.splice(index,1)
    },
    remoteContent(keyWord) {
      this.contentList = []
      if(!(keyWord && keyWord.length > 1)) {
        return
      }
      this.getLoading = true;
      this.search.contentTitle = keyWord
      let params = {
        userId: this.info.userId,
        username: this.info.userName,
        ...this.search
      }
      this.api.searchContentTitle(params).then( response => {
        this.getLoading = false
        if(response.status === 200) {
          this.contentList = response.data
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
      }).catch(() => {
        this.getLoading = false
      })
    },
    changeType() {
      this.contentData = {}
      this.contentList = []
    },
    addContent() {
      // 去重
      if(this.submitData.contentList.some(item=>item.contentId==this.contentData.contentId)) {
        this.PUBLIC_Methods.apiNotify("此专题已存在，请勿重复添加", "warning")
        return
      }
      if(this.submitData.contentList.length > 499) {
        this.PUBLIC_Methods.apiNotify("最多添加500条内容", "warning")
        return
      }
      let classifyList = [];
      if(this.tags.length) {
        this.tags.forEach((item) => {
          classifyList.push({
            classifyName: item
          })
        })
      }
      let row = {
        classifyList: classifyList,
        contentCover: this.contentData.contentCover?this.contentData.contentCover:'',
        contentId: this.contentData.contentId?this.contentData.contentId:0,
        contentSummary: this.contentData.contentSummary?this.contentData.contentSummary:'',
        contentTitle: this.contentData.contentTitle?this.contentData.contentTitle:'',
        contentType: this.contentData.contentType?this.contentData.contentType:'',
        createdName: this.contentData.createdName?this.contentData.createdName:'',
        publishedTime: this.contentData.publishedTime?this.contentData.publishedTime:'',
        type: this.contentData.type?this.contentData.type:0,
        sort: this.submitData.contentList.length + 1
      }
      this.submitData.contentList.push(row)
      this.contentData = {}
      this.contentList = []
      this.search.contentTitle = ""
    },
    deleteRow(index) {
      this.submitData.contentList.splice(index,1)
      this.resort()
    },
    resort() {
      var newArray = this.submitData.contentList.slice(0);
      this.submitData.contentList = [];
      newArray.forEach((item,index)=>{
        this.submitData.contentList.push({
          classifyList: item.classifyList,
          contentCover: item.contentCover,
          contentId: item.contentId,
          contentSummary: item.contentSummary,
          contentTitle: item.contentTitle,
          contentType: item.contentType,
          createdName: item.createdName,
          publishedTime: item.publishedTime,
          type: item.type,
          sort: index + 1
        })
      })
    },
    setSort() {
      const el = this.$refs.dragTable.$el.querySelectorAll('.el-table__body-wrapper > table > tbody')[0]
      this.sortable = Sortable.create(el, {
        ghostClass: 'sortable-ghost', 
        setData: function(dataTransfer) {
          dataTransfer.setData('Text', '')
        },
        onEnd: evt => {
          let drapObj = this.submitData.contentList[evt.oldIndex]
          this.submitData.contentList.splice(evt.oldIndex, 1)
          this.submitData.contentList.splice(evt.newIndex, 0, drapObj)
          var newArray = this.submitData.contentList.slice(0);
          this.submitData.contentList = [];
          this.$nextTick(() => {
            newArray.forEach((item,index)=>{
              this.submitData.contentList.push({
                classifyList: item.classifyList,
                contentCover: item.contentCover,
                contentId: item.contentId,
                contentSummary: item.contentSummary,
                contentTitle: item.contentTitle,
                contentType: item.contentType,
                createdName: item.createdName,
                publishedTime: item.publishedTime,
                type: item.type,
                sort: index + 1
              })
            })
          })
        }
      })
    },
    // 数据校验
    validateData() {
      return new Promise((resolve) => {
        this.$refs["submitRef"].validate( valid => {
          if (valid) {
            resolve()
          }else {
            this.$emit('changeTab','content')
          }
        })
      })
    }
	}
}
</script>
<style scoped>
.flex-item /deep/ .el-form-item__content {
  display: flex;
  flex-direction: row;
}
.tips {
  font-size: 14px;
  margin-top: 6px;
  color: #999;
  text-align: right;
}
.tag-wrap{
  display: flex;
  justify-content: flex-start;
  padding-left: 100px;
  margin: -10px 0 10px 0;
  flex-wrap: wrap;
}
.tag-btn{
  flex: none;
  display: inline-block;
  margin-right: 20px;
  margin-bottom: 6px;
  position: relative;
  height: 24px;
  line-height: 24px;
  padding: 0 10px 0 15px;
  border-radius: 5px;
  background-color: #f0f2f5;
  font-size: 11px;
  font-weight: 400;
  display: inline-block;
  cursor: pointer;
  color: #606266;
}
.tag-btn label{
  margin-right: 5px;
}
</style>