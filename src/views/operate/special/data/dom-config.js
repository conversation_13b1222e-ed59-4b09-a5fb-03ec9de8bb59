// import store from '@/store'

const domConfig = {
  listSearch: [
    {
      label: '专题标题',
      placeholder: '请输入',
      model: 'specialTopicTitle',
      component: 'ms-input'
    },
  ],
  tableHeader: [
    { label: 'ID', property: 'id', sortable: true, width: '60' },
    { label: '专题标题', property: 'specialTopicTitle', width: '250'},
    { label: '创建时间', property: 'createdTime', sortable: true,  width: '130'  },
    { label: '创建人', property: 'createdName' },
    { label: '状态', property: 'status'},
    { label: '发布时间', property: 'publishedTime', sortable: true,  width: '130'  },
    { label: 'PV/UV', property: 'fields', width: '160' },

  ],
  tableButtons: [
    {
      label: '编辑',
      icon: '',
      role: '',
      operation: 'edit',
      component: 'special-operation',
      way: 'page',
      type: 'primary',
      path: 'special-operation',
      params: ['id']
    },
    {
      label: '',
      icon: '',
      role: '',
      operation: 'editStatus',
      component: 'msSpecialOperation',
      way: 'dialog',
      field: 'status',
      rule: {
        1: { label: '去审', type: '', operation: 'toreview' },
        0: { label: '审核', type: 'success', operation: 'approval' }
      }
    },
    {
      label: '删除',
      way: 'dialog',
      type: 'danger',
      operation: 'delete',
      component: 'msSpecialOperation'
    },
  ],
  soltButtons: [
    { 
      label: '创建', 
      type: 'primary', 
      icon: 'el-icon-plus',
      operation: 'created',
      component: 'special-operation',
      way: 'page',
      path: 'special-operation',
      params: ['id']
    },
    { 
      label: '批量审核', 
      type: 'primary',
      operation: 'approval',
      component: 'msSpecialOperation',
      way: 'batch'
    },
    { 
      label: '批量去审', 
      type: 'primary',
      operation: 'toreview',
      component: 'msSpecialOperation',
      way: 'batch'
    },
    { 
      label: '批量删除',
      type: 'info',
      icon: 'el-icon-close',
      operation: 'delete',
      component: 'msSpecialOperation',
      way: 'batch'
    },
    {
      label: '回收站',
      type: 'info',
      operation: '',
      component: 'msSpecialRecycle',
      way: 'dialog',
      title: '专题回收站',
      width: '90%'
    }
  ]
}

export default domConfig;
