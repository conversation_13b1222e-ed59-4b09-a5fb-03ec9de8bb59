<template>
  <ms-operation-dialog>
    <template slot="content">
      <el-form
        ref="submitRef"
        class="rule-form"
        :model="submitData"
        :rules="rules"
        label-width="90px"
        v-loading="getLoading"
      >
        <el-row>
          <el-col :span="24">
            <el-form-item label="二维码" prop="fileUrl">
              <ms-image-upload
                :imageUrl.sync="submitData.fileUrl"
                @bindData="bindData"
                :disabled="!!submitData.id"
              ></ms-image-upload>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </template>
    <template slot="footer">
      <el-button
        @click="submitForm"
        :loading="loading"
        size="mini"
        type="primary"
        >确 定</el-button
      >
      <el-button @click="chanelDialog" size="mini">取 消</el-button>
    </template>
  </ms-operation-dialog>
</template>

<script>
import { mapGetters } from "vuex";
import msImageUpload from "@/components/UpFile/ms-image-upload";
export default {
  name: "ms-image-edit",
  data() {
    return {
      loading: false,
      getLoading: false,
      submitData: {
        fileKey: "",
        fileSize: "",
        fileUrl: "",
        isShare: 0,
        tagId: 0,
        tagName: "",
        title: "",
      },
      rules: {
        fileUrl: [{ required: true, message: "请上传图片", trigger: "change" }],
        title: [{ required: true, message: "请填写标题", trigger: "blur" }],
        tagName: [{ required: true, message: "请选择关键字", trigger: "blur" }],
      },
    };
  },
  props: {
    model: Object,
    operation: String,
  },
  computed: {
    ...mapGetters(["info"]),
  },
  components: {
    msImageUpload,
  },
  created() {
    this.init();
  },
  methods: {
    init() {
      if (this.model.id) {
        this.submitData = { ...this.model };
      }
    },
    bindData(params) {
      this.submitData.fileKey = params.key;
      this.submitData.fileSize = params.size;
      this.submitData.fileUrl = params.url;
      this.submitData.title = params.name;
    },
    submitForm() {
      this.$refs["submitRef"].validate((valid) => {
        if (valid) {
          this.loading = true;
          if (this.model.id) {
            this.submitEdit();
          } else {
            this.submitAdd();
          }
        }
      });
    },
    submitEdit() {
      let params = {
        userId: this.info.userId,
        username: this.info.userName,
        id: this.submitData.id,
        tagId: this.submitData.tagId,
        tagName: this.submitData.tagName,
        title: this.submitData.title,
      };
      this.api
        .updateImageMaterial(params)
        .then((response) => {
          this.loading = false;
          if (response.status === 200) {
            this.PUBLIC_Methods.apiNotify(
              response.message || "请求成功",
              "success"
            );
            this.$emit("up-date");
          } else {
            this.PUBLIC_Methods.apiNotify(
              response.message || "请求出错",
              "warning"
            );
          }
        })
        .catch(() => {
          this.loading = false;
        });
    },
    submitAdd() {
      let params = {
        userId: this.info.userId,
        username: this.info.userName,
        ...this.submitData,
      };
      this.api
        .saveImageMaterial(params)
        .then((response) => {
          this.loading = false;
          if (response.status === 200) {
            this.PUBLIC_Methods.apiNotify(
              response.message || "请求成功",
              "success"
            );
            this.$emit("up-date");
          } else {
            this.PUBLIC_Methods.apiNotify(
              response.message || "请求出错",
              "warning"
            );
          }
        })
        .catch(() => {
          this.loading = false;
        });
    },
    chanelDialog() {
      this.$emit("close");
    },
  },
};
</script>
