const domConfig = {
  // 搜索配置
  listSearch: [
  ],

  // 表头配置
  tableHeader: [
    { label: '序号', property: 'id', width: '80' },
    { label: '类型属性', property: 'orderProperty', sortable: false, width: '150' },
    { label: '发放类型', property: 'orderPropertyName', sortable: false, width: '120' },
    { label: '说明', property: 'orderPropertyExplain', sortable: false, width: '120' },
    { label: '创建人', property: 'createdName', sortable: false, width: '150' },
    { label: '创建时间', property: 'createdTime', sortable: true, width: '150' },
    { label: '状态', property: 'status', sortable: false, width: '120' }
  ],

  // 行内列表按钮配置
  tableButtons: [
    {
      label: '编辑',
      icon: '',
      role: '',
      operation: 'edit',
      component: 'ms-grantType-add',
      way: 'dialog',
      params: ['id'],
      type: 'primary',
      title: '编辑',
      position: 'right',
      width: '50%'
    },
    {
      icon: '',
      role: '',
      operation: 'editStatus',
      component: 'msGrantTypeStatus',
      way: 'dialog',
      field: 'status',
      rule: {
        0: { label: '启用', type: 'success' },
        1: { label: '禁用', type: 'info' }
      }
    },
  ],

  // 新建项目按钮
  soltButtons: [
    {
      label: '创建权限类型',
      icon: 'el-icon-plus',
      type: 'primary',
      operation: 'created',
      way: 'dialog',
      component: 'ms-grantType-add',
      title: '创建权限类型',
      position: 'right',
      width: '50%'
    }
  ]
}
export default domConfig
