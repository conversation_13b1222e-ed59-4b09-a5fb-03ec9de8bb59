<template>
  <div class="lecture">
    <el-form
          label-width="100px"
          class="rule-form"
          ref="submitRef"
          :rules="rule"
          :model="submitData"
        >
      <el-row>
        <el-col :span="24">
          <el-form-item label="类型属性" prop="orderProperty">
                <el-select v-model="submitData.orderProperty" placeholder="请选择" style="width:100%">
                  <el-option :key="1" label="会员" :value="1"></el-option>
                  <el-option :key="2" label="课程" :value="2"></el-option>
                </el-select>
              </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="类型名称" prop="orderPropertyName">
            <el-input
              v-model.trim="submitData.orderPropertyName"
              placeholder="请输入发放类型，该名称会在用户订单详情展示"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="描述:" prop="orderPropertyExplain">
            <el-input
              v-model="submitData.orderPropertyExplain"
              type="textarea"
              :rows="4"
              placeholder="请输入本发放类型的其他描述，＜50字"
              :maxlength="50"
              show-word-limit
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div class="buttons">
      <el-button type="primary" @click="submit">保存</el-button>
      <el-button type="info" @click="close">返回</el-button>
    </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex"

export default {
  name: "ms-grantType-add",
  data() {
    return {
      submitData: {
        orderProperty: "",
        orderPropertyName: "",
        orderPropertyExplain: "",
      },
      imgUploadTip: '',
      rule: {
        orderProperty: [
          { required: true, message: "请选择类型属性", trigger: "blur" },
        ],
        orderPropertyName: [
          { required: true, message: "请输入类型名称", trigger: "blur" },
        ],
        orderPropertyExplain: [
          { required: true, message: "请输入类型描述", trigger: "blur" },
        ],
      },
    }
  },
  props: {
    model: Object,
    operation: String,
  },
  computed: {
    ...mapGetters(["info"]),
  },
  created() {
    if (this.operation === "edit") {
      let data = Object.assign({},this.model)
      this.submitData = data
    }
  },
  methods: {
    submit() {
      this.$refs["submitRef"].validate(valid => {
        if (valid) {
          let userInfo = this.$store.getters.info || {}
          this.submitData.projectId = userInfo.projectId;
          this.submitData.userId = userInfo.userId;
          this.submitData.username = userInfo.userName;
          this.api[
            this.operation === "edit"
              ? "updateProperty"
              : "createProperty"
          ](this.submitData).then(response => {
            if (response.status === 200) {
              this.PUBLIC_Methods.apiNotify(
                response.message || "添加成功",
                "success"
              )
              this.close()
              this.$emit("up-date")
            } else {
              this.PUBLIC_Methods.apiNotify(
                response.message || "添加失败",
                "warning"
              )
            }
          })
        }
      })
    },
    close() {
      this.$emit("close");
    },
  },
}
</script>

<style lang="scss" scoped>
.buttons {
  position: fixed;
  bottom: 20px;
  right: 20px;
}
.hint{
  color: rgba(0,0,0,0.4);
}
</style>
