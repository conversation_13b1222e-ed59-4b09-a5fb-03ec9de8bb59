/* 付费会员组件 */
<template>
  <ms-table
    :currentPage="searchParams.pageIndex"
    :loading="loading"
    :operationButtons="domConfig.tableButtons"
    :pageSize="searchParams.pageSize"
    :scopeConfig="scopeConfig.show"
    :scopeHeader="scopeConfig.headerShow"
    :tableData="list"
    :tableHeader="domConfig.tableHeader"
    :total="total"
    @current-change="current_change"
    @operation-change="operation_change"
    @size-change="size_change"
    @header-operation="header_operation"
    @handleSelectionChange="handleSelectionChange"
  >
    <!-- 列表搜索去区域插槽 -->
    <template slot="ms-table-header">
      <div class="slot-button-article clearfix">
        <template v-for="(item, index) in domConfig.soltButtons">
          <el-button
            :key="index"
            :type="item.type"
            :icon="item.icon"
            @click="operation_change({ operation: item })"
            plain
            >{{ item.label }}</el-button
          >
        </template>
      </div>
      <el-dialog
        :visible.sync="dialog"
        closeable
        show-close
        :close-on-click-modal="false"
        :width="dialogWidth"
        :title="dialogTitle"
      >
        <component
          :is="dialogComponent"
          :model="scopeInfo"
          :operation="dialogOperation"
          @close="dialog = !dialog"
          @up-date="init"
          v-if="dialog"
        ></component>
      </el-dialog>
      <ms-right-dialog
        :visible.sync="r_dialog"
        :width="dialogWidth"
        :title="dialogTitle"
      >
        <component
          :is="dialogComponent"
          :model="scopeInfo"
          :operation="dialogOperation"
          @close="r_dialog = !r_dialog"
          @up-date="init"
          v-if="r_dialog"
        ></component>
      </ms-right-dialog>
    </template>
  </ms-table>
</template>

<script>
import dataMixin from "./grantType-mixin"
import tableMixins from "@/views/mixins/table"
import msGrantTypeAdd from "./tool/ms-grantType-add"
import msGrantTypeStatus from "./tool/ms-grantType-status"
export default {
  name: "grantType-manage",
  mixins: [dataMixin, tableMixins],
  components: {
    msGrantTypeAdd,
    msGrantTypeStatus
  },
  data() {
    return {
      show: false,
      loading: false,
      searchParams: {
        // => 列表查询传参
      },
    }
  },
  methods: {
    apiInit(params) {
      let searchParams = { ...params }
      this.api
        .queryProperty(searchParams)
        .then(response => {
          this.loading = false
          this.total = response.totalSize || 0
          this.list = response.data || []
          if (response.status !== 200) {
            this.PUBLIC_Methods.apiNotify(
              response.message || "请求出错",
              "warning"
            )
          }
        })
        .catch(() => (this.loading = false))
    },
  },
}
</script>

<style lang="scss" scoped>
/deep/ .label_value {
  max-width: 500px;
}

/deep/ .label {
  height: 100%;
  display: inline-block;
}
</style>
