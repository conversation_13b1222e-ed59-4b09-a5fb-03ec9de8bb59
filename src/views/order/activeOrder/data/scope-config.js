const scopeConfig = {
    show: {
      nikeName: () => {
        return {
          type: 'webLink',
          config: {
            role: '',
            operation: 'edit',
            way: 'page',
            path: 'user-detail',
            params: [{
              keyName: 'id',
              valName: 'userId'
            }],
          }
        }
      },
      orderType: () => {
        return {
          type: 'status-char',
          rule: {
            1: { label: '会员充值' },
            2: { label: '视频购买' }
          }
        }
      },
      onlinePayType: () => {
        return {
          type: 'status-char',
          rule: {
            1: { label: '微信' },
            2: { label: '支付宝' },
            3: { label: '光大支付' },
            99: { label: '积分' }
          }
        }
      },
      createdAt: () => {
        return {
          type: 'formatTime',
          cFormat: '{y}-{m}-{d} {h}:{i}:{s}'
        }
      },
      paymentLastAt: () => {
        return {
          type: 'formatTime',
          cFormat: '{y}-{m}-{d} {h}:{i}:{s}'
        }
      },
      invoiceStatus: () => {
        return {
          type: 'status',
          rule: {
            0: { label: '未开票' },
            1: { label: '审核中' },
            2: { label: '已开票' }
          }
        }
      },
      payment: () => {
        return {
          type: 'render',
          render: (h, scope) => {
            return h(
              "div",
              scope.row.onlinePayType == 99 ? scope.row.payment + '积分' : scope.row.payment + '元'
            )
          }
        }
      }
    },
    headerShow: {
      status: () => {
        return {
          type: 'dropdown',
          icon: 'icon-funnel',
          options: [
            { label: '全部', value: null },
            { label: '待审核', value: '待审核' },
            { label: '待启动', value: '待启动' },
            { label: '在执行', value: '在执行' },
            { label: '已完成', value: '已完成' }
          ],
          operation: 'query'
        }
      },
    }
  }
  
  export default scopeConfig;
  