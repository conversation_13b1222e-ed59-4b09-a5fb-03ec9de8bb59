const domConfig = {
    listSearch: [
      {
        label: '活动名称',
        model: 'typeName',
        component: 'ms-input'
      },
      {
        label: '活动日期',
        model: 'activityTime',
        component: 'ms-picker',
        type: 'daterange',
        width: '240px'
      },
      {
        label: '推荐好友',
        model: 'realname',
        component: 'ms-input'
      },
      {
        label: '推荐人',
        model: 'recommendsMobile',
        component: 'ms-input'
      },
    ],
    tableHeader: [
      { label: '提交时间', property: 'createdTime', width: '120' },
      { label: '活动名称', property: 'typeName', width: '100' },
      { label: '活动类型', property: 'type', width: '100' },
      { label: '活动时间', property: 'activityTime', width: '300' },
      { label: '推荐好友', property: 'realname', width: '100' },
      { label: '好友单位', property: 'unit', width: '100' },
      { label: '所在科室', property: 'department', width: '130' },
      { label: '手机号', property: 'mobile', width: '100' },
      { label: '推荐人', property: 'userMobile', width: '100' },
    ],
    soltButtons: [
      { 
        label: '导出表格',
        icon: '',
        type: 'primary', 
        way: 'banthExport',
        identify: 'banthExport'
      }
    ],
    tableButtons: [
    ]
  }
  
  export default domConfig;
  