<template>
	<ms-table
		:currentPage="searchParams.pageIndex"
		:loading="loading"
		:operationButtons="domConfig.tableButtons"
		:pageSize="searchParams.pageSize"
		:scopeConfig="scopeConfig.show"
		:scopeHeader="scopeConfig.headerShow"
		:tableData="list"
		:tableHeader="domConfig.tableHeader"
		:total="total"
		@current-change="current_change"
		@operation-change="operation_change"
		@size-change="size_change"
		@header-operation="header_operation"
		class="small-table-td"
	>
    <!-- 列表搜索去区域插槽 -->
		<template slot="ms-table-header">
			<div class="slot-search" style="width:100%;">
				<template v-for="(searchItem, key) in domConfig.listSearch">
					<component
						:index="searchItem.index || ''"
						:is="searchItem.component"
						:key="key"
                        :width="searchItem.width || '150px'"
                        :model.sync="searchParams[searchItem.model]"
                        :label="searchItem.label"
						:operation="searchItem.operation || ''"
						:options="searchItem.options || []"
						:placeholder="searchItem.placeholder || ''"
						:type="searchItem.type || ''"
                        :multiple="searchItem.multiple"
                        :disabled="searchItem.disabled"
                        :code="searchItem.code"
					></component>
				</template>
                <div class="inlineBlock">
                    <el-button @click="handleClick('query')" type="primary" plain icon="el-icon-search">查询</el-button>
                    <el-button @click="handleClick('reset')">重置</el-button>
                </div>
                <div class="slot-button">
                    <template v-for="(item, index) in domConfig.soltButtons">
                        <el-button :key="index" :type="item.type" :icon="item.icon" :disabled="item.disabled" v-show="!item.roleDisabled" @click="operation_change({operation: item})" plain>{{ item.label }}</el-button>
                    </template>
                    <!-- <el-alert class="article-total" :title="`共搜索到${total}个表单`" type="info" show-icon></el-alert> -->
                </div>
			</div>
			<el-dialog :visible.sync="dialog" 
                 closeable 
                 show-close
                 :close-on-click-modal="false"
                 :width="dialogWidth"
                 :title="dialogTitle">
				<component :is="dialogComponent" 
                   :model="scopeInfo" 
                   :operation="dialogOperation" 
                   @close="dialog = !dialog" 
                   @up-date="init" 
                   v-if="dialog"></component>
			</el-dialog>
		</template>
	</ms-table>
</template>

<script>
import mixin from "./mixin"
import tableMixins from "../../common/mixins/table"
export default {
    name: "ms-erp-order",
    mixins: [tableMixins,mixin],
    data () {
        return {}
    },
    methods: {
        apiInit (params) {
            if(params.activityTime && params.activityTime.length) {
                params.startTime = params.activityTime[0];
                params.endTime = params.activityTime[1];
            } else {
                params.startTime = '';
                params.endTime = '';
            }
            let searchParams = {...params}
            this.api.getActivityList(searchParams).then(response => {
                this.loading = false
                if (response.status !== 200) {
                    this.list = []
                    this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
                    return false
                }
                this.total = response.totalSize || 0;
                response.data.forEach(element => {
                    element.startTime = element.startTime ? element.startTime : ''
                    element.endTime = element.endTime ? element.endTime : ''
                    element.activityTime = element.startTime + ' - ' + element.endTime
                });
                this.list = response.data || []
            }).catch(() => this.loading = false)
        },
        getActivityStatistics() {
            let searchParams = {}
            this.api.getActivityStatistics(searchParams).then(response => {
                console.log(response, 'response')
                if (response.status !== 200) {
                    this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
                }
            }).catch(() => {})

        },
        operation_change_module(val) {
            console.log(this.searchParams, '11')
            switch (val.operation.way) {
                case "banthExport":
                    this.api.exportActivityExcel(this.searchParams).then(response => {
                        const link=document.createElement('a');
                        link.href=window.URL.createObjectURL(new Blob(response.data))
                        link.download='梅斯有你.xls';
                        link.click()
                        // window.location.href = response.data;
                        if (response.status !== 200) {
                            this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
                        }
                    }).catch(() => {})
                break;
                default: break;
            }
        }
    }
}
</script>
