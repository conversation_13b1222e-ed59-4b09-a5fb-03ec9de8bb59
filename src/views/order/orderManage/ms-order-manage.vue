<template>
    <section>
	<ms-table
		:currentPage="searchParams.pageIndex"
		:loading="loading"
		:operationButtons="domConfig.tableButtons"
		:pageSize="searchParams.pageSize"
		:scopeConfig="scopeConfig.show"
		:scopeHeader="scopeConfig.headerShow"
		:tableData="list"
		:tableHeader="domConfig.tableHeader"
		:total="total"
		@current-change="current_change"
		@operation-change="operation_change"
		@size-change="size_change"
		@header-operation="header_operation"
		class="small-table-td"
	>
    <!-- 列表搜索去区域插槽 -->
		<template slot="ms-table-header">
			<div class="slot-search">
				<template v-for="(searchItem, key) in domConfig.listSearch">
					<component
						:index="searchItem.index || ''"
						:is="searchItem.component"
						:key="key"
                        :width="searchItem.width || '150px'"
                        :model.sync="searchParams[searchItem.model]"
                        :label="searchItem.label"
						:operation="searchItem.operation || ''"
						:options="searchItem.options || []"
						:placeholder="searchItem.placeholder || ''"
						:type="searchItem.type || ''"
                        :multiple="searchItem.multiple"
                        :disabled="searchItem.disabled"
                        :code="searchItem.code"
					></component>
				</template>
          <div class="inlineBlock" style="margin-right:15px">
              <el-button @click="handleClick('query')" type="primary" plain icon="el-icon-search">查询</el-button>
              <el-button @click="handleClick('reset')">重置</el-button>
          </div>
          <div class="slot-button">
            <template v-for="(item, index) in domConfig.soltButtons">
              <el-button :key="index" :type="item.type" :icon="item.icon" :disabled="item.disabled" v-show="!item.roleDisabled" @click="operation_change({operation: item})" plain>{{ item.label }}</el-button>
            </template>
          </div>
			</div>
			<el-dialog :visible.sync="dialog" 
                 closeable 
                 show-close
                 :close-on-click-modal="false"
                 :width="dialogWidth"
                 :title="dialogTitle">
				<component :is="dialogComponent" 
                   :model="scopeInfo" 
                   :operation="dialogOperation" 
                   @close="dialog = !dialog" 
                   @up-date="init" 
                   v-if="dialog"></component>
			</el-dialog>
      <!--大图--> 
      <el-image-viewer
        v-if="showImageViewer"
        :on-close="closeImageViewer"
        :url-list="viewerImgList"
      />
		</template>
	</ms-table>
  <ms-right-dialog :visible.sync="r_dialog" :width="dialogWidth" :title="dialogTitle">
      <component
        :is="dialogComponent"
        :model="scopeInfo"
        :operation="dialogOperation"
        @close="r_dialog = !r_dialog"
        @up-date="apiInit"
        @import-date="uploadSuccess"
        v-if="r_dialog"
      ></component>
    </ms-right-dialog>
    </section>
</template>

<script>
import ElImageViewer from 'element-ui/packages/image/src/image-viewer'
import orderMixin from "./order-mixin"
import tableMixins from "../../common/mixins/table"
import msOrderImport from "./tool/ms-order-import"
export default {
    name: "ms-order-manage",
    mixins: [tableMixins,orderMixin],
    data () {
        return {
          showImageViewer: false,
          viewerImgList: [],
        }
    },
    components: {
      ElImageViewer,
      msOrderImport
    },
    methods: {
      uploadSuccess() {
        this.dialog = false;
        this.r_dialog = false;
        this.uploadDisabled = true
        this.apiInit({pageIndex: 1,pageSize: 20,projectId: 1})
      },
        apiInit (params) {
            let searchParams = {...params}
            if (searchParams.createTime) {
              searchParams.orderCreatedStartTime = searchParams.createTime[0] || ''
              searchParams.orderCreatedEndTime = searchParams.createTime[1] || ''
            }
            this.api.orderList(searchParams).then(response => {
                this.loading = false
                this.total = response.totalSize || 0;
                this.list = response.data || []
                if (response.status !== 200) {
                    this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
                }
            }).catch(() => this.loading = false)
        },
        operation_change_module(val) {
          if (val.operation.way === 'image-preview') {
            this.viewerImgList = val.model.paymentVouchers
            this.showImageViewer = true;
          }
          if(val.operation.way === 'banthExport') {
            this.loading = true
            let params = {
              wxzfbOrderId: this.searchParams.wxzfbOrderId || '',
              name: this.searchParams.name || '', 
              mobile: this.searchParams.mobile || '',
              orderType: this.searchParams.orderType || '',
              startPrice: this.searchParams.startPrice || '',
              endPrice: this.searchParams.endPrice || '',
              orderCreatedStartTime: this.searchParams.createTime ? this.searchParams.createTime[0] : '',
              orderCreatedEndTime: this.searchParams.createTime ? this.searchParams.createTime[1] : '',
            }
            this.api.exportOrdersExcel(params).then(response => {
              if(response.status === 200) {
                window.location.href = response.data;
              } else {
                this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
              }
              this.loading = false
            }).catch(() => this.loading = false)
          }
        },
        closeImageViewer () {
          this.showImageViewer = false;
        }
    }
}
</script>
