<template>
  <ms-operation-dialog :title="`是否发放权限`">
    <template slot="content">
      <el-tag v-for="(id, index) in modelArr" 
              :key="index" 
              type="info" 
              style="margin: 0 5px 5px 0">{{id}}</el-tag>
    </template>
    <template slot="footer">
      <el-button @click="submitForm"
                 :loading="loading"
                 size="mini"
                 type="primary">确 定</el-button>
      <el-button @click="$emit('close')"
                 size="mini">取 消</el-button>
    </template>
  </ms-operation-dialog>
</template>

<script>
export default {
	name: "ms-order-operation",
	data () {
		return {
      loading: false,
      userInfo: {},
      type: null,
      orderId: '',
      modelArr: []
		}
	},
	props: [
		"model",
		"operation"
  ],
  created() {
    this.userInfo = this.$store.getters.info || {}
    let arr = []
    let showArr = []
    this.type = 1;
    if (this.PUBLIC_Methods.isArrayFn(this.model)) {
      this.model.forEach(item => {
        arr.push(item.id)
        showArr.push(item.nikeName)
      });
    } else {
      arr.push(this.model.id)
      showArr.push(this.model.nikeName)
    }
    this.ids = arr
    this.modelArr = showArr
  },
	methods: {
		submitForm () {
      this.loading = true;
      let params = {
        userId: this.userInfo.userId,
        username: this.userInfo.userName,
        orderId: this.ids[0]
      }
      this.api.grantPermissions(params).then(response => {
        if(response.status === 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
        this.$emit('up-date')
        this.loading = false
      }).catch( () => {
        this.loading = false;
        this.$emit('close')
      })
		}
	}
}
</script>
