<template>
  <ms-right-content class="right">
    <ms-order-upload @up-date="submitForm" style="margin-bottom: 20px"></ms-order-upload>
    <el-button style="margin-right: 20px" type="success" plain :disabled="loading" @click="downTmp()">下载导入模版</el-button>
    <span>支持扩展名：.xls .xlsx</span>
    <template slot="footer">
      <div style="text-align:right; margin-right: 20px">
        <!-- <el-button @click="submitForm"
              :loading="loading"
              type="primary">确 认</el-button> -->
        <el-button @click="$emit('close')" >取 消</el-button>
      </div>
    </template>
  </ms-right-content>
</template>

<script>
import tableMixins  from "../../../common/mixins/table"
import msOrderUpload from "./ms-order-upload"
import { mapGetters } from "vuex";
export default {
	name: "ms-questionGather-add",
  mixins: [tableMixins],
	data () {
		return {
      loading: false
		}
	},
	props: {
		model: Object,
		operation: String
  },
  computed: {
    ...mapGetters(["info"])
  },
  components: {
   msOrderUpload
  },
  created() {
  },
	methods: {
    apiInit() {
      this.loading = false
    },
    // 下载模版
    downTmp() {
      this.loading = true
        window.location.href = 'https://static.medsci.cn/doc/%E6%89%B9%E9%87%8F%E5%AF%BC%E5%85%A5%E4%BA%BA%E5%B7%A5%E8%AE%A2%E5%8D%95%E6%A8%A1%E6%9D%BF.xlsx'
    },
    submitForm(data) {
      this.$emit('import-date', data)
    }
	}
}
</script>
