<template>
  <section>
    <el-form :model="submitData"
             ref="submitRef"
             class="rule-form info-form"
             :rules="rules"
             label-width="100px">
      <el-row :gutter="20"> 
        <el-col :span="24">
          <el-row>
            <el-col :span="12">
              <el-form-item label="订单类型" prop="orderType">
                <el-select v-model="submitData.orderType" placeholder="请选择" style="width:100%" @change="changeOrderType">
                  <el-option :key="1" label="会员充值" :value="1"></el-option>
                  <el-option :key="2" label="视频购买" :value="2"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="商品名称" prop="itemId">
                <el-select v-model="submitData.itemId" filterable :remote="submitData.orderType==2" :remote-method="initVideoList" clearable placeholder="请选择" style="width:100%">
                  <template v-if="submitData.orderType==1">
                    <el-option  v-for="(item,index) in memberList" :key="index" :label="item.cardName" :value="item.id"></el-option>
                  </template>
                  <template v-if="submitData.orderType==2">
                    <el-option  v-for="(item,index) in videoList" :key="index" :label="item.title" :value="item.id"></el-option>
                  </template>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="发放类型" prop="orderProperty">
                <el-select v-model="submitData.orderProperty" placeholder="请选择" style="width:100%">
                  <el-option v-for="(item,index) in orderPropertyList" :key="index" :label="item.orderPropertyName" :value="item.orderPropertyName"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="订单说明" prop="orderExplain">
                <el-input v-model="submitData.orderExplain" placeholder="请输入订单号或支付流水号等信息" maxlength="50" show-word-limit></el-input>
              </el-form-item>
            </el-col>
          </el-row>
           <el-row>
            <el-col :span="12">
              <el-form-item label="发放凭证" prop="paymentVoucher">
                <ms-single-image v-model="submitData.paymentVoucher"></ms-single-image>
                <p class="hint">请上传用户支付的订单信息，需展示订单号和支付流水号</p>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="用户手机号码" prop="mobile">
                <el-input v-model="submitData.mobile" placeholder="请输入" @input="check"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12" style="text-align: left; padding-left: 20px">
              <el-button @click="checkMobile" :disabled="checkDisabled" size="mini" type="primary">校验</el-button>
              <span class="userName" @click="toUserPage">{{checkInfo.userName}}</span>
            </el-col>
          </el-row>
          <el-row v-if="submitData.orderType==2">
            <el-col :span="12">
              <el-form-item label="课程有效期" prop="itemExpirationDay">
                <el-input-number style="margin-right:10px" v-model="submitData.itemExpirationDay" :step="1" step-strictly :min="0"></el-input-number>天
              </el-form-item>
            </el-col>
          </el-row>
        </el-col>
      </el-row>
    </el-form>
  </section>
</template>

<script>
import { mapGetters } from "vuex";
import {ms_rule_phone} from "@/utils/form-rule.js";
export default {
	name: "ms-prescription-content",
	data () {
		return {
      rules: {
        orderType: [
          { required: true, message: "请选择订单类型", trigger: 'blur' }
        ],
        itemId: [
          { required: true, message: "请选择商品名称", trigger: 'blur' }
        ],
        orderProperty: [
          { required: true, message: "请选择发放类型", trigger: 'blur' }
        ],
        orderExplain: [
          { required: true, message: "请输入订单说明", trigger: 'blur' }
        ],
        mobile: [
          { required: true, message: "请输入用户手机号码", trigger: 'blur' },
          { validator: ms_rule_phone, trigger: 'blur' }
        ],
      },
      memberList: [],
      videoList: [],
      orderPropertyList: [],
      checkDisabled: true,
      checkInfo: {}
		}
  },
  components: {
  },
  computed: {
    ...mapGetters(["submitData", "info"])
  },
  created() {
    this.initMemberList()
    this.initOrderPropertyList()
  },
	methods: {
    check(val) {
      if ((/^1[3456789]\d{9}$/.test(val))) {
        this.checkDisabled = false
      }else {
        this.checkDisabled = true
      }
    },
    changeOrderType() {
      delete this.submitData.itemId
      delete this.submitData.orderProperty
      this.initOrderPropertyList()
    },
    initMemberList() {
      let searchParams = {
        pageIndex: 0,
        pageSize: 99,
        status: 1
      }
      this.api.getCardByCardDurationList(searchParams).then(response => {
        this.memberList = response.data || []
        if (response.status !== 200) {
          this.PUBLIC_Methods.apiNotify(
            response.message || "请求出错",
            "warning"
          )
        }
      })
      .catch((err) => (console.log(err)))
    },
    initOrderPropertyList() {
      let searchParams = {
        pageIndex: 1,
        pageSize: 99,
        orderProperty: this.submitData.orderType,
        status: 1
      }
      this.orderPropertyList = []
      this.api.queryProperty(searchParams).then(response => {
        this.orderPropertyList = response.data || []
        if (response.status !== 200) {
          this.PUBLIC_Methods.apiNotify(
            response.message || "请求出错",
            "warning"
          )
        }
      })
      .catch((err) => (console.log(err)))
    },
    initVideoList(keyword) {
      if(!keyword || this.submitData.orderType == 1) {
        return
      }
      let searchParams = {
        pageIndex: 1,
        pageSize: 20,
        status: 1,
        permission: 3,
        title: keyword
      }
      this.api.videoPageList(searchParams).then(response => {
        this.videoList = response.data || []
        if (response.status !== 200) {
          this.PUBLIC_Methods.apiNotify(
            response.message || "请求出错",
            "warning"
          )
        }
      })
      .catch((err) => (console.log(err)))
    },
    checkMobile() {
      let searchParams = {
        params: {
          mobile: this.submitData.mobile
        }
      }
      this.api.verifyUser(searchParams).then(response => {
        if (response.status !== 200) {
          this.PUBLIC_Methods.apiNotify(
            response.message || "请求出错",
            "warning"
          )
        }else {
          this.checkInfo = response.data
          this.$emit('checkMoblie')
        }
      })
      .catch((err) => (console.log(err)))
    },
    toUserPage() {
      const { href } = this.$router.resolve({
          path: "/user-detail",
          query: {
            operation: 'edit',
            id: this.checkInfo.encodeUserId
          }
      });
      window.open(href, '_blank');
    },
    // 数据校验
    validateData(callback) {
      this.submitData.paymentVouchers = []
      this.submitData.paymentVouchers[0] = this.submitData.paymentVoucher
      let params = {
        ...this.submitData,
        userId: this.info.userId,
        username: this.info.userName
      }
      this.$refs["submitRef"].validate( valid => {
        if (valid) {
          callback(params)
        }
      })
    },
	}
}
</script>

<style scope lang="scss">
.add-mail {
  cursor: pointer;
  width: 100%;
  height: 30px;
  line-height: 28px;
  border: 1px dashed #DCDFE6;
  border-radius: 4px;
  text-align: center;
  font-size: 12px;
  color: #333;
  outline: none;
  margin-top: 10px;
  background-color: rgba(0,0,0, 0);
}
.link-pos {
  position: relative;
  top: -2px;
}
.info-form{
  /deep/ .el-checkbox {
    margin-right: 0px;
  }
  &-right {
    /deep/ .el-form-item {
      margin-bottom: 16px;
    }
  }
}
.userName {
  padding-left: 20px;
  cursor: pointer;
  color: #409EFF;
  font-size: 14px;
}
.hint{
  color: rgba(0,0,0,0.4);
}
</style>
