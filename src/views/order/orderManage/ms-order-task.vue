<template>
    <div>
        <iframe :src="url"  class="iframe-style" frameborder="0"></iframe>
    </div>
</template>

<script>
import { mapGetters } from "vuex";
export default {
	name: "ms-order-task",
	data () {
		return {
            url: 'https://mid.medsci.cn/jmreport/view/644350468583432192?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6Ik1lZHNjaSIsImlhdCI6MTUxNjIzOTAyMn0.hwPxJMCzrmj4SPEa55TQ3pPJxJkjU-FG0u7IJSOBxb0&orderway=desc&order_top=5&orderby=%E8%AF%BE%E7%A8%8B%E9%87%91%E9%A2%9D'
        }
	},
	props: {
		model: Object,
		operation: String
    },
    computed: {
        ...mapGetters(["info"])
    },
    components: {
    },
    created() {
    },
	methods: {
	}
}
</script>

<style scoped>
    .iframe-style {
        width: 100%;
        height: calc(100vh - 110px);
    }
</style>
