
import serveUrl from '@/store/data/serveUrl.js'
const scopeConfig = {
    show: {
      paymentVouchers: () => {
        return {
          type: 'vouchers',
          config: {
            way: 'image-preview'
          }
        }
      },
      nikeName: () => {
        return {
          type: 'webLink',
          config: {
            role: '',
            operation: 'edit',
            way: 'page',
            path: 'user-detail',
            params: [{
              keyName: 'id',
              valName: 'decodeUserId'
            }],
          }
        }
      },
      orderType: () => {
        return {
          type: 'status-char',
          rule: {
            1: { label: '会员充值' },
            2: { label: '视频购买' },
            3: { label: '运营活动' }
          }
        }
      },
      itemTitle: (row) => {
        if(row.orderType==2 && row.onlinePayType!=99) {
          return {
            type: 'preview',
            config: {
              noCheck: true,
              pageUrl: `${serveUrl['edu']}`,
              paramsVal: ['decodeItemId']
            }
          }
        }
      },
      status: () => {
        return {
          type: 'status-char',
          rule: {
            1: { label: '待支付' },
            // 2: { label: '已付款' },
            // 3: { label: '未发货' },
            // 4: { label: '已发货' },
            5: { label: '已完成' },
            6: { label: '已取消' },
            7: { label: '待发放' },
          }
        }
      },
      onlinePayType: () => {
        return {
          type: 'status-char',
          rule: {
            1: { label: '微信' },
            2: { label: '支付宝' },
            3: { label: '光大支付' },
            4: { label: '人工订单' },
            5: { label: '卡密激活' },
            99: { label: '积分' }
          }
        }
      },
      itemExpirationTime: () => {
        return {
          type: 'formatTime',
          cFormat: '{y}-{m}-{d} {h}:{i}:{s}'
        }
      },
      createdTime: () => {
        return {
          type: 'formatTime',
          cFormat: '{y}-{m}-{d} {h}:{i}:{s}'
        }
      },
      paymentTime: () => {
        return {
          type: 'formatTime',
          cFormat: '{y}-{m}-{d} {h}:{i}:{s}'
        }
      },
      invoiceStatus: () => {
        return {
          type: 'status',
          rule: {
            0: { label: '未开票' },
            1: { label: '审核中' },
            2: { label: '已开票' }
          }
        }
      },
      payment: () => {
        return {
          type: 'render',
          render: (h, scope) => {
            return h(
              "div",
              scope.row.onlinePayType == 99 ? scope.row.payment + '积分' : (scope.row.payment?scope.row.payment + '元':'0元')
            )
          }
        }
      }
    },
    headerShow: {
      status: () => {
        return {
          type: 'dropdown',
          icon: 'icon-funnel',
          options: [
            { label: '全部', value: null },
            { label: '待支付', value: 1 },
            // { label: '已付款', value: 2 },
            // { label: '未发货', value: 3 },
            // { label: '已发货', value: 4 },
            { label: '已完成', value: 5 },
            { label: '已取消', value: 6 }
          ],
          operation: 'query'
        }
      },
      onlinePayType: () => {
        return {
          type: 'dropdown',
          icon: 'icon-funnel',
          options: [
            { label: '全部', value: null },
            { label: '微信', value: 1 },
            { label: '支付宝', value: 2 },
            { label: '光大支付', value: 3 },
            { label: '人工订单', value: 4 },
            { label: '积分', value: 99 }
          ],
          operation: 'query'
        }
      },
      invoiceStatus: () => {
        return {
          type: 'dropdown',
          icon: 'icon-funnel',
          options: [
            { label: '全部', value: null },
            { label: '未开票', value: 0 },
            { label: '审核中', value: 1 },
            { label: '已开票', value: 2 }
          ],
          operation: 'query'
        }
      }
    }
  }
  
  export default scopeConfig;
  