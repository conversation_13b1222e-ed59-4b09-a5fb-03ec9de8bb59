const domConfig = {
    listSearch: [
      {
        label: '订单号',
        model: 'wxzfbOrderId',
        component: 'ms-input'
      },
      {
        label: '商品名称',
        model: 'name',
        component: 'ms-input'
      },
      {
        label: '手机号码',
        model: 'mobile',
        component: 'ms-input'
      },
      {
        label: '订单类型',
        model: 'orderType',
        component: 'ms-select-local',
        code: 'orderType',
      },
      {
        label: '最小价格',
        model: 'startPrice',
        component: 'ms-input-number',
        code: 'startPrice',
      },
      {
        label: '最大价格',
        model: 'endPrice',
        component: 'ms-input-number',
        code: 'endPrice',
      },
      {
        label: '创建时间',
        placeholder: '请选择时间段',
        model: 'createTime',
        component: 'ms-picker',
        type: 'daterange',
        width: '240px'
      },
    ],
    tableHeader: [
      { label: '订单ID', property: 'id', width: '60' },
      { label: '订单类型', property: 'orderType', width: '100' },
      { label: '商品ID', property: 'itemId', width: '126' },
      { label: '商品名称', property: 'itemTitle', width: '100' },
      { label: '活动ID', property: 'activityId', width: '126' },
      { label: '活动名称', property: 'activityName', width: '100' },
      { label: '购买用户', property: 'nikeName', width: '100' },
      { label: '手机号码', property: 'mobile', width: '100' },
      { label: '订单创建时间', property: 'createdTime', width: '130' },
      { label: '创建人', property: 'createdName', width: '100' },
      { label: '订单金额', property: 'payment', width: '100' },
      { label: '订单状态', property: 'status', width: '100' },
      { label: '支付方式', property: 'onlinePayType', width: '100' },
      { label: '支付时间', property: 'paymentTime', width: '130' },
      { label: '交易单号', property: 'wxzfbOrderId', width: '130' },
      { label: '开票状态', property: 'invoiceStatus', width: '100' },
      { label: '发放类型', property: 'orderProperty', width: '100' },
      { label: '商品过期时间', property: 'itemExpirationTime', width: '130' },
      { label: '订单说明', property: 'orderExplain', width: '130' },
      { label: '发放凭证', property: 'paymentVouchers', width: '100' },
    ],
    tableButtons: [
      {
        label: '发放',
        icon: '',
        role: '',
        component: 'msOrderOperation',
        way: 'dialog',
        type: 'primary',
        params: ['id'],
        disabled: (val) => {
          return val.status == 7 ? false : true
        },
        showCallback: (val) => {
          return val.onlinePayType == 4 ? true : false
        },
      }
    ],
    soltButtons: [
      { 
        label: '批量导出',
        type: 'info',
        way: 'banthExport',
        identify: 'banthExport'
      },
      { 
        label: '人工订单', 
        type: 'primary', 
        icon: 'el-icon-plus',
        title: '人工订单',
        operation: 'created',
        component: 'orderManage-operation',
        path: 'orderManage-operation',
        way: 'page',
        identify: 'artificialOrder'
      },
      {
        label: '批量导入人工订单',
        icon: '',
        role: '',
        operation: 'edit',
        component: 'msOrderImport',
        way: 'dialog',
        type: 'primary',
        position: 'right',
        title: '导入订单',
        width: '55%',
        identify: 'batchArtificialOrder'
      },
      // { 
      //   label: '订单看板', 
      //   icon: '',
      //   type: 'primary', 
      //   operation: 'created',
      //   way: 'page',
      //   path: 'orderTask',
      // }
    ]
  }
  
  export default domConfig;
  