<template>
  <section class="form-tab" v-loading="getLoading">
    <ms-order-content ref="contentTemp" @checkMoblie="checkMoblie"></ms-order-content>
    <!-- 提交按钮 -->
    <footer-tool-bar v-loading="buttonLoading">
      <template slot="tool-content">
        <el-button type="primary" :disabled="!checked" @click="info_operation('save')">保存</el-button>
        <el-button type="info" @click="info_operation('back')">返回</el-button>
      </template>
    </footer-tool-bar>
  </section>
</template>

<script>
import msOrderContent from './tool/ms-order-content'
import orderMixin from "./order-mixin"
import FooterToolBar from '@/components/ToolBar/footer-tool-bar'
import { mapGetters } from "vuex";
export default {
  name: "order-operation",
  mixins: [orderMixin],
	data () {
		return {
      dataId: this.$route.query.id ? this.$route.query.id : 0,
      buttonLoading: false,
      getLoading: false,
      submitData: {},
      checked: false
		}
  },
  created() {
    this.init()
  },
  components: {
    msOrderContent,
    FooterToolBar
  },
  computed: {
    ...mapGetters(["info"])
  },
  methods: {
    init() {
      this.$store.dispatch('SetSubmitData', this.submitData);
      this.dialog = false;
    },
    checkMoblie() {
      this.checked = true
    },
    info_operation(val) {
      switch (val) {
        case 'save': 
          this.$refs['contentTemp'].validateData(this.saveData)
          break;
        case 'back':
          this.$router.back();
          break;
        default: break;
      }
    },
    saveData(params) {
      this.buttonLoading = true;
      let url = 'createArtificialOrder'
      this.api[url](params).then(response => {
        this.buttonLoading = false
        if(response.status === 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
          this.$router.back()
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
      }).catch(() => this.buttonLoading = false)
    },
  }
}
</script>

<style scoped>
  .live-address, .live-img .label{
    font-size: 12px;
  }
  .live-img .label {
    text-align: center;
  }
  .live-img .img {
    padding: 10px;
    text-align: center;
    height: 100px
    
  }
  .live-img .img img{
    width: auto;
    height: 100%;
  }
</style>
