const domConfig = {
    listSearch: [
      {
        label: '合同编号',
        model: 'contractCode',
        component: 'ms-input'
      },
      {
        label: '手机号码',
        model: 'mobile',
        component: 'ms-input'
      }
    ],
    tableHeader: [
      { label: '合同编号', property: 'contractCode', width: '120' },
      { label: '产品类型', property: 'catagoryName', width: '100' },
      { label: '价格类型', property: 'productName', width: '100' },
      { label: '价格（元）', property: 'price', width: '100' },
      { label: '购买用户', property: 'customerPrivateName', width: '100' },
      { label: '手机号码', property: 'mobile', width: '100' },
      { label: '合同创建时间', property: 'createdAt', width: '130' },
      { label: '合同状态', property: 'status', width: '100' },
      { label: '支付金额（元）', property: 'paymentAmount', width: '100' },
      { label: '支付时间', property: 'paymentLastAt', width: '130' },
      { label: '开票状态', property: 'invoiceStatusLabel', width: '100' }
    ],
    soltButtons: [
    ],
    tableButtons: [
    ]
  }
  
  export default domConfig;
  