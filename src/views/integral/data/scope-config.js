const scopeConfig = {
  show: {
    createdTime: () => {
      return {
        type: 'formatTime',
        cFormat: '{y}-{m}-{d} {h}:{i}'
      }
    },
    integral: () => {
      return {
        type: 'number'
      }
    }
  },
  headerShow: {
    sourceTypeName: () => {
      return {
        type: 'dropdown',
        icon: 'icon-funnel',
        options: [
          { label: '全部', value: null },
          { label: '阅读资讯', value: 1 },
          { label: '签到', value: 2 },
          { label: '购买课程', value: 3 },
          { label: '调研提交', value: 4 },
          { label: '充值', value: 5 },
          { label: '手工修改', value: 6 },
          { label: '下载指南', value: 7 },
          { label: '新手任务', value: 17 },
          { label: '日常任务', value: 18 },
          { label: '用户禁用', value: 19 },
          { label: '兑换码', value: 27 },
          { label: '下载资讯', value: 34 },
        ],
        operation: 'query',
        params: 'sourceType'
      }
    },
    integralType: () => {
      return {
        type: 'dropdown',
        icon: 'icon-funnel',
        options: [
          { label: '全部', value: null },
          { label: '积分', value: 2 },
          { label: '梅花', value: 1 }
        ],
        operation: 'query'
      }
    }
  },
}

export default scopeConfig
