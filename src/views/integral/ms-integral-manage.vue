<template>
	<ms-table
		:currentPage="searchParams.pageIndex"
		:loading="loading"
		:operationButtons="domConfig.tableButtons"
		:pageSize="searchParams.pageSize"
		:scopeConfig="scopeConfig.show"
    :scopeHeader="scopeConfig.headerShow"
		:tableData="list"
		:tableHeader="domConfig.tableHeader"
		:total="total"
		@current-change="current_change"
		@operation-change="operation_change"
		@size-change="size_change"
    @header-operation="header_operation"
    class="small-table-td"
	>
    <!-- 列表搜索去区域插槽 -->
		<template slot="ms-table-header">
			<div class="slot-search">
				<template v-for="(searchItem, key) in domConfig.listSearch">
					<component
						:index="searchItem.index || ''"
						:is="searchItem.component"
						:key="key"
            :width="searchItem.width || '150px'"
            :model.sync="searchParams[searchItem.model]"
            :label="searchItem.label"
						:operation="searchItem.operation || ''"
						:options="searchItem.options || []"
						:placeholder="searchItem.placeholder || ''"
						:type="searchItem.type || ''"
					></component>
				</template>
        <div class="inlineBlock">
          <el-button @click="handleClick('query')" type="primary" plain icon="el-icon-search">查询</el-button>
          <el-button @click="handleClick('reset')">重置</el-button>
        </div>
			</div>
		</template>
	</ms-table>
</template>

<script>
import tableMixins  from "../common/mixins/table"
import domConfig from "./data/dom-config";
import scopeConfig from "./data/scope-config";

export default {
  name: "ms-integral-manage",
  mixins: [tableMixins],
  data () {
    return {
      domConfig: domConfig, // => dom展示数据
      scopeConfig: scopeConfig, // => 显示信息配置 
      searchParams: { // => 用户列表查询传参
        userName: "",
        createTime: [],
        startDate: "",
        endDate: ""
      }
    };
  },
  methods: {
    apiInit () {
      this.loading = true;
      if (this.searchParams.createTime) {
        this.searchParams.startDate = this.searchParams.createTime[0] || ''
        this.searchParams.endDate = this.searchParams.createTime[1] || ''
      }
      this.api.getUserIntegralTransactionPage(this.searchParams).then(response => {
        this.loading = false
        this.total = response.totalSize || 0;
        this.list = response.data || []
        if (response.status === 200) {
          this.list = response.data.map(v => {
            return {
              ...v,
              integralType: v.integralType === 1 ? '梅花' : '积分'
            }
          })
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
      }).catch(() => this.loading = false)
    }
  }
}
</script>
