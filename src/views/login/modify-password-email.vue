<template>
    <main class="login-main">
        <section class="login">
            <header class="login-header">
                <div class="login-header-title">找回登录密码</div>
                <div class="login-header-nav">
                  <span>通过邮箱重置密码，或</span>
                  <router-link to="/modifyPasswordPhone">手机号重置密码</router-link>
                </div>
            </header>
            <el-form :model="loginForm" 
                     ref="loginRef" 
                     size="medium" 
                     :rules="rules" 
                     class="login-form">
                <el-form-item prop="email">
                    <el-input v-model="loginForm.email" 
                              placeholder="请输入邮箱"></el-input>
                </el-form-item>
            </el-form>
            <footer class="login-footer">
                <!-- <nav>
                  <router-link to="/login">返回登录</router-link>
                </nav> -->
                <el-button type="primary" 
                           @click="login_in" 
                           size="medium"
                           class="login-btn"
                           :loading="loading">确 认</el-button>
            </footer>
        </section>
    </main>
</template>

<script>
export default {
  name: "modify-password-email",
  data () {
    return {
      loading: false,
      loginForm: {
        email: ""
      },
      rules: {
        email: [{ required: true, message: "请输入用户名或邮箱" }]
      }
    };
  },
  methods: {
    login_in () {
      this.$refs["loginRef"].validate(valid => {
        if (valid) {
          this.loading = true;
          this.api
            .reset_password(this.loginForm)
            .then(resp => {
              this.$message({
                message: resp.message,
                type: "success"
              });
              this.loading = false;
              this.$router.push("/login");
            })
            .catch(() => {
              this.loading = false;
            });
        } else {
          return false;
        }
      });
    }
  }
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
@import "../../styles/modules/login.scss";
</style>
