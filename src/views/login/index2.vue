<template>
    <main class="login-main">
        <section class="login">
            <header class="login-header">
                <div class="login-header-title">梅斯后台管理系统</div>
            </header>
            <el-form :model="loginForm" 
                     ref="loginRef" 
                     size="medium" 
                     :rules="rules" 
                     class="login-form">
                <el-form-item prop="username">
                    <el-input v-model="loginForm.username" 
                              placeholder="请输入用户名"></el-input>
                </el-form-item>
                <el-form-item prop="password">
                    <el-input v-model="loginForm.password"  
                              show-password 
                              placeholder="请输入密码"></el-input>
                </el-form-item>
                <div v-show="codeSlideShow" id="codeValidate" style="position: static;"></div>
                <el-form-item style="margin-top:22px" v-show="smsShow" prop="smsCode">
                  <el-input v-model="loginForm.smsCode"
                                maxlength="6"
                                placeholder="请输入短信验证码"></el-input>
                </el-form-item>
            </el-form>
            <footer class="login-footer">
                <nav>
                    <!-- <router-link to="/modify-password-email">忘记密码？</router-link> -->
                </nav>
                <el-button type="primary" 
                           size="medium"
                           class="login-btn"
                           @click="openPopup" 
                           :loading="loading">登 录</el-button>
            </footer>
        </section>

        <div id="captchaPopup"></div>
    </main>
</template>

<script>
import {hex_md5} from "../../utils/md5";
import { Message } from "element-ui";
export default {
  name: "login",
  data () {
    return {
      loading: false,
      loginForm: {
        username: "",
        password: "",
        smsCode: ""
      },
      rules: {
        username: [{ required: true, message: "请输入用户名" }],
        password: [{ required: true, message: "请输入密码" }],
        smsCode: [{ required: true, message: "请输入短信验证码" }],
      },
      loginCaptcha: null,
      codeSlideShow: false,
      smsShow: false,
      token: "",
    };
  },
  mounted() {
    // eslint-disable-next-line no-undef
    // this.loginCaptcha = _dx.Captcha(document.getElementById('captchaPopup'), {
    //     appId: '59d5a5f2850d95faf4c6d1d0d42e366d', //appId，在控制台中“应用管理”或“应用配置”模块获取
    //     style: 'popup',
    //     success: ((token) => {
    //       if (token) {
    //         this.loginCaptcha.hide()
    //         this.loading = true;
    //         if (this.$route.query && this.$route.query.from === 'scrm') {
    //           this.$store.dispatch("Login", {
    //             username: "",
    //             password: "",
    //             projectType: 0,
    //             token: token,
    //             ip: '',
    //             from: this.$route.query.from,
    //             thirdtoken: this.$route.query.thirdtoken
    //           }).then(resp => {
    //             this.loading = false;
    //             this.$router.push(resp);
    //           }).catch(() => {
    //             this.loading = false;
    //           });
    //         } else {
    //           this.$store.dispatch("Login", {
    //             username: this.loginForm.username,
    //             password: hex_md5(hex_md5(this.loginForm.password).substr(8,16)),
    //             projectType: 0,
    //             token: token,
    //             ip: ''
    //           }).then(resp => {
    //             this.loading = false;
    //             this.$router.push(resp);
    //           }).catch(() => {
    //             this.loading = false;
    //           });
    //         }
            
    //       }
    //     })
    // })
    
    // this.loginCaptcha.show()

    // 实例化nvc 对无痕验证进行初始化操作
		var that = this;
    // eslint-disable-next-line
		AWSC.use("nvc", function (state, module) {
			// 初始化 调用module.init进行初始化
			window.nvc = module.init({
				// 应用类型标识。它和使用场景标识（scene字段）一起决定了无痕验证的业务场景与后端对应使用的策略模型。您可以在阿里云验证码控制台的配置管理页签找到对应的appkey字段值，请务必正确填写。
				appkey: "FFFF0N000000000065DC",
				//使用场景标识。它和应用类型标识（appkey字段）一起决定了无痕验证的业务场景与后端对应使用的策略模型。您可以在阿里云验证码控制台的配置管理页签找到对应的scene值，请务必正确填写。
				scene: "nc_message",
				// 二次验证获取人机信息串，跟随业务请求一起上传至业务服务器，由业务服务器进行验签。
				success: function (data) {
					// console.log(data, 'data')
          that.token = data
          that.sendSms();
					// 在需要重置的地方重置
					// that.windowNc.reset();
				},
				// 前端二次验证失败时触发该回调参数
				fail: function (failCode) {
					console("", 'warning', failCode);
				},
				// 前端二次验证加载异常时触发该回调参数。
				error: function (errorCode) {
					console("", 'warning', errorCode);
				}
			});
			that.windowNc = window.nvc;
      if (that.$route.query.from === 'scrm') {
        // this.loginCaptcha.reload()
        // this.loginCaptcha.show()
        that.codeSlideShow = true;
        document.getElementById('codeValidate').innerHTML = '';
        var ncoption = {
          // 声明滑动验证需要渲染的目标ID。
          renderTo: "codeValidate",
        } 
        // console.log(that.windowNc, 'that.windowNc')
        that.windowNc.getNC(ncoption);
      }
		});
  },
  methods: {
    // 验证码逻辑
    sendSms() {
      this.smsShow = true;
      let params = {
        tenant: 100,
        token: this.token,
        username: this.loginForm.username,
        channel: 2,
        type:2
      }
      this.loading = true
      this.api.backStagesSendSms(params).then(response => {
        this.loading = false
        if (response.status !== 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }else {
          Message.success('滑动验证通过，已发送短信验证码')
        }
      }).catch(() => this.loading = false)
    },
    openPopup() {
      this.$refs["loginRef"].validate(valid => {
        if (this.loginForm.username && this.loginForm.password) {
          // this.login_in()
          // this.loginCaptcha.reload()
          // this.loginCaptcha.show()
          this.codeSlideShow = true;
          document.getElementById('codeValidate').innerHTML = '';
          var ncoption = {
            // 声明滑动验证需要渲染的目标ID。
            renderTo: "codeValidate",
          } 
          // console.log(this.windowNc, 'that.windowNc')
          this.windowNc.getNC(ncoption);
          if(valid) {
            this.login_in()
          }
        }
      })
      
    },
    login_in () {
      if (this.$route.query && this.$route.query.from === 'scrm') {
        this.$store.dispatch("Login", {
          username: "",
          password: "",
          projectType: 0,
          token: this.token,
          smsCode: "",
          ip: '',
          from: this.$route.query.from,
          thirdtoken: this.$route.query.thirdtoken,
          channel: 2,
          type:2
        }).then(resp => {
          this.loading = false;
          this.$router.push(resp);
        }).catch(() => {
          this.loading = false;
        });
      } else {
        this.$store.dispatch("Login", {
          username: this.loginForm.username,
          password: hex_md5(hex_md5(this.loginForm.password).substr(8,16)),
          smsCode: this.loginForm.smsCode,
          projectType: 0,
          token: this.token,
          ip: '',
          channel: 2,
          type:2
        }).then(resp => {
          this.loading = false;
          this.$router.push(resp);
        }).catch(() => {
          this.loading = false;
        });
      }

        // this.loading = true;
        // this.$store
        //   .dispatch("Login", {
        //     username: this.loginForm.username,
        //     password: hex_md5(hex_md5(this.loginForm.password).substr(8,16)),
        //     projectType: 0,
        //     token: token,
        //     ip: ''
        //   })
        //   .then(resp => {
        //     this.loading = false;
        //     this.$router.push(resp);
        //   })
        //   .catch(() => {
        //     this.loading = false;
        //     this.loginCaptcha.reload()
        //   });
    }
  }
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
@import "../../styles/modules/login.scss";
</style>

<style>
#codeValidate .nc_wrapper{
  width: 400px !important;
  height: 36px !important;
}
#codeValidate .nc_iconfont.btn_slide{
  height: 34px;
}
</style>
