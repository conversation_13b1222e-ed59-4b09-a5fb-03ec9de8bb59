<template>
    <main class="login-main">
        <section class="login">
            <header class="login-header">
                <div class="login-header-title">找回登录密码</div>
                <div class="login-header-nav">
                  <span>通过您的手机号重置密码，或</span>
                  <router-link to="/modifyPasswordEmail">邮箱重置密码</router-link>
                </div>
            </header>
            <el-form :model="loginForm" 
                     ref="loginRef" 
                     size="medium" 
                     :rules="rules" 
                     class="login-form">
                <el-form-item prop="phone">
                    <el-input v-model="loginForm.phone" 
                              placeholder="请输入手机号"></el-input>
                </el-form-item>
                <el-form-item prop="code">
                    <el-input v-model="loginForm.code" 
                              placeholder="请输入短信验证码">
                        <el-button slot="append"
                                   class="code-btn"
                                   type="primary">获取短信验证码</el-button>
                    </el-input>
                </el-form-item>
            </el-form>
            <footer class="login-footer">
                <!-- <nav>
                  <router-link to="/login">返回登录</router-link>
                </nav> -->
                <el-button type="primary" 
                           @click="login_in" 
                           size="medium"
                           class="login-btn"
                           :loading="loading">下一步</el-button>
            </footer>
        </section>
    </main>
</template>

<script>
export default {
  name: "modify-password-phone",
  data () {
    return {
      loading: false,
      loginForm: {
        phone: "",
        code: ""
      },
      rules: {
        phone: [{ required: true, message: "请输入手机号" }],
        code: [{ required: true, message: "请输入验证码" }],
      }
    };
  },
  methods: {
    login_in () {
      this.$refs["loginRef"].validate(valid => {
        if (valid) {
          this.$router.push('/resetPassword')
        } else {
          return false;
        }
      });
    }
  }
};
</script>

<style>
    .code-btn {
        background-color: #E1E3EA !important;
        border: 1px solid #E1E3EA !important;
    }
</style>

<style rel="stylesheet/scss" lang="scss" scoped>
@import "../../styles/modules/login.scss";
</style>

