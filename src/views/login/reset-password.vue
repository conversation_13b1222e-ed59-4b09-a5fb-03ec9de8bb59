<template>
    <main class="login-main">
        <section class="login">
            <header class="login-header">
                <div class="login-header-title">设置新密码密码</div>
            </header>
            <el-form :model="loginForm" 
                     ref="loginRef" 
                     size="medium" 
                     :rules="rules" 
                     class="login-form">
                <el-form-item prop="password">
                    <el-input v-model="loginForm.password" 
                              placeholder="请输入密码"></el-input>
                </el-form-item>
                <el-form-item prop="twicPassword">
                    <el-input v-model="loginForm.twicPassword" 
                              placeholder="请再次输入密码">
                    </el-input>
                </el-form-item>
            </el-form>
            <footer class="login-footer">
                <el-button type="primary" 
                           @click="login_in" 
                           size="medium"
                           class="login-btn"
                           :loading="loading">确 定</el-button>
            </footer>
        </section>
    </main>
</template>

<script>
export default {
  name: "reset-password",
  data () {
    return {
      loading: false,
      loginForm: {
        phone: "",
        code: ""
      },
      rules: {
        password: [{ required: true, message: "请输入密码" }],
        twicPassword: [{ required: true, message: "请再次输入密码" }],
      }
    };
  },
  methods: {
    login_in () {
      this.$refs["loginRef"].validate(valid => {
        if (valid) {
          this.$router.push('/login')
          // this.loading = true;
          // this.api
          //   .reset_password(this.loginForm)
          //   .then(resp => {
          //     this.$message({
          //       message: resp.message,
          //       type: "success"
          //     });
          //     this.loading = false;
          //     this.$router.push("/login");
          //   })
          //   .catch(() => {
          //     this.loading = false;
          //   });
        } else {
          return false;
        }
      });
    }
  }
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
@import "../../styles/modules/login.scss";
</style>

