<template>
	<ms-table
		:currentPage="searchParams.pageIndex"
		:loading="loading"
		:operationButtons="domConfig.tableButtons"
		:pageSize="searchParams.pageSize"
		:tableData="list"
		:tableHeader="domConfig.tableHeader"
    :scopeConfig="scopeConfig.show"
		:total="total"
    :expandList="expandList"
		@current-change="current_change"
		@operation-change="operation_change"
		@size-change="size_change"
    @header-operation="header_operation"
	>
    <!-- 列表搜索去区域插槽 -->
		<template slot="ms-table-header">
			<div class="slot-search">
				<template v-for="(searchItem, key) in domConfig.listSearch">
					<component
						:index="searchItem.index || ''"
						:is="searchItem.component"
						:key="key"
            :width="searchItem.width || '150px'"
            :model.sync="searchParams[searchItem.model]"
            :label="searchItem.label"
						:operation="searchItem.operation || ''"
						:options="searchItem.options"
						:placeholder="searchItem.placeholder || ''"
						:type="searchItem.type || ''"
            :multiple="searchItem.multiple"
            :disabled="searchItem.disabled" 
            :code="searchItem.code"
					></component>
				</template>
        <el-button @click="handleClick('query')" type="primary" plain icon="el-icon-search">查询</el-button>
        <el-button @click="handleClick('reset')">重置</el-button>
			</div>
		</template>
	</ms-table>
</template>

<script>
import domConfig from "./data/dom-config";
import scopeConfig from "./data/scope-config";
import tableMixins  from "../common/mixins/table"
import MsJobtitleSearch from '@/components/MsCommon/ms-jobtitle-search'
export default {
  name: "ms-recruitment-user",
  mixins: [tableMixins],
  data () {
    return {
      domConfig: domConfig.user,
      scopeConfig: scopeConfig,
      searchParams: { // => 列表查询传参
        title: "",
        userRecordSchool: "",
        startBeginAt: "",
        startStopAt: "",
        createTime: []
      },
      expandList: [
        { label: '电话', property: 'userTell', colSpan: 6 },
        { label: 'E-mail', property: 'userEmail', colSpan: 6  },
        { label: '工作年限', property: 'userWorkYear', colSpan: 6  },
        { label: '要求薪资', property: 'userExpTreatment', colSpan: 6  },
        { label: '教育背景', property: 'userEducation', colSpan: 24, type: 'htm' },
        { label: '工作经验', property: 'userWork', colSpan: 24, type: 'htm' },
      ]
    }
  },
  components: {
    MsJobtitleSearch
  },
  methods: {
    init () {
      this.loading = true;
      this.dialog = false;
      if (this.searchParams.createTime) {
        this.searchParams.startBeginAt = this.searchParams.createTime[0] || ''
        this.searchParams.startStopAt = this.searchParams.createTime[1] || ''
      }
      let searchParams = {...this.searchParams}
      this.api.getRecruitmentUserPage(searchParams).then(response => {
        this.loading = false
        this.total = response.totalSize || 0;
        this.list = response.data || []
      }).catch(() => this.loading = false)
    },
    operation_change (val) {
      switch (val.operation.way) {
        case "delete":
          this.$confirm('此操作将永久删除招聘信息，是否继续', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            let params = {
              ids: [val.model.id]
            }
            this.api.delRecruitmentUser(params).then(response => {
              if(response.status === 200) {
                this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
              } else {
                this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
              }
              this.init()
            })
          })
          break;
        case "download":
          var fileName = val.model && val.model.userAttachment;
          var suffix = fileName.substr(fileName.lastIndexOf(".") + 1)
          var writeArr = ['txt', 'jpg', 'jpeg', 'png', 'gif']
          if (writeArr.indexOf(suffix) !== -1) {
            window.open(fileName, '_blank')
          } else {
            window.location.href = val.model.userAttachment
          }
          break;
        default: break;
      }
    },
  }
};
</script>
