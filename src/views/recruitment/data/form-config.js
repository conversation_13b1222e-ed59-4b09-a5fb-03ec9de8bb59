const formConfig = {
  formField: [
    {
      label: '标题',
      prop: 'title',
      colSpan: 12,
      component: 'ms-input'
    },
    {
      label: '排序',
      prop: 'rank',
      colSpan: 12,
      component: 'ms-input-number'
    },
    {
      label: '待遇',
      prop: 'treatment',
      colSpan: 12,
      component: 'ms-input'
    },
    {
      label: '招聘人数',
      prop: 'userNumber',
      colSpan: 12,
      component: 'ms-input'
    },
    {
      label: '截止日期',
      prop: 'endTime',
      colSpan: 12,
      component: 'ms-picker',
      type: 'date'
    },
    {
      label: '工作地点',
      prop: 'workplace',
      colSpan: 12,
      component: 'MsAreaCascader',
      multiple: true
    },
    {
      label: '全职/兼职',
      prop: 'jobType',
      colSpan: 12,
      component: 'ms-radio',
      options: [
        { label: '全职', value: 0 },
        { label: '兼职', value: 1 }
      ]
    },
    {
      label: '内容',
      prop: 'content',
      colSpan: 24,
      component: 'ms-editor'
    }
  ],
  rule: {
    title: [
      { required: true, message: "请输入标题", trigger: 'blur' }
    ],
    content: [
      { required: true, message: "请输入内容", trigger: 'blur' }
    ]
  }
}

export default formConfig;
