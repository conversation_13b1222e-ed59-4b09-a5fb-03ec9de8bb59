const domConfig = {
  manager: {
    listSearch: [
      {
        label: '招聘标题',
        placeholder: '请输入',
        model: 'title',
        component: 'ms-input'
      },
      {
        label: '创建时间',
        placeholder: '请选择时间段',
        model: 'createTime',
        component: 'ms-picker',
        type: 'daterange',
        width: '240px'
      }
    ],
    tableHeader: [
      { label: 'ID', property: 'id', sortable: true, width: 60 },
      { label: '招聘标题', property: 'title', width: '250'},
      { label: '招聘人数', property: 'userNumber' },
      { label: '点击量', property: 'clickNumber', sortable: true},
      { label: '排序', property: 'rank', sortable: true },
      { label: '创建人', property: 'createdName'},
      { label: '创建时间', property: 'createdTime', width: 130, sortable: true }
    ],
    tableButtons: [
      {
        label: '编辑',
        icon: '',
        role: '',
        operation: 'edit',
        component: 'recruitment-operation',
        way: 'page',
        type: 'primary',
        path: 'recruitment-operation',
        params: ['id']
      },
      {
        label: '',
        icon: '',
        role: '',
        operation: 'editStatus',
        component: 'msRecruitmentOperation',
        way: 'dialog',
        field: 'status',
        rule: {
          1: { label: '去审', type: '', operation: 'toreview' },
          0: { label: '审核', type: 'success', operation: 'approval' }
        }
      },
      {
        label: '删除',
        icon: '',
        role: '',
        type: 'danger',
        way: 'dialog',
        operation: 'delete',
        component: 'msRecruitmentOperation',
      }
    ],
    soltButtons: [
      { 
        label: '添加招聘', 
        icon: 'el-icon-plus',
        type: 'primary', 
        operation: 'created',
        component: 'recruitment-operation',
        way: 'page',
        path: 'recruitment-operation',
        params: ['id']
      },
      { 
        label: '批量审核', 
        type: 'primary',
        operation: 'approval',
        component: 'msRecruitmentOperation',
        way: 'batch'
      },
      { 
        label: '批量去审', 
        type: 'primary',
        operation: 'toreview',
        component: 'msRecruitmentOperation',
        way: 'batch'
      },
      { 
        label: '批量删除',
        icon: 'el-icon-close',
        type: 'info',
        operation: 'delete',
        component: 'msRecruitmentOperation',
        way: 'batch'
      }
    ]
  },
  user: {
    listSearch: [
      {
        label: '应聘岗位',
        placeholder: '请输入',
        model: 'title',
        component: 'MsJobtitleSearch'
      },
      {
        label: '最高学历',
        placeholder: '请选择',
        model: 'userRecordSchool',
        component: 'ms-select-local',
        code: 'education'
      },
      {
        label: '应聘时间',
        placeholder: '请选择时间段',
        model: 'createTime',
        component: 'ms-picker',
        type: 'daterange',
        width: '240px'
      }
    ],
    tableHeader: [
      { label: '应聘岗位', property: 'jobTitle' },
      { label: '姓名', property: 'userName' },
      { label: '学位', property: 'userRecordSchool' },
      { label: '性别', property: 'userSex' },
      { label: '出生日期', property: 'userBirth' },
      { label: '学校', property: 'userSchool' },
      { label: '应聘时间', property: 'createdTime', width: 130, sortable: true }
    ],
    tableButtons: [
      {
        label: '下载简历',
        icon: '',
        role: '',
        type: 'primary',
        way: 'download',
        showField: 'userAttachment',
        tdWidth: 80
      },
      
      {
        label: '删除',
        icon: '',
        role: '',
        type: 'danger',
        way: 'delete',
        tdWidth: 80
      }
    ],
    soltButtons: []
  }
}

export default domConfig;
