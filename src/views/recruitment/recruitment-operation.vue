<template>
  <section class="form-tab" v-loading="getLoading">
    <!-- 表单内容 -->
    <el-form ref="submitRef"
          class="rule-form"
          :model="submitData"
          :rules="formConfig.rule"
          label-width="80px">
      <el-row>
        <template v-for="(item, index) in formConfig.formField">
          <el-col :key="index"
                  :span="item.colSpan">
            <el-form-item :prop="item.prop"
                          :label="item.label">
              <component :is="item.component"
                        :model.sync="submitData[item.prop]"
                        v-model="submitData[item.prop]"
                        :width="item.width || '100%'"
                        :disabled="item.disabled"
                        :type="item.type"
                        :multiple="item.multiple"
                        :options="item.options"
                        @bindData="bindData">
              </component>
            </el-form-item>
          </el-col>
        </template>
      </el-row>
    </el-form>
    <!-- 弹出层 -->
    <el-dialog :visible.sync="dialog" 
               closeable 
               :show-close="false"
               :close-on-click-modal="false"
               width="50%"
               :title="dialogTitle">
      <component :is="dialogComponent" 
                   :model="dialogInfo" 
                   :operation="dialogOperation" 
                   @close="dialog = !dialog" 
                   @up-date="init" 
                   v-if="dialog"></component>
    </el-dialog>
    <!-- 提交按钮 -->
    <footer-tool-bar v-loading="buttonLoading">
      <template slot="tool-content">
        <el-button plain v-show="dataId" @click="info_operation('approval')">{{submitData.status === 1 ? '去审' : '审核'}}</el-button>
        <el-button type="primary" @click="info_operation('save')">保存</el-button>
        <el-button type="info" @click="info_operation('back')">返回</el-button>
      </template>
    </footer-tool-bar>
  </section>
</template>

<script>
import FooterToolBar from '@/components/ToolBar/footer-tool-bar'
import formConfig from './data/form-config' 
import MsAreaCascader from '@/components/MsCommon/ms-area-cascader'
import MsEditor from '@/components/MsEditor'
import recruitmentMixin from "./recruitment-mixin"
import { mapGetters } from "vuex";
export default {
  name: "recruitment-operation",
  mixins: [recruitmentMixin],
	data () {
		return {
      buttonLoading: false,
      getLoading: false,
      formConfig: formConfig,
      dataId: this.$route.query.id ? this.$route.query.id : 0,

      submitData: { 
        title: "",
        userNumber: "",
        workplace: [],
        content: "",
        endTime: "",
        treatment: "",
        rank: 0,
        status: 0,
        jobType: 0
      },
      dialogComponent: '',
      dialog: false,
      dialogTitle: '',
      dialogOperation: '',
      dialogInfo: {}
		}
  },
  components: {
    FooterToolBar,
    MsAreaCascader,
    MsEditor
  },
  computed: {
    ...mapGetters(["info"])
  },
  created() {
    this.init()
  },
  methods: {
    init() {
      this.dialog = false
      let id = this.dataId
      if(id !== 0) {
        this.getLoading = true;
        this.submitData.id = id;
        this.api.getRecruitmentJob({id: id}).then( response => {
          this.getLoading = false;
          if(response.status === 200) {
            let res = response.data
            this.submitData = {
              ...this.submitData,
              ...res
            }
          } else {
            this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
          }
        }).catch(() => {
          this.getLoading = false;
        })
      } 
    },
    info_operation(val) {
      switch (val) {
        case 'save': 
          this.$refs["submitRef"].validate( valid => {
            if (valid) {
              this.dataId ? this.updateRecruitment() : this.createRecruitment()
            }
          })
          break;
        case 'back':
          this.$router.back();
          break;
        case 'approval':
          var params = {
            way: 'dialog',
            operation: this.submitData.status === 1 ? 'toreview' : 'approval',
            component: 'msRecruitmentOperation',
            data: this.submitData
          }
          this.dialog = true;
          this.dialogInfo = params.data
          this.dialogOperation = params.operation;
          this.dialogComponent = params.component;
          this.dialogTitle = params.title;
          break;
        default: break;
      }
    },
    createRecruitment() {
      this.buttonLoading = true;
      let params = {
        userId: this.info.userId,
        username: this.info.userName,
        ...this.submitData
      }
      this.api.insertRecruitmentJob(params).then(response => {
        this.buttonLoading = false
        if(response.status === 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
          this.$router.back()
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
      }).catch(() => this.buttonLoading = false)
    },
    updateRecruitment() {
      this.buttonLoading = true;
      let params = {
        userId: this.info.userId,
        username: this.info.userName,
        ...this.submitData
      }
      this.api.updateRecruitmentJob(params).then(response => {
        this.buttonLoading = false
        if(response.status === 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
          this.$router.back()
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
      }).catch(() => this.buttonLoading = false)
    },
    bindData(val) {
      let workplace = []
      if (val && val.length > 0) {
        val.forEach(v => {
          if ( v.level === 2 ) {
            workplace.push({
              id: v.value,
              cityName: v.label
            })
          }
        });
      }
      this.submitData.workplace = workplace
    }
  }
}
</script>
