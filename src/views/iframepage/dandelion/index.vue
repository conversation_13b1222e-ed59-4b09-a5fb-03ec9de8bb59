<template>
  <section>
        <iframe :src="iframeSrc" class="iframe-style"  frameborder=0></iframe>
  </section>
</template>
<script>
import serveUrl from '@/store/data/serveUrl.js'
export default {
    name: "dandelion",
    data () {
        return {
            iframeSrc: `${serveUrl['dandelion']}?token=${this.$store.getters.token}&project_id=${this.$store.getters.projectId}`
        }
    }
}
</script>

<style scoped>
    .iframe-style {
        width: 100%;
        height: calc(100vh - 100px); 
    }
</style>
