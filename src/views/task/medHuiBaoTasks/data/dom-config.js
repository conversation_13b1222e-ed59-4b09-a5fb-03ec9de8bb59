const domConfig = {
    listSearch: [
      {
        label: '任务标题',
        model: 'taskTitle',
        component: 'ms-input'
      },
      {
        label: '任务类型',
        model: 'taskType',
        component: 'ms-select',
        options: [
          { label: '全部', value: '' },
          { label: '调研任务', value: 'survey' },
          { label: '直播任务', value: 'live' },
          { label: '阅读任务', value: 'reading' },
          { label: '关注任务', value: 'follow' },
          { label: '邀请任务', value: 'invitation' },
          { label: '其他任务', value: 'other' }
        ]
      },
      {
        label: '项目负责人',
        model: 'projectManager',
        component: 'ms-input'
      },
      {
        label: '创建时间',
        placeholder: '请选择时间段',
        model: 'createTime',
        component: 'ms-picker',
        type: 'daterange',
        width: '240px'
      },
    ],
    tableHeader: [
      { label: 'ID', property: 'id', width: '60' },
      { label: '任务标题', property: 'taskTitle', width: '150' },
      { label: '任务说明', property: 'taskDescription', width: '200' },
      { label: '任务类型', property: 'taskType', width: '100' },
      { label: '任务时限', property: 'taskDuration', width: '100' },
      { label: '限量任务', property: 'taskLimits', width: '120' },
      { label: '完成数量限制', property: 'quantityLimit', sortable: true, width: '100' },
      { label: '真实获取积分', property: 'actualPoints', sortable: true, width: '100' },
      { label: '显示积分文案', property: 'pointsDisplayText', width: '120' },
      { label: 'URL链接', property: 'urlLink', width: '150' },
      { label: '项目负责人', property: 'projectManager', width: '100' },
      { label: '创建时间', property: 'createdTime', width: '130'  },
      { label: '创建人', property: 'createdBy', width: '80'  }
    ],
    soltButtons: [
      { 
        label: '添加医汇宝任务', 
        type: 'primary', 
        icon: 'el-icon-plus',
        operation: 'created',
        title: '添加医汇宝任务',
        component: 'msMedHuiBaoTaskOperation',
        way: 'dialog',
        width: '70%'
      },
    ],
    tableButtons: [
      {
        label: '编辑',
        way: 'dialog',
        type: 'primary',
        operation: 'update',
        component: 'msMedHuiBaoTaskOperation',
        title: '编辑医汇宝任务',
        width: '70%'
      },
      {
        label: '删除',
        way: 'dialog',
        type: 'danger',
        operation: 'delete',
        component: 'msMedHuiBaoTaskDelete'
      }
    ]
  }
  
  export default domConfig;
