const scopeConfig = {
    show: {
      taskType: () => {
        return {
          type: 'status-char',
          rule: {
            'survey': { label: '调研任务' },
            'live': { label: '直播任务' },
            'reading': { label: '阅读任务' },
            'follow': { label: '关注任务' },
            'invitation': { label: '邀请任务' },
            'other': { label: '其他任务' }
          }
        }
      },
      taskDuration: () => {
        return {
          type: 'status-char',
          rule: {
            'long_term': { label: '长期任务' },
            'time_limited': { label: '限时任务' }
          }
        }
      },
      taskLimits: () => {
        return {
          type: 'status-char',
          rule: {
            'quantity_limit': { label: '项目有完成数量限制' },
            'unlimited_points': { label: '无限制提交有积分' },
            'once_points': { label: '无限制只有一次积分' }
          }
        }
      },
      actualPoints: () => {
        return {
          type: 'number',
          suffix: '分'
        }
      },
      urlLink: () => {
        return {
          type: 'link',
          linkText: '预览链接',
          target: '_blank'
        }
      }
    },
    headerShow: {
      taskType: () => {
        return {
          type: 'dropdown',
          icon: 'icon-funnel',
          options: [
            { label: '全部', value: null },
            { label: '调研任务', value: 'survey' },
            { label: '直播任务', value: 'live' },
            { label: '阅读任务', value: 'reading' },
            { label: '关注任务', value: 'follow' },
            { label: '邀请任务', value: 'invitation' },
            { label: '其他任务', value: 'other' }
          ],
          operation: 'query'
        }
      },
      taskDuration: () => {
        return {
          type: 'dropdown',
          icon: 'icon-funnel',
          options: [
            { label: '全部', value: null },
            { label: '长期任务', value: 'long_term' },
            { label: '限时任务', value: 'time_limited' }
          ],
          operation: 'query'
        }
      }
    }
  }
  
  export default scopeConfig;
