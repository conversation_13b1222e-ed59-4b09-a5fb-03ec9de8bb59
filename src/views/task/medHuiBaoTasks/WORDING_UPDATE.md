# 任务时限文案更新说明

## 📋 更新概述

对医汇宝任务模块中"任务时限"字段的文案进行了优化，使其更加简洁明了。

## ✅ 文案变更

### 任务时限选项文案修改

| 原文案 | 新文案 |
|--------|--------|
| 长期项目 | 长期任务 |
| 起始-截止时间项目 | 限时任务 |

## 📄 修改的文件

### 1. 组件文件
- `src/views/task/medHuiBaoTasks/tool/ms-medhuibao-task-operation.vue`
  - 更新表单中的单选按钮文案

### 2. 配置文件
- `src/views/task/medHuiBaoTasks/data/scope-config.js`
  - 更新`show.taskDuration`中的标签文案
  - 更新`headerShow.taskDuration`中的选项文案

### 3. 文档文件
- `src/views/task/medHuiBaoTasks/README.md`
  - 更新任务时限字段说明
- `src/views/task/medHuiBaoTasks/TEST_CHECKLIST.md`
  - 更新测试项目中的文案

## 🔍 修改详情

### 表单组件修改
```vue
<!-- 修改前 -->
<el-radio label="long_term">长期项目</el-radio>
<el-radio label="time_limited">起始-截止时间项目</el-radio>

<!-- 修改后 -->
<el-radio label="long_term">长期任务</el-radio>
<el-radio label="time_limited">限时任务</el-radio>
```

### 配置文件修改
```javascript
// 修改前
rule: {
  'long_term': { label: '长期项目' },
  'time_limited': { label: '起始-截止时间项目' }
}

// 修改后
rule: {
  'long_term': { label: '长期任务' },
  'time_limited': { label: '限时任务' }
}
```

## ✨ 优化效果

1. **简洁性**: 文案更加简洁，减少冗余词汇
2. **一致性**: 与"任务"主题保持一致
3. **易读性**: 更容易理解和记忆
4. **用户体验**: 提升界面的专业性和易用性

## 🧪 测试要点

- [ ] 表单中任务时限选项显示正确文案
- [ ] 列表页面任务时限列显示正确文案
- [ ] 筛选下拉框显示正确文案
- [ ] 数据保存和读取功能正常
- [ ] 现有数据显示不受影响

## 📝 注意事项

1. **数据兼容性**: 底层数据值(`long_term`, `time_limited`)保持不变，仅修改显示文案
2. **API兼容性**: 不影响任何API接口的参数和返回值
3. **向后兼容**: 现有数据和功能完全兼容

## 🚀 部署说明

此次修改仅涉及前端显示文案，无需后端配合，可直接部署。部署后用户将看到更新后的文案，但所有功能和数据处理逻辑保持不变。
