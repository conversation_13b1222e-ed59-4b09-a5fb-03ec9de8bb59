# 医汇宝任务管理模块

## 模块概述
医汇宝任务管理模块用于管理六种不同类型的任务，每种任务都有特定的内容要求和表单字段。

## 任务类型

### 1. 调研任务 (Survey Tasks)
- **功能**: 实现按标题模糊搜索功能
- **API**: 使用与`/form`页面完全相同的列表接口 (`getFormMaterialList`)
- **搜索参数**: 仅传递 `value` 参数
- **表单字段**: 关联内容搜索 + URL链接输入框（带预览按钮）
- **URL生成**: 直接使用接口返回的`url`字段

### 2. 直播任务 (Live Stream Tasks)
- **功能**: 实现按标题模糊搜索功能
- **API**: 使用与`/medsciLive`页面完全相同的列表接口 (`medsciLiveList`)
- **搜索参数**: 仅传递 `searchTitle` 参数
- **表单字段**: 关联内容搜索 + URL链接输入框（带预览按钮）
- **URL生成**: 使用`serveUrl.live + encodeId`拼接

### 3. 阅读任务 (Reading Tasks)
- **功能**: 实现按标题模糊搜索功能
- **API**: 使用与`/article`页面完全相同的列表接口 (`getArticlePage`)
- **搜索参数**: 传递 `value` 参数和 `isTitle: "2"` 参数
- **表单字段**: 关联内容搜索 + URL链接输入框（带预览按钮）
- **URL生成**: 使用`serveUrl.article + encodeId`拼接

### 4. 关注任务 (Follow Tasks)
- **功能**: 无需内容搜索
- **表单字段**: 仅需二维码图片上传字段

### 5. 邀请任务 (Invitation Tasks)
- **功能**: 无需内容输入或文件上传
- **表单字段**: 基础任务信息

### 6. 其他任务 (Other Tasks)
- **功能**: 仅需URL链接输入
- **表单字段**: URL链接输入字段

## 表单字段说明

### a. 任务时限 (Task Duration)
- **类型**: 单选按钮
- **选项1**: 长期任务 - 无日期字段
- **选项2**: 限时任务 - 显示开始/结束日期选择器

### b. 限量任务 (Task Limits)
- **类型**: 下拉选择
- **选项**:
  - 项目有完成数量限制 - 显示数量输入字段
  - 无限制提交有积分 - 无额外字段
  - 无限制只有一次积分 - 无额外字段

### c. 项目备注 (Project Notes)
- **类型**: 文本域字段
- **用途**: 内部备注

### d. 项目负责人 (Project Manager)
- **类型**: 文本输入或用户选择字段

### e. 任务标题 (Task Title)
- **类型**: 文本输入字段
- **验证**: 必填

### f. 任务说明 (Task Description)
- **类型**: 文本输入
- **限制**: 20字符限制
- **显示**: 在列表视图中显示在标题下方

### g. 真实获取积分数量 (Actual Points Earned)
- **类型**: 数字输入字段
- **允许**: 0积分
- **精度**: 1位小数 (例如: 1.5, 2.0)
- **验证**: 最小值0，合理的最大值限制

### h. 显示积分文案 (Points Display Text)
- **类型**: 文本输入
- **用途**: 向用户显示的自定义积分描述

## 业务逻辑说明

### 特殊业务逻辑:
- **邀请任务**: 用户可重复完成并每次获得积分
- **调研任务**: 可重复访问但仅首次完成获得积分
- **调研任务2**: 仅可完成一次获得积分（需实现完成跟踪）

## 文件结构

```
src/views/task/medHuiBaoTasks/
├── ms-medhuibao-tasks.vue          # 主列表页面
├── medhuibao-tasks-mixin.js        # 混入文件
├── data/
│   ├── dom-config.js               # DOM配置
│   └── scope-config.js             # 作用域配置
├── components/
│   └── ms-content-search.vue       # 内容搜索组件
├── tool/
│   ├── ms-medhuibao-task-operation.vue  # 任务操作组件
│   └── ms-medhuibao-task-delete.vue     # 任务删除组件
└── README.md                       # 说明文档
```

## API接口

### 主要接口:
- `getMedHuiBaoTaskPage` - 获取任务分页列表
- `getMedHuiBaoTaskById` - 根据ID获取任务详情
- `saveMedHuiBaoTask` - 保存新任务
- `updateMedHuiBaoTaskById` - 更新任务
- `deleteMedHuiBaoTaskById` - 删除任务

### 内容搜索接口:
- `getFormMaterialList` - 调研内容搜索（与/form页面相同）
- `medsciLiveList` - 直播内容搜索（与/medsciLive页面相同）
- `getArticlePage` - 文章内容搜索（与/article页面相同）

### URL生成规则:
- **调研任务**: 直接使用接口返回的`url`字段
- **直播任务**: `serveUrl.live + encodeId`
- **阅读任务**: `serveUrl.article + encodeId`

## 实现要求

1. **代码结构**: 遵循现有的 `src/views/task/taskManage/ms-task-manage.vue` 模式
2. **API集成**: 实现内容搜索API，支持模糊搜索和类型过滤
3. **表单验证**: 必填字段验证、字符限制验证、数字验证
4. **文件上传**: 使用现有项目上传模式实现二维码图片上传
5. **条件UI逻辑**: 根据选择的任务类型和时限显示/隐藏表单字段
6. **数据结构**: 初期使用模拟/测试数据，直到实际后端接口定义完成

## 使用说明

1. 导入模块到路由配置
2. 确保API服务已正确配置
3. 根据实际需求调整表单验证规则
4. 测试所有任务类型的创建和编辑功能
