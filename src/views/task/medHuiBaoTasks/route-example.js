// 路由配置示例 - 将此配置添加到主路由文件中

export const medHuiBaoTasksRoute = {
  path: '/medhuibao-tasks',
  name: 'MedHuiBaoTasks',
  component: () => import('@/views/task/medHuiBaoTasks/ms-medhuibao-tasks.vue'),
  meta: {
    title: '医汇宝任务管理',
    icon: 'el-icon-s-order',
    requiresAuth: true,
    permissions: ['medhuibao:task:view']
  }
}

// 如果需要添加到现有的任务管理菜单下，可以这样配置：
export const taskManagementRoutes = {
  path: '/task',
  name: 'TaskManagement',
  component: Layout,
  meta: {
    title: '任务管理',
    icon: 'el-icon-s-order'
  },
  children: [
    {
      path: 'manage',
      name: 'TaskManage',
      component: () => import('@/views/task/taskManage/ms-task-manage.vue'),
      meta: {
        title: '推广任务管理',
        permissions: ['task:manage:view']
      }
    },
    {
      path: 'record',
      name: 'TaskRecord',
      component: () => import('@/views/task/taskRecord/ms-task-record.vue'),
      meta: {
        title: '任务记录',
        permissions: ['task:record:view']
      }
    },
    {
      path: 'medhuibao',
      name: 'MedHuiBaoTasks',
      component: () => import('@/views/task/medHuiBaoTasks/ms-medhuibao-tasks.vue'),
      meta: {
        title: '医汇宝任务',
        permissions: ['medhuibao:task:view']
      }
    }
  ]
}
