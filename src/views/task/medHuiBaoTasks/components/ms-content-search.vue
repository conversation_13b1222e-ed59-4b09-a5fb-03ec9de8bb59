<template>
  <div class="content-search">
    <!-- 内容搜索选择器 -->
    <el-select
      v-model="selectedContent"
      filterable
      remote
      clearable
      :loading="loading"
      :remote-method="remoteSearch"
      :placeholder="placeholder"
      style="width: 100%"
      @change="handleChange"
      value-key="id"
    >
      <el-option
        v-for="item in contentList"
        :key="item.id"
        :label="item.title"
        :value="item"
      >
        <span style="float: left">{{ item.title }}</span>
        <span style="float: right; color: #8492a6; font-size: 13px">{{ item.type }}</span>
      </el-option>
    </el-select>

    <!-- URL链接输入框 - 仅对调研、直播、阅读任务显示 -->
    <div v-if="showUrlInput" style="margin-top: 10px;">
      <el-input
        v-model="urlLink"
        placeholder="请输入或选择内容后自动填充URL链接"
      >
        <el-button
          slot="append"
          icon="el-icon-view"
          :disabled="!urlLink"
          @click="previewUrl"
        >
          预览
        </el-button>
      </el-input>
    </div>
  </div>
</template>

<script>
import serveUrl from '@/store/data/serveUrl.js'

export default {
  name: 'ms-content-search',
  props: {
    contentType: {
      type: String,
      required: true,
      validator: value => ['survey', 'live', 'reading'].includes(value)
    },
    placeholder: {
      type: String,
      default: '请输入标题关键字搜索内容'
    },
    value: {
      type: Object,
      default: null
    },
    urlValue: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      loading: false,
      contentList: [],
      selectedContent: null,
      urlLink: ''
    }
  },
  computed: {
    showUrlInput() {
      return ['survey', 'live', 'reading'].includes(this.contentType)
    }
  },
  watch: {
    value: {
      handler(val) {
        this.selectedContent = val
      },
      immediate: true
    },
    urlValue: {
      handler(val) {
        this.urlLink = val
      },
      immediate: true
    }
  },
  methods: {
    remoteSearch(query) {
      if (query && query.length >= 2) {
        this.loading = true
        this.searchContent(query)
      } else {
        this.contentList = []
      }
    },
    
    async searchContent(title) {
      try {
        let response
        let params

        // 根据内容类型调用不同的API，使用不同的参数名
        switch (this.contentType) {
          case 'survey':
            // 调研任务使用表单列表API，参数title改为value
            params = {
              value: title,
              pageIndex: 1,
              pageSize: 20
            }
            response = await this.api.getFormMaterialList(params)
            break
          case 'live':
            // 直播任务使用直播列表API，参数title改为searchTitle
            params = {
              searchTitle: title,
              pageIndex: 1,
              pageSize: 20
            }
            response = await this.api.medsciLiveList(params)
            break
          case 'reading':
            // 阅读任务使用文章列表API，参数title改为value，并添加isTitle: "2"
            params = {
              value: title,
              isTitle: "2",
              pageIndex: 1,
              pageSize: 20
            }
            if(window.location.href.includes('.medon.com.cn') || window.location.href.includes('localhost')) {
              params.createdBy = 5155447
            }
            response = await this.api.getArticlePage(params)
            break
          default:
            throw new Error('Unsupported content type')
        }

        this.loading = false
        this.contentList = this.formatResponseData(response, this.contentType)

        if (response.status !== 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
      } catch (error) {
        this.loading = false
        // Fallback to mock data if API is not available
        const mockResponse = this.getMockData(title, this.contentType)
        this.contentList = mockResponse.data || []
        console.warn('API not available, using mock data:', error)
      }
    },
    
    getMockData(title, type) {
      // Mock data for different content types
      const mockData = {
        survey: [
          { id: 1, title: `${title}相关调研问卷1`, type: '调研', description: '医学调研问卷' },
          { id: 2, title: `${title}相关调研问卷2`, type: '调研', description: '临床调研问卷' }
        ],
        live: [
          { id: 1, title: `${title}相关直播1`, type: '直播', description: '医学直播课程' },
          { id: 2, title: `${title}相关直播2`, type: '直播', description: '专家讲座直播' }
        ],
        reading: [
          { id: 1, title: `${title}相关文章1`, type: '阅读', description: '医学文献' },
          { id: 2, title: `${title}相关文章2`, type: '阅读', description: '临床指南' }
        ]
      }
      
      return {
        status: 200,
        data: mockData[type] || [],
        totalSize: mockData[type].length || 0
      }
    },
    
    // 格式化不同API返回的数据为统一格式
    formatResponseData(response, type) {
      let formattedData = []

      switch (type) {
        case 'survey':
          // 处理调研数据
          formattedData = (response.data || []).map(item => ({
            id: item.id,
            title: item.templateName || item.templateSurveyName,
            type: '调研',
            url: item.url, // 直接使用接口返回的url字段
            encodeId: item.encodeId,
            description: item.description || ''
          }))
          break
        case 'live':
          // 处理直播数据
          formattedData = (response.data || []).map(item => ({
            id: item.id,
            title: item.name,
            type: '直播',
            encodeId: item.encodeId,
            description: item.description || ''
          }))
          break
        case 'reading':
          // 处理文章数据
          formattedData = (response.data || []).map(item => ({
            id: item.id,
            title: item.title,
            type: '文章',
            encodeId: item.encodeId,
            description: item.description || ''
          }))
          break
      }

      return formattedData
    },

    // 根据内容类型和选中内容生成URL
    generateUrl(content) {
      if (!content) return ''

      switch (this.contentType) {
        case 'survey':
          // 调研任务直接使用接口返回的url字段
          return content.url || ''
        case 'live':
          // 直播任务使用serveUrl.js中的live配置
          return `${serveUrl.live}${content.encodeId}`
        case 'reading':
          // 阅读任务使用serveUrl.js中的article配置
          return `${serveUrl.article}${content.encodeId}`
        default:
          return ''
      }
    },

    // 预览URL
    previewUrl() {
      if (this.urlLink) {
        window.open(this.urlLink, '_blank')
      }
    },

    handleChange(value) {
      this.$emit('input', value)

      // 如果选择了内容，自动生成URL
      if (value) {
        this.urlLink = this.generateUrl(value)
      } else {
        this.urlLink = ''
      }

      // 发送URL和内容变更事件
      this.$emit('change', value)
      this.$emit('url-change', this.urlLink)
    }
  }
}
</script>

<style scoped>
.content-search {
  width: 100%;
}
</style>
