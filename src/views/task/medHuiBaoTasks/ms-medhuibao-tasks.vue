<template>
	<ms-table
		:currentPage="searchParams.pageIndex"
		:loading="loading"
		:operationButtons="domConfig.tableButtons"
		:pageSize="searchParams.pageSize"
		:scopeConfig="scopeConfig.show"
        :scopeHeader="scopeConfig.headerShow"
		:tableData="list"
		:tableHeader="domConfig.tableHeader"
		:total="total"
		@current-change="current_change"
		@operation-change="operation_change"
		@size-change="size_change"
        @header-operation="header_operation"
        class="small-table-td"
	>
    <!-- 列表搜索区域插槽 -->
		<template slot="ms-table-header">
			<div class="slot-search">
				<template v-for="(searchItem, key) in domConfig.listSearch">
					<component
						:index="searchItem.index || ''"
						:is="searchItem.component"
						:key="key"
                        :width="searchItem.width || '150px'"
                        :model.sync="searchParams[searchItem.model]"
                        :label="searchItem.label"
						:operation="searchItem.operation || ''"
						:options="searchItem.options || []"
						:placeholder="searchItem.placeholder || ''"
						:type="searchItem.type || ''"
                        :multiple="searchItem.multiple"
                        :disabled="searchItem.disabled"
                        :code="searchItem.code"
					></component>
				</template>
                <div class="inlineBlock">
                    <el-button @click="handleClick('query')" type="primary" plain icon="el-icon-search">查询</el-button>
                    <el-button @click="handleClick('reset')">重置</el-button>
                </div>
        
			</div>
			<div class="slot-button">
				<template v-for="(item, index) in domConfig.soltButtons">
					<el-button :key="index" 
                     :type="item.type" 
                     size="mini"
                     :icon="item.icon"
                     @click="operation_change({operation: item})"
                     :disabled="item.roleDisabled"
                     plain>{{ item.label }}</el-button>
				</template>
			</div>
			<el-dialog :visible.sync="dialog" 
                 closeable 
                 show-close
                 :close-on-click-modal="false"
                 :width="dialogWidth"
                 :title="dialogTitle">
				<component :is="dialogComponent" 
                   :model="scopeInfo" 
                   :operation="dialogOperation" 
                   @close="dialog = !dialog" 
                   @up-date="init" 
                   v-if="dialog"></component>
			</el-dialog>
		</template>
	</ms-table>
</template>

<script>
import medHuiBaoTasksMixin from "./medhuibao-tasks-mixin"
import tableMixins from "../../common/mixins/table"
export default {
    name: "ms-medhuibao-tasks",
    mixins: [tableMixins, medHuiBaoTasksMixin],
    data () {
        return {}
    },
    methods: {
        apiInit (params) {
            let searchParams = {...params}
            if (searchParams.createTime) {
                searchParams.statisticsStartTime = searchParams.createTime[0] || ''
                searchParams.statisticsEndTime = searchParams.createTime[1] || ''
            }
            // Mock API call - replace with actual API when available
            this.api.getMedHuiBaoTaskPage(searchParams).then(response => {
                this.loading = false
                this.total = response.totalSize || 0;
                this.list = response.data || []
                if (response.status !== 200) {
                    this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
                }
            }).catch(() => this.loading = false)
        }
    }
}
</script>
