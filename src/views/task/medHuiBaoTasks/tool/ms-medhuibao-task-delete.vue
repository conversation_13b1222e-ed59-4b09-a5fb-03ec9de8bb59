<template>
    <ms-operation-dialog>
        <template slot="content">
            <div class="delete-content">
                <div class="warning-icon">
                    <i class="el-icon-warning" style="color: #E6A23C; font-size: 24px;"></i>
                </div>
                <div class="delete-message">
                    <p>确定要删除以下医汇宝任务吗？</p>
                    <div class="task-info">
                        <p><strong>任务标题：</strong>{{ model.taskTitle }}</p>
                        <p><strong>任务类型：</strong>{{ getTaskTypeLabel(model.taskType) }}</p>
                        <p><strong>创建时间：</strong>{{ model.createdTime }}</p>
                    </div>
                    <p class="warning-text">此操作不可恢复，请谨慎操作！</p>
                </div>
            </div>
        </template>
        <template slot="footer">
            <el-button @click="confirmDelete"
                        :loading="loading"
                        size="mini"
                        type="danger">确认删除</el-button>
            <el-button @click="$emit('close')"
                        size="mini">取 消</el-button>
        </template>
    </ms-operation-dialog>
</template>

<script>
import { mapGetters } from "vuex";

export default {
    name: "ms-medhuibao-task-delete",
    data() {
        return {
            loading: false
        }
    },
    props: {
        model: Object,
        operation: String
    },
    computed: {
        ...mapGetters(["info"])
    },
    methods: {
        getTaskTypeLabel(type) {
            const typeLabels = {
                'survey': '调研任务',
                'live': '直播任务',
                'reading': '阅读任务',
                'follow': '关注任务',
                'invitation': '邀请任务',
                'other': '其他任务'
            }
            return typeLabels[type] || type
        },
        
        confirmDelete() {
            this.loading = true;
            
            let params = {
                userId: this.info.userId,
                username: this.info.userName,
                id: this.model.id
            }
            
            this.api.deleteMedHuiBaoTaskById(params).then(response => {
                if(response.status === 200) {
                    this.PUBLIC_Methods.apiNotify(response.message || '删除成功', 'success')
                } else {
                    this.PUBLIC_Methods.apiNotify(response.message || '删除失败', 'warning')
                }
                this.$emit('up-date')
                this.loading = false
                this.$emit('close')
            }).catch(() => {
                this.loading = false;
                this.PUBLIC_Methods.apiNotify('删除失败', 'error')
                this.$emit('close')
            })
        }
    }
}
</script>

<style scoped>
.delete-content {
    display: flex;
    align-items: flex-start;
    padding: 20px;
}

.warning-icon {
    margin-right: 15px;
    margin-top: 5px;
}

.delete-message {
    flex: 1;
}

.task-info {
    background-color: #f5f7fa;
    padding: 15px;
    border-radius: 4px;
    margin: 15px 0;
}

.task-info p {
    margin: 5px 0;
    color: #606266;
}

.warning-text {
    color: #E6A23C;
    font-weight: bold;
    margin-top: 15px;
}
</style>
