<template>
    <ms-operation-dialog>
        <template slot="content">
            <el-form ref="submitRef"
                    class="rule-form"
                    :model="submitData"
                    :rules="rules"
                    label-width="130px"
                    v-loading="getLoading">
                <el-row>
                    <!-- 任务标题 -->
                    <el-col :span="12">
                        <el-form-item prop="taskTitle" label="任务标题">
                            <ms-input :model.sync="submitData.taskTitle" placeholder="请输入任务标题"></ms-input>
                        </el-form-item>
                    </el-col>
                    
                    <!-- 任务类型 -->
                    <el-col :span="12">
                        <el-form-item prop="taskType" label="任务类型">
                            <el-select v-model="submitData.taskType" placeholder="请选择任务类型" style="width: 100%" @change="handleTaskTypeChange">
                                <el-option label="调研任务" value="survey"></el-option>
                                <el-option label="直播任务" value="live"></el-option>
                                <el-option label="阅读任务" value="reading"></el-option>
                                <el-option label="关注任务" value="follow"></el-option>
                                <el-option label="邀请任务" value="invitation"></el-option>
                                <el-option label="其他任务" value="other"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    
                    <!-- 任务说明 -->
                    <el-col :span="24">
                        <el-form-item prop="taskDescription" label="任务说明">
                            <el-input 
                                v-model="submitData.taskDescription" 
                                placeholder="请输入任务说明（最多20个字符）"
                                maxlength="20"
                                show-word-limit
                            ></el-input>
                        </el-form-item>
                    </el-col>
                    
                    <!-- 内容搜索 - 仅对调研、直播、阅读任务显示 -->
                    <el-col :span="24" v-if="showContentSearch">
                        <el-form-item prop="selectedContent" label="关联内容">
                            <ms-content-search
                                :content-type="submitData.taskType"
                                v-model="submitData.selectedContent"
                                :url-value="submitData.urlLink"
                                :placeholder="getContentSearchPlaceholder()"
                                @url-change="handleUrlChange"
                            ></ms-content-search>
                        </el-form-item>
                    </el-col>
                    
                    <!-- QR码图片上传 - 仅对关注任务显示 -->
                    <el-col :span="24" v-if="submitData.taskType === 'follow'">
                        <el-form-item prop="qrCodeImage" label="二维码图片">
                            <ms-image-upload 
                                :imageUrl="submitData.qrCodeImage"
                                @bindData="handleQrCodeUpload"
                                :disabled="false"
                            ></ms-image-upload>
                        </el-form-item>
                    </el-col>
                    
                    <!-- URL链接 - 仅对其他任务显示 -->
                    <el-col :span="24" v-if="submitData.taskType === 'other'">
                        <el-form-item prop="urlLink" label="URL链接">
                            <el-input
                                v-model="submitData.urlLink"
                                placeholder="请输入URL链接"
                            >
                                <el-button
                                    slot="append"
                                    icon="el-icon-view"
                                    :disabled="!submitData.urlLink"
                                    @click="previewUrl"
                                >
                                    预览
                                </el-button>
                            </el-input>
                        </el-form-item>
                    </el-col>
                    
                    <!-- 任务时限 -->
                    <el-col :span="24">
                        <el-form-item prop="taskDuration" label="任务时限">
                            <el-radio-group v-model="submitData.taskDuration" @change="handleDurationChange">
                                <el-radio label="long_term">长期任务</el-radio>
                                <el-radio label="time_limited">限时任务</el-radio>
                            </el-radio-group>
                        </el-form-item>
                    </el-col>
                    
                    <!-- 起始-截止时间 - 仅在选择时间限制项目时显示 -->
                    <el-col :span="12" v-if="submitData.taskDuration === 'time_limited'">
                        <el-form-item prop="startTime" label="开始时间">
                            <ms-picker type="datetime" :model.sync="submitData.startTime"></ms-picker>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12" v-if="submitData.taskDuration === 'time_limited'">
                        <el-form-item prop="endTime" label="结束时间">
                            <ms-picker type="datetime" :model.sync="submitData.endTime"></ms-picker>
                        </el-form-item>
                    </el-col>

                    <!-- 限量任务 -->
                    <el-col :span="24">
                        <el-form-item prop="taskLimits" label="限量任务">
                            <el-select v-model="submitData.taskLimits" placeholder="请选择限量任务类型" style="width: 100%" @change="handleLimitsChange">
                                <el-option label="项目有完成数量限制" value="quantity_limit"></el-option>
                                <el-option label="无限制提交有积分" value="unlimited_points"></el-option>
                                <el-option label="无限制只有一次积分" value="once_points"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>

                    <!-- 完成数量限制 - 仅在选择数量限制时显示 -->
                    <el-col :span="12" v-if="submitData.taskLimits === 'quantity_limit'">
                        <el-form-item prop="quantityLimit" label="完成数量限制">
                            <ms-input-number :model.sync="submitData.quantityLimit" :min="1" width="100%"></ms-input-number>
                        </el-form-item>
                    </el-col>

                    <!-- 真实获取积分数量 -->
                    <el-col :span="12">
                        <el-form-item prop="actualPoints" label="真实获取积分数量">
                            <ms-input-number :model.sync="submitData.actualPoints" :min="0" :precision="1" width="100%"></ms-input-number>
                        </el-form-item>
                    </el-col>

                    <!-- 显示积分文案 -->
                    <el-col :span="12">
                        <el-form-item prop="pointsDisplayText" label="显示积分文案">
                            <ms-input :model.sync="submitData.pointsDisplayText" placeholder="请输入显示给用户的积分描述"></ms-input>
                        </el-form-item>
                    </el-col>

                    <!-- 项目负责人 -->
                    <el-col :span="12">
                        <el-form-item prop="projectManager" label="项目负责人">
                            <ms-input :model.sync="submitData.projectManager" placeholder="请输入项目负责人"></ms-input>
                        </el-form-item>
                    </el-col>

                    <!-- 项目备注 -->
                    <el-col :span="24">
                        <el-form-item prop="projectNotes" label="项目备注">
                            <el-input
                                type="textarea"
                                v-model="submitData.projectNotes"
                                placeholder="请输入项目备注"
                                :rows="3"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
        </template>
        <template slot="footer">
            <el-button @click="submitForm"
                        :loading="loading"
                        size="mini"
                        type="primary">确 定</el-button>
            <el-button @click="$emit('close')"
                        size="mini">取 消</el-button>
        </template>
    </ms-operation-dialog>
</template>

<script>
import msContentSearch from '../components/ms-content-search'
import msImageUpload from '@/components/UpFile/ms-image-upload'
import { mapGetters } from "vuex";
import { ms_rule_url } from "@/utils/form-rule.js";

export default {
	name: "ms-medhuibao-task-operation",
	data () {
		return {
            loading: false,
            getLoading: false,
            submitData: {
                taskTitle: "",
                taskType: "",
                taskDescription: "",
                selectedContent: null,
                qrCodeImage: "",
                urlLink: "",
                contentUrl: "",
                taskDuration: "long_term",
                startTime: "",
                endTime: "",
                taskLimits: "unlimited_points",
                quantityLimit: null,
                projectNotes: "",
                projectManager: "",
                actualPoints: 0,
                pointsDisplayText: ""
            }
        }
    },
    computed: {
        ...mapGetters(["info"]),
        showContentSearch() {
            return ['survey', 'live', 'reading'].includes(this.submitData.taskType)
        },
        rules() {
            const baseRules = {
                taskTitle: [
                    { required: true, message: "请输入任务标题", trigger: 'blur' }
                ],
                taskType: [
                    { required: true, message: "请选择任务类型", trigger: 'change' }
                ],
                taskDescription: [
                    { required: true, message: "请输入任务说明", trigger: 'blur' },
                    { max: 20, message: "任务说明不能超过20个字符", trigger: 'blur' }
                ]
            }
            
            // 根据任务类型添加条件验证规则
            if (this.showContentSearch) {
                baseRules.selectedContent = [
                    { required: true, message: "请选择关联内容", trigger: 'change' }
                ]
            }
            
            if (this.submitData.taskType === 'follow') {
                baseRules.qrCodeImage = [
                    { required: true, message: "请上传二维码图片", trigger: 'change' }
                ]
            }
            
            if (this.submitData.taskType === 'other') {
                baseRules.urlLink = [
                    { required: true, message: "请输入URL链接", trigger: 'blur' },
                    { validator: ms_rule_url, trigger: 'blur' }
                ]
            }
            
            if (this.submitData.taskDuration === 'time_limited') {
                baseRules.startTime = [
                    { required: true, message: "请选择开始时间", trigger: 'change' }
                ]
                baseRules.endTime = [
                    { required: true, message: "请选择结束时间", trigger: 'change' }
                ]
            }
            
            return baseRules
        }
    },
    components: {
        msContentSearch,
        msImageUpload
    },
    props: {
		model: Object,
		operation: String
    },
    created() {
        this.init()
    },
	methods: {
        init() {
            let id = this.model.id
            if(id) {
                this.getLoading = true;
                this.api.getMedHuiBaoTaskById({id: id}).then( response => {
                    this.getLoading = false;
                    if(response.status === 200) {
                        this.submitData = { ...this.submitData, ...response.data }
                    } else {
                        this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
                    }
                }).catch(() => {
                    this.getLoading = false;
                })
            }
        },
        
        handleTaskTypeChange() {
            // 清空相关字段当任务类型改变时
            this.submitData.selectedContent = null
            this.submitData.qrCodeImage = ""
            this.submitData.urlLink = ""
            this.submitData.contentUrl = ""
        },

        handleDurationChange(value) {
            if (value === 'long_term') {
                this.submitData.startTime = ""
                this.submitData.endTime = ""
            }
        },

        handleLimitsChange(value) {
            if (value !== 'quantity_limit') {
                this.submitData.quantityLimit = null
            }
        },

        handleQrCodeUpload(data) {
            this.submitData.qrCodeImage = data.url
        },

        handleUrlChange(url) {
            this.submitData.urlLink = url
        },

        previewUrl() {
            if (this.submitData.urlLink) {
                window.open(this.submitData.urlLink, '_blank')
            }
        },

        getContentSearchPlaceholder() {
            const placeholders = {
                survey: '请搜索选择调研内容',
                live: '请搜索选择直播内容',
                reading: '请搜索选择阅读内容'
            }
            return placeholders[this.submitData.taskType] || '请搜索选择内容'
        },

		submitForm () {
            this.$refs["submitRef"].validate(valid => {
                if(valid) {
                    this.loading = true;
                    if (this.model.id) {
                        this.submitUpdate()
                    } else {
                        this.submitAdd()
                    }
                }
            })
        },

        submitAdd() {
            let params = {
                userId: this.info.userId,
                username: this.info.userName,
                ...this.submitData
            }
            this.api.saveMedHuiBaoTask(params).then(response => {
                if(response.status === 200) {
                    this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
                } else {
                    this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
                }
                this.$emit('up-date')
                this.loading = false
            }).catch( () => {
                this.loading = false;
                this.$emit('close')
            })
        },

        submitUpdate() {
            let params = {
                userId: this.info.userId,
                username: this.info.userName,
                id: this.model.id,
                ...this.submitData
            }
            this.api.updateMedHuiBaoTaskById(params).then(response => {
                if(response.status === 200) {
                    this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
                } else {
                    this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
                }
                this.$emit('up-date')
                this.loading = false
            }).catch( () => {
                this.loading = false;
                this.$emit('close')
            })
        }
    }
}
</script>
