# 医汇宝任务模块测试清单

## 基础功能测试

### 1. 页面加载测试
- [ ] 主列表页面正常加载
- [ ] 表格数据正常显示
- [ ] 搜索表单正常显示
- [ ] 操作按钮正常显示

### 2. 搜索功能测试
- [ ] 任务标题搜索功能
- [ ] 任务类型筛选功能
- [ ] 项目负责人搜索功能
- [ ] 创建时间范围搜索功能
- [ ] 重置搜索功能

### 3. 分页功能测试
- [ ] 分页控件正常显示
- [ ] 页面切换功能正常
- [ ] 每页显示数量调整功能

## 任务创建测试

### 4. 基础字段验证
- [ ] 任务标题必填验证
- [ ] 任务类型必选验证
- [ ] 任务说明字符限制验证（20字符）
- [ ] 真实获取积分数量数字验证
- [ ] 真实获取积分数量小数位验证（1位）

### 5. 调研任务创建
- [ ] 选择调研任务类型
- [ ] 内容搜索组件正常显示
- [ ] URL链接输入框正常显示
- [ ] 模糊搜索功能正常（使用/form页面相同API，参数名为value）
- [ ] 搜索结果正常显示
- [ ] 选择内容后URL自动填充
- [ ] URL预览按钮功能正常
- [ ] 手动输入URL功能正常
- [ ] 表单验证通过
- [ ] 保存功能正常

### 6. 直播任务创建
- [ ] 选择直播任务类型
- [ ] 内容搜索组件正常显示
- [ ] URL链接输入框正常显示
- [ ] 模糊搜索功能正常（使用/medsciLive页面相同API，参数名为searchTitle）
- [ ] 搜索结果正常显示
- [ ] 选择内容后URL自动填充
- [ ] URL预览按钮功能正常
- [ ] 手动输入URL功能正常
- [ ] 表单验证通过
- [ ] 保存功能正常

### 7. 阅读任务创建
- [ ] 选择阅读任务类型
- [ ] 内容搜索组件正常显示
- [ ] URL链接输入框正常显示
- [ ] 模糊搜索功能正常（使用/article页面相同API，参数名为value，附加isTitle: "2"参数）
- [ ] 搜索结果正常显示
- [ ] 选择内容后URL自动填充
- [ ] URL预览按钮功能正常
- [ ] 手动输入URL功能正常
- [ ] 表单验证通过
- [ ] 保存功能正常

### 8. 关注任务创建
- [ ] 选择关注任务类型
- [ ] 二维码图片上传组件正常显示
- [ ] 图片上传功能正常
- [ ] 图片格式验证正常
- [ ] 图片大小验证正常
- [ ] 上传成功后表单验证通过
- [ ] 保存功能正常

### 9. 邀请任务创建
- [ ] 选择邀请任务类型
- [ ] 无额外内容输入字段
- [ ] 基础表单验证通过
- [ ] 保存功能正常

### 10. 其他任务创建
- [ ] 选择其他任务类型
- [ ] URL链接输入字段正常显示
- [ ] URL预览按钮功能正常
- [ ] URL格式验证正常
- [ ] 保存功能正常

## URL链接功能测试

### 11. URL自动填充测试
- [ ] 调研任务选择内容后URL正确填充（使用接口返回的url字段）
- [ ] 直播任务选择内容后URL正确填充（使用serveUrl.live拼接）
- [ ] 阅读任务选择内容后URL正确填充（使用serveUrl.article拼接）
- [ ] URL格式符合各自页面的跳转逻辑

### 12. URL预览功能测试
- [ ] 预览按钮在URL为空时禁用
- [ ] 预览按钮在有URL时启用
- [ ] 点击预览按钮正确打开新窗口
- [ ] 预览链接能正确跳转到对应内容

### 13. URL手动输入测试
- [ ] 可以手动输入URL覆盖自动填充的URL
- [ ] 手动输入的URL能正确保存
- [ ] 手动输入的URL预览功能正常

## 条件字段测试

### 14. 任务时限测试
- [ ] 选择"长期任务"时无日期字段
- [ ] 选择"限时任务"时显示日期字段
- [ ] 开始时间必填验证
- [ ] 结束时间必填验证
- [ ] 时间范围逻辑验证

### 15. 限量任务测试
- [ ] 选择"项目有完成数量限制"时显示数量输入字段
- [ ] 数量限制必填验证
- [ ] 数量限制最小值验证
- [ ] 选择其他选项时隐藏数量字段

## 任务编辑测试

### 16. 编辑功能测试
- [ ] 点击编辑按钮正常打开编辑对话框
- [ ] 现有数据正常回填到表单
- [ ] 修改数据后保存功能正常
- [ ] 编辑后列表数据正常更新

### 17. 任务类型切换测试
- [ ] 编辑时切换任务类型
- [ ] 相关字段正常清空
- [ ] 新类型对应字段正常显示

## 删除功能测试

### 18. 删除确认测试
- [ ] 点击删除按钮显示确认对话框
- [ ] 确认对话框显示正确的任务信息
- [ ] 取消删除功能正常
- [ ] 确认删除功能正常
- [ ] 删除后列表数据正常更新

## 表单验证测试

### 19. 必填字段验证
- [ ] 任务标题为空时显示错误提示
- [ ] 任务类型未选择时显示错误提示
- [ ] 条件必填字段验证正常

### 20. 格式验证测试
- [ ] URL格式验证正常
- [ ] 数字格式验证正常
- [ ] 字符长度限制验证正常

## API集成测试

### 21. 数据加载测试
- [ ] 列表数据API调用正常
- [ ] 编辑时数据回填API调用正常
- [ ] 内容搜索API调用正常

### 21.1. API参数验证测试
- [ ] 调研任务搜索使用正确的参数名（value）
- [ ] 直播任务搜索使用正确的参数名（searchTitle）
- [ ] 阅读任务搜索使用正确的参数名（value）和附加参数（isTitle: "2"）
- [ ] 所有API调用包含正确的分页参数（pageIndex, pageSize）

### 22. 数据保存测试
- [ ] 新建任务API调用正常
- [ ] 更新任务API调用正常
- [ ] 删除任务API调用正常

### 23. 错误处理测试
- [ ] API错误时显示正确错误信息
- [ ] 网络错误时有适当的降级处理
- [ ] 加载状态正常显示

## 用户体验测试

### 24. 界面交互测试
- [ ] 表单字段切换动画流畅
- [ ] 加载状态指示器正常
- [ ] 成功/错误消息提示正常
- [ ] 对话框打开/关闭正常

### 25. 响应式测试
- [ ] 不同屏幕尺寸下布局正常
- [ ] 移动端适配正常
- [ ] 表格在小屏幕下正常显示

## 性能测试

### 26. 加载性能测试
- [ ] 页面初始加载时间合理
- [ ] 大量数据时列表渲染性能正常
- [ ] 搜索响应时间合理

### 27. 内存使用测试
- [ ] 长时间使用无内存泄漏
- [ ] 组件销毁时正常清理资源

## 兼容性测试

### 28. 浏览器兼容性
- [ ] Chrome浏览器正常
- [ ] Firefox浏览器正常
- [ ] Safari浏览器正常
- [ ] Edge浏览器正常

## 安全性测试

### 29. 输入安全测试
- [ ] XSS攻击防护正常
- [ ] SQL注入防护正常
- [ ] 文件上传安全验证正常

## 测试完成标准

- [ ] 所有测试项目通过
- [ ] 无阻塞性bug
- [ ] 性能指标达标
- [ ] 用户体验良好
- [ ] 代码质量符合标准
