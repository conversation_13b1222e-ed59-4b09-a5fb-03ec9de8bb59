<template>
	<ms-table
		:currentPage="searchParams.pageIndex"
		:loading="loading"
		:operationButtons="domConfig.tableButtons"
		:pageSize="0"
		:scopeConfig="scopeConfig.show"
        :scopeHeader="scopeConfig.headerShow"
		:tableData="list"
		:tableHeader="domConfig.tableHeader"
		:total="total"
		@current-change="current_change"
		@operation-change="operation_change"
		@size-change="size_change"
        @header-operation="header_operation"
        class="small-table-td"
	>
    <!-- 列表搜索去区域插槽 -->
		<template slot="ms-table-header">
			<div class="slot-search">
				<template v-for="(searchItem, key) in domConfig.listSearch">
					<component
						:index="searchItem.index || ''"
						:is="searchItem.component"
						:key="key"
                        :width="searchItem.width || '150px'"
                        :model.sync="searchParams[searchItem.model]"
                        :label="searchItem.label"
						:operation="searchItem.operation || ''"
						:options="searchItem.options || []"
						:placeholder="searchItem.placeholder || ''"
						:type="searchItem.type || ''"
                        :multiple="searchItem.multiple"
                        :disabled="searchItem.disabled"
                        :code="searchItem.code"
                        :config="searchItem.config"
					></component>
				</template>
                <div class="inlineBlock">
                    <el-button @click="handleClick('query')" type="primary" plain icon="el-icon-search">查询</el-button>
                    <el-button @click="handleClick('reset')">重置</el-button>
                </div>
			</div>
            <div class="slot-button">
				<template v-for="(item, index) in domConfig.soltButtons">
					<el-button :key="index" 
                     :type="item.type" 
                     size="mini"
                     :icon="item.icon"
                     @click="operation_change({operation: item})"
                     :disabled="item.roleDisabled"
                     plain>{{ item.label }}</el-button>
				</template>
			</div>
		</template>
	</ms-table>
</template>

<script>
import tableMixins from "../../common/mixins/table"
import domConfig from "./data/dom-config";
import scopeConfig from "./data/scope-config";
import taskDateRange from "./tool/ms-task-daterange"
export default {
    name: "ms-task-record",
    mixins: [tableMixins],
    data () {
        return {
            domConfig: domConfig, // => dom展示数据
            scopeConfig: scopeConfig, // => 显示信息配置 
        }
    },
    components: {
        taskDateRange
    },
    methods: {
        apiInit (params) {
            let searchParams = {}
            if (params.createTime) {
                searchParams.statisticsStartTime = params.createTime[0] || ''
                searchParams.statisticsEndTIme = params.createTime[1] || ''
            }
            this.domConfig.tableHeader = [
                { label: '任务名称', property: 'taskName', width: '100', fixed: 'left' }
            ]
            this.api.taskStatistics(searchParams).then(response => {
                this.loading = false
                this.list = []
                if (response.status === 200) {
                    response.data.dateArr.forEach(v => {
                        this.domConfig.tableHeader.push({
                            label: v, 
                            property: v, 
                            width: '100'
                        })
                    });
                    response.data.list.forEach(v => {
                        this.list.push({
                            taskName: v.taskName,
                            ...v.reporMap
                        })
                    })
                } else {
                    this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
                }
            }).catch(() => this.loading = false)
        },
        operation_change_module(val) {
            switch (val.operation.way) {
                case "import":
                    var searchParams = {}
                    if (this.searchParams.createTime) {
                        searchParams.statisticsStartTime = this.searchParams.createTime[0] || ''
                        searchParams.statisticsEndTIme = this.searchParams.createTime[1] || ''
                    }
                    this.loading = true
                    this.api.excelExport(searchParams).then(response => {
                        this.loading = false
                        if (Number(response.status) === 200) {
                            window.location.href = response.data
                        }
                    })
                break;
                default: break;
            }
        }
    }
}
</script>
