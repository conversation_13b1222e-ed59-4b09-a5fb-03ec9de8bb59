const scopeConfig = {
    show: {
      extendTaskType: () => {
        return {
          type: 'status-char',
          rule: {
            'article': { label: '资讯' },
            'guider': { label: '指南' },
            'tool_impact_factor': { label: '期刊' },
            'topic': { label: '话题' },
            'video': { label: '视频' },
          }
        }
      },
      taskType: () => {
        return {
          type: 'status-char',
          rule: {
            1: { label: '日常任务' },
            2: { label: '新手任务' },
            3: { label: '推广任务' }
          }
        }
      },
      paymentType: () => {
        return {
          type: 'status-button',
          rule: {
            1: { label: '积分', background: '#67C23A' },
            2: { label: '梅花', background: '#E6A23C' }
          }
        }
      },
      extendTaskValidTime: () => {
        return {
          type: 'formatTime',
          cFormat: '{y}-{m}-{d}'
        }
      }
    },
    headerShow: {
      taskType: () => {
        return {
          type: 'dropdown',
          icon: 'icon-funnel',
          options: [
            { label: '全部', value: null },
            { label: '日常任务', value: 1 },
            { label: '新手任务', value: 2 },
            { label: '推广任务', value: 3 }
          ],
          operation: 'query'
        }
      },
      extendTaskType: () => {
        return {
          type: 'dropdown',
          icon: 'icon-funnel',
          options: [
            { label: '全部', value: null },
            { label: '资讯', value: 'article' },
            { label: '指南', value: 'guider' },
            { label: '期刊', value: 'tool_impact_factor' },
            { label: '话题', value: 'topic' },
            { label: '视频', value: 'video' }
          ],
          operation: 'query'
        }
      }
    }
  }
  
  export default scopeConfig;
  