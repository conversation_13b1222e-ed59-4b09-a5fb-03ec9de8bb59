const domConfig = {
    listSearch: [
      {
        label: '任务名称',
        model: 'taskName',
        component: 'ms-input'
      },
      {
        label: '任务创建人',
        model: 'createdName',
        component: 'ms-input'
      },
      {
        label: '截止日期',
        placeholder: '请选择时间段',
        model: 'createTime',
        component: 'ms-picker',
        type: 'daterange',
        width: '240px'
      },
    ],
    tableHeader: [
      { label: 'ID', property: 'id', width: '60' },
      { label: '任务名称', property: 'taskName', width: '100' },
      { label: '任务类型', property: 'taskType', width: '100' },
      { label: '推广任务类型', property: 'extendTaskType', width: '100' },
      { label: '完成限制次数', property: 'times', sortable: true, width: '100' },
      { label: '已完成次数', property: 'finishCount', sortable: true, width: '80' },
      { label: '支付额度', property: 'paymentAmount', sortable: true, width: '80' },
      { label: '截至日期', property: 'extendTaskValidTime', width: '80' },
      { label: '创建时间', property: 'createdTime', width: '130'  },
      { label: '创建人', property: 'createdName', width: '80'  }
    ],
    soltButtons: [
      { 
        label: '添加任务', 
        type: 'primary', 
        icon: 'el-icon-plus',
        operation: 'created',
        title: '添加推广任务',
        component: 'msTaskOperation',
        way: 'dialog',
        width: '65%'
      },
    ],
    tableButtons: [
      {
        label: '编辑',
        way: 'dialog',
        type: 'primary',
        operation: 'update',
        component: 'msTaskOperation',
        title: '编辑推广任务',
        width: '65%',
        showCallback: (val) => {
          if (val.taskType === 3) {
            return true
          } else {
            return false
          }
        }
      },
      {
        label: '删除',
        way: 'dialog',
        type: 'danger',
        operation: 'delete',
        component: 'msTaskDelete',
        showCallback: (val) => {
          if (val.taskType === 3) {
            return true
          } else {
            return false
          }
        }
      }
    ]
  }
  
  export default domConfig;
  