const formConfig = {
    formField: [
      {
        label: '任务名称',
        prop: 'taskName',
        colSpan: 12,
        component: 'ms-input'
      },
      {
        label: '任务标识',
        prop: 'taskKey',
        colSpan: 12,
        component: 'ms-input'
      },
      {
        label: '任务类型',
        prop: 'taskType',
        colSpan: 12,
        component: 'ms-select-local',
        options: [
            { label: '推广任务', value: 3 }
        ]
      },
      {
        label: '推广任务类型',
        prop: 'extendTaskType',
        colSpan: 12,
        component: 'ms-select-local',
        options: [
            { label: '资讯', value: 'article' },
            { label: '指南', value: 'guider' },
            { label: '期刊', value: 'tool_impact_factor' },
            { label: '话题', value: 'topic' },
            { label: '视频', value: 'video' }
        ]
      },
      
      {
        label: '单位',
        prop: 'mgUnit',
        colSpan: 12,
        component: 'ms-input',
        disabled: true
      },
      {
        label: '电话',
        prop: 'mgTell',
        colSpan: 6,
        component: 'ms-input',
        disabled: true
      },
      {
        label: '邮箱',
        prop: 'mgEmail',
        colSpan: 6,
        component: 'ms-input',
        disabled: true
      },
      {
        label: '留言时间',
        prop: 'mgCreateTime',
        colSpan: 6,
        component: 'ms-picker',
        type: 'date',
        disabled: true
      },
      {
        label: '留言内容',
        prop: 'mgContent',
        colSpan: 24,
        component: 'ms-input',
        type: 'textarea',
        disabled: true
      },
      {
        label: '附件',
        prop: 'mgImageList',
        colSpan: 24,
        component: 'ms-image-list'
      },
      {
        label: '内容',
        prop: 'replyContent',
        colSpan: 24,
        component: 'ms-editor'
      }
    ],
    rule: {
      replyContent: [
        { required: true, message: "请输入回复内容", trigger: 'blur' }
      ]
    }
  }
  
  export default formConfig;
  