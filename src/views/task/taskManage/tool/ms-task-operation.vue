<template>
    <ms-operation-dialog>
        <template slot="content">
            <el-form ref="submitRef"
                    class="rule-form"
                    :model="submitData"
                    :rules="rules"
                    label-width="115px"
                    v-loading="getLoading">
                <el-row>
                    <el-col :span="12">
                        <el-form-item prop="taskName" label="任务名称">
                            <ms-input :model.sync="submitData.taskName"></ms-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item prop="taskKey" label="任务标识">
                            <ms-input :model.sync="submitData.taskKey"></ms-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item prop="extendTaskType" label="对象类型">
                            <ms-dictionary-search :model.sync='submitData.extendTaskType' type="system_message_type" style="width: 100%;" :disabled="true"></ms-dictionary-search>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item prop="extendTaskObjectId" :label="objIdLabel[submitData.extendTaskType] || '对象ID'">
                            <el-input v-model="submitData.extendTaskObjectId" :placeholder="objIdPlaceholder[submitData.extendTaskType] || '请粘贴对应模块Id'">
                                <el-button slot="append" icon="el-icon-d-arrow-right" @click="goModel()">前往模块</el-button>
                            </el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item prop="sort" label="任务排序">
                            <ms-input-number :model.sync="submitData.sort" width="100%"></ms-input-number>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item prop="paymentAmount" label="支付积分">
                            <ms-input-number :model.sync="submitData.paymentAmount" width="100%"></ms-input-number>
                        </el-form-item>
                    </el-col>
                    <!-- <el-col :span="12">
                        <el-form-item prop="times" label="每日完成次数限制">
                            <ms-input-number v-model="submitData.times"></ms-input-number>
                        </el-form-item>
                    </el-col> -->
                    <el-col :span="12">
                        <el-form-item prop="extendTaskValidTime" label="有效期时间">
                            <ms-picker type="date" :model.sync="submitData.extendTaskValidTime"></ms-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
        </template>
        <template slot="footer">
            <el-button @click="submitForm"
                        :loading="loading"
                        size="mini"
                        type="primary">确 定</el-button>
            <el-button @click="$emit('close')"
                        size="mini">取 消</el-button>
        </template>
    </ms-operation-dialog>
</template>

<script>
import msDictionarySearch from '@/components/MsCommon/ms-dictionary-search'
import { mapGetters } from "vuex";
export default {
	name: "ms-task-operation",
	data () {
		return {
            loading: false,
            getLoading: false,
            submitData: {
                "taskKey": "",
                "taskName": "",
                "sort": 0,
                "paymentAmount": "",
                "extendTaskType": "",
                "extendTaskObjectId": "",
                "extendTaskValidTime": ""
            },
            rules: {
                taskName: [
                    { required: true, message: "请输入任务名", trigger: 'blur' }
                ],
                taskKey: [
                    { required: true, message: "请输入任务标识", trigger: 'blur' }
                ],
                paymentAmount: [
                    { required: true, message: "请输入支付积分", trigger: 'blur' }
                ],
                extendTaskType: [
                    { required: true, message: "请选择对象类型", trigger: 'change' }
                ],
                extendTaskObjectId: [
                    { required: true, message: "请输入对象Id", trigger: 'blur' }
                ]
            },

            //common
            operationLocal: "",
            objIdLabel: {
                "article": '资讯ID',
                "guider": '指南ID',
                "video": '视频ID',
                "tool_impact_factor": '期刊ID',
                "topic": '话题ID'
            },
            objIdPlaceholder: {
                "article": '请粘贴资讯ID',
                "guider": '请粘贴指南ID',
                "video": '请粘贴视频ID',
                "tool_impact_factor": '请粘贴期刊ID',
                "topic": '请粘贴话题ID'
            },
            objIdPath: {
                "article": 'article',
                "guider": 'guider',
                "video": 'video',
                "tool_impact_factor": 'journal',
                "topic": 'topickey'
            }
		}
	},
	props: {
		model: Object,
		operation: String
    },
    computed: {
        ...mapGetters(["info"])
    },
    components: {
        msDictionarySearch
    },
    created() {
        this.init()
    },
	methods: {
        init() {
            let id = this.model.id
            if(id) {
                this.getLoading = true;
                this.api.getTaskById({id: id}).then( response => {
                    this.getLoading = false;
                    if(response.status === 200) {
                        let res = response.data
                        this.submitData = {
                            "taskKey": res.taskKey,
                            "taskName": res.taskName,
                            "sort": res.sort,
                            "paymentAmount": res.paymentAmount,
                            "extendTaskType": res.extendTaskType,
                            "extendTaskObjectId": res.extendTaskObjectId,
                            "extendTaskValidTime": res.extendTaskValidTime
                        }
                    } else {
                        this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
                    }
                }).catch(() => {
                    this.getLoading = false;
                })
            } 
        },

		submitForm () {
            this.$refs["submitRef"].validate(valid => {
                if(valid) {
                    this.loading = true;
                    if (this.model.id) {
                        this.submitUpdate()
                    } else {
                        this.submitAdd()
                    }
                    
                }
            })
        },

        goModel() {
            if (this.submitData.extendTaskType) {
                this.$router.push({
                    path: this.objIdPath[this.submitData.extendTaskType]
                })
            } else {
                this.$message({
                    message: '请选择对象类型',
                    type: 'warning'
                });
            }
        },

        submitAdd() {
            let params = {
                userId: this.info.userId,
                username: this.info.userName,
                ...this.submitData
            }
            this.api.saveTask(params).then(response => {
                if(response.status === 200) {
                    this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
                } else {
                    this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
                }
                this.$emit('up-date')
                this.loading = false
            }).catch( () => {
                this.loading = false;
                this.$emit('close')
            })
        },

        submitUpdate() {
            let params = {
                userId: this.info.userId,
                username: this.info.userName,
                id: this.model.id,
                ...this.submitData
            }
            this.api.updateTaskById(params).then(response => {
                if(response.status === 200) {
                    this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
                } else {
                    this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
                }
                this.$emit('up-date')
                this.loading = false
            }).catch( () => {
                this.loading = false;
                this.$emit('close')
            })
        }
	}
}
</script>
