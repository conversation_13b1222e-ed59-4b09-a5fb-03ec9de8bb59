<template>
  <ms-operation-dialog>
    <template slot="content">
      <el-form ref="submitRef"
                class="rule-form"
                :model="submitData"
                :rules="formConfig.rule"
                label-width="90px"
                v-loading="getLoading">
        <el-row>
          <template v-for="(item, index) in formConfig.formField">
            <el-col :key="index"
                    :span="item.colSpan">
              <el-form-item :prop="item.prop"
                            :label="item.label">
                <component :is="item.component"
                            v-model="submitData[item.prop]"
                            :model.sync="submitData[item.prop]"
                            :placeholder="item.placeholder"
                            :width="item.width || '100%'"
                            :disabled="item.disabled "
                            :type="item.type"
                            :options="item.options"
                            :active="item.active"
                            :inactive="item.inactive">
                </component>
              </el-form-item>
            </el-col>
          </template>
        </el-row>
      </el-form>
    </template>
    <template slot="footer">
      <el-button @click="submitForm"
                 :loading="loading"
                 size="mini"
                 type="primary">确 定</el-button>
      <el-button @click="$emit('close')"
                 size="mini">取 消</el-button>
    </template>
  </ms-operation-dialog>
</template>

<script>
import formConfig from '../data/form-config' 
import { mapGetters } from "vuex";
export default {
	name: "ms-role-edit",
	data () {
		return {
      loading: false,
      getLoading: false,
      formConfig: formConfig,
      submitData: {
        "roleName": "",
        "roleEnglishName": "",
        "remarks": "",
        "identityFlag": 1
      },

      //common
      operationLocal: ""
		}
	},
	props: {
		model: Object,
		operation: String
  },
  computed: {
    ...mapGetters(["info"])
  },
  created() {
    this.init()
  },
	methods: {
    init() {
      this.operationLocal = this.operation || this.$route.query.operation
      if(this.operationLocal === 'edit' && this.model.id) {
        this.getDetail(this.model.id)
      } 
    },
    getDetail(id) {
      this.getLoading = true;
      let data = {
        id: id,
        projectId: this.$route.query.id
      }
      this.api.getMedsciRoleDetail(data).then( response => {
        this.getLoading = false;
        if(response.status === 200) {
          this.submitData = {...response.data}
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
      }).catch(() => this.getLoading = false)
    },
		submitForm () {
      this.$refs["submitRef"].validate(valid => {
        if(valid) {
          this.loading = true;
          if(this.operationLocal === 'edit') {
            this.submitEdit()
          } else {
            this.submitAdd()
          }
        }
      })
    },
    submitEdit() {
      let params = {
        userId: this.info.userId,
        username: this.info.userName,
        projectId: this.$route.query.id,
        ...this.submitData
      }
      this.api.updateMedsciRole(params).then(response => {
        if(response.status === 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
          this.$emit('up-date')
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
        this.loading = false
      }).catch( () => {
        this.loading = false;
        this.$emit('close')
      })
    },
    submitAdd() {
      let params = {
        userId: this.info.userId,
        username: this.info.userName,
        projectId: this.$route.query.id,
        ...this.submitData
      }
      this.api.addMedsciRole(params).then(response => {
        if(response.status === 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
          this.$emit('up-date')
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
        this.loading = false
      }).catch( () => {
        this.loading = false;
        this.$emit('close')
      })
    },
	}
}
</script>
