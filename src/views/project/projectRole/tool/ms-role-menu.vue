<template>
  <ms-right-content>
    <el-tree ref="tree"
             :data="dataTree"
             show-checkbox
             node-key="menuId"
             :expand-on-click-node="false"
             :check-on-click-node="true"
             :check-strictly="true"
             :default-expand-all="true"
             :default-checked-keys="checkedTree"
             :props="{ label: 'menuChineseName', children: 'childMenu' }"
             v-loading="getLoading">
    </el-tree>
    <template slot="footer">
      <el-button @click="submitForm"
                 :loading="loading"
                 size="mini"
                 type="primary">确 定</el-button>
      <el-button @click="$emit('close')"
                 size="mini">取 消</el-button>
    </template>
  </ms-right-content>
</template>

<script>
import { mapGetters } from "vuex";
export default {
	name: "ms-role-menu",
	data () {
		return {
      loading: false,
      getLoading: false,
      dataTree: [],
      checkedTree: []
		}
	},
	props: {
		model: Object,
		operation: String
  },
  computed: {
    ...mapGetters(["info"])
  },
  created() {
    this.init()
  },
	methods: {
    init() {
      this.getLoading = true
      let searchParams = {
        projectId: this.$route.query.id,
        roleId: this.model.id,
      }
      this.api.getMedsciRoleAndMenuList(searchParams).then( response => {
        if (response.status === 200) {
          this.dataTree = response.data.getMedsciRoleAndMenuResponses
          this.checkedTree = response.data.selectMenuIds
        }
        this.getLoading = false
      }).catch(() => this.getLoading = false)
    },
    submitForm() {
      this.loading = true
      let checkedNode = this.$refs.tree.getCheckedNodes() || []
      let params = {
        roleId: this.model.id,
        selectMenuIds: [],
        userId: this.info.userId,
        username: this.info.userName,
        projectId: this.$route.query.id
      }
      checkedNode.forEach(item => {
        params.selectMenuIds.push(item.menuId)
      });
      this.api.saveMedsciRoleAndMenu(params).then(response => {
        this.loading = false
        if(response.status === 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
          this.$emit('up-date')
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
      }).catch(()=> { this.loading = false })
    }
	}
}
</script>
