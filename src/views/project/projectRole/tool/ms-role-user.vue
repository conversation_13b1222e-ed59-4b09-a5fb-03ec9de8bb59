<template>
  <ms-right-content>
    <ms-table	:currentPage="searchParams.pageIndex"
              :loading="loading"
              :pageSize="searchParams.pageSize"
              :tableData="list"
              :tableHeader="tableHeader"
              :total="total"
              @current-change="current_change"
              @size-change="size_change" 
              @rowClick="rowClick"
              :style="{height: 'calc(100vh - 145px)'}">
      <div slot="ms-table-header">
        <ms-input width="150px" :model.sync="searchParams['name']" label="用户名"></ms-input>
        <el-button @click="init" type="primary" plain icon="el-icon-search">查询</el-button>
      </div>
    </ms-table>
    <template slot="footer">
      <el-button @click="$emit('close')"
                 size="mini">取 消</el-button>
    </template>
  </ms-right-content>
</template>

<script>
import { mapGetters } from "vuex";
export default {
	name: "ms-role-menu",
	data () {
		return {
      loading: false,
      getLoading: false,
      searchParams: {
        pageSize: 10,
        pageIndex: 0,
        name: ''
      },
      total: 0,
      list: [],
      tableHeader: [
        { label: '序号', property: 'no' },
        { label: '账号', property: 'userName' },
        { label: '用户名', property: 'realName' },
      ]
		}
	},
	props: {
		model: {
      type: Object,
      default: () => {
        return {}
      }
    },
		operation: String
  },
  computed: {
    ...mapGetters(["info"])
  },
  created() {
    this.init()
  },
	methods: {
    init() {
      this.loading = true;
      let searchParams = {
        projectId: this.$route.query.id,
        roleId: this.model.id,
        ...this.searchParams
      }
      this.api.getMedsciUserRolePage(searchParams).then(response => {
        this.loading = false
        this.total = response.totalSize || 0;
        this.list = response.data || []
        if (response.status !== 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
      }).catch(() => this.loading = false)
    },
    size_change (val) {
      this.searchParams.pageSize = val;
      this.$store.dispatch('SetPagesize', val)
      this.init();
    },
    current_change (val) {
      this.searchParams.pageIndex = val;
      this.init();
    },
    rowClick(row) {
      this.$router.push({
        path: 'user',
        query: {
          userName: row.userName
        }
      })
    }
	}
}
</script>
