const formConfig = {
  formField: [
    {
      label: '角色编号',
      prop: 'roleCode',
      colSpan: 24,
      component: 'ms-input',
      disabled: true,
      placeholder: '后台自动生成'
    },
    {
      label: '角色中文名',
      prop: 'roleName',
      colSpan: 24,
      component: 'ms-input'
    },
    {
      label: '角色英文名',
      prop: 'roleEnglishName',
      colSpan: 24,
      component: 'ms-input',
    },
    {
      label: '级别排序',
      prop: 'roleGrade',
      colSpan: 12,
      component: 'ms-input-number',
    },
    {
      label: '后台身份',
      prop: 'identityFlag',
      colSpan: 24,
      component: 'ms-switch',
      active: 1,
      inactive: 0
    },
    {
      label: '备注',
      prop: 'remarks',
      colSpan: 24,
      component: 'ms-input',
      type: 'textarea'
    }
  ],
  rule: {
    roleName: [
      { required: true, message: "请输入角色中文名", trigger: 'blur' }
    ],
    roleEnglishName: [
      { required: true, message: "请输入角色英文名", trigger: 'blur' }
    ]
  }
}

export default formConfig;
