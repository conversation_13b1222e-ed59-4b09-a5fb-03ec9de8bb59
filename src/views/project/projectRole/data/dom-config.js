const domConfig = {
  listSearch: [
    {
      label: '角色名称',
      placeholder: '请输入',
      model: 'name',
      component: 'ms-input'
    },
    {
      label: '角色编号',
      placeholder: '请输入',
      model: 'roleCode',
      component: 'ms-input'
    }
  ],
  tableHeader: [
    { label: '角色编号', property: 'roleCode', sortable: true, width: 100 },
    { label: '角色中文名', property: 'roleName', width: 200 },
    { label: '角色英文名', property: 'roleEnglishName' },
    { label: '创建时间', property: 'createdTime', sortable: true },
    { label: '备注', property: 'remarks',width: 200 },
  ],
  tableButtons: [
    {
      label: '编辑',
      icon: '',
      role: '',
      operation: 'edit',
      component: 'msRoleEdit',
      way: 'dialog',
      type: 'primary',
      title: '项目编辑',
      width: '45%'
    },
    {
      label: '权限',
      icon: '',
      role: '',
      operation: '',
      component: 'msRoleMenu',
      way: 'dialog',
      type: 'primary',
      position: 'right',
      title: '角色菜单权限',
      width: '30%'
    },
    {
      label: '更多',
      operation: 'more',
      type: 'info',
      children: [
        {
          label: '删除',
          way: 'delete',
          operation: 'delete',
          component: '',
        },
        {
          label: '用户列表',
          component: 'msRoleUser',
          way: 'dialog',
          type: 'primary',
          position: 'right',
          title: '角色用户列表',
          width: '45%'
        }
      ]
    }
  ],
  soltButtons: [
    { 
      label: '添加角色', 
      icon: 'el-icon-plus',
      type: 'primary', 
      operation: 'created',
      component: 'msRoleEdit',
      way: 'dialog',
      title: '角色添加',
      width: '45%'
    }
  ]
}

export default domConfig;
