<template>
  <div class="form-tab">
    <el-tabs v-model="activeName" @tab-click="tab_click">
      <el-tab-pane label="项目菜单配置" name="menu"></el-tab-pane>
      <el-tab-pane label="项目角色配置" name="role"></el-tab-pane>
      <el-tab-pane label="项目成员配置" name="member"></el-tab-pane>
    </el-tabs>
    <component :is="projectComponent"></component>
  </div>
</template>

<script>
import MsProjectMenu from '../projectMenu/ms-menu-manage'
import MsProjectRole from '../projectRole/ms-role-manage'
import MsProjectMember from '../projectMember/ms-member-manage'
export default {
  name: "ms-project-allocate",
  data () {
    return {
      activeName: 'member',
      projectComponent: 'MsProjectMember'
    }
  },
  created() {
    let id = this.$route.query.id
    if (!id) {
      this.PUBLIC_Methods.apiNotify('请选择项目', 'warning')
      this.$router.back()
    }
  },
  components: {
    MsProjectMenu,
    MsProjectRole,
    MsProjectMember
  },
  methods: {
    tab_click() {
      if (this.activeName === 'menu') {
        this.projectComponent = 'MsProjectMenu'
      } else if (this.activeName === 'role') {
        this.projectComponent = 'MsProjectRole'
      } else if (this.activeName === 'member') {
        this.projectComponent = 'MsProjectMember'
      }
    }
  }
}
</script>
