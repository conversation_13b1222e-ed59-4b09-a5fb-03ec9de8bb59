import store from '@/store/index'
const domConfig = {
  listSearch: [
    {
      label: '项目名称',
      placeholder: '请输入',
      model: 'name',
      component: 'ms-input'
    }
  ],
  tableHeader: [
    { label: '项目ID', property: 'id', sortable: true, width: '60' },
    { label: '项目名称', property: 'name' },
    { label: '项目需求', property: 'projectNeed', width: '300' },
    { label: '创建人', property: 'createdName' },
    { label: '状态', property: 'state' },
    { label: '创建时间', property: 'createdTime', sortable: true }
  ],
  tableButtons: [
    {
      label: '新增',
      icon: '',
      role: '',
      operation: 'created',
      component: 'msProjectEdit',
      way: 'dialog',
      type: 'primary',
      position: 'right',
      title: '项目新建',
      width: '70%',
      showCallback: (val) => {
        // 判断站点项目只能新增一层子集
        if (val.pid != store.getters.info.projectId) {
          return false
        } else {
          return true
        }
      },
    },
    {
      label: '编辑',
      icon: '',
      role: '',
      operation: 'edit',
      component: 'msProjectEdit',
      way: 'dialog',
      type: 'primary',
      position: 'right',
      title: '项目编辑',
      width: '70%'
    },
    {
      label: '项目配置',
      icon: '',
      role: '',
      operation: '',
      component: 'project-allocate',
      way: 'page',
      type: 'primary',
      path: 'project-allocate',
      tdWidth: '100px',
      params: ['id']
    },
    // {
    //   label: '更多',
    //   operation: 'more',
    //   type: 'info',
    //   children: [
    //     {
    //       label: '删除',
    //       way: 'delete',
    //       operation: 'delete',
    //       component: '',
    //     },
    //   ]
    // }
  ],
  soltButtons: [
    {
      label: '创建项目',
      icon: 'el-icon-plus',
      type: 'primary',
      operation: 'created',
      component: 'msProjectEdit',
      way: 'dialog',
      position: 'right',
      title: '项目新建',
      width: '70%'
    }
  ]
}

export default domConfig;
