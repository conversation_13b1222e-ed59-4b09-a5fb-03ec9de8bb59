const formConfig = {
  projectField: [
    {
      label: '项目名称',
      prop: 'name',
      colSpan: 12,
      component: 'ms-input'
    },
    {
      label: '起止时间',
      prop: 'timeRange',
      colSpan: 12,
      component: 'ms-picker',
      type: 'daterange',
    },
    {
      label: '客户名称',
      prop: 'customerName',
      colSpan: 12,
      component: 'ms-input'
    },
    {
      label: '合同编号',
      prop: 'contractNumber',
      colSpan: 12,
      component: 'ms-input'
    },

   
    {
      label: '项目LOGO(135*64px)',
      prop: 'projectLogo',
      colSpan: 12,
      component: 'ms-single-image'
    },
    {
      label: '项目描述',
      prop: 'description',
      colSpan: 12,
      component: 'ms-input',
      type: 'textarea',
      minRows: 5,
      maxRows: 5
    },
  ],
  webField: [
   
    {
      label: '项目属性',
      prop: 'disparkState',
      colSpan: 12,
      component: 'ms-radio',
      options: [
        {label: '封闭式', value: 0},
        {label: '开放式', value: 1},
        {label: '半开放式', value: 2},
      ]
    },
    {
      label: '账号域名',
      prop: 'domain',
      colSpan: 12,
      component: 'ms-input',
      slotName:'Https://'
    },
    {
      label: '账号数量',
      prop: 'maxAccount',
      colSpan: 12,
      component: 'ms-input'
    },
    {
      label: 'iMSL账号数量',
      prop: 'imslAccountNum',
      colSpan: 12,
      component: 'ms-input'
    },
    {
      label: '超级管理员账号',
      prop: 'projectAdmin',
      colSpan: 12,
      component: 'ms-input'
    },


  ],
  rule: {
    name: [
      { required: true, message: "请输入项目名称", trigger: 'blur' }
    ],
    timeRange: [
      { required: true, message: "请输入起止时间", trigger: 'blur' }
    ],
    customerName: [
      { required: true, message: "请输入客户名称", trigger: 'blur' }
    ],
    maxAccount: [
      { required: true, message: "请输入账号数量", trigger: 'blur' }
    ],
    imslAccountNum: [
      { required: true, message: "请输入iMSL账号数量", trigger: 'blur' }
    ],
    // property: [
    //   { required: true, message: "请选择账号属性", trigger: 'blur' }
    // ],
    disparkState: [
      { required: true, message: "请选择项目属性", trigger: 'blur' }
    ],
    
    domain: [
      { required: true, message: "请输入账号域名", trigger: 'blur' }
    ],
    projectAdmin: [
      { required: true, message: "请输入超级管理员账号", trigger: 'blur' }
    ],
    checkedProjects: [
      { required: true, message: "请选择项目需求", trigger: 'blur' }
    ],
    otherInfo: [
      { required: true, message: "请填写定制开发", trigger: 'blur' }
    ],
    accountType: [
      { required: true, message: "请选择账号体系", trigger: 'blur' }
    ]
  }
}

export default formConfig

