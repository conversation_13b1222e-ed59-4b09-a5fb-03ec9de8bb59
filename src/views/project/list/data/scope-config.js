const scopeConfig = {
  show: {
    state: () => {
      return {
        type: 'status-button',
        rule: {
          0: { label: '未开始', background: '#A2CF8E' },
          1: { label: '进行中', background: '#70A857' },
          2: { label: '已结束', background: '#A8ACB6' }
        }
      }
    },
    createdTime: () => {
      return {
        type: 'formatTime',
        cFormat: '{y}-{m}-{d} {h}:{i}'
      }
    },
    projectNeed: (scope) => {
      let arr = []
      if(scope.projectNeed) {
        let need = JSON.parse(scope.projectNeed)
        if(need.list && need.list.length) {
          arr = need.list.split(',')
        }
        if(need.other) {
          arr.push(need.other)
        }
      }
      return {
        type: 'render',
        render: (h) => {
          return h(
            "div",
            arr.join('，')
          )
        }

      }
    }
  },
  headerShow: {
    state: () => {
      return {
        type: 'dropdown',
        icon: 'icon-funnel',
        options: [
          { label: '全部', value: null },
          { label: '未开始', value: 0 },
          { label: '进行中', value: 1 },
          { label: '已结束', value: 2 }
        ],
        operation: 'query'
      }
    }
  }
}

export default scopeConfig;
