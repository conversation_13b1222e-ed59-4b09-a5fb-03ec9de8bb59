<template>
  <ms-right-content>
    <el-form
      ref="submitRef"
      class="rule-form"
      :model="submitData"
      :rules="formConfig.rule"
      label-width="130px"
      v-loading="getLoading"
    >
      <el-row style="display: flex; flex-direction: row; flex-wrap: wrap">
        <div class="title">项目信息</div>
        <!-- 项目信息 -->
        <template v-for="(item, index) in formConfig.projectField">
          <el-col :key="index" :span="item.colSpan">
            <el-form-item :prop="item.prop" :label="item.label">
              <component
                :is="item.component"
                v-model="submitData[item.prop]"
                :model.sync="submitData[item.prop]"
                :modelName.sync="submitData[item.propShow]"
                :width="item.width || '100%'"
                :disabled="item.disabled"
                :type="item.type"
                :minRows="item.minRows"
                :maxRows="item.maxRows"
                :options="item.options"
                :active="item.active"
                :inactive="item.inactive"
                :multiple="item.multiple"
                :slotName="item.slotName"
              >
              </component>
            </el-form-item>
          </el-col>
        </template>
        <div class="title">微网站信息</div>
        <!--  微网站信息-->
        <template v-for="(item, index) in formConfig.webField">
          <el-col :key="index" :span="item.colSpan">
            <el-form-item :prop="item.prop" :label="item.label">
              <template slot="label" v-if="item.label=='iMSL账号数量'">
                {{item.label}} <el-tooltip
                  popper-class="atooltip"
                  effect="light"
                  placement="top"
                  
                >
                  <i
                    class="el-icon-question"
                    style="font-size: 14px; vertical-align: middle;color:#666666;"
                  ></i>
                  <template #content>
                    <div>
                      <div >
                        付费使用AI功能的项目，需要填写账号数量
                      </div>
                    </div>
                  </template>
                </el-tooltip>
              </template>
             
              <component
                :is="item.component"
                v-model="submitData[item.prop]"
                :model.sync="submitData[item.prop]"
                :modelName.sync="submitData[item.propShow]"
                :width="item.width || '100%'"
                :disabled="item.disabled"
                :type="item.type"
                :minRows="item.minRows"
                :maxRows="item.maxRows"
                :options="item.options"
                :active="item.active"
                :inactive="item.inactive"
                :multiple="item.multiple"
                :slotName="item.slotName"
              >
              </component>
            </el-form-item>
          </el-col>
        </template>
        <!--  微网站信息-->
        <template>
          <el-col :span="24">
            <el-col :span="12">
              <el-form-item
                label=" 账号体系"
                prop="accountTypeOne"
                style="width: 100%"
              >
              <el-radio-group @change="change"  v-model="accountTypeOne">
                <el-radio :disabled="operationLocal === 'edit' && model.id?true:false" :label="-1">不带管理属性<el-tooltip
                  popper-class="atooltip"
                  effect="light"
                  placement="top"
                >
                  <i
                    class="el-icon-question"
                    style="font-size: 14px; vertical-align: middle;color:#666666;margin-left:5px"
                  ></i>
                  <template #content>
                    <div>
                      <div style="color:">
                        项目使用1种账号体系
                      </div>
                    </div>
                  </template>
                </el-tooltip></el-radio>
                <el-radio :disabled="operationLocal === 'edit' && model.id?true:false" :label="2">带管理属性<el-tooltip
                  popper-class="atooltip"
                  effect="light"
                  placement="top"
                >
                  <i
                    class="el-icon-question"
                    style="font-size: 14px; vertical-align: middle;color:#666666;margin-left:5px"
                  ></i>
                  <template #content>
                    <div>
                      <div style="color:">
                        项目使用两种账号体系
                      </div>
                    </div>
                  </template>
                </el-tooltip></el-radio>
              </el-radio-group>
                <!-- <component
                  :is="'ms-radio'"
                  v-model="submitData.accountTypeOne"
                  :model.sync="submitData.accountTypeOne"
                  :width="'100%'"
                  :options="[
                    { label: '', value: -1 },
                    { label: '带管理属性', value: 2 },
                  ]"
                  style="
                    display: inline-block;
                    margin-left: 5px;
                    margin-left: 0;
                  "
                ></component> -->
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="accountTypeOne == -1">
              <el-form-item label="" prop="accountType" style="width: 100%">
                <el-radio-group   v-model="submitData.accountType">
                <el-radio :disabled="operationLocal === 'edit' && model.id?true:false" :label="0">医生类</el-radio>
                <el-radio :disabled="operationLocal === 'edit' && model.id?true:false" :label="1">患者类</el-radio>
              </el-radio-group>
              </el-form-item>
            </el-col>
          </el-col>
          <el-form-item label="积分商城" prop="pointsEnable" style="width: 100%">
            <template slot="label">
              积分商城
              <el-tooltip
                  popper-class="atooltip"
                  effect="light"
                  placement="top"
                >
                  <i
                    class="el-icon-question"
                    style="font-size: 14px; vertical-align: middle;color:#666666;"
                  ></i>
                  <template #content>
                    <div>
                      <div >
                        打开后，项目中产生的积分，仅限在本项目的积分商城中兑换
                      </div>
                    </div>
                  </template>
                </el-tooltip>
            </template>
            <el-col :span="24">
              
              <component
                :is="'ms-switch'"
                v-model="submitData['pointsEnable']"
                :model.sync="submitData['pointsEnable']"
                :width="'100%'"
                active="1"
                inactive="0"
                :disabled="operationLocal === 'edit' && model.id&&submitData.pointsEnableCopy==1?true:false"
                style="display: inline-block; margin-left: 5px"
              ></component>
               <span style="font-size:14px;margin-left:10px"></span>
            </el-col>
          </el-form-item>
          <el-form-item label="运营模式" prop="possessor" style="width: 100%">
            <el-col :span="6">
              <span>APOC<el-tooltip
                  popper-class="atooltip"
                  effect="light"
                  placement="top"
                >
                  <i
                    class="el-icon-question"
                    style="font-size: 14px; vertical-align: middle;color:#666666;margin-left:2px"
                  ></i>
                  <template #content>
                    <div>
                      <div style="color:">
                        若打开，则会在主站医讯达列表中展示
                      </div>
                    </div>
                  </template>
                </el-tooltip></span>
              <component
                :is="'ms-switch'"
                v-model="submitData['possessor']"
                :model.sync="submitData['possessor']"
                :width="'100%'"
                active="APOC"
                inactive=""
                style="display: inline-block; margin-left: 5px"
              ></component>
            </el-col>
            <el-col :key="index" :span="6">
              <span>需要推荐<el-tooltip
                  popper-class="atooltip"
                  effect="light"
                  placement="top"
                >
                  <i
                    class="el-icon-question"
                    style="font-size: 14px; vertical-align: middle;color:#666666;margin-left:2px"
                  ></i>
                  <template #content>
                    <div>
                      <div >
                        若打开，则会在主站医讯达列表的【推荐】模块展示
                      </div>
                    </div>
                  </template>
                </el-tooltip></span>
              <component
                :is="'ms-switch'"
                v-model="submitData['recommend']"
                :model.sync="submitData['recommend']"
                :width="'100%'"
                active="1"
                inactive="0"
                style="display: inline-block; margin-left: 5px"
              ></component>
            </el-col>
            <!-- <el-col :key="index" :span="6">
              <span>需要引流</span>
              <component
                :is="'ms-switch'"
                v-model="submitData['drainage']"
                :model.sync="submitData['drainage']"
                :width="'100%'"
                active="1"
                inactive="0"
                style="display: inline-block; margin-left: 5px"
              ></component>
            </el-col> -->
          </el-form-item>
        </template>
        <!-- 项目需求写死 -->
        <div class="title">项目需求</div>
        <el-col>
          <el-form-item
            label="请选择项目需求"
            :prop="
              submitData.otherInfo || submitData.checkedProjects.length
                ? ''
                : 'checkedProjects'
            "
          >
            <el-checkbox
              :indeterminate="isIndeterminate"
              v-model="checkAll"
              @change="handleCheckAllChange"
              >全选</el-checkbox
            >
            <div style="margin: 10px 0"></div>
            <el-checkbox-group
              v-model="submitData.checkedProjects"
              @change="handleCheckedProjectsChange"
              style="margin-left: 20px"
            >
              <template v-for="(project, index) in projects">
                <br v-if="project === 'next'" :key="index" />
                <el-checkbox
                  style="width: 60px"
                  v-else
                  :label="project"
                  :key="project"
                  >{{ project }}</el-checkbox
                >
              </template>
            </el-checkbox-group>
          </el-form-item>
          <el-form-item
            label=""
            :prop="
              submitData.otherInfo || submitData.checkedProjects.length
                ? ''
                : 'otherInfo'
            "
            style="margin-top: -10px"
          >
            <div class="flex">
              <el-checkbox
                v-model="otherChecked"
                label="定制开发"
                @change="changeOther"
              ></el-checkbox>
              <el-input
                v-model="submitData.otherInfo"
                v-if="otherChecked"
                style="margin-left: 20px"
                type="textarea"
                placeholder="请输入客户的需求"
              ></el-input>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template slot="footer">
      <el-button
        @click="submitForm"
        :loading="loading"
        size="mini"
        type="primary"
        >确 定</el-button
      >
      <el-button @click="$emit('close')" size="mini">取 消</el-button>
    </template>
  </ms-right-content>
</template>

<script>
import formConfig from "../data/form-config";
import msCompanySearch from "@/components/MsCommon/ms-company-search";
import { mapGetters } from "vuex";
const projectOptions = [
  "注册登录",
  "个人中心",
  "权限管理",
  "帮助中心",
  "合规审核",
  "信息推送",
  "next",
  "观看直播",
  "发起直播",
  "资讯",
  "指南",
  " 期刊",
  "医学公式",
  "next",
  "调研",
  " 病例征集",
  "投票",
  "next",
  "病例分享",
  "病例讨论",
  "next",
  "课程",
  "考试测评",
  "学习报告",
  "知识竞赛",
  "next",
  "活动专题",
  "活动报名",
  "活动签到",
  "next",
  "医生画像",
  "虚拟诊疗",
  "AI问答",
];
export default {
  name: "ms-project-edit",
  data() {
    return {
      loading: false,
      getLoading: false,
      formConfig: formConfig,
      submitData: {
        name: "",
        startTime: "",
        endTime: "",
        description: "",
        projectLogo: "",
        timeRange: [],
        maxAccount: "",
        projectMenus: "",
        projectMenusArray: [],
        checkedProjects: [],
        projectNeed: {},
        otherInfo: "",
        drainage: "0",
        recommend: 0,
        accountType: 0,
        pointsEnable:0,
        pointsEnableCopy:0
      },
        accountTypeOne: -1,


      //common
      operationLocal: "",
      checkAll: false,
      projects: projectOptions,
      isIndeterminate: true,
      otherChecked: false,
    };
  },
   watch: {  
  
  } ,
  props: {
    model: Object,
    operation: String,
  },
  computed: {
    ...mapGetters(["info"]),
  },
  components: {
    msCompanySearch,
  },
  created() {
    this.init();
  },

  methods: {
    change(e){
      if(e==-1){
        this.submitData.accountType=0
      }
    },
    init() {
      this.operationLocal = this.operation || this.$route.query.operation;
      if (this.operationLocal === "edit" && this.model.id) {
        this.getDetail();
      }
    },
    changeOther() {
      this.$set(this.submitData, "otherInfo", "");
    },
    handleCheckAllChange(val) {
      let options = [];
      projectOptions.forEach((i) => {
        if (i != "next") {
          options.push(i);
        }
      });
      this.submitData.checkedProjects = val ? options : [];
      this.isIndeterminate = false;
    },
    handleCheckedProjectsChange(value) {
      let checkedCount = value.length;
      let allLength = this.projects.length - 6; //6个next换行符
      this.checkAll = checkedCount === allLength;
      this.isIndeterminate = checkedCount > 0 && checkedCount < allLength;
    },
    getDetail() {
      this.submitData = { ...this.model, timeRange: [], projectMenusArray: [] };
      this.submitData.timeRange[0] = this.model.startTime || "";
      this.submitData.timeRange[1] = this.model.endTime || "";
     
      this.submitData.pointsEnable = String(this.model.pointsEnable)
      this.submitData.pointsEnableCopy = String(JSON.parse(JSON.stringify(this.model.pointsEnable)))
      if (this.model.projectMenus) {
        this.submitData.projectMenusArray = this.model.projectMenus.split(",");
      }
      this.$set(this.submitData, "checkedProjects", []);
      this.$set(this.submitData, "otherInfo", "");
      if (this.model.projectNeed) {
        let need = JSON.parse(this.model.projectNeed);
        if (need.list && need.list.length) {
          this.$set(this.submitData, "checkedProjects", need.list.split(","));
        } else {
          this.$set(this.submitData, "checkedProjects", []);
        }
        if (need.other) {
          this.otherChecked = true;
          this.$set(this.submitData, "otherInfo", need.other);
        }
      }
       if (this.model.accountType == 2) {
        this.accountTypeOne = parseInt(this.model.accountType);
      } else {
        this.accountTypeOne = -1;
        this.submitData.accountType = parseInt(this.model.accountType);
      }
    },
    submitForm() {
      if (this.submitData.timeRange) {
        this.submitData.startTime = this.submitData.timeRange[0] || "";
        this.submitData.endTime = this.submitData.timeRange[1] || "";
      }
      if (this.submitData.projectMenusArray.length > 0) {
        this.submitData.projectMenus =
          this.submitData.projectMenusArray.join(",");
      }
      let other = "";
      if (this.otherChecked) {
        other = this.submitData.otherInfo;
      }
      let json = {
        list: [],
        other: other,
      };
      if (this.submitData.checkedProjects.length > 0) {
        json.list = this.submitData.checkedProjects.join(",");
      }

      this.submitData.projectNeed = JSON.stringify(json);
      this.submitData.accountType =
        this.accountTypeOne == -1
          ? this.submitData.accountType
          : this.accountTypeOne;
      this.$refs["submitRef"].validate((valid) => {
        if (valid) {
          this.loading = true;
          if (this.operationLocal === "edit") {
            this.submitEdit();
          } else {
            this.submitAdd();
          }
        }
      });
    },
    submitEdit() {
      let params = {
        userId: this.info.userId,
        username: this.info.userName,
        ...this.submitData,
      };
      if (params.possessor && params.possessor === "f") {
        params.possessor = "";
      }
      this.api
        .updateMedsciProject(params)
        .then((response) => {
          if (response.status === 200) {
            this.PUBLIC_Methods.apiNotify(
              response.message || "请求成功",
              "success"
            );
            this.$emit("up-date");
          } else {
            this.PUBLIC_Methods.apiNotify(
              response.message || "请求出错",
              "warning"
            );
          }
          this.loading = false;
        })
        .catch(() => {
          this.loading = false;
          this.$emit("close");
        });
    },
    submitAdd() {
      let params = {
        userId: this.info.userId,
        username: this.info.userName,
        pid: this.model.id ? this.model.id : this.info.projectId,
        ...this.submitData,
      };
      if (params.possessor && params.possessor === "f") {
        params.possessor = "";
      }
      this.api
        .addChildrenProject(params)
        .then((response) => {
          if (response.status === 200) {
            this.PUBLIC_Methods.apiNotify(
              response.message || "请求成功",
              "success"
            );
            this.$emit("up-date");
          } else {
            this.PUBLIC_Methods.apiNotify(
              response.message || "请求出错",
              "warning"
            );
          }
          this.loading = false;
        })
        .catch(() => {
          this.loading = false;
          this.$emit("close");
        });
    },
  },
};
</script>
<style scoped>
.rule-form /deep/ .ms-radio .el-radio {
  margin-right: 26px !important;
  margin-left: 0px !important;
}
.title {
  background: #f2f2f2;
  width: 100%;
  line-height: 50px;
  padding-left: 50px;
  margin-bottom: 10px;
}
</style>
