const scopeConfig = {
  show: {
    status: () => {
      return {
        type: 'status',
        rule: {
          0: { label: '禁用', background: '#A7ADBD' },
          1: { label: '启用', background: '#40A23F' }
        }
      }
    },
    menuType: () => {
      return {
        type: 'code',
        rule: {
          1: { label: '目录' },
          2: { label: '菜单' },
          3: { label: '权限' }
        }
      }
    },
  },
  headerShow: {
    status: () => {
      return {
        type: 'dropdown',
        icon: 'icon-funnel',
        options: [
          { label: '全部', value: null },
          { label: '启用', value: 1 },
          { label: '禁用', value: 0 }
        ],
        operation: 'query'
      }
    }
  }
}

export default scopeConfig;
