const formConfig = {
  formField: [
      // {
      //   label: '菜单类型',
      //   prop: 'menuType',
      //   colSpan: 24,
      //   component: 'ms-radio',
      //   options: [
      //     { label: '目录', value: 1 },
      //     { label: '菜单', value: 2 }
      //   ]
      // },
      // {
      //   label: (type) => {
      //     return type === 3 ? '权限中文名' : '菜单中文名'
      //   },
      //   prop: 'menuChineseName',
      //   colSpan: 12,
      //   component: 'ms-input'
      // },
      // {
      //   label: (type) => {
      //     return type === 3 ? '权限英文名' : '菜单英文名'
      //   },
      //   prop: 'menuEnglishName',
      //   colSpan: 12,
      //   component: 'ms-input'
      // },
      // {
      //   label: '路由',
      //   prop: 'menuRoute',
      //   colSpan: 12,
      //   component: 'ms-input'
      // },
      // {
      //   label: '排序',
      //   prop: 'menuSort',
      //   colSpan: 12,
      //   component: 'ms-input-number'
      // },
      {
        label: '图标',
        prop: 'menuCron',
        colSpan: 12,
        component: 'ms-icon-search'
      },
      {
        label: '状态',
        prop: 'state',
        colSpan: 12,
        component: 'ms-switch',
        active: 1,
        inactive: 0
      },
      {
        label: '备注',
        prop: 'remarks',
        colSpan: 24,
        component: 'ms-input',
        type: 'textarea'
      }
    ]
}

export default formConfig;
