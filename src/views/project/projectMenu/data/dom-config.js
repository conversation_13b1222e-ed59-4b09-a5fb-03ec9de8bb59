const domConfig = {
  listSearch: [
    {
      label: '菜单名称',
      placeholder: '请输入',
      model: 'menuChineseName',
      component: 'ms-input'
    }
  ],
  tableHeader: [
    { label: '中文名', property: 'menuChineseName', width: 200 },
    { label: '路由', property: 'menuRoute' },
    { label: '类型', property: 'menuType' },
    { label: '状态', property: 'status' },
    { label: '排序', property: 'menuSort' },
  ],
  tableButtons: [
    {
      icon: '',
      role: '',
      operation: 'editStatus',
      component: 'msMenuStatus',
      way: 'dialog',
      field: 'status',
      rule: {
        1: { label: '禁用', type: 'info' },
        0: { label: '启用', type: 'success' }
      }
    },
    {
      label: '删除',
      operation: 'delete',
      type: 'danger',
      way: 'dialog',
      component: 'msMenuDelete',
    }
  ],
  soltButtons: [
    { 
      label: '导入菜单', 
      icon: 'el-icon-plus',
      type: 'primary', 
      operation: '',
      component: 'msMenuImport',
      way: 'dialog',
      position: 'right',
      title: '项目菜单配置',
      width: '30%'
    },
    { 
      label: '批量删除', 
      icon: 'el-icon-close',
      type: 'info',
      operation: 'delete',
      component: 'msMenuDelete',
      way: 'batch'
    }
  ]
}

export default domConfig;
