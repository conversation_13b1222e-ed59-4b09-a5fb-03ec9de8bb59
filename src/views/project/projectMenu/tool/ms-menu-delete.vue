<template>
  <ms-operation-dialog :title="title">
    <template slot="content">
      <el-tag v-for="(item, index) in tagArr" 
              :key="index" 
              type="info" 
              style="margin: 0 5px 5px 0">{{item}}</el-tag>
    </template>
    <template slot="footer">
      <el-button @click="submitForm"
                 :loading="loading"
                 size="mini"
                 type="primary">确 定</el-button>
      <el-button @click="$emit('close')"
                 size="mini">取 消</el-button>
    </template>
  </ms-operation-dialog>
</template>

<script>
import { mapGetters } from "vuex";
export default {
	name: "ms-menu-delete",
	data () {
		return {
      tagArr: [],
      ids: [],
      title: '',
      loading: false
		}
	},
	props: [
		"model",
		"operation"
  ],
  computed: {
    ...mapGetters(["info"])
  },
  created() {
    this.init()
  },
	methods: {
    init() {
      let arr = []
      let ids = []
      this.title = '是否删除菜单'
      if (this.PUBLIC_Methods.isArrayFn(this.model)) {
        this.model.forEach(item => {
          arr.push(item.menuChineseName)
          ids.push(item.projectMenuId)
        });
      } else {
        arr.push(this.model.menuChineseName)
        ids.push(this.model.projectMenuId)
      }
      this.tagArr = arr
      this.ids = ids
    },
		submitForm () {
      let params = {
        ids: this.ids,
        projectId: this.$route.query.id
      }
      this.$confirm('此操作将永久删除菜单信息，是否继续', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.apiOperation(params)
      })
    },
    apiOperation(params) {
      this.loading = true;
      this.api.deleteBatchProjectMenu(params).then(response => {
        if(response.status === 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
        this.$emit('up-date')
        this.loading = false
      }).catch( () => {
        this.loading = false;
        this.$emit('close')
      })
    }
	}
}
</script>
