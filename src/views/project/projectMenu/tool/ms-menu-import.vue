<template>
  <ms-right-content>
    <el-tree ref="tree"
             :data="dataTree"
             show-checkbox
             node-key="id"
             :expand-on-click-node="false"
             :check-on-click-node="true"
             :check-strictly="true"
             :default-expand-all="true"
             :default-checked-keys="checkedTree"
             :props="{ label: 'menuChineseName', children: 'childMenu' }"
             v-loading="getLoading">
    </el-tree>
    <template slot="footer">
      <el-button @click="submitForm"
                 :loading="loading"
                 size="mini"
                 type="primary">确 定</el-button>
      <el-button @click="$emit('close')"
                 size="mini">取 消</el-button>
    </template>
  </ms-right-content>
</template>

<script>
import { mapGetters } from "vuex";
export default {
	name: "ms-menu-important",
	data () {
		return {
      loading: false,
      getLoading: false,
      dataTree: [],
      checkedTree: []
		}
	},
	props: {
		model: Object,
		operation: String
  },
  computed: {
    ...mapGetters(["info"])
  },
  created() {
    this.init()
  },
	methods: {
    init() {
      this.getLoading = true
      let searchParams = {
        projectId: this.$route.query.id
      }
      this.api.getImportProjectMenuTreeList(searchParams).then( response => {
        if (response.status === 200) {
          // 非站点项目过滤项目管理菜单
          this.dataTree = response.data.medsciMenuPageResponses.filter(item => item.menuRoute!='project')
          this.checkedTree = response.data.selectMenuIds
        }
        this.getLoading = false
      }).catch(() => this.getLoading = false)
    },
    submitForm() {
      this.loading = true
      let checkedNode = this.$refs.tree.getCheckedNodes() || []
      let params = {
        menuIds: [],
        userId: this.info.userId,
        username: this.info.userName,
        projectId: this.$route.query.id
      }
      checkedNode.forEach(item => {
        params.menuIds.push(item.id)
      });
      this.api.saveProjectMenuList(params).then(response => {
        this.loading = false
        if(response.status === 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
          this.$emit('up-date')
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
      }).catch(()=> { this.loading = false })
    }
	}
}
</script>
