import {ms_rule_phone, ms_rule_mail} from "@/utils/form-rule.js";
const formConfig = {
  formField: [
    {
      label: 'ID',
      prop: 'id',
      placeholder: '后台自动生成',
      component: 'ms-input',
      disabled: true
    },
    {
      label: '用户名',
      prop: 'userName',
      component: 'ms-input',
    },
    {
      label: '真实姓名',
      prop: 'realName',
      component: 'ms-input'
    },
    {
      label: '手机号',
      prop: 'mobile',
      component: 'ms-input'
    },
    {
      label: '邮箱',
      prop: 'email',
      component: 'ms-input'
    },
    {
      label: '微信号',
      prop: 'wechatAccount',
      component: 'ms-input'
    },
    {
      label: 'QQ',
      prop: 'qqAccount',
      component: 'ms-input'
    },
    // {
    //   label: '密码',
    //   prop: 'password',
    //   component: 'ms-input'
    // },
    {
      label: '角色',
      prop: 'roleList',
      component: 'ms-role-search'
    },
    {
      label: '状态',
      prop: 'status',
      component: 'ms-radio',
      options: [
        { label: '启用', value: 1 },
        { label: '禁用', value: 0 },
        { label: '未激活', value: 2 }
      ]
    }
  ],
  formUserExtField: [
    {
      label: '城市',
      prop: 'areaMap',
      colSpan: 6,
      component: 'ms-area-cascader',
    },
    {
      label: '单位',
      prop: 'companyName',
      colSpan: 12,
      component: 'ms-hospital-search'
    },
    {
      label: '科室',
      prop: 'departmentName',
      colSpan: 6,
      component: 'ms-department-search'
    },
    {
      label: '职称',
      prop: 'professionalMap',
      colSpan: 6,
      component: 'ms-professional-cascader'
    },
    {
      label: '临床擅长标签',
      prop: 'clinicalTags',
      colSpan: 24,
      component: 'ms-input'
    },
    {
      label: '研究擅长标签',
      prop: 'researchTags',
      colSpan: 24,
      component: 'ms-input'
    },
    {
      label: '个人简介',
      prop: 'briefIntroduction',
      colSpan: 24,
      component: 'ms-input',
      type: "textarea"
    },
    {
      label: '头像',
      prop: 'avatar',
      colSpan: 12,
      component: 'ms-single-image'
    }
  ],
  rule: {
    userName: [
      { required: true, message: "请输入用户名", trigger: 'blur' }
    ],
    realName: [
      { required: true, message: "请输入姓名", trigger: 'blur' }
    ],
    email: [
      { required: true, message: "请输入邮箱", trigger: 'blur' },
      { validator: ms_rule_mail, trigger: 'blur' }
    ],
    mobile: [
      { required: true, message: "请输入手机号", trigger: 'blur' },
      { validator: ms_rule_phone, trigger: 'blur' }
    ],
    roleList: [
      { validator: (rule, value, callback) => {
        if (value && value.length > 0) {
          callback()
        } else {
          callback("请选择角色")
        } 
      }, trigger: 'blur' },
    ]
  },
  ruleEdit: {
    userName: [
      { required: true, message: "请输入用户名", trigger: 'blur' }
    ],
    realName: [
      { required: true, message: "请输入姓名", trigger: 'blur' }
    ],
    email: [
      { required: true, message: "请输入邮箱", trigger: 'blur' },
      { validator: ms_rule_mail, trigger: 'blur' }
    ],
    mobile: [
      { required: true, message: "请输入手机号", trigger: 'blur' },
    ],
    roleList: [
      { validator: (rule, value, callback) => {
        if (value && value.length > 0) {
          callback()
        } else {
          callback("请选择角色")
        } 
      }, trigger: 'blur' },
    ]
  }
}

export default formConfig;
