const domConfig = {
  listSearch: [
    {
      label: '用户名',
      placeholder: '请输入',
      model: 'userName',
      component: 'ms-input',
      width: '130px'
    },
    {
      label: '姓名',
      placeholder: '请输入',
      model: 'realName',
      component: 'ms-input',
      width: '130px'
    },
    {
      label: '手机号',
      placeholder: '请输入',
      model: 'mobile',
      component: 'ms-input',
      width: '130px'
    },
    {
      label: '邮箱',
      placeholder: '请输入',
      model: 'email',
      component: 'ms-input',
      width: '130px'
    },
    {
      label: '创建时间',
      placeholder: '请选择时间段',
      model: 'createTime',
      component: 'ms-picker',
      format: 'yyyy-MM-dd HH:mm:ss',
      type: 'datetimerange',
      width: '240px'
    },
    {
      label: '身份',
      placeholder: '请选择身份标识',
      model: 'identityFlag',
      component: 'ms-select-local',
      options: [
        { label: '全部', value: null },
        { label: '前台用户', value: 0 },
        { label: '后台管理账户', value: 1 }
      ]
    },
  ],
  tableHeader: [
    { label: '用户名', property: 'userName',width: 120 },
    { label: '真实姓名', property: 'realName', width: 180 },
    // { label: '手机号', property: 'mobile', width: 120 },
    // { label: '邮箱', property: 'email', width: 180 },
    { label: '状态', property: 'status',width: 80 },
    { label: '创建时间', property: 'createdTime', sortable: true, width: 150 }
  ],
  tableButtons: [
    {
      label: '编辑',
      icon: '',
      role: '',
      operation: 'edit',
      component: 'msMemberEdit',
      way: 'dialog',
      type: 'primary',
      position: 'right',
      title: '成员编辑',
      width: '45%'
    },
    {
      icon: '',
      role: '',
      operation: 'editStatus',
      component: 'msMemberStatus',
      way: 'dialog',
      field: 'status',
      rule: {
        1: { label: '禁用', type: 'info' },
        0: { label: '启用', type: 'success' }
      }
    },
    {
      label: '剔除',
      icon: '',
      role: '',
      type: 'danger',
      way: 'dialog',
      operation: 'delete',
      component: 'msMemberDelete',
    }
  ],
  soltButtons: [
    { 
      label: '手工添加', 
      icon: 'el-icon-plus',
      type: 'primary', 
      operation: 'created',
      component: 'msMemberEdit',
      way: 'dialog',
      position: 'right',
      title: '成员新建',
      width: '45%'
    },
    { 
      label: '平台库导入', 
      icon: '',
      type: 'primary', 
      operation: 'created',
      component: 'msMemberImport',
      way: 'dialog',
      position: 'right',
      title: '平台库导入成员',
      width: '70%'
    },
    { 
      label: '批量删除', 
      type: 'info', 
      icon: 'el-icon-close',
      operation: 'delete',
      component: 'msMemberDelete',
      way: 'batch'
    }
  ]
}

export default domConfig;
