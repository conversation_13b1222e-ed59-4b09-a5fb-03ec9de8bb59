<template>
	<ms-table
		:currentPage="searchParams.pageIndex"
		:loading="loading"
		:operationButtons="domConfig.tableButtons"
		:pageSize="searchParams.pageSize"
		:scopeConfig="scopeConfig.show"
    :scopeHeader="scopeConfig.headerShow"
		:tableData="list"
		:tableHeader="domConfig.tableHeader"
		:total="total"
    :showSelection="true"
		@current-change="current_change"
		@operation-change="operation_change"
		@size-change="size_change"
    @header-operation="header_operation"
    @handleSelectionChange="handleSelectionChange"
    
	>
    <!-- 列表搜索去区域插槽 -->
		<template slot="ms-table-header">
      <div class="slot-search">
        <template v-for="(searchItem, key) in domConfig.listSearch">
          <component
            :index="searchItem.index || ''"
            :is="searchItem.component"
            :key="key"
            v-if="key<=3"
            :width="searchItem.width || '150px'"
            :model.sync="searchParams[searchItem.model]"
            :label="searchItem.label"
            :operation="searchItem.operation || ''"
            :options="searchItem.options || []"
            :placeholder="searchItem.placeholder || ''"
            :type="searchItem.type || ''"
            :multiple="searchItem.multiple"
            :disabled="searchItem.disabled"
          ></component>
        </template>
        <div class="inlineBlock">
          <el-button @click="handleClick('query')" type ="primary" plain icon="el-icon-search">查询</el-button>
          <el-button @click="handleClick('reset')">重置</el-button>
          <el-button v-if="domConfig.listSearch.length>3" @click="show = !show">
            {{show? '收起' : '展开'}}
            <i :class="show?'el-icon-arrow-up':'el-icon-arrow-down'"></i>
          </el-button>
        </div>
      </div>
      <div class="slot-search">
        <div style="width:100vw"></div>
      </div>
      <div class="slot-search">
        <template v-for="(searchItem, key) in domConfig.listSearch">
          <component
            :index="searchItem.index || ''"
            :is="searchItem.component"
            :key="key"
            v-if="key>3&&show"
            :width="searchItem.width || '150px'"
            :model.sync="searchParams[searchItem.model]"
            :label="searchItem.label"
            :operation="searchItem.operation || ''"
            :options="searchItem.options || []"
            :placeholder="searchItem.placeholder || ''"
            :type="searchItem.type || ''"
            :multiple="searchItem.multiple"
            :disabled="searchItem.disabled"
          ></component>
        </template>
      </div>
      <div class="slot-button" :style="{float: show ? 'right' : 'left'}">
        <template v-for="(item, index) in domConfig.soltButtons">
            <el-button :key="index" :type="item.type" :icon="item.icon" @click="operation_change({operation: item})" plain>{{ item.label }}</el-button>
          </template>
      </div>
      <ms-right-dialog :visible.sync="r_dialog"
                       :width="dialogWidth"
                       :title="dialogTitle">
        <component :is="dialogComponent" 
                   :model="scopeInfo" 
                   :operation="dialogOperation" 
                   @close="r_dialog = !r_dialog" 
                   @up-date="init" 
                   v-if="r_dialog"></component>
      </ms-right-dialog>
      <el-dialog :visible.sync="dialog" 
                 closeable 
                 show-close
                 :close-on-click-modal="false"
                 :width="dialogWidth"
                 :title="dialogTitle">
				<component :is="dialogComponent" 
                   :model="scopeInfo" 
                   :operation="dialogOperation" 
                   @close="dialog = !dialog" 
                   @up-date="init" 
                   v-if="dialog"></component>
      </el-dialog>
		</template>
	</ms-table>
</template>

<script>
import tableMixins  from "../../mixins/table"
import domConfig from "./data/dom-config"
import scopeConfig from "./data/scope-config"
import msMemberEdit from './tool/ms-member-edit'
import msMemberStatus from "./tool/ms-member-status"
import msMemberDelete from "./tool/ms-member-delete"
import msMemberImport from "./tool/ms-member-import"
export default {
  name: "ms-member-manage",
  mixins: [tableMixins],
  data () {
    return {
      show: false,
      domConfig: domConfig,
      scopeConfig: scopeConfig, 
      searchParams: { // => 列表查询传参
         userName: "",
        realName: "",
        email: "",
        mobile: "",
        strStartCreateTime: "",
        strEndCreateTime: "",
        createTime: [],
        status: null
      },
      projectType: 0
    }
  },
  components: {
    msMemberStatus,
    msMemberDelete,
    msMemberEdit,
    msMemberImport
  },
  created() {
    if (this.$store.getters.selectProject) {
      this.projectType = this.$store.getters.selectProject.projectType
    }
  },
  methods: {
    init () {
      this.loading = true;
      this.dialog = false;
      this.r_dialog = false;
      if ((this.searchParams.mobile && !(/^1[3456789]\d{9}$/.test(this.searchParams.mobile))) || (this.searchParams.email && !(/^\w+((.\w+)|(-\w+))@[A-Za-z0-9]+((.|-)[A-Za-z0-9]+).[A-Za-z0-9]+$/.test(this.searchParams.email))) ) {
        this.loading = false;
        this.$message({
          message: this.searchParams.mobile ? '手机号格式错误，请重新输入' : this.searchParams.email ? '邮箱格式错误，请重新输入' : '请输入查询条件',
          type: 'warning'
        });
        return;
      }
      if (this.searchParams.createTime) {
        this.searchParams.strStartCreateTime = this.searchParams.createTime[0] || ''
        this.searchParams.strEndCreateTime = this.searchParams.createTime[1] || ''
      }
      let searchParams = {
        projectId: this.$route.query.id,
        ...this.searchParams
      }
      this.api.getMedsciProjectUserPage(searchParams).then(response => {
        this.loading = false
        this.total = response.totalSize || 0;
        this.list = response.data || []
        if (response.status !== 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
      }).catch(() => this.loading = false)
    }
  }
}
</script>
