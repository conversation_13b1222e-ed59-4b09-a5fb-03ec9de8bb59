<template>
    <ms-table	:loading="loading"
              :tableData="list"
              :tableHeader="tableHeader"
              :showSelection="true"
              @handleSelectionChange="handleSelectionChange"
              :style="{height: 'calc(100vh - 245px)'}">
    </ms-table>
</template>

<script>
export default {
	name: "member-import-user",
	data () {
		return {
      loading: false,
      list: [],
      tableHeader: [
        { label: '角色', property: 'roleName' },
      ],
      selectList: []
		}
	},
  created() {
    this.init()
  },
	methods: {
    init() {
      this.loading = true;
      let searchParams = {
        projectId: this.$route.query.id 
      }
      this.api.getMedsciRoleDicList(searchParams).then(response => {
        this.loading = false
        this.total = response.totalSize || 0;
        this.list = response.data || []
        if (response.status !== 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
      }).catch(() => this.loading = false)
    },
    handleSelectionChange(val) {
      this.selectList = [...val]
    }
	}
}
</script>
