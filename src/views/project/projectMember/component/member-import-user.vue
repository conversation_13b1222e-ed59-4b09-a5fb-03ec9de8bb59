<template>
    <ms-table	:currentPage="searchParams.pageIndex"
              :loading="loading"
              :pageSize="searchParams.pageSize"
              :tableData="list"
              :tableHeader="tableHeader"
              :total="total"
              :showSelection="true"
              @current-change="current_change"
              @size-change="size_change" 
              @handleSelectionChange="handleSelectionChange"
              :style="{height: 'calc(100vh - 245px)'}">
      <div slot="ms-table-header">
        <msProjectSearch :model.sync="searchParams['selectProjectId']" width="150px" label="项目"></msProjectSearch>
        <ms-input width="180px" :model.sync="searchParams['name']"  label="用户" placeholder="用户名/姓名/手机/邮箱"></ms-input>
        <ms-picker width="240px" :model.sync="searchParams['createTime']" label="创建时间" type="daterange"></ms-picker>
        <el-button @click="init" type="primary" plain icon="el-icon-search">查询</el-button>
      </div>
    </ms-table>
</template>

<script>
import msProjectSearch from "@/components/MsCommon/ms-project-search"
export default {
	name: "member-import-user",
	data () {
		return {
      loading: false,
      searchParams: {
        pageSize: 10,
        pageIndex: 0,
        selectProjectId: '',
        name: "",
        startDate: "",
        endDate: "",
        createTime: []
      },
      total: 0,
      list: [],
      tableHeader: [
        { label: '用户名', property: 'userName' },
        { label: '姓名', property: 'realName' },
        { label: '医院', property: 'companyName' },
        { label: '科室', property: 'departmentName' },
        { label: '职称', property: 'professionalName' },
        { label: '创建时间', property: 'createdTime' }
      ],
      selectList: []
		}
  },
  components: {
    msProjectSearch
  },
  created() {
    this.init()
  },
	methods: {
    init() {
      this.loading = true;
      if (this.searchParams.createTime) {
        this.searchParams.startDate = this.searchParams.createTime[0] || ''
        this.searchParams.endDate = this.searchParams.createTime[1] || ''
      }
      let searchParams = {
        projectId: this.$route.query.id,
        ...this.searchParams
      }
      this.api.getPtkProjectUserPage(searchParams).then(response => {
        this.loading = false
        this.total = response.totalSize || 0;
        this.list = response.data || []
        if (response.status !== 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
      }).catch(() => this.loading = false)
    },
    size_change (val) {
      this.searchParams.pageSize = val;
      this.$store.dispatch('SetPagesize', val)
      this.init();
    },
    current_change (val) {
      this.searchParams.pageIndex = val;
      this.init();
    },
    handleSelectionChange(val) {
      this.selectList = [...val]
    }
	}
}
</script>
