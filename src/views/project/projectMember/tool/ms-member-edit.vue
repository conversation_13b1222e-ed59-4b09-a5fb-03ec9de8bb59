<template>
  <div class="right-dialog">
    <el-scrollbar wrap-class="right-dialog-scroll" ref="memberScroll">
      <div class="user-container" v-loading="getLoading" id="dialogScroll">
        <div class="user-header">
          <div class="tab-li" :class="{'ch-li': activeName === 'base'}" @click="tab_change('base')">基本信息</div>
          <div class="tab-li" :class="{'ch-li': activeName === 'ext'}" @click="tab_change('ext')">扩展信息</div>
        </div>
        <el-form ref="submitRef"
                :model="userData"
                :rules="model.id && operationLocal == 'edit'?formConfig.ruleEdit:formConfig.rule"
                label-width="85px"
                class="rule-form"
                style="margin-top: 10px;">
          <el-row>
            <el-col :span="20" :offset="1" v-show="activeName === 'base'">
              <template v-for="(item,index) in formConfig.formField">
                <!-- <el-form-item v-if="!(item.prop === 'password' && model.id)" -->
                <el-form-item v-if="!(item.prop === 'mobile' && model.id && !model.mobile)"
                              :key="index"
                              :prop="item.prop"
                              :label="item.label">
                  <component :is="item.component" 
                            :model.sync="userData[item.prop]"
                            :index="item.prop"
                            :width="item.width || '100%'"
                            :disabled="item.disabled || (item.prop=='mobile'&& model.id && operationLocal == 'edit')"
                            :type="item.type"
                            :placeholder="item.placeholder"
                            :options="item.options"
                            @blurEvent="blurEvent">
                  </component>
                </el-form-item>
              </template>
            </el-col>
            <el-col :span="20" :offset="2" v-show="activeName === 'ext'">
              <el-form-item v-for="(item,index) in formConfig.formUserExtField"
                            :key="index"
                            :label="item.label">
                <component :is="item.component"
                          v-model="userExtData[item.prop]"
                          :model.sync="userExtData[item.prop]"
                          :width="item.width || '100%'"
                          :disabled="item.disabled "
                          :type="item.type"
                          :placeholder="item.placeholder"
                          :options="item.options"
                          :projectId="$store.getters.projectId || $route.query.id"
                          @bindData="bindData">
                </component>
              </el-form-item>
            </el-col>
          </el-row>
          <!-- 重复信息 -->
          <div class="repet-area" v-if="isRepetInfo">
            <p>提示: 发现可能已存在的账号, 需要重新确认用户身份及信息</p>
            <el-table :data="repetList" border style="width: 98%;">
              <el-table-column prop="userName" label="用户名"></el-table-column>
              <el-table-column prop="realName" label="真实姓名"></el-table-column>
              <el-table-column prop="mobile" label="手机号"></el-table-column>
              <el-table-column prop="email" label="邮箱"></el-table-column>
              <el-table-column fixed="right" width="80" label="操作">
                <template slot-scope="scope">
                  <el-button @click="getUserDetail(scope.row.id)" class="repet-button" plain>确认</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-form>
      </div>
    </el-scrollbar>
    <footer class="right-dialog-footer">
      <el-button @click="submitForm"
                 :loading="loading"
                 size="mini"
                 type="primary">确 定</el-button>
      <el-button @click="$emit('close')"
                 size="mini">取 消</el-button>
    </footer>
  </div>
</template>

<script>
import formConfig from '../data/form-config' 
import MsDepartmentSearch from '@/components/MsCommon/ms-department-search'
import MsHospitalSearch from '@/components/MsCommon/ms-hospital-search'
import MsProfessionalCascader from '@/components/MsCommon/ms-professional-lazycascader'
import MsAreaCascader from '@/components/MsCommon/ms-area-lazycascader'
import MsRoleSearch from '@/components/MsCommon/ms-role-search'
import { mapGetters } from "vuex";
export default {
  name: "ms-member-edit",
	data () {
		return {
      loading: false,
      getLoading: false,
      activeName: 'base',
      formConfig: formConfig,
      userData: {
        userName: "",
        realName: "",
        email: "",
        mobile: "",
        wechatAccount: "",
        qqAccount: "",
        status: 1,
        roleList: []
      },
      userExtData: {
        provinceId: 0,
        provinceName: "",
        cityId: 0,
        cityName: "",
        districtId: 0,
        districtName: "",
        companyId: 0,
        companyName: "",
        departmentId: 0,
        departmentName: "",
        professionalId: 0,
        professionalName: "",
        professionalCatId: 0,
        professionalCatName: "",
        clinicalTags: "",
        researchTags: "",
        briefIntroduction: "",
        avatar: "",
      },
      submitData: {},
      isRepetInfo: false,
      repetList: [],
      //common
      operationLocal: ""
		}
	},
	props: {
		model: Object,
		operation: String
  },
  components: {
    MsDepartmentSearch,
    MsHospitalSearch,
    MsProfessionalCascader,
    MsAreaCascader,
    MsRoleSearch
  },
  computed: {
    ...mapGetters(["info"])
  },
  created() {
    this.init()
  },
	methods: {
    init() {
      this.operationLocal = this.operation || this.$route.query.operation
      if(this.operationLocal === 'edit' && this.model.userId) {
        this.getUserDetail(this.model.userId)
      } 
    },
    getUserRole(id) {
      let params = {
        userId: id,
        projectId: this.$route.query.id
      }
      this.getLoading = true;
      this.api.getProjectUserAndRoleDicList(params).then( response => {
        this.getLoading = false;
        if(response.status === 200) {
          this.userData.roleList = response.data
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '获取角色信息失败')
        }
      }).catch(() => this.getLoading = false)
    },
    getUserDetail(id) {
      let params = {
        userId: id,
        projectId: this.$route.query.id
      }
      this.getLoading = true;
      this.api.getUserEeatil(params).then( response => {
        this.getLoading = false;
        if(response.status === 200 && response.data && response.data.getUserResponse) {
          this.userData = { ...response.data.getUserResponse, roleList: [] }
          this.userExtData = {
            ...this.userExtData, 
            ...response.data.getUserExtResponse, 
            avatar: response.data.getUserResponse.avatar,
            areaMap: [response.data.getUserExtResponse.provinceId, response.data.getUserExtResponse.cityId, response.data.getUserExtResponse.districtId],
            professionalMap: response.data.getUserExtResponse.professionalId ? [response.data.getUserExtResponse.professionalCatId, response.data.getUserExtResponse.professionalId] : [response.data.getUserExtResponse.professionalCatId]
          }
          this.getUserRole(this.userData.id)
          this.operationLocal = 'edit'
        }
      }).catch(() => this.getLoading = false)
    },
    tab_change(val) {
      this.activeName = val
    },
    blurEvent(index) {
      if (index === 'userName' || index === 'mobile' || index === 'email') {
        this.$refs["submitRef"].validateField(index, valid => {
          if (!valid) {
            this.checkUserInfo(false)
          }
        })
      }
    },
		submitForm () {

      this.$refs["submitRef"].validate(valid => {
        if(valid) {
          this.loading = true;
          this.checkUserInfo(true);
        }
      })
    },
    checkUserInfo(apiExc) {
      let checkParams = {
        userName: this.userData.userName,
        mobile: this.userData.mobile,
        email: this.userData.email,
        projectId: this.$route.query.id,
        id: this.operationLocal === 'edit' && this.userData.id ? this.userData.id : null
      }
      this.api.checkUserList(checkParams).then(response => {
        if (response.status === 200) {
          if (response.data && response.data.length > 0) {
            this.repetList = response.data
            this.isRepetInfo = true;
            this.loading = false
            this.$nextTick(() => {
              this.$refs['memberScroll'].wrap.scrollTop = this.$refs['memberScroll'].wrap.scrollHeight
            })
          } else {
            this.repetList = [];
            this.isRepetInfo = false;
            if (apiExc) {
              if(this.operationLocal === 'edit' && this.userData.id) {
                this.updateUserApi()
              } else {
                this.addUserApi()
              }
            } 
          }
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
          this.loading = false
        }
      }).catch( () => {
        this.loading = false;
        this.$emit('close')
      })
    },
    updateUserApi() {
      let params = {
        projectUserId: this.model.id,
        userId: this.info.userId,
        username: this.info.userName,
        projectId: this.$route.query.id,
        updateMedsciUserRequest: {...this.userData, avatar: this.userExtData.avatar, opUserId: this.info.userId},
        updateMedsciUserExtRequest: this.userExtData,
        getMedsciRoleDicListResponses: this.userData.roleList
      }
      delete params.updateMedsciUserRequest.mobile
      this.api.updateMedsciUserRole(params).then(response => {
        if(response.status === 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
          this.$emit('up-date')
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
        this.loading = false
      }).catch( () => {
        this.loading = false;
        this.$emit('close')
      })
    },
    addUserApi() {
      let params = {
        userId: this.info.userId,
        username: this.info.userName,
        projectId: this.$route.query.id,
        addMedsciUserRequest: {...this.userData, avatar: this.userExtData.avatar, opUserId: this.info.userId},
        addMedsciUserExtRequest: this.userExtData,
        getMedsciRoleDicListResponses: this.userData.roleList,
        registerFrom: 0 // 注册来源
      }
      this.api.addMedsciUserRole(params).then(response => {
        if(response.status === 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
          this.$emit('up-date')
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
        this.loading = false
      }).catch( () => {
        this.loading = false;
        this.$emit('close')
      })
    },
    bindData(val) {
      if(val.operation === 'department') {
        this.userExtData.departmentId = val.model.id;
        this.userExtData.departmentName = val.model.name;
      } else if (val.operation === 'hospital') {
        this.userExtData.companyId = val.model.id;
        this.userExtData.companyName = val.model.name;
      } else if (val.operation === 'area') {
        this.userExtData.provinceId = val.model[0].id;
        this.userExtData.provinceName = val.model[0].name;
        this.userExtData.cityId = val.model[1].id;
        this.userExtData.cityName = val.model[1].name;
        this.userExtData.districtId = val.model[2].id;
        this.userExtData.districtName = val.model[2].name;
      } else if (val.operation === 'professional') {
        this.userExtData.professionalCatId = val.model[0].id;
        this.userExtData.professionalCatName = val.model[0].name;
        this.userExtData.professionalId = val.model[1] ? val.model[1].id : 0;
        this.userExtData.professionalName = val.model[1] ? val.model[1].name : '';
      }
    }
	}
}
</script>

<style lang="scss" scoped>
.user-container {
  .user-header {
    width: 100%;
    height: 25px;
    text-align: center;
    .tab-li {
      display: inline-block;
      width: 80px;
      height: 25px;
      line-height: 28px;
      font-size: 12px;
      text-align: center;
      margin: 0 20px;
      cursor: pointer;
      &.ch-li {
        font-weight: 600;
        color: #409EFF;
        border-bottom: 1px solid #409EFF;
      }
    }
  }
  .right-dialog {
    height: 100%;
    position: relative;
    &-scroll {
      max-height: calc(100vh - 30px - 40px - 55px);
    }
    &-footer {
      position: absolute;
      width: 100%;
      bottom: 0;
      text-align: center;
    }
  }
  .repet-area {
    padding: 5px 0 0 0;
    p {
      font-size: 12px;
      color: #B9363B;
      padding-bottom: 5px;
    }
    .repet-row {
      margin: 10px 0;
      .repet-span {
        color: #333;
        font-size: 12px;
        padding-right: 20px;
      }
      .repet-width {
        min-width: 70px;
      }
      .repet-button {
        padding: 4px 12px !important;
        font-size: 12px !important;
        position: relative;
        top: -4px;
      }
    }
  }
}
</style>
