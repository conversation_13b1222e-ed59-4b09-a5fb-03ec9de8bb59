<template>
  <ms-right-content>
    <el-steps :active="activeStep" finish-status="success" align-center class="ms-step" space="200px">
      <el-step description="选择成员"></el-step>
      <el-step description="设定角色"></el-step>
      <el-step description="成功导入"></el-step>
    </el-steps>
    <MemberImportUser v-show="activeStep === 0" ref="importUser"></MemberImportUser>
    <MemberImportRole v-show="activeStep === 1" ref="importRole"></MemberImportRole>
    <div class="import-success" v-show="activeStep > 2">
      <div class="success-font">已成功导入{{success_total}}位项目成员！</div>
      <el-button plain class="success-btn" @click="$emit('up-date')">关闭</el-button>
    </div>
    <template slot="footer" v-if="activeStep < 2">
      <el-button @click="next"
                 :loading="loading"
                 size="mini"
                 type="primary"
                 v-text="activeStep < 1 ? '下一步' : '确认添加'"></el-button>
      <el-button @click="$emit('close')"
                 size="mini">取 消</el-button>
    </template>
  </ms-right-content>
</template>

<script>
import MemberImportUser from '../component/member-import-user'
import MemberImportRole from '../component/member-import-role'
import { mapGetters } from "vuex";
export default {
	name: "ms-member-import",
	data () {
		return {
      loading: false,
      activeStep: 0,
      success_total: 0
		}
	},
	props: {
		model: {
      type: Object,
      default: () => {
        return {}
      }
    },
		operation: String
  },
  components: {
    MemberImportUser,
    MemberImportRole
  },
  computed: {
    ...mapGetters(["info", "step", "userList", "roleList", "userImportData"])
  },
  created() {
    // 可能后面会加上选中值，从vuex中获取
  },
	methods: {
    next() {
      if (this.activeStep === 0) {
        let userList = this.$refs.importUser.selectList
        let list = []
        if (userList.length > 0) {
          this.activeStep = 1;
          userList.forEach(item => {
            list.push({userId: item.userId})
          });
          this.$store.dispatch('SetImportStep', this.activeStep)
          this.$store.dispatch('SetImportUser', list)
        } else {
          this.PUBLIC_Methods.apiNotify('请选择项目成员', 'warning')
        }
      } else if (this.activeStep === 1) {
        let roleList = this.$refs.importRole.selectList
        if (roleList.length > 0) {
          this.loading = true;
          this.$store.dispatch('SetImportRole', roleList)

          let params = {
            projectId: this.$route.query.id,
            userId: this.info.userId,
            username: this.info.userName,
            userIds: this.userImportData.userList,
            roleIds: this.userImportData.roleList
          }
          this.success_total = this.userImportData.userList && this.userImportData.userList.length
          this.api.addBatchOtherProjectUserAndRole(params).then(response => {
            if(response.status === 200) {
              this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
              this.activeStep = 3
            } else {
              this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
            }
            this.loading = false
          }).catch( () => {this.loading = false;})
            
        } else {
          this.PUBLIC_Methods.apiNotify('请选择项目角色', 'warning')
        }
      } 
    }
	}
}
</script>

<style lang="scss" scoped>
  .ms-step{
    justify-content: center;
    /deep/ .el-step__description {
      margin: 5px 0 10px;
    }
  }
  .import-success {
    text-align: center;
    .success-font {
      text-align: center;
      margin: 120px 0 100px;
      font-size: 18px;
    }
    .success-btn {
      width: 150px !important;
    }
  }
</style>
