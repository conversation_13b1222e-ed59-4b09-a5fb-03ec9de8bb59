// import store from '@/store'

const domConfig = {
  listSearch: [
  ],
  // 日常活动
  tableHeaderO: [
    { label: '活动ID', property: 'activityTemplateId', sortable: true, width: '80' },
    { label: '活动名称', property: 'activityTemplateName'},
    { label: '参与时间', property: 'createdTime',width:120},
    { label: '参与用户', property: 'username'},
    { label: '用户姓名', property: 'realName'},
    { label: '用户医院', property: 'hospital'},
    { label: '用户科室', property: 'department'},
    { label: '职称', property: 'professionalName'},
    { label: '手机号', property: 'mobile'},
    { label: '备注', property: 'remarks'}
  ],
  // 助力活动
  tableHeader: [
    { label: '活动ID', property: 'activityTemplateId', sortable: true, width: '80' },
    { label: '活动名称', property: 'activityTemplateName'},
    { label: '参与时间', property: 'createdTime',width:120},
    { label: '参与用户', property: 'username'},
    { label: '推荐人', property: 'recommenderUserName'},
    { label: '邀请用户（人数）', property: 'recommenderNumber', width: '110'},
    { label: '用户姓名', property: 'realName'},
    { label: '用户医院', property: 'hospital'},
    { label: '用户科室', property: 'department'},
    { label: '职称', property: 'professionalName'},
    { label: '手机号', property: 'mobile'},
    { label: '备注', property: 'remarks'}
  ],
  // tableButtons: [
  //   {
  //     label: '查看',
  //     icon: '',
  //     role: '',
  //     operation: 'edit',
  //     component: '',
  //     way: 'page',
  //     params: ['id'],
  //     type: 'primary',
  //     title: '查看详情',
  //     width: '45%'
  //   },
  // ],
  soltButtons: [
    { 
      label: '批量导出',
      type: 'info',
      way: 'banthExport',
      identify: 'banthExport'
    },
  ],
}

export default domConfig;
