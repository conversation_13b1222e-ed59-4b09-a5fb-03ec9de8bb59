// import store from '@/store'
const domConfig = {
  listSearch: [
    {
      label: '活动名称',
      placeholder: '请输入',
      model: 'name',
      component: 'ms-input'
    },
    {
      label: '创建人',
      placeholder: '请选择',
      model: 'createdBy',
      component: 'ms-createby-search'
    },
    {
      label: '活动时间',
      placeholder: '请选择时间段',
      model: 'createTime',
      component: 'ms-picker',
      type: 'daterange',
      width: '240px'
    },
  ],
  tableHeader: [
    { label: '活动ID', property: 'id', sortable: true, width: '60' },
    { label: '活动标题', property: 'name', width: '200'},
    { label: '活动类型', property: 'type', width: '100' },
    { label: '活动时间', property: 'activeTime', width: '80' },
    { label: '活动状态', property: 'status', width: '80' },
    { label: '参与用户/访问用户', property: 'activeUser'},
    { label: '创建人', property: 'createdName'},
    { label: '创建时间', property: 'createdTime'}
  ],
  tableButtons: [
    {
      label: '编辑',
      icon: '',
      role: '',
      operation: 'edit',
      component: 'active-operation',
      way: 'page',
      type: 'primary',
      path: 'active-operation',
      params: ['id'],
      identify: 'edit'
    },
    {
      label: '',
      icon: '',
      role: '',
      operation: 'editStatus',
      component: 'msActiveOperation',
      way: 'dialog',
      field: 'status',
      rule: {
        1: { label: '去审', type: '', operation: 'toreview' },
        0: { label: '审核', type: 'success', operation: 'approval' }
      },
      identify: 'status'
    }
  ],
  soltButtons: [
    // { 
    //   label: '批量审核', 
    //   type: 'primary',
    //   operation: 'approval',
    //   component: 'msActiveOperation',
    //   way: 'batch',
    //   identify: 'batch_approval'
    // },
    // { 
    //   label: '批量去审', 
    //   type: 'primary',
    //   operation: 'toreview',
    //   component: 'msActiveOperation',
    //   way: 'batch',
    //   identify: 'batch_toreview'
    // },
    { 
      label: '创建活动', 
      type: 'primary',
      operation: 'approval',
      component: 'msActiveType',
      way: 'dialog',
      identify: ''
    }
  ]
}

export default domConfig;
