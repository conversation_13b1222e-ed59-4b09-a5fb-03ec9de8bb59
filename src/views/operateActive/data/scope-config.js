import activeUrl from '@/store/data/activeUrl.js'

const scopeConfig = {
  show: {
    status: () => {
      return {
        type: 'status',
        rule: {
          0: { label: '待审核', background: '#A7ADBD' },
          1: { label: '已审核', background: '#40A23F' },
        }
      }
    },
    type: () => {
      return {
        type: 'status',
        rule: {
          1: { label: '日常活动'},
          2: { label: '助力活动'}
        }
      }
    },
    name: () => {
      return {
        type: 'preview',
        config: {
          field: 'activePreview',
          pageUrl: `${activeUrl['active']}`,
          previewName: 'msActivePreview',
          way: 'activePreview'
        }
      }
    },
    publishedTime: () => {
      return {
        type: 'formatTime',
        cFormat: '{y}-{m}-{d} {h}:{i}'
      }
    },
    createdTime: () => {
      return {
        type: 'formatTime',
        cFormat: '{y}-{m}-{d} {h}:{i}'
      }
    },
    activeUser: () => {
       return {
        type: 'fidd',
        fields: [
          {name: 'numberOfParticipants', way: 'page', path: 'active-details',
          params: ['id',{
            keyName: 'activeType',
            valName: 'type'
          }]},
          {name: 'numberOfVisitors', way: 'text'}
        ]
       }
    },
    username: () => {
      return {
        type: 'webLink',
        config: {
          role: '',
          operation: 'edit',
          way: 'page',
          path: 'active-user-details',
          params: [{
            keyName: 'id',
            valName: 'encodeUserId'
          }],
        }
      }
    },
    recommenderNumber: () => {
      return {
        type: 'preview',
        config: {
          type: 'preview',
          way:'dialog',
          component: 'activePreview'
        }
      }
    },
  },
  headerShow: {
    type: () => {
      return {
        type: 'dropdown',
        icon: 'icon-funnel',
        options: [
          { label: '全部', value: null },
          { label: '日常活动', value: 1 },
          { label: '助力活动', value: 2 },
        ],
        operation: 'query'
      }
    },
    status: () => {
      return {
        type: 'dropdown',
        icon: 'icon-funnel',
        options: [
          { label: '全部', value: null },
          { label: '待审核', value: 0 },
          { label: '已审核', value: 1 },
        ],
        operation: 'query'
      }
    }
  }
}

export default scopeConfig;
