<template>
	<ms-table
		:currentPage="searchParams.pageIndex"
		:loading="loading"
		:operationButtons="detailsConfig.tableButtons"
		:pageSize="searchParams.pageSize"
		:scopeConfig="scopeConfig.show"
    :scopeHeader="scopeConfig.headerShow"
		:tableData="list"
    :showSelection="true"
		:tableHeader="$route.query.activeType == 1 ? detailsConfig.tableHeaderO:detailsConfig.tableHeader"
		:total="total"
		@current-change="current_change"
		@operation-change="operation_change"
		@size-change="size_change"
    @header-operation="header_operation"
    @handleSelectionChange="handleSelectionChange"
    class="table-svg"
	>
    <!-- 列表搜索去区域插槽 -->
		<template slot="ms-table-header">
			<div class="slot-search">
				<template v-for="(searchItem, key) in detailsConfig.listSearch">
					<component
						:index="searchItem.index || ''"
						:is="searchItem.component"
						:key="key"
            :width="searchItem.width || '150px'"
            :model.sync="searchParams[searchItem.model]"
            :label="searchItem.label"
						:operation="searchItem.operation || ''"
						:options="searchItem.options || []"
						:placeholder="searchItem.placeholder || ''"
						:type="searchItem.type || ''"
            :multiple="searchItem.multiple"
            :disabled="searchItem.disabled"
            :code="searchItem.code"
					></component>
				</template>
        
			</div>
			<div class="slot-button-article clearfix">
				<template v-for="(item, index) in detailsConfig.soltButtons">
					<el-button :key="index" 
                     :type="item.type" 
                     size="mini"
                     :icon="item.icon"
                     @click="operation_change({operation: item})"
                     :disabled="item.roleDisabled"
                     plain>{{ item.label }}</el-button>
				</template>
			</div>
			<el-dialog :visible.sync="dialog" 
                 closeable 
                 show-close
                 :close-on-click-modal="false"
                 :width="dialogWidth"
                 :title="dialogTitle">
				<component :is="dialogComponent" 
                   :model="scopeInfo" 
                   :operation="dialogOperation" 
                   @close="dialog = !dialog" 
                   @up-date="init" 
                   v-if="dialog"></component>
			</el-dialog>
		</template>
	</ms-table>
</template>

<script>
import operateActiveMixin from "./operateActive-mixin"
import tableMixins  from "../common/mixins/table"
import activePreview from "./tool/ms-active-preview"
export default {
  name: "ms-user-details",
  mixins: [tableMixins,operateActiveMixin],
  data () {
    return {
      dealType: 1,
      ids: []
    }
  },
  components: {
    activePreview
  },
  created() {
    this.handleClick('reset')
  },
  methods: {
    apiInit (params) {
      let searchParams = {
        ...params,
        activityTemplateId: this.$route.query.id
      }
      this.api.getParticipatePage(searchParams).then(response => {
        this.loading = false
        this.total = response.totalSize || 0;
        this.list = response.data || []
        if (response.status !== 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
      }).catch(() => this.loading = false)
    },
    handleSelectionChange(val) {
      this.ids = []
      val.forEach(element => {
        this.ids.push(element.id)
      });
    },
    operation_change_module(val) {
      if(val.operation.way === 'banthExport') {
        if(this.ids.length == 0) {
          this.$message.warning('请选择至少一条数据')
          return
        }
        this.loading = true
        let params = {
          activityTemplateId: this.$route.query.id,
          dealType: this.dealType,
          ids: this.ids
        }
        this.api.participateExportExcel(params).then(response => {
          if(response.status === 200) {
            window.location.href = response.data;
          } else {
            this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
          }
          this.loading = false
        }).catch(() => this.loading = false)
      }
  },
  },
};
</script>
