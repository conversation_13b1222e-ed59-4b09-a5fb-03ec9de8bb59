<template>
  <ms-operation-dialog title="活动类型选择">
    <template slot="content">
      <div v-for="(item, index) in activeTypeList" :key="index" class="active-item" @click="createActive(item.typeId)">
        <p class="title">{{ item.type }}</p>
        <p v-html="item.summary"></p>
      </div>
    </template>
  </ms-operation-dialog>
</template>

<script>
export default {
	name: "ms-active-type",
	data () {
		return {
      userInfo: {},
      activeTypeList: [
        {typeId: 1, type: '日常活动', summary: '拉新、完善率提升<br>礼品发放走线下流程'},
        {typeId: 2, type: '助力活动', summary: '多人助力领好礼'},
      ]
		}
	},
	props: [
		"model",
		"operation"
  ],
  created() {
    this.userInfo = this.$store.getters.info || {}
  },
	methods: {
    createActive(typeId) {
      console.log(typeId)
      this.$router.push({
        path:'active-operation',
        query: {typeId: typeId}
      })
    }
	}
}
</script>

<style scoped lang="scss">
/deep/ .ms-dialog-main{
  padding: 30px 0px!important;
  box-sizing: border-box;
  display: flex;
  align-items: center;
}
.active-item{
  width: 33%;
  height: 67px;
  text-align: center;
  border: .5px solid #eee;
  padding: 6px;
  margin-right: 15px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  .title{
    font-weight: 550;
    margin-bottom: 5px;
  }
}
</style>
