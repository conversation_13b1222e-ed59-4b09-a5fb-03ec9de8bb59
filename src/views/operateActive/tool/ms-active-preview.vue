<template>
  <ms-operation-dialog :title="title">
    <template slot="content">
    <ms-table
      :currentPage="pageIndex"
      :loading="loading"
      :pageSize="pageSize"
      :tableData="list"
      :tableHeader="tableHeader"
      :scopeConfig="show"
      :total="total"
      :elTableHeight="350"
      @current-change="current_change"
      @operation-change="operation_change"
      @size-change="size_change"
      class="table-svg high-row-none"
    >
    </ms-table>
    </template>
  </ms-operation-dialog>
</template>

<script>
import tableMixins  from "../../common/mixins/table"
export default {
	name: "ms-active-preview",
  mixins: [tableMixins],
	data () {
		return {
      loading: false,
      pageIndex:1,
      tableHeader: [
        { label: '邀请用户', property: 'username', width: '100' },
        { label: '用户姓名', property: 'realName', width: '100'},
        { label: '用户医院', property: 'hospital', width: '100' },
        { label: '用户科室', property: 'department', width: '80' },
        { label: '职称', property: 'professionalName', width: '80' },
        { label: '手机号', property: 'mobile'},
        { label: '备注', property: 'remarks'}
      ],
      show: {
        username: () => {
          return {
            type: 'webLink',
            config: {
              role: '',
              operation: 'edit',
              way: 'page',
              path: 'active-user-details',
              params: [{
                keyName: 'id',
                valName: 'encodeUserId'
              }],
            }
          }
        },
      },
      total: 0,
      list: [],
      title: '邀请用户(5)人'
		}
	},
	props: {
		model: Object,
		operation: String
    },
    created() {
      this.init()
    },
    methods: {
      apiInit() {
          this.loading = true
          let params = {
            pageIndex: this.pageIndex,
            pageSize: this.pageSize,
            activityTemplateId: this.model.activityTemplateId,
            recommenderUserId: this.model.encodeUserId
          }
          this.api.getRecommenderParticipatePage(params).then(response => {
              this.loading = false;
              console.log(response, 'encodeUserId')
              if (response.status === 200) {
                  this.list = response.data
                  this.total = response.totalSize
                  this.title = `邀请用户（${response.totalSize}）人`
              } else {
                this.list = []
                this.total = 0
                this.title = '邀请用户（0）人'
              }
          })
        }
    }
}
</script>

<style scoped lang='scss'>
// /deep/ .el-table--mini{
// }
// /deep/ .el-table__body{
//   min-height: 100px!important;
//   max-height: 450px!important;
//   overflow: scroll;
// }
	.content-top {
		margin-bottom: 10px;
		font-size: 14px;
		text-align: left;
	}
	.content-top .top-label {
		font-weight: bold;
		margin-right: 12px;
		display: inline-block;
	}
</style>
