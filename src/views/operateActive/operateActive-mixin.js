import msActiveOperation from './tool/ms-active-operation'
import msActiveType from './tool/ms-active-type'
import domConfig from "./data/dom-config";
import scopeConfig from "./data/scope-config";
import detailsConfig from "./data/details-config";
import msActivePreview from './tool/ms-active-preview'

export default {
  data() {
    return {
      domConfig: domConfig, // => dom展示数据
      scopeConfig: scopeConfig, // => 显示信息配置 
      detailsConfig: detailsConfig
    }
  },
  components: {
    msActiveOperation,
    msActivePreview,
    msActiveType
  }
}
