<template>
	<ms-table
		:currentPage="searchParams.pageIndex"
		:loading="loading"
		:operationButtons="domConfig.tableButtons"
		:pageSize="searchParams.pageSize"
		:scopeConfig="scopeConfig.show"
    :scopeHeader="scopeConfig.headerShow"
		:tableData="list"
		:tableHeader="domConfig.tableHeader"
		:total="total"
		@current-change="current_change"
		@operation-change="operation_change"
		@size-change="size_change"
    @header-operation="header_operation"
    @handleSelectionChange="handleSelectionChange"
    class="table-svg high-row-none"
	>
    <!-- 列表搜索去区域插槽 -->
		<template slot="ms-table-header">
			<div class="slot-search">
				<template v-for="(searchItem, key) in domConfig.listSearch">
					<component
						:index="searchItem.index || ''"
						:is="searchItem.component"
						:key="key"
            :width="searchItem.width || '150px'"
            :model.sync="searchParams[searchItem.model]"
            :label="searchItem.label"
						:operation="searchItem.operation || ''"
						:options="searchItem.options || []"
						:placeholder="searchItem.placeholder || ''"
						:type="searchItem.type || ''"
            :multiple="searchItem.multiple"
            :disabled="searchItem.disabled" 
            :config="searchItem.config"
            v-if="key < search_number"
					></component>
				</template>
        <div class="inlineBlock">
          <el-button @click="handleClick('query')" type="primary" plain icon="el-icon-search">查询</el-button>
          <el-button @click="handleClick('reset')">重置</el-button>
        </div>
        
			</div>
			<div class="slot-button-article clearfix">
				<template v-for="(item, index) in domConfig.soltButtons">
					<el-button :key="index" 
                     :type="item.type" 
                     size="mini"
                     :icon="item.icon"
                     v-show="!item.roleDisabled"
                     @click="operation_change({operation: item})"
                     plain>{{ item.label }}</el-button>
				</template>
			</div>
			<el-dialog :visible.sync="dialog" 
                 closeable 
                 show-close
                 :close-on-click-modal="false"
                 :width="dialogWidth"
                 :title="dialogTitle">
				<component :is="dialogComponent" 
                   :model="scopeInfo" 
                   :operation="dialogOperation" 
                   @close="dialog = !dialog" 
                   @up-date="init" 
                   v-if="dialog"></component>
			</el-dialog>
		</template>
	</ms-table>
</template>

<script>
import operateActiveMixin from "./operateActive-mixin"
import tableMixins  from "../common/mixins/table"
export default {
  name: "ms-operateActive-manage",
  mixins: [tableMixins,operateActiveMixin],
  components: {
    
  },
  data () {
    return {
      notifyInst: null,
      tipFlag: true,
      search_number: 3
    }
  },
  created() {
  },
  beforeDestroy() {
  },
  methods: {
    apiInit (params) {
      let searchParams = {...params}
      if (searchParams.createTime) {
        searchParams.startTime = searchParams.createTime[0] || ''
        searchParams.endTime = searchParams.createTime[1] || ''
      }
      this.api.getActivityTemplatePage(searchParams).then(response => {
        this.loading = false
        this.total = response.totalSize || 0;
        this.list = response.data || []
        response.data.forEach(element => {
            element.startTime = element.startTime ? element.startTime : ''
            element.endTime = element.endTime ? element.endTime : ''
            element.activeTime = '起'+ element.startTime + '止' + element.endTime
        });
        if (response.status !== 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
      }).catch(() => this.loading = false)
    }
  }
};
</script>
