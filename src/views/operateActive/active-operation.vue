<template>
  <section v-loading="getLoading" class="form-tab">
    <!-- 表单内容 -->
    <el-form ref="submitRef"
              class="rule-form"
              :model="submitData"
              :rules="rule"
              label-width="100px">
      <div class="base-set-style">基础设置</div>
      <el-form-item prop="type" label="活动类型">
        <el-select v-model="submitData.type" style="width:400px" disabled>
          <el-option
            v-for="(item, index) in activityTypeOptions"
            :key="index"
            :label="item.name"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item prop="name" label="活动名称">
        <el-input v-model="submitData.name" style="width:400px" placeholder="输入活动名称，不超过20个字" maxlength="20"></el-input>
      </el-form-item>
      <el-form-item prop="activeTime" label="活动时间">
        <el-date-picker
            v-model="submitData.activeTime"
            type="datetimerange"
            style="400px"
            value-format="yyyy-MM-dd HH:mm:ss"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期">
        </el-date-picker>
      </el-form-item>
      <div class="base-set-style">参与用户条件设置</div>
      <el-form-item prop="newUsersRestrict" label="用户要求">
        <el-radio-group v-model="submitData.newUsersRestrict">
          <el-radio :label="0">不限制</el-radio>
          <el-radio :label="1">新用户</el-radio>
        </el-radio-group>
        <p style="font-size:12px;color:#999">新用户：活动期间新注册的用户，以手机号为准。</p>
      </el-form-item>
      <el-form-item prop="usersPerfectInformation" label="完善信息">
        <el-radio-group v-model="submitData.usersPerfectInformation" @change="changePerfectInfo">
          <el-radio :label="0">不要求完善信息</el-radio>
          <el-radio :label="1">要求完善信息</el-radio>
        </el-radio-group>
        <p style="font-size:12px;color:#999">要求完善信息的活动，判断用户是否完善信息，未完善引导去完善，通过活动提供用户的信息完善率</p>
        <p v-if="submitData.usersPerfectInformation == 1"><el-button type="text" @click="downLoad">限制医院批量上传模版.xlsl</el-button> <span style="font-size:12px;color:#999">限制医院较多的情况下可下载该模版批量添加上传</span></p>
      </el-form-item>
      <template class="complete-cont" v-if="submitData.usersPerfectInformation == 1">
        <el-row
          v-for="(domain, index) in submitData.restrictList"
          :key="domain.key"
        >
          <el-col :span="12">
            <el-form-item
              label="限制医院："
              :prop="'restrictList.' + index + '.restrictHospital'"
              label-width="180px"
              :rules="{
                required: true, message: '是否限制医院', trigger: 'change'
              }"
            >
              <el-radio-group v-model="domain.restrictHospital">
                <el-radio :label="0">不限制</el-radio>
                <el-radio :label="1">限制</el-radio>
              </el-radio-group>
            </el-form-item>
            <template v-if="domain.restrictHospital == 1">
              <el-row :gutter="10">
                <el-col :span="12">
                  <el-form-item label="" :prop="'restrictList.' + index + '.hospitalList'">
                    <el-select
                      v-model="domain.hospitalList"
                      clearable
                      filterable
                      multiple
                      remote
                      :remote-method="remoteMethodH"
                      style="width:300px">
                      <el-option
                        v-for="item in hospitalOptions"
                        :key="item.id"
                        :label="item.hospitalName"
                        :value="item.hospitalName"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="10">
                  <el-form-item label="" :prop="'restrictList.' + index + '.fileList'">
                    <ms-file-upload v-model="domain.fileList" buttonLabel="批量上传添加" :isPrivate="false" fileSuffix="activity/upload" :limit=1></ms-file-upload>
                  </el-form-item>
                </el-col>
              </el-row>
            </template>
            <el-form-item
              label="限制科室："
              :prop="'restrictList.' + index + '.restrictDepartment'"
              label-width="180px"
              :rules="{
                required: true, message: '是否限制科室', trigger: 'change'
              }"
            >
              <el-radio-group v-model="domain.restrictDepartment">
                <el-radio :label="0">不限制</el-radio>
                <el-radio :label="1">限制</el-radio>
              </el-radio-group>
            </el-form-item>
            <template v-if="domain.restrictDepartment == 1">
              <el-row :gutter="10">
                <el-col :span="10">
                  <el-form-item label="" :prop="'restrictList.' + index + '.departmentList'">
                    <el-select
                      v-model="domain.departmentList"
                      clearable
                      filterable
                      multiple
                      remote
                      :remote-method="remoteMethodD"
                      style="width: 300px">
                      <el-option
                        v-for="item in departmentOptions"
                        :key="item.id"
                        :label="item.departmentName"
                        :value="item.departmentName"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
            </template>
            <el-form-item
              label="报名成功："
              :prop="'restrictList.' + index + '.method'"
              label-width="180px"
            >
              <el-radio-group v-model="domain.enterpriseWechat">
                <el-radio :label="1">添加企业微信</el-radio>
              </el-radio-group>
            </el-form-item>
            <template v-if="domain.enterpriseWechat">
              <el-form-item
                label=""
                :prop="'restrictList.' + index + '.enterpriseWechatQrCode'"
                label-width="180px"
              >
                <ms-single-image v-model="domain.enterpriseWechatQrCode"></ms-single-image>
              </el-form-item>
            </template>
            <el-form-item
              label="备注："
              :prop="'restrictList.' + index + '.remarks'"
              label-width="180px"
            >
              <el-input type="textarea" v-model="domain.remarks" placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item>
              <el-button @click.prevent="removeDomain(domain)" type="danger"  v-if="submitData.restrictList.length>1">删除</el-button>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item>
          <el-button @click="addDomain" style="margin-top:10px" type="primary">添加</el-button>
        </el-form-item>
      </template>
      <div class="base-set-style">活动页面设置</div>
      <el-form-item
        label="活动海报："
        prop="poster"
      >
        <ms-single-image v-model="submitData.poster"></ms-single-image>
        <p style="font-size:12px;color:#999">尺寸：宽750*高大于1334，大小不超过2M</p>
      </el-form-item>
      <el-form-item
        v-if="submitData.type == 2"
        label="邀请海报："
        prop="invitationPoster"
        :rules="{ required: true, message: '请上传邀请海报', trigger: 'change'}"
      >
        <ms-single-image v-model="submitData.invitationPoster"></ms-single-image>
        <p style="font-size:12px;color:#999">尺寸：宽750*高大于1334，大小不超过2M，注意分享二维码的位置，<el-button type="text" @click="downLoadPoster">下载海报预览图</el-button><span></span></p>
      </el-form-item>
      <el-form-item
        :label="submitData.type == 1 ? '参加按钮：': '按钮'"
        prop="participateButton"
      >
        <el-radio-group v-model="submitData.participateButton">
          <el-radio :label="0">固定在最下方</el-radio><el-button v-if="submitData.type == 2" type="text" icon="el-icon-info" style="color:#000;margin-left:-25px;margin-right:20px" @click="showFixedImg = !showFixedImg"></el-button><img v-if='showFixedImg' style="position:absolute;z-index: 10;width:188px;height:183px" src="https://static.medsci.cn/public-image/ms-image/b2c70330-2cf5-11ed-b66b-937b834e3ef9_固定.jpg" alt="">
          <el-radio :label="1">浮动在页面下方</el-radio><el-button v-if="submitData.type == 2" type="text" icon="el-icon-info" style="color:#000;margin-left:-25px;" @click="showFloatImg=!showFloatImg"></el-button><img v-if='showFloatImg' style="position:absolute;z-index: 10;width:188px;height:183px" src="https://static.medsci.cn/public-image/ms-image/e7b88540-2cf6-11ed-b66b-937b834e3ef9_悬浮.jpg" alt="">
        </el-radio-group>
      </el-form-item>
      <el-row style="background:#fff" v-if="submitData.type == 1">
        <el-col>
          <el-form-item prop="participateButtonPicture" label="" label-width="90px">
            <ms-single-image v-model="submitData.participateButtonPicture"></ms-single-image>
            <p style="font-size:12px;color:#999">尺寸：宽750*高120</p>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row style="background:#fff" v-if="submitData.type == 2">
        <el-col :span="5">
          <el-form-item prop="participateButtonPicture" label="" label-width="90px">
            <ms-single-image v-model="submitData.participateButtonPicture"></ms-single-image>
            <p style="font-size:12px;color:#999;">参与按钮：宽小于750*高小于120</p>
          </el-form-item>
        </el-col>
        <el-col :span="7">
          <el-form-item prop="participateButtonPictureRight" label="" label-width="0px">
            <ms-single-image v-model="submitData.participateButtonPictureRight"></ms-single-image>
            <p style="font-size:12px;color:#999;">分享按钮：宽小于750*高小于120</p>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-form-item
        label="分享小图："
        prop="shareIcon"
      >
        <ms-single-image v-model="submitData.shareIcon"></ms-single-image>
        <p style="font-size:12px;color:#999">H5分享出去展示的小图，尺寸200*200</p>
      </el-form-item>
      <el-form-item label="分享文案：" prop="shareTitle">
        <el-input placeholder="输入分享标题" v-model="submitData.shareTitle" style="width:400px"></el-input>
      </el-form-item>
      <el-form-item label="" prop="shareSummary">
        <el-input type="textarea" placeholder="输入分享内容" v-model="submitData.shareSummary" style="width:400px"></el-input>
        <p style="font-size:12px;color:#999">H5分享出去展示的标题及内容文案</p>
      </el-form-item>
    </el-form>
    <!-- 提交按钮 -->
    <footer-tool-bar v-loading="buttonLoading">
      <template slot="tool-content">
        <el-button type="primary" @click="info_operation('save')">保存</el-button>
        <el-button type="info" @click="info_operation('back')">返回</el-button>
      </template>
    </footer-tool-bar>
  </section>
</template>

<script>
import FooterToolBar from '@/components/ToolBar/footer-tool-bar'
import operateActiveMixin from "./operateActive-mixin"
import { mapGetters } from "vuex";
export default {
  name: "active-operation",
  mixins: [operateActiveMixin],
	data () {
		return {
      buttonLoading: false,
      getLoading: false,
      dataId: this.$route.query.id ? this.$route.query.id : 0,
      activityTypeOptions: [
        {id: 1, name: "日常活动"},
        {id: 2, name: "助力活动"},
      ],
      showFixedImg: false,
      showFloatImg: false,
      submitData: {
        restrictList: [{restrictHospital: '', hospitalList:[], fileList: [], restrictDepartment: '',departmentList: [],enterpriseWechat: '', enterpriseWechatQrCode:'',remarks: ''}],
        type: "",
        name: "",
        activeTime: [],
        newUsersRestrict: "",
        usersPerfectInformation: "",
        poster: "",
        invitationPoster: "",
        participateButton: "",
        participateButtonPicture: "",
        participateButtonPictureRight: "",
        shareIcon: "",
        shareTitle: "",
        shareSummary: "",
        // 规则
        activityRules: "0",
        activityRulesContent: "hhhh"
      },
      rule: {
        type: [
          { required: true, message: "请填写活动类型", trigger: 'blur' }
        ],
        name: [
          { required: true, message: "请输入活动名称", trigger: 'blur' }
        ],
        activeTime: [
          { required: true, message: "请选择活动时间", trigger: 'change' }
        ],
        newUsersRestrict: [
          { required: true, message: "请选择用户要求类型", trigger: 'change'}
        ],
        usersPerfectInformation: [
          { required: true, message: "请选择是否需要完善信息", trigger: 'change'}
        ],
        poster: [
          { required: true, message: "请上传活动海报", trigger: 'change'}
        ],
        participateButton: [
          { required: true, message: "请选择参加按钮位置", trigger: 'change'}
        ],
        participateButtonPicture: [
          { required: true, message: "请上传参加按钮图片", trigger: 'change'}
        ],
        participateButtonPictureRight: [
          { required: true, message: "请上传分享按钮图片", trigger: 'change'}
        ],
        shareIcon: [
           { required: true, message: "请上传分享小图", trigger: 'change'}
        ],
        shareTitle: [
          { required: true, message: "请填写分享标题", trigger: 'blur'}
        ],
        shareSummary: [
          { required: true, message: "请填写分享内容", trigger: 'blur'}
        ]
      },
      hospitalOptions: [],
      departmentOptions: []
		}
  },
  components: {
    FooterToolBar,
  },
  computed: {
    ...mapGetters(["info"])
  },
  created() {
    if(this.$route.query.typeId) {
      this.submitData.type = this.$route.query.typeId
    }
    this.init()
  },
  methods: {
    changePerfectInfo(val) {
      if(val){
        this.submitData.restrictList = [{restrictHospital: '', hospitalList:[], fileList: [], restrictDepartment: '',departmentList: [],enterpriseWechat: '', enterpriseWechatQrCode:'',remarks: ''}]
      }
    },
    addDomain() {
      this.submitData.restrictList.push({
        restrictHospital: '',
        hospitalList: [],
        fileList: [],
        restrictDepartment: '',
        departmentList: [],
        enterpriseWechat: '',
        enterpriseWechatQrCode: '',
        remarks: '',
        key: Date.now()
      });
    },
    removeDomain(item) {
      var index = this.submitData.restrictList.indexOf(item)
      if (index !== -1) {
        this.submitData.restrictList.splice(index, 1)
      }
    },
    remoteMethodH(val){
      if(val) {
        let params = {
          "name": val,
          "pageIndex": 1,
          "pageSize": 20
        }
        this.api.getNewHospitalList(params).then(res => {
          this.hospitalOptions = res.data
        })
      }
    },
    remoteMethodD(val){
      if(val) {
        let params = {
          "level": 0,
          "name": val,
          "parentId": -1
        }
        this.api.getHospitalDepartmentList(params).then(res => {
          this.departmentOptions = res.data
        })
      }
    },
    init() {
      let id = this.dataId
      if(id !== 0) {
        this.getLoading = true;
        this.submitData.id = id;
        this.api.getActivityTemplateById(id).then( response => {
          this.getLoading = false;
          if(response.status === 200) {
            let res = response.data
            if(res.usersPerfectInformation){
              res.restrictList.forEach(item => {
                if(item.restrictHospital) {
                  item.fileList = item.hospitalFile ? [{name: item.hospitalFile, url: item.hospitalFile}] : []
                }
              })
            }
            this.submitData = {
              ...this.submitData,
              ...res
            }
            this.submitData.activeTime = [res.startTime,res.endTime]
          } else {
            this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
          }
        }).catch(() => {
          this.getLoading = false;
        })
      } 
    },
    info_operation(val) {
      switch (val) {
        case 'save': 
          this.$refs["submitRef"].validate( valid => {
            if (valid) {
              this.dataId ? this.updateActive() : this.createActive()
            }
          })
          break;
        case 'back':
          this.$router.back();
          break;
        default: break;
      }
    },
    createActive() {
      this.buttonLoading = true;
      if(this.submitData.usersPerfectInformation){
        this.submitData.restrictList.forEach( item => {
          if(item.restrictHospital) {
            item.hospitalFile = item.fileList.length == 0 ? "": item.fileList[0].url
          }
        })
      }
      this.submitData.type =  this.$route.query.typeId
      let params = {
        userId: this.info.userId,
        username: this.info.userName,
        ...this.submitData,
        startTime: this.submitData.activeTime&&this.submitData.activeTime.length ? this.submitData.activeTime[0]: '',
        endTime: this.submitData.activeTime&&this.submitData.activeTime.length ? this.submitData.activeTime[1]: ''
      }
      this.api.insertActivityTemplate(params).then(response => {
        this.buttonLoading = false
        if(response.status === 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
          this.$router.back()
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
      }).catch(() => this.buttonLoading = false)
    },
    updateActive() {
      this.buttonLoading = true;
      if(this.submitData.usersPerfectInformation){
        this.submitData.restrictList.forEach( item => {
          if(item.restrictHospital) {
            item.hospitalFile = item.fileList ? (item.fileList.length == 0 ? "" : item.fileList[0].url) : ""
          }
        })
      }
      let params = {
        userId: this.info.userId,
        username: this.info.userName,
        ...this.submitData,
        startTime: this.submitData.activeTime&&this.submitData.activeTime.length ? this.submitData.activeTime[0]: '',
        endTime: this.submitData.activeTime&&this.submitData.activeTime.length ? this.submitData.activeTime[1]: ''
      }
      console.log(params)
      this.api.updateActivityTemplate(params).then(response => {
        this.buttonLoading = false
        if(response.status === 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
          this.$router.back()
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
      }).catch(() => this.buttonLoading = false)
    },
    downLoad() {
      window.location.href = 'https://static.medsci.cn/product/medsci-site/%E9%99%90%E5%88%B6%E5%8C%BB%E9%99%A2%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BF.xlsx'
    },
    downLoadPoster() {
      window.location.href = 'https://static.medsci.cn/public-image/ms-image/海报预览图.png'
    }
  },
  destroyed() {
    this.submitData.restrictList = []
  }
}
</script>
<style scoped lang='scss'>
/deep/ .el-row{
  background-color: #f9f9f9;
  margin-top: 10px;
  padding: 10px;
}
.rule-form{
  .base-set-style{
    text-align: left;
    padding-bottom: 6px;
    margin-bottom: 10px;
    border-bottom: 2px solid #eee;
    font-size: 14px;
    font-weight: 550;
  }
}
</style>
