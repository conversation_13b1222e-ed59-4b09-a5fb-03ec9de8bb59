<template>
    <section style="padding-bottom: 80px">
        <el-tabs v-model="activeName">
            <el-tab-pane label="调研信息" name="design">
                <el-form
                    ref="form"
                    style="text-align: left;"
                    :model="form"
                    :rules="rules"
                    label-width="100px"
                >   
                <el-row :gutter="26">
                  <el-col :span="16">
                    <el-row>
                      <el-col :span="24">
                        <el-form-item label="调研类型">
                          <el-radio-group v-model="form.isOpenResult" @input="changeResult">
                            <el-radio :label="0">问卷</el-radio>
                            <el-radio :label="1">投票</el-radio>
                          </el-radio-group>
                        </el-form-item>
                      </el-col>
                      <el-col :span="24">
                        <el-form-item label="调研内容" prop="templateId">
                          <ms-form-search v-model="templateSurveyName" :model.sync="templateSurveyName" @bindData="bindForm" :placeholder="'请输入关键词搜索，并选择'" style="width:400px"></ms-form-search>
                        </el-form-item>
                      </el-col>
                      <el-col :span="24">
                        <el-form-item label="调研名称" prop="templateName">
                          <el-input style="width: 400px" v-model="form.templateName"></el-input>
                        </el-form-item>
                      </el-col>
                      <el-col :span="24">
                        <el-form-item label="投放时间"
                        prop="openStartTime"
                        :rules="[
                            { required: true, message: '请选择投放时间', trigger: 'change' },
                        ]"
                        >
                            <el-date-picker
                                v-model="time"
                                type="datetimerange"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期">
                            </el-date-picker>
                        </el-form-item>
                      </el-col>
                      <el-col :span="24">
                        <el-form-item label="调研介绍" prop="surveyIntroduce">
                          <ms-editor :height="360" v-model="form.surveyIntroduce"></ms-editor>
                        </el-form-item>
                      </el-col>
                        <el-col :span="12">
                        <el-form-item label="分享文案" prop="shareCopyWriting">
                          <el-input style="width: 210px" v-model="form.shareCopyWriting" type="textarea" :rows="4" @keyup.native="summaryKeyUp = true" maxlength="30" show-word-limit placeholder="通过微信分享时，转发卡片会显示文案，≤30字"></el-input>
                        </el-form-item>
                      </el-col>
                      <el-col :span="12">
                        <el-form-item label="调研封面" prop="surveyCover">
                          <ms-single-image v-model="form.surveyCover" :upFileSize="0.5"></ms-single-image>
                        </el-form-item>
                        <!-- <el-form-item label="调研封面" prop="fileUrl">
                          <ms-image-upload :imageUrl.sync="submitData.fileUrl" @bindData="bindData" :disabled="!!submitData.id"></ms-image-upload>
                        </el-form-item> -->
                      </el-col>                     
                    </el-row>
                  </el-col>
                  <el-col :span="8" class="info-form-right">
                    <el-row>
                      <el-col :span="24">
                        <el-form-item label="参与权限" prop="loginAuthority">
                          <el-radio-group style="width: 400px" v-model="form.loginAuthority" placeholder="请选择参与权限">
                            <el-radio :label="1">免登录</el-radio>
                            <el-radio :label="2">登录</el-radio>
                            <el-radio :label="3">完善信息</el-radio>
                          </el-radio-group>
                        </el-form-item>
                      </el-col>
                      <el-col :span="24">
                        <el-form-item label="调研属性" prop="formType">
                          <el-radio-group style="width: 400px" v-model="form.formType" placeholder="请选择调研属性">
                            <el-radio :label="1">平台调研</el-radio>
                            <el-radio :label="2">商业调研</el-radio>
                            <el-radio :label="3">病例征集</el-radio>
                          </el-radio-group>
                        </el-form-item>
                      </el-col>
                      <el-col :span="24">
                        <el-form-item label="奖励方式" v-if="form.loginAuthority == 2 || form.loginAuthority == 3">
                            {{form.formType === 1 ? '积分':'梅花'}}
                            <el-input-number style="margin-left:10px" v-model="form.bonusPoints" :step="1" step-strictly  :min="0" :max="5000"></el-input-number>
                          </el-form-item>
                      </el-col>
                      <!-- <el-col :span="24">
                        <el-form-item label="积分设置">
                          <div class="inlines">
                            前
                            <el-input-number :step="1" step-strictly  v-model="form.rewardUsers" controls-position="right"  @change="handleChange" :min="1" label=""></el-input-number>
                            名用户提交后可得
                            <el-input-number :step="1" step-strictly v-model="form.bonusPoints" controls-position="right" @change="handleChange" :min="0" label=""></el-input-number>
                            {{form.formType === 1 ? '积分':'梅花'}}
                          </div>
                        </el-form-item>
                      </el-col> -->
                      <el-col :span="24" v-if="form.isOpenResult === 0">
                        <el-form-item label="跳转路径" prop="wordReactUrl">
                          <el-input v-model="form.wordReactUrl" style="width: 100%" placeholder="请输入问卷提交后跳转的地址"></el-input>
                        </el-form-item>
                      </el-col>
                      <el-col :span="24">
                        <el-form-item label="投票结果" prop="pollResults" v-if="form.isOpenResult === 1">
                          <el-radio-group style="width: 400px" v-model="form.pollResults" placeholder="请选择投票结果">
                            <el-radio :label="1">提交后显示</el-radio>
                            <el-radio :label="2">投放结束后显示</el-radio>
                          </el-radio-group>
                        </el-form-item>
                      </el-col>
                      <!-- <el-col :span="24">
                        <el-form-item label="完善信息" v-if="form.isLogin == 1&&form.isOpenResult === 0">
                            <el-switch :active-value="1" :inactive-value="0" v-model="form.isPerfectInfo"></el-switch>
                        </el-form-item>
                      </el-col> -->
                      <el-col :span="12">
                        <el-form-item label="投放主站">
                            <el-switch :active-value="1" :inactive-value="0" v-model="form.showMedsci"></el-switch>
                        </el-form-item>
                      </el-col>
                      <!-- <el-col :span="24">
                         <el-form-item label="定向投放" prop="targetedSwitch" v-if="form.isLogin == 1 && form.isPerfectInfo == 1&&form.isOpenResult === 0">
                          <el-switch v-model="form.targetedSwitch" :active-value="1" :inactive-value="0"></el-switch>
                        </el-form-item>
                      </el-col> -->
                      <el-col :span="24">
                        <el-form-item label="分页" v-if="form.isOpenResult === 0">
                          <el-switch style="margin-right: 20px" :active-value="isPage===0?1:isPage" :inactive-value="0" v-model="form.isPage"></el-switch>
                          <template v-if="form.isPage!==0">每页
                            <el-input-number style="margin: 0 10px" v-model="form.isPage" :step="1" step-strictly :min="1" @change="isPage=form.isPage"></el-input-number>个题
                          </template>
                        </el-form-item>
                      </el-col>
                     
                      <el-col :span="12">
                        <el-form-item label="临时保存" v-if="form.isOpenResult === 0">
                          <el-switch :active-value="1" :inactive-value="0" v-model="form.temporarySave"></el-switch>
                        </el-form-item>
                      </el-col>
                      <el-col :span="12">
                        <el-form-item label="限提交一次" v-if="(form.loginAuthority == 2 || form.loginAuthority == 3) && form.isOpenResult === 0">
                          <el-switch :active-value="1" :inactive-value="0" v-model="form.isSubmitOne"></el-switch>
                        </el-form-item>
                      </el-col>
                    </el-row>
                  </el-col>
                </el-row>
                   
                   
                    <!-- <el-form-item :label="form.isOpenResult === 0?'调研标题':'投票名称'" prop="templateName">
                      <el-input style="width: 400px" v-model="form.templateName"></el-input>
                    </el-form-item> -->
                   
                    <!-- <el-col :span="24"> -->
                      
                    <!-- </el-col> -->
                   
                    <!-- <el-col :span="24"> -->
                      
                    <!-- </el-col> -->
                    <!-- <el-form-item label="需要登录">
                      <el-switch :active-value="1" :inactive-value="0" v-model="form.isLogin"></el-switch>
                    </el-form-item> -->
                    
                    
                    
                    
                    <!-- <el-form-item label="奖励方式" v-if="form.isLogin == 1||form.isOpenResult === 1">
                      {{form.formType === 1 ? '积分':'梅花'}}
                      <el-input-number style="margin-left:10px" v-model="form.bonusPoints" :step="1" step-strictly  :min="0" :max="5000"></el-input-number>
                    </el-form-item> -->
                    
                   
                    <!-- <el-form-item label="公开结果">
                        <el-switch :active-value="1" :inactive-value="0" v-model="form.isOpenResult"></el-switch>
                    </el-form-item> -->
                    
                   
                    <!-- <el-row v-if="form.isOpenResult === 0">
                      <el-col :span="8">
                        <el-form-item label="跳转地址" prop="wordReactUrl">
                          <el-input v-model="form.wordReactUrl" style="width: 100%" placeholder="请输入问卷提交后跳转的地址"></el-input>
                        </el-form-item>
                      </el-col>
                      <el-col :span="8">
                        <el-form-item prop="wordReactText" label-width="10px">
                          <el-input v-model="form.wordReactText" style="width: 100%" placeholder="请输入跳转提示语" maxlength="50" show-word-limit></el-input>
                        </el-form-item>
                      </el-col>
                    </el-row>
                    <el-form-item label="只要提交一次" v-if="form.isLogin == 1&&form.isOpenResult === 0">
                        <el-switch :active-value="1" :inactive-value="0" v-model="form.isSubmitOne"></el-switch>
                    </el-form-item>
                    <el-form-item label="作答设备控制" v-if="form.isLogin == 0&&form.isOpenResult === 0">
                      <el-switch style="margin-right: 20px" :active-value="respEquipmentMaxNum===0?1:respEquipmentMaxNum" :inactive-value="0" v-model="form.respEquipmentMaxNum"></el-switch>
                      <template v-if="form.respEquipmentMaxNum!==0">限手机或电脑只能作答
                        <el-input-number style="margin: 0 10px" v-model="form.respEquipmentMaxNum" :step="1" step-strictly :min="1" @change="respEquipmentMaxNum=form.respEquipmentMaxNum"></el-input-number>次
                        <span class="tips">此功能依赖于浏览器Cookie，浏览器无痕模式或用户清理缓存后失效。</span>
                      </template>
                    </el-form-item>
                    <el-form-item label="IP地址限制" v-if="form.isLogin == 0&&form.isOpenResult === 0" >
                      <el-switch style="margin-right: 20px" :active-value="ipAddressMaxNum===0?1:ipAddressMaxNum" :inactive-value="0" v-model="form.ipAddressMaxNum"></el-switch>
                      <template v-if="form.ipAddressMaxNum!==0">限IP地址只能作答
                        <el-input-number style="margin: 0 10px" v-model="form.ipAddressMaxNum" :step="1" step-strictly :min="1" @change="ipAddressMaxNum=form.ipAddressMaxNum"></el-input-number>次
                      </template>
                    </el-form-item>
                    <el-form-item label="临时保存" v-if="form.isOpenResult === 0">
                      <el-switch :active-value="1" :inactive-value="0" v-model="form.temporarySave"></el-switch>
                    </el-form-item>
                    <el-form-item label="基础人数" v-if="form.isOpenResult === 0">
                      <el-input-number v-model="form.basisNumber" :step="1" step-strictly  :min="0"></el-input-number>
                    </el-form-item>
                    <el-form-item label="人数倍数" v-if="form.isOpenResult === 0">
                      <el-input-number v-model="form.multipleNumber" :precision="2" :step="1" :min="0"></el-input-number>
                    </el-form-item>
                    <el-form-item prop="cssContent" label="HTML代码片段" v-if="form.isOpenResult === 0">
                      <prism-editor v-model="form.cssContent" language="html" class="prise-editor"></prism-editor>
                    </el-form-item> -->
                    <!-- <el-form-item label="调研角标">
                      <el-checkbox v-model="form.isRecommend" :true-label='1' :false-label='0'>推荐</el-checkbox>
                    </el-form-item> -->
                    <!-- <el-form-item label="显示选项">
                        <el-checkbox :true-label="1" :false-label="0" v-model="form.isPcVisible"
                            >PC</el-checkbox
                        >
                        <el-checkbox :true-label="1" :false-label="0" v-model="form.isAppVisible"
                            >APP</el-checkbox
                        >
                    </el-form-item> -->
                    <!-- <el-form-item label="定向填写" prop="targetedSwitch" v-if="form.isLogin == 1 && form.isPerfectInfo == 1&&form.isOpenResult === 0">
                      <el-switch v-model="form.targetedSwitch" :active-value="1" :inactive-value="0"></el-switch>
                    </el-form-item> -->
                    <!-- <template v-if="form.targetedSwitch == 1 && form.isLogin == 1 && form.isPerfectInfo == 1&&form.isOpenResult === 0">
                      <el-row>
                        <el-col :span="12">
                          <el-form-item label="" prop="content" class="flex-item">
                            <ms-hospital-search v-model="hospital" :model.sync="hospital" @bindData="bindHospital" :placeholder="'请输入医院名称搜索，并选择'" style="width:100%"></ms-hospital-search>
                          </el-form-item>
                        </el-col>
                        <el-col :span="12">
                          <el-form-item label-width="10px" prop="content" class="flex-item">
                            <ms-category-cascader
                              v-model="department" :model.sync="department" :config="{moduleName:'chufang'}" :type="'all'" :multiple="true" @bindData="bindDepartment" :placeholder="'请选择科室，默认全选'" style="width:100%"
                            ></ms-category-cascader>
                            <el-button :disabled="!hospital && !department.length" style="flex:1;margin-left:10px" type="primary" @click="addMessList">添加</el-button>
                          </el-form-item>
                        </el-col>
                      </el-row>
                      <el-form-item label="" prop="">
                        <el-table
                          row-key="additionId" 
                          :data="form.orientationParamRequest" 
                          :header-cell-style="headerCellStyle" 
                          :header-row-style="headerRowStyle" 
                          style="width: 100%;border-left: 1px solid #EBEEF5;border-right: 1px solid #EBEEF5">
                          <el-table-column align="center" label="序号" type="index" min-width="50px"></el-table-column>
                          <el-table-column align="center" label="医院" min-width="200px">
                            <template slot-scope="scope">
                              <span>{{ scope.row.hospitalName }}</span>
                            </template>
                          </el-table-column>
                          <el-table-column align="center" label="科室" min-width="200px">
                            <template slot-scope="scope">
                              <span>{{ scope.row.departmentList.map(i=>{return i.categoryName}).join('、') }}</span>
                            </template>
                          </el-table-column>
                          <el-table-column align="center" label="操作" min-width="80px">
                            <template slot-scope="scope">
                              <el-tooltip effect="dark" content="删除" placement="bottom">
                                <span @click="removeMessList(scope.$index)">
                                  <svg-icon class-name="drag-handler" icon-class="icon-shanchu" />
                                </span>
                              </el-tooltip>
                            </template>
                          </el-table-column>
                        </el-table>
                      </el-form-item>
                    </template> -->
                    <!-- 筛选规则 -->
                    <!-- <el-form-item label="筛选规则" prop="filterRuleSwitch" v-if="form.isOpenResult === 0">
                      <el-switch v-model="form.filterRuleSwitch" :active-value="1" :inactive-value="0"></el-switch>
                    </el-form-item>
                    <template v-if="form.filterRuleSwitch == 1">
                      <el-form-item label="添加筛选规则">
                        <el-select style="width: 400px" v-model="type" @change="changeType" placeholder="请选择筛选规则类型">
                          <el-option
                            v-for="item in filterRuleOptions"
                            :key="item.id"
                            :label="item.label"
                            :value="item.id">
                          </el-option>
                        </el-select>
                      </el-form-item> -->
                      <!-- 省份城市 -->
                      <!-- <template v-if="type == '1'">
                        <el-form-item label="" prop="">
                          <el-select style="width: 400px" @change="changeCityRule" v-model="here" placeholder="请选择">
                            <el-option
                              v-for="item in filterRuleCityOptions"
                              :key="item.id"
                              :label="item.label"
                              :value="item.id">
                            </el-option>
                          </el-select>
                        </el-form-item>
                        <el-row>
                          <el-col :span="12">
                            <el-form-item label="" prop="content" class="flex-item">
                              <ms-province-search v-model="province" :model.sync="province" @bindData="bindProvince" :placeholder="'请输入省份搜索，并选择'" style="width:100%"></ms-province-search>
                            </el-form-item>
                          </el-col>
                          <el-col :span="12">
                            <el-form-item label-width="10px" prop="content" class="flex-item">
                              <el-select style="width: 400px" v-model="cityId" @change="bindCity" placeholder="请选择城市，默认全选">
                                <el-option
                                  v-for="item in allCityOptions"
                                  :key="item.id"
                                  :label="item.name"
                                  :value="item.id">
                                </el-option>
                              </el-select>
                              <el-button :disabled="!province" style="flex:1;margin-left:10px" type="primary" @click="addCityList">添加</el-button>
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <el-form-item label="" prop="">
                          <el-table
                            row-key="provinceCityId" 
                            :data="addProvinceCityTableData" 
                            :header-cell-style="headerCellStyle" 
                            :header-row-style="headerRowStyle" 
                            style="width: 100%;border-left: 1px solid #EBEEF5;border-right: 1px solid #EBEEF5">
                            <el-table-column align="center" label="序号" type="index" min-width="50px"></el-table-column>
                            <el-table-column align="center" label="省份" min-width="200px">
                              <template slot-scope="scope">
                                <span>{{ scope.row.provinceName }}</span>
                              </template>
                            </el-table-column>
                            <el-table-column align="center" label="城市" min-width="200px">
                              <template slot-scope="scope">
                                <span>{{ scope.row.cityName }}</span>
                              </template>
                            </el-table-column>
                            <el-table-column align="center" label="操作" min-width="80px">
                              <template slot-scope="scope">
                                <el-tooltip effect="dark" content="删除" placement="bottom">
                                  <span @click="removeCityList(scope.$index)">
                                    <svg-icon class-name="drag-handler" icon-class="icon-shanchu" />
                                  </span>
                                </el-tooltip>
                              </template>
                            </el-table-column>
                          </el-table>
                          <el-button :disabled="!addProvinceCityTableData.length" style="float:right;margin-top:20px" type="primary" @click="addRuleList">生成筛选规则</el-button>
                        </el-form-item>
                      </template> -->
                      <!-- 填写时间 -->
                      <!-- <template v-if="type == '2'">
                        <el-row>
                          <el-col :span="12">
                            <el-form-item label="" prop="content" class="flex-item">
                              <el-select style="width: 400px" v-model="gt" placeholder="请选择">
                                <el-option
                                  v-for="item in filterRuleTimeOptions"
                                  :key="item.id"
                                  :label="item.label"
                                  :value="item.id">
                                </el-option>
                              </el-select>
                            </el-form-item>
                          </el-col>
                          <el-col :span="12">
                            <el-form-item label-width="10px" prop="content" class="flex-item">
                              <el-input v-model.trim="filterRuletime" type="number" min="0" max="1000000" @keyup.native="validateNumber" placeholder="请输入"></el-input>秒
                              <el-button :disabled="!filterRuletime" style="flex:1;margin-left:10px" type="primary" @click="addRuleList">生成筛选规则</el-button>
                            </el-form-item>
                          </el-col>
                        </el-row>
                      </template> -->
                      <!-- 单题规则 -->
                      <!-- <template v-if="type == '3'">
                        <el-form-item label="" prop="">
                          <el-select style="width: 400px" v-model="question" @change="changeQuestion" placeholder="请选择题目">
                            <el-option
                              v-for="item in filterSingleOptions"
                              :key="item.prop"
                              :label="item.label"
                              :value="item.prop">
                            </el-option>
                          </el-select>
                        </el-form-item>
                        <el-form-item label="" prop="">
                          <el-select style="width: 400px" v-model="select" placeholder="请选择">
                            <el-option
                              v-for="item in userSelectOptions"
                              :key="item.id"
                              :label="item.label"
                              :value="item.id">
                            </el-option>
                          </el-select>
                        </el-form-item>
                        <el-form-item>
                          <el-checkbox-group v-model="options">
                            <el-checkbox v-for="item in quetionItem" :label="item.value" :key="item.value">{{ (filterSingleOptions.filter(item => item.prop == question))[0].label}}的{{item.label}}</el-checkbox>
                          </el-checkbox-group>
                        </el-form-item>
                        <el-form-item>
                          <el-radio-group v-model="and">
                            <el-radio :label="0">或（选中任意一个时)</el-radio>
                            <el-radio :label="1">与（同时选中时）</el-radio>
                          </el-radio-group>
                        </el-form-item>
                        <el-form-item>
                          <el-button :disabled="!(question&&options.length)" type="primary" @click="addRuleList">生成筛选规则</el-button>
                        </el-form-item>
                      </template> -->
                      <!-- 双题规则 -->
                      <!-- <template v-if="type == '4'">
                        <el-row>
                          <el-col :span="10">
                            <el-form-item label="" prop="">
                              <el-select style="width: 400px" v-model="firstQuestion.question" @change="changeFirstQuestion" placeholder="请选择题目">
                                <el-option
                                  v-for="item in firstQuestionOptions"
                                  :key="item.prop"
                                  :label="item.label"
                                  :value="item.prop"
                                  :disabled="item.disabled"
                                  >
                                </el-option>
                              </el-select>
                            </el-form-item>
                            <el-form-item>
                              <el-checkbox-group v-model="firstQuestion.options">
                                <el-checkbox v-for="item in firstQuestionItem" :label="item.value" :key="item.value">{{ (firstQuestionOptions.filter(item => item.prop == firstQuestion.question))[0].label}}的{{item.label}}</el-checkbox>
                              </el-checkbox-group>
                            </el-form-item>
                            <el-form-item>
                              <el-radio-group v-model="firstQuestion.and">
                                <el-radio :label="0">或（选中任意一个时)</el-radio>
                                <el-radio :label="1">与（同时选中时）</el-radio>
                              </el-radio-group>
                            </el-form-item>
                          </el-col>
                          <el-col :span="2">
                            <el-form-item label="" prop="">
                              <span style="color:#000">&amp;</span>
                            </el-form-item>
                          </el-col>
                          <el-col :span="10">
                            <el-form-item label="" prop="">
                              <el-select style="width: 400px" v-model="secondQuestion.question" @change="changeSecondQuestion" placeholder="请选择题目">
                                <el-option
                                  v-for="item in secondQuestionOptions"
                                  :key="item.prop"
                                  :label="item.label"
                                  :value="item.prop"
                                  :disabled="item.disabled"
                                  >
                                </el-option>
                              </el-select>
                            </el-form-item>
                            <el-form-item>
                              <el-checkbox-group v-model="secondQuestion.options">
                                <el-checkbox v-for="item in secondQuestionItem" :label="item.value" :key="item.value">{{ (secondQuestionOptions.filter(item => item.prop == secondQuestion.question))[0].label}}的{{item.label}}</el-checkbox>
                              </el-checkbox-group>
                            </el-form-item>
                            <el-form-item>
                              <el-radio-group v-model="secondQuestion.and">
                                <el-radio :label="0">或（选中任意一个时)</el-radio>
                                <el-radio :label="1">与（同时选中时）</el-radio>
                              </el-radio-group>
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <el-form-item>
                          <p style="font-size:12px;color:#666;">当指定的两道题的答卷选项同时满足指定的条件时为无效答卷；</p>
                          <el-button :disabled="!(firstQuestion.question&&firstQuestion.options.length&&secondQuestion.question&&secondQuestion.options.length)" type="primary" @click="addRuleList">生成筛选规则</el-button>
                        </el-form-item>
                      </template> -->
                      <!-- <el-form-item label="" prop="">
                        <p>所有筛选规则<span style="font-size:12px;color:#666">（满足以下任意一条规则的答卷为无效答卷。）</span></p>
                        <el-table
                          row-key="allRuleId" 
                          :data="filterRulesData" 
                          :header-cell-style="headerCellStyle" 
                          :header-row-style="headerRowStyle" 
                          style="width: 100%;border-left: 1px solid #EBEEF5;border-right: 1px solid #EBEEF5">
                          <template slot="empty">
                            <span>该问卷暂时没有筛选规则，请创建新的筛选规则。</span>
                          </template>
                          <el-table-column align="center" label="序号" min-width="60px">
                            <template slot-scope="scope">
                              <span>筛选规则{{ scope.$index + 1 }}</span>
                            </template>
                          </el-table-column>
                          <el-table-column align="center" label="规则详情" min-width="200px">
                            <template slot-scope="scope">
                              <span>{{ scope.row.ruleDetail }}</span>
                            </template>
                          </el-table-column>
                          <el-table-column align="center" label="操作" min-width="60px">
                            <template slot-scope="scope">
                              <el-tooltip effect="dark" content="删除" placement="bottom">
                                <span @click="removeRuleList(scope.$index)">
                                  <svg-icon class-name="drag-handler" icon-class="icon-shanchu" />
                                </span>
                              </el-tooltip>
                            </template>
                          </el-table-column>
                        </el-table>
                      </el-form-item> -->
                    <!-- </template> -->
                    <!-- 配额规则 -->
                    <!-- <el-form-item label="配额规则" prop="quotaRuleSwitch" v-if="form.isOpenResult === 0">
                      <el-switch v-model="form.quotaRuleSwitch" :active-value="1" :inactive-value="0"></el-switch>
                    </el-form-item> -->
                    <!-- <template v-if="form.quotaRuleSwitch == 1">
                      <el-form-item label="添加配额规则" prop="quotaRuleType">
                        <el-select style="width: 400px" v-model="form.quotaRuleType" clearable @change="changeQuotaType" placeholder="请选择配额规则类型">
                          <el-option
                            v-for="item in quotaRuleOptions"
                            :key="item.id"
                            :label="item.label"
                            :value="item.id">
                          </el-option>
                        </el-select>
                      </el-form-item>
                      <template v-if="form.quotaRuleType == 0|| form.quotaRuleType == 1">
                        <el-form-item label="配额已满提示文案" prop="" v-if="form.quotaRuleType == 1">
                          <el-input style="width: 400px" maxlength="10" v-model="form.quotaFull" placeholder="配额已满（最多十个字）"></el-input>
                        </el-form-item>
                        <el-form-item label="规则条件" prop="">
                          <el-select style="width: 400px" v-model="quotaQuestion" @change="changeQuotaQuestion" placeholder="请选择题目">
                            <el-option
                              v-for="item in quotaQuestionOptions"
                              :key="item.prop"
                              :label="item.label"
                              :value="item.prop">
                            </el-option>
                          </el-select>
                        </el-form-item>
                        <template v-if="quotaQuestion&&quotaQuestion!='quotaquestionlast'">
                          <el-form-item label="配额详情">
                            <el-table
                              row-key="quotaDetailId" 
                              :data="quotaDetailData"
                              :header-cell-style="headerCellStyle" 
                              :header-row-style="headerRowStyle" 
                              style="width: 50%;border-left: 1px solid #EBEEF5;border-right: 1px solid #EBEEF5">
                              <el-table-column align="center" label="选项" width="200px">
                                <template slot-scope="scope">
                                  <span>{{ scope.row.optionStr }}</span>
                                </template>
                              </el-table-column>
                              <el-table-column align="center" label="配额数量" min-width="200px">
                                <template slot-scope="scope">
                                  <el-input style="width: 100px" type="number" min="1" max="50000"  @keyup.native="validateQuotaNum(scope)" v-model="scope.row.count" v-show="scope.row.show"></el-input>
                                  <span v-show="!scope.row.show">{{ scope.row.count }}</span>
                                </template>
                              </el-table-column>
                            </el-table>
                            <p style="color:#666;font-size:12px">可留空，留空即为不设置配额总量</p>
                            <el-button style="margin-left:50%;transform:translateX(-100%)" :disabled="!quotaDetailData.length" type="primary" @click="addQuotaRuleList">生成筛选规则</el-button>
                          </el-form-item>
                        </template>
                        <template v-if="quotaQuestion&&quotaQuestion =='quotaquestionlast'">
                          <el-row>
                            <el-col :span="12">
                              <el-form-item label="" prop="content" class="flex-item">
                                <ms-province-search v-model="quotaProvince" :model.sync="quotaProvince" @bindData="bindQuotaProvince" :placeholder="'请输入省份搜索，并选择'" style="width:100%"></ms-province-search>
                              </el-form-item>
                            </el-col>
                            <el-col :span="12">
                              <el-form-item label-width="10px" prop="content" class="flex-item">
                                <el-select style="width: 400px" v-model="quotaCityId" @change="bindQuotaCity" placeholder="请选择城市，默认全选">
                                  <el-option
                                    v-for="item in allQuotaCityOptions"
                                    :key="item.id"
                                    :label="item.name"
                                    :value="item.id">
                                  </el-option>
                                </el-select>
                                <el-button :disabled="!quotaProvince" style="flex:1;margin-left:10px" type="primary" @click="addQuotaCityList">添加</el-button>
                              </el-form-item>
                            </el-col>
                          </el-row>
                          <el-form-item label="" prop="">
                            <el-table
                              row-key="quotaProvinceId" 
                              :data="addQuotaProvinceCityData" 
                              :header-cell-style="headerCellStyle" 
                              :header-row-style="headerRowStyle" 
                              style="width: 100%;border-left: 1px solid #EBEEF5;border-right: 1px solid #EBEEF5">
                              <el-table-column align="center" label="序号" type="index" min-width="50px"></el-table-column>
                              <el-table-column align="center" label="省份" min-width="200px">
                                <template slot-scope="scope">
                                  <span>{{ scope.row.provinceName }}</span>
                                </template>
                              </el-table-column>
                              <el-table-column align="center" label="城市" min-width="200px">
                                <template slot-scope="scope">
                                  <span>{{ scope.row.cityName }}</span>
                                </template>
                              </el-table-column>
                              <el-table-column align="center" label="配额数量" min-width="80px">
                                <template slot-scope="scope">
                                  <el-input style="width: 100px" type="number" min="1" max="50000"  @keyup.native="validateQuotaNum(scope)" v-model="scope.row.count" v-show="scope.row.show"></el-input>
                                  <span v-show="!scope.row.show">{{ scope.row.count }}</span>
                                </template>
                              </el-table-column>
                            </el-table>
                            <p style="color:#666;font-size:12px">配额数量可留空，留空即为不设置配额总量</p>
                            <el-button :disabled="!addQuotaProvinceCityData.length" style="float:right;margin-top:20px" type="primary" @click="addQuotaRuleList">生成筛选规则</el-button>
                          </el-form-item>
                        </template>
                      </template>
                      <el-form-item>
                        <p>所有配额规则<span style="font-size:12px;color:#666">（使用配额规则，可以控制每个选项或选项组合最大可选次数。）</span></p>
                        <el-table
                          row-key="quotaRuleId" 
                          :data="quotaRulesData" 
                          :header-cell-style="headerCellStyle" 
                          :header-row-style="headerRowStyle" 
                          style="width: 100%;border-left: 1px solid #EBEEF5;border-right: 1px solid #EBEEF5">
                          <template slot="empty">
                            <span>该问卷暂时没有配额规则，请创建新的配额规则。</span>
                          </template>
                          <el-table-column align="center" label="序号" min-width="60px">
                            <template slot-scope="scope">
                              <span>筛选规则{{ scope.$index + 1 }}</span>
                            </template>
                          </el-table-column>
                          <el-table-column align="center" label="规则详情" min-width="200px">
                            <template slot-scope="scope">
                              <span>{{ scope.row.ruleDetail }}</span>
                            </template>
                          </el-table-column>
                          <el-table-column align="center" label="操作" min-width="60px">
                            <template slot-scope="scope">
                              <el-tooltip effect="dark" content="删除" placement="bottom">
                                <span @click="removeQuotaRuleList(scope.$index)">
                                  <svg-icon class-name="drag-handler" icon-class="icon-shanchu" />
                                </span>
                              </el-tooltip>
                            </template>
                          </el-table-column>
                        </el-table>
                      </el-form-item>
                    </template> -->
                </el-form>
            </el-tab-pane>
            <el-tab-pane label="数据回收" name="collect" v-if="form.isOpenResult === 0">
              <el-form
                    style="text-align: left;"
                    :model="form"
                    :rules="rules"
                    label-width="100px"
              >
                <el-row :gutter="26">
                  <el-col :span="16">
                    <!-- 筛选规则 -->
                    <el-form-item label="筛选规则" prop="filterRuleSwitch" v-if="form.isOpenResult === 0">
                      <el-switch v-model="form.filterRuleSwitch" :active-value="1" :inactive-value="0"></el-switch>
                    </el-form-item>
                    <template v-if="form.filterRuleSwitch == 1">
                      <el-form-item label="添加筛选规则">
                        <el-select style="width: 400px" v-model="type" @change="changeType" placeholder="请选择筛选规则类型">
                          <el-option
                            v-for="item in filterRuleOptions"
                            :key="item.id"
                            :label="item.label"
                            :value="item.id">
                          </el-option>
                        </el-select>
                      </el-form-item>
                      <!-- 省份城市 -->
                      <template v-if="type == '1'">
                        <el-form-item label="" prop="">
                          <el-select style="width: 400px" @change="changeCityRule" v-model="here" placeholder="请选择">
                            <el-option
                              v-for="item in filterRuleCityOptions"
                              :key="item.id"
                              :label="item.label"
                              :value="item.id">
                            </el-option>
                          </el-select>
                        </el-form-item>
                        <el-row>
                          <el-col :span="12">
                            <el-form-item label="" prop="content" class="flex-item">
                              <ms-province-search v-model="province" :model.sync="province" @bindData="bindProvince" :placeholder="'请输入省份搜索，并选择'" style="width:100%"></ms-province-search>
                            </el-form-item>
                          </el-col>
                          <el-col :span="12">
                            <el-form-item label-width="10px" prop="content" class="flex-item">
                              <el-select style="width: 400px" v-model="cityId" @change="bindCity" placeholder="请选择城市，默认全选">
                                <el-option
                                  v-for="item in allCityOptions"
                                  :key="item.id"
                                  :label="item.name"
                                  :value="item.id">
                                </el-option>
                              </el-select>
                              <el-button :disabled="!province" style="flex:1;margin-left:10px" type="primary" @click="addCityList">添加</el-button>
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <el-form-item label="" prop="">
                          <el-table
                            row-key="provinceCityId" 
                            :data="addProvinceCityTableData" 
                            :header-cell-style="headerCellStyle" 
                            :header-row-style="headerRowStyle" 
                            style="width: 100%;border-left: 1px solid #EBEEF5;border-right: 1px solid #EBEEF5">
                            <el-table-column align="center" label="序号" type="index" min-width="50px"></el-table-column>
                            <el-table-column align="center" label="省份" min-width="200px">
                              <template slot-scope="scope">
                                <span>{{ scope.row.provinceName }}</span>
                              </template>
                            </el-table-column>
                            <el-table-column align="center" label="城市" min-width="200px">
                              <template slot-scope="scope">
                                <span>{{ scope.row.cityName }}</span>
                              </template>
                            </el-table-column>
                            <el-table-column align="center" label="操作" min-width="80px">
                              <template slot-scope="scope">
                                <el-tooltip effect="dark" content="删除" placement="bottom">
                                  <span @click="removeCityList(scope.$index)">
                                    <svg-icon class-name="drag-handler" icon-class="icon-shanchu" />
                                  </span>
                                </el-tooltip>
                              </template>
                            </el-table-column>
                          </el-table>
                          <el-button :disabled="!addProvinceCityTableData.length" style="float:right;margin-top:20px" type="primary" @click="addRuleList">生成筛选规则</el-button>
                        </el-form-item>
                      </template>
                      <!-- 填写时间 -->
                      <template v-if="type == '2'">
                        <el-row>
                          <el-col :span="12">
                            <el-form-item label="" prop="content" class="flex-item">
                              <el-select style="width: 400px" v-model="gt" placeholder="请选择">
                                <el-option
                                  v-for="item in filterRuleTimeOptions"
                                  :key="item.id"
                                  :label="item.label"
                                  :value="item.id">
                                </el-option>
                              </el-select>
                            </el-form-item>
                          </el-col>
                          <el-col :span="12">
                            <el-form-item label-width="10px" prop="content" class="flex-item">
                              <el-input v-model.trim="filterRuletime" type="number" min="0" max="1000000" @keyup.native="validateNumber" placeholder="请输入"></el-input>秒
                              <el-button :disabled="!filterRuletime" style="flex:1;margin-left:10px" type="primary" @click="addRuleList">生成筛选规则</el-button>
                            </el-form-item>
                          </el-col>
                        </el-row>
                      </template>
                      <!-- 单题规则 -->
                      <template v-if="type == '3'">
                        <el-form-item label="" prop="">
                          <el-select style="width: 400px" v-model="question" @change="changeQuestion" placeholder="请选择题目">
                            <el-option
                              v-for="item in filterSingleOptions"
                              :key="item.prop"
                              :label="item.label"
                              :value="item.prop">
                            </el-option>
                          </el-select>
                        </el-form-item>
                        <el-form-item label="" prop="">
                          <el-select style="width: 400px" v-model="select" placeholder="请选择">
                            <el-option
                              v-for="item in userSelectOptions"
                              :key="item.id"
                              :label="item.label"
                              :value="item.id">
                            </el-option>
                          </el-select>
                        </el-form-item>
                        <el-form-item>
                          <el-checkbox-group v-model="options">
                            <el-checkbox v-for="item in quetionItem" :label="item.value" :key="item.value">{{ (filterSingleOptions.filter(item => item.prop == question))[0].label}}的{{item.label}}</el-checkbox>
                          </el-checkbox-group>
                        </el-form-item>
                        <el-form-item>
                          <el-radio-group v-model="and">
                            <el-radio :label="0">或（选中任意一个时)</el-radio>
                            <el-radio :label="1">与（同时选中时）</el-radio>
                          </el-radio-group>
                        </el-form-item>
                        <el-form-item>
                          <el-button :disabled="!(question&&options.length)" type="primary" @click="addRuleList">生成筛选规则</el-button>
                        </el-form-item>
                      </template>
                      <!-- 双题规则 -->
                      <template v-if="type == '4'">
                        <el-row>
                          <el-col :span="10">
                            <el-form-item label="" prop="">
                              <el-select style="width: 400px" v-model="firstQuestion.question" @change="changeFirstQuestion" placeholder="请选择题目">
                                <el-option
                                  v-for="item in firstQuestionOptions"
                                  :key="item.prop"
                                  :label="item.label"
                                  :value="item.prop"
                                  :disabled="item.disabled"
                                  >
                                </el-option>
                              </el-select>
                            </el-form-item>
                            <el-form-item>
                              <el-checkbox-group v-model="firstQuestion.options">
                                <el-checkbox v-for="item in firstQuestionItem" :label="item.value" :key="item.value">{{ (firstQuestionOptions.filter(item => item.prop == firstQuestion.question))[0].label}}的{{item.label}}</el-checkbox>
                              </el-checkbox-group>
                            </el-form-item>
                            <el-form-item>
                              <el-radio-group v-model="firstQuestion.and">
                                <el-radio :label="0">或（选中任意一个时)</el-radio>
                                <el-radio :label="1">与（同时选中时）</el-radio>
                              </el-radio-group>
                            </el-form-item>
                          </el-col>
                          <el-col :span="2">
                            <el-form-item label="" prop="">
                              <span style="color:#000">&amp;</span>
                            </el-form-item>
                          </el-col>
                          <el-col :span="10">
                            <el-form-item label="" prop="">
                              <el-select style="width: 400px" v-model="secondQuestion.question" @change="changeSecondQuestion" placeholder="请选择题目">
                                <el-option
                                  v-for="item in secondQuestionOptions"
                                  :key="item.prop"
                                  :label="item.label"
                                  :value="item.prop"
                                  :disabled="item.disabled"
                                  >
                                </el-option>
                              </el-select>
                            </el-form-item>
                            <el-form-item>
                              <el-checkbox-group v-model="secondQuestion.options">
                                <el-checkbox v-for="item in secondQuestionItem" :label="item.value" :key="item.value">{{ (secondQuestionOptions.filter(item => item.prop == secondQuestion.question))[0].label}}的{{item.label}}</el-checkbox>
                              </el-checkbox-group>
                            </el-form-item>
                            <el-form-item>
                              <el-radio-group v-model="secondQuestion.and">
                                <el-radio :label="0">或（选中任意一个时)</el-radio>
                                <el-radio :label="1">与（同时选中时）</el-radio>
                              </el-radio-group>
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <el-form-item>
                          <p style="font-size:12px;color:#666;">当指定的两道题的答卷选项同时满足指定的条件时为无效答卷；</p>
                          <el-button :disabled="!(firstQuestion.question&&firstQuestion.options.length&&secondQuestion.question&&secondQuestion.options.length)" type="primary" @click="addRuleList">生成筛选规则</el-button>
                        </el-form-item>
                      </template>
                      <el-form-item label="" prop="">
                        <p>所有筛选规则<span style="font-size:12px;color:#666">（满足以下任意一条规则的答卷为无效答卷。）</span></p>
                        <el-table
                          row-key="allRuleId" 
                          :data="filterRulesData" 
                          :header-cell-style="headerCellStyle" 
                          :header-row-style="headerRowStyle" 
                          style="width: 100%;border-left: 1px solid #EBEEF5;border-right: 1px solid #EBEEF5">
                          <template slot="empty">
                            <span>该问卷暂时没有筛选规则，请创建新的筛选规则。</span>
                          </template>
                          <el-table-column align="center" label="序号" min-width="60px">
                            <template slot-scope="scope">
                              <span>筛选规则{{ scope.$index + 1 }}</span>
                            </template>
                          </el-table-column>
                          <el-table-column align="center" label="规则详情" min-width="200px">
                            <template slot-scope="scope">
                              <span>{{ scope.row.ruleDetail }}</span>
                            </template>
                          </el-table-column>
                          <el-table-column align="center" label="操作" min-width="60px">
                            <template slot-scope="scope">
                              <el-tooltip effect="dark" content="删除" placement="bottom">
                                <span @click="removeRuleList(scope.$index)">
                                  <svg-icon class-name="drag-handler" icon-class="icon-shanchu" />
                                </span>
                              </el-tooltip>
                            </template>
                          </el-table-column>
                        </el-table>
                      </el-form-item>
                    </template>
                    <!-- 配额规则 -->
                    <el-form-item label="配额规则" prop="quotaRuleSwitch" v-if="form.isOpenResult === 0">
                      <el-switch v-model="form.quotaRuleSwitch" :active-value="1" :inactive-value="0"></el-switch>
                    </el-form-item>
                    <template v-if="form.quotaRuleSwitch == 1">
                      <el-form-item label="添加配额规则" prop="quotaRuleType">
                        <el-select style="width: 400px" v-model="form.quotaRuleType" clearable @change="changeQuotaType" placeholder="请选择配额规则类型">
                          <el-option
                            v-for="item in quotaRuleOptions"
                            :key="item.id"
                            :label="item.label"
                            :value="item.id">
                          </el-option>
                        </el-select>
                      </el-form-item>
                      <template v-if="form.quotaRuleType == 0|| form.quotaRuleType == 1">
                        <el-form-item label="配额已满提示文案" prop="" v-if="form.quotaRuleType == 1">
                          <el-input style="width: 400px" maxlength="10" v-model="form.quotaFull" placeholder="配额已满（最多十个字）"></el-input>
                        </el-form-item>
                        <el-form-item label="规则条件" prop="">
                          <el-select style="width: 400px" v-model="quotaQuestion" @change="changeQuotaQuestion" placeholder="请选择题目">
                            <el-option
                              v-for="item in quotaQuestionOptions"
                              :key="item.prop"
                              :label="item.label"
                              :value="item.prop">
                            </el-option>
                          </el-select>
                        </el-form-item>
                        <template v-if="quotaQuestion&&quotaQuestion!='quotaquestionlast'">
                          <el-form-item label="配额详情">
                            <el-table
                              row-key="quotaDetailId" 
                              :data="quotaDetailData"
                              :header-cell-style="headerCellStyle" 
                              :header-row-style="headerRowStyle" 
                              style="width: 50%;border-left: 1px solid #EBEEF5;border-right: 1px solid #EBEEF5">
                              <el-table-column align="center" label="选项" width="200px">
                                <template slot-scope="scope">
                                  <span>{{ scope.row.optionStr }}</span>
                                </template>
                              </el-table-column>
                              <el-table-column align="center" label="配额数量" min-width="200px">
                                <template slot-scope="scope">
                                  <el-input style="width: 100px" type="number" min="1" max="50000"  @keyup.native="validateQuotaNum(scope)" v-model="scope.row.count" v-show="scope.row.show"></el-input>
                                  <span v-show="!scope.row.show">{{ scope.row.count }}</span>
                                </template>
                              </el-table-column>
                            </el-table>
                            <p style="color:#666;font-size:12px">可留空，留空即为不设置配额总量</p>
                            <el-button style="margin-left:50%;transform:translateX(-100%)" :disabled="!quotaDetailData.length" type="primary" @click="addQuotaRuleList">生成筛选规则</el-button>
                          </el-form-item>
                        </template>
                        <template v-if="quotaQuestion&&quotaQuestion =='quotaquestionlast'">
                          <el-row>
                            <el-col :span="12">
                              <el-form-item label="" prop="content" class="flex-item">
                                <ms-province-search v-model="quotaProvince" :model.sync="quotaProvince" @bindData="bindQuotaProvince" :placeholder="'请输入省份搜索，并选择'" style="width:100%"></ms-province-search>
                              </el-form-item>
                            </el-col>
                            <el-col :span="12">
                              <el-form-item label-width="10px" prop="content" class="flex-item">
                                <el-select style="width: 400px" v-model="quotaCityId" @change="bindQuotaCity" placeholder="请选择城市，默认全选">
                                  <el-option
                                    v-for="item in allQuotaCityOptions"
                                    :key="item.id"
                                    :label="item.name"
                                    :value="item.id">
                                  </el-option>
                                </el-select>
                                <el-button :disabled="!quotaProvince" style="flex:1;margin-left:10px" type="primary" @click="addQuotaCityList">添加</el-button>
                              </el-form-item>
                            </el-col>
                          </el-row>
                          <el-form-item label="" prop="">
                            <el-table
                              row-key="quotaProvinceId" 
                              :data="addQuotaProvinceCityData" 
                              :header-cell-style="headerCellStyle" 
                              :header-row-style="headerRowStyle" 
                              style="width: 100%;border-left: 1px solid #EBEEF5;border-right: 1px solid #EBEEF5">
                              <el-table-column align="center" label="序号" type="index" min-width="50px"></el-table-column>
                              <el-table-column align="center" label="省份" min-width="200px">
                                <template slot-scope="scope">
                                  <span>{{ scope.row.provinceName }}</span>
                                </template>
                              </el-table-column>
                              <el-table-column align="center" label="城市" min-width="200px">
                                <template slot-scope="scope">
                                  <span>{{ scope.row.cityName }}</span>
                                </template>
                              </el-table-column>
                              <el-table-column align="center" label="配额数量" min-width="80px">
                                <template slot-scope="scope">
                                  <el-input style="width: 100px" type="number" min="1" max="50000"  @keyup.native="validateQuotaNum(scope)" v-model="scope.row.count" v-show="scope.row.show"></el-input>
                                  <span v-show="!scope.row.show">{{ scope.row.count }}</span>
                                </template>
                              </el-table-column>
                            </el-table>
                            <p style="color:#666;font-size:12px">配额数量可留空，留空即为不设置配额总量</p>
                            <el-button :disabled="!addQuotaProvinceCityData.length" style="float:right;margin-top:20px" type="primary" @click="addQuotaRuleList">生成筛选规则</el-button>
                          </el-form-item>
                        </template>
                      </template>
                      <el-form-item>
                        <p>所有配额规则<span style="font-size:12px;color:#666">（使用配额规则，可以控制每个选项或选项组合最大可选次数。）</span></p>
                        <el-table
                          row-key="quotaRuleId" 
                          :data="quotaRulesData" 
                          :header-cell-style="headerCellStyle" 
                          :header-row-style="headerRowStyle" 
                          style="width: 100%;border-left: 1px solid #EBEEF5;border-right: 1px solid #EBEEF5">
                          <template slot="empty">
                            <span>该问卷暂时没有配额规则，请创建新的配额规则。</span>
                          </template>
                          <el-table-column align="center" label="序号" min-width="60px">
                            <template slot-scope="scope">
                              <span>筛选规则{{ scope.$index + 1 }}</span>
                            </template>
                          </el-table-column>
                          <el-table-column align="center" label="规则详情" min-width="200px">
                            <template slot-scope="scope">
                              <span>{{ scope.row.ruleDetail }}</span>
                            </template>
                          </el-table-column>
                          <el-table-column align="center" label="操作" min-width="60px">
                            <template slot-scope="scope">
                              <el-tooltip effect="dark" content="删除" placement="bottom">
                                <span @click="removeQuotaRuleList(scope.$index)">
                                  <svg-icon class-name="drag-handler" icon-class="icon-shanchu" />
                                </span>
                              </el-tooltip>
                            </template>
                          </el-table-column>
                        </el-table>
                      </el-form-item>
                    </template>
                  </el-col>
                </el-row>

              </el-form>   

            </el-tab-pane>
             <el-tab-pane label="显示栏目" name="category">
              <ms-info-setting :categoryModel.sync="form.formCategoryList" :drainageModel.sync="form.drainageInfoDtoList" :categoryConfigChild="{moduleName: 'medsci_survey'}" headerShow></ms-info-setting>
            </el-tab-pane>
        </el-tabs>

        <!-- 提交按钮 -->
        <footer-tool-bar v-loading="buttonLoading">
            <template slot="tool-content">
                <el-button type="primary" @click="info_operation('save')"
                    >保存</el-button
                >
                <el-button type="info" @click="info_operation('back')"
                    >返回</el-button
                >
            </template>
        </footer-tool-bar>
    </section>
</template>
<script>
import msImageUpload from "@/components/UpFile/ms-image-upload"
import FooterToolBar from "@/components/ToolBar/footer-tool-bar"
import MsHospitalSearch from '@/components/MsCommon/ms-hospital-search'
import MsFormSearch from '@/components/MsCommon/ms-form-search'
import msCategoryCascader from '@/components/MsCommon/ms-category-cascader'
import MsProvinceSearch from '@/components/MsCommon/ms-province-search'
import msInfoSetting from '@/components/MsCommon/ms-info-setting'
// import PrismEditor from 'vue-prism-editor'
import MsEditor from '@/components/MsEditor'
import { getEditContent } from '@/utils/index'
// import "prismjs"
// import "prismjs/themes/prism.css";
import { mapGetters } from "vuex";
import { parseTime } from "@/utils"

export default {
    name: "formMenu",
    data() {
        return {
            // 所选tab
            activeName: "design",
            buttonLoading: false,
            templateSurveyName: null,
            time: null,
            isPage: 1,
            respEquipmentMaxNum: 1,
            ipAddressMaxNum: 1,
            submitData: {
              "fileKey": "",
              "fileSize": "",
              "fileUrl": "",
              "isShare": 0,
              "tagId": 0,
              "tagName": "",
              "title": "",
            },
            // 调研属性
            form: {
                isSubmitOne: 1, //  是否只能提交一次,0否,1是
                isPage: 0, // 是否分页
                // isPcVisible: 1, // PC是否可见(0.不可见,1.可见)
                // isAppVisible: 1, // APP是否可见(0.不可见,1.可见)
                isOpenResult: 0, // 是否公开结果(0,不公开,1公开)
                openStartTime: "", // 开放时间start
                openEndTime: "", // 开放时间end
                bonusPoints: "", // 积分奖励
                // rewardUsers: "", // 奖励用户数
                formCategoryList: [], // 调研分类
                basisNumber: undefined, //在线观看基础人数
                multipleNumber: undefined, //在线观看人数倍数
                // isRecommend: null, //调研角标 0.不推荐 1.推荐
                surveyIntroduce:'',
                surveyIntroduceRemovePic:'',
                shareCopyWriting: '', // => 分享文案 (string)
                surveyCover: '', // => 调研封面 （string）
                pollResults:1, //投票结果 1.提交后显示 2.投放结束后显示
                loginAuthority: 1, //参与权限  1.免登录 2.登录 3.完善信息
                formType: 1, //调研属性 1.平台调研 2.商业调研
                orientationParamRequest: [], //定向群发参数集合
                targetedSwitch: null, //定向医院开关 1-开启 0-关闭
                templateId: null, //模板idxxw
                templateName: null, //调研name
                templateSurveyName: null, //模版name

                filterRuleSwitch: 0, // 筛选规则
                filterRules: [],//所有筛选规则数据集合

                quotaRuleSwitch: 0, //配额规则开关 (0:关闭 1:开启)
                quotaRules: [], //配额规则集合
                quotaRuleType: 1, // 0->隐式配额,1->显示配额
                quotaFull: '',// 显示配额的提示文案

                isLogin: 1, //默认需要登录
                isPerfectInfo: 1, //默认打开完善信息
                showMedsci: 1, //默认主站展示
                wordReactUrl: "", //跳转地址
                wordReactText: "", //跳转提示
                respEquipmentMaxNum: 0, //作答设备控制
                ipAddressMaxNum: 0, //ip地址限制
                temporarySave: 0, //临时保存
                drainageInfoDtoList:[],
            },
            rules: {
              fileUrl: [
                { required: true, message: "请上传图片", trigger: 'change' }
              ],
              templateName: [
                { required: true, message: "请输入调研名称", trigger: 'change' }
              ],
              templateId: [
                { required: true, message: "请选择调研", trigger: 'change' }
              ],
              formType: [
                { required: true, message: "请选择调研属性", trigger: 'change' }
              ],
              loginAuthority: [
                { required: true, message: "请选择参与权限", trigger: 'change' }
              ],
              pollResults: [
                { required: true, message: "请选择投票结果", trigger: 'change' }
              ],
              surveyIntroduce: [
                { required: false, message: "", trigger: 'blur' }
              ],
              shareCopyWriting: [
                { required: false, message: "", trigger: 'blur' }
              ],
              bonusPoints: [
                { required: false, message: "", trigger: 'blur' }
              ],
              // rewardUsers: [
              //   { required: false, message: "", trigger: 'blur' }
              // ],
              surveyCover: [
                { required: false, message: "", trigger: 'blur' }
              ],
              wordReactUrl:[
                { validator: (rule, value, callback) => {
                  // eslint-disable-next-line no-useless-escape
                  if (value && !(/(http|https):\/\/([\w.]+(?:\.[\w\.-]+))+[\w\-\._~:/?#[\]@!\$&'\*\+,;=.]+$/.test(value))) {
                    callback("请输入带有协议前缀的正确网站域名")
                  } else {
                    callback()
                  }
                }, trigger: 'blur' }
              ]
            },
            headerCellStyle: {
              "background-color": "#EBEEF5"
            },
            headerRowStyle: {
              "background-color": "#EBEEF5"
            },
            addData: {
              hospitalId: '',
              hospitalName: '',
              departmentList: []
            },
            hospital: '',
            department: [],
            // 筛选规则
            filterRuleOptions: [
              {id:1,label: '省份城市'},
              {id:2,label: '填写时间'},
              {id:3,label: '单题规则'},
              {id:4,label: '双题规则'},
            ],// 筛选规则选项
            type: null,// 筛选规则的双向绑定
            allCityOptions: [], // 省份下的城市选项
            here: 1,// 省份城市双向绑定
            filterRuleCityOptions: [
              {id:1, label: '当前用户所在的省份/城市位于'},
              {id:0, label: '当前用户所在的省份/城市没有位于'},
            ],
            addProvinceCityTableData: [],// 添加省市的列表数据
            
            gt: 0,// 填写时间双向绑定
            filterRuleTimeOptions: [
              {id:0, label: '当用户填写时间小于'},
              {id:1, label: '当用户填写时间大于'}
            ],// 填写时间选项
            filterRuletime: null,// 填写的时间值
            question: '', // 跟表单绑定的题目
            select: 1, // 用户选择
            options:[],// 选择的选项
            and: 0, // 单题规则的与或
            userSelectOptions: [
              {id: 1, label: '当用户选择'},
              {id: 0, label: '当用户没有选择'},
            ], // 用户选择
            filterSingleOptions: [],// 跟表单绑定的题目选项
            formContent: null,//表单属性
            quetionItem: [],// 题目对应选项
            firstQuestion: {
              question: '', // 跟表单绑定的题目
              options:[],// 选择的选项
              and: 0, // 单题规则的与或
            }, //双题规则1
            secondQuestion: {
              question: '', // 跟表单绑定的题目
              options:[],// 选择的选项
              and: 0, // 单题规则的与或
            }, //双题规则2
            firstQuestionOptions: [],//双题1
            secondQuestionOptions: [],//双题2
            firstQuestionItem: [],// 双题1对应题目选项
            secondQuestionItem: [],// 双题1对应题目选项
            
            quotaQuestion: '', // 配额规则条件
            quotaRuleOptions: [
              {id:1,label: '显示配额'},
              {id:0,label: '隐式配额'}
            ],// 配额规则选项
            quotaQuestionOptions: [
              {prop:'quotaquestionlast',label:"省份城市"}
            ],// 显示配额表单题目
            quotaDetailData: [],// 配额详情表格
            // 省份城市添加
            addCityData: {
              seq: 1,
              type: 1,
              here: 1,
              province: '',
              provinceName: '',
              city: '',
              cityName: ''
            },
            province: '',
            cityId: '',
            city: '',
            // 填写时间添加
            addTimeArr: [],
            // 单题选项添加
            addSingleQuestionArr: [],
            // 双题选项添加
            addDdoubleQuestionArr: [],
            // 隐式配额添加
            addSecondQuotaArr: [],
            // 配额规则省份城市
            quotaProvince: '',
            quotaCityId: '',
            quotaCity: '',
            // 添加配额规则省份城市
            addQuotaCityData: {
              type: 1,
              province: '',
              provinceName: '',
              city: '',
              cityName: '',
              count: '',
              show: true
            },
            allQuotaCityOptions: [], //省份下所有城市
            // 配额添加省份城市
            addQuotaProvinceCityData: [],
            // 筛选总表数据
            filterRulesData: [],
            // 配额总表数据
            quotaRulesData: []
        }
    },
    watch: {
        time() {
            if (this.time && this.time.length > 0) {
                this.form.openStartTime = parseTime(this.time[0], '{y}-{m}-{d} {h}:{i}:{s}') 
                this.form.openEndTime = parseTime(this.time[1], '{y}-{m}-{d} {h}:{i}:{s}') 
            } else {
                this.form.openStartTime = ''
                this.form.openEndTime = ''
            } 
        },
        'form.surveyIntroduce': function (val) {
          if (val && (typeof val) == 'string') {
              this.form.surveyIntroduceRemovePic = this.PUBLIC_Methods.unexcapeHtml(getEditContent(val))
              this.form.surveyIntroduce = this.PUBLIC_Methods.unexcapeHtml(val)
          }
        },
        editorImgArr: function(val) {
          if (val && val.length > 0 && !this.submitData.id && !this.submitData.cover) {
            this.submitData.cover = val[0]
          }
        }
        // 'form.isOpenResult':function(e){
        //   console.log(e,'e');
        // }
    },
    computed: {
      ...mapGetters(["info", "editorImgArr"])
    },
    components: {
        FooterToolBar,
        MsHospitalSearch,
        msCategoryCascader,
        msInfoSetting,
        MsFormSearch,
        // PrismEditor,
        MsProvinceSearch,
        MsEditor,
        msImageUpload,
    },
    mounted() {
        if(this.$route.query.id) {
          this.PUBLIC_Methods.apiNotify("修改表单会使已设置的相关筛选规则与配额规则失效，请谨慎修改。", "warning")
        }
        this.init()
    },
    methods: {
      bindData (params) {
        this.submitData.fileKey = params.key;
        this.submitData.fileSize = params.size;
        this.submitData.fileUrl = params.url;
        this.submitData.title = params.name;
      },
      changeType(){
        this.addProvinceCityTableData = []
      },
      bindForm(val) {
        console.log(123);
        this.form.templateId = val.model.formId
        this.form.templateSurveyName = val.model.formName
        this.form.templateName = val.model.formName
        // 表单属性赋值
        this.formContent = val.model.formContent
        this.question = ''
        this.quetionItem = []
        // 跟表单绑定的题目选项
        this.filterSingleOptions = []
        if(this.formContent) {
          this.filterSingleOptions.push(...this.formContent.column.filter(item => item.type == 'radio'))
          this.filterSingleOptions.push(...this.formContent.column.filter(item => item.type == 'checkbox'))
          this.filterSingleOptions.push(...this.formContent.column.filter(item => item.type == 'select'))
        }
        this.firstQuestionOptions = this.secondQuestionOptions = this.filterSingleOptions
        this.quotaQuestionOptions.unshift(...this.filterSingleOptions)
        // 修改表单即清空所有数组
        this.form.filterRules = []
        this.filterRulesData = []
        this.form.quotaRules = []
        this.quotaRulesData = []
      },
      bindHospital(val) {
        this.addData.hospitalId = val.model.id
        this.addData.hospitalName = val.model.name
      },
      bindDepartment(val) {
        this.addData.departmentList = val.model
      },
      addMessList() {
        if(this.form.orientationParamRequest.length > 99) {
          this.PUBLIC_Methods.apiNotify("最多添加100个医院", "warning")
          return
        }
        if(!this.addData.hospitalId) {
          this.addData.hospitalId = -1
        }
        if(this.addData.departmentList.length < 1) {
          this.addData.departmentList.push({
            categoryId: -1,
            categoryName: '全部'
          })
        }
        this.form.orientationParamRequest.push(this.addData)
        this.addData = {
          hospitalId: '',
          hospitalName: '',
          departmentList: []
        }
        this.hospital = ""
        this.department = []
      },
      removeMessList(index) {
        this.form.orientationParamRequest.splice(index, 1)
      },
      // 省份
      bindProvince(val){
        this.cityId = ''
        this.addCityData.province = val.model.id
        this.addCityData.provinceName = val.model.shortName
        this.api.getMedsciTreeAddress({levelType: 2}).then( response => {
          if(response.status === 200 && response.data && response.data.length > 0) {
            this.allCityOptions = []
            this.allCityOptions.push(...(response.data[0].child.filter(item => item.id === val.model.id)[0].child))
          }
        })
      },
      // 城市
      bindCity(val){
        this.addCityData.city = val
        this.addCityData.cityName = this.allCityOptions.length > 0 ? (this.allCityOptions.filter(item => item.id === val))[0].name : ''
      },
      // 修改用户位于/不位于
      changeCityRule(val){
        console.log(val)
        this.addCityData.here = val
        console.log(this.here)
      },
      // 修改单选的题目
      changeQuestion(val) {
        this.options = []
        let arr = this.formContent.column.filter(item=> item.prop == val)
        this.quetionItem = arr[0].dicData
      },
      // 修改双选第一题
      changeFirstQuestion(val){
        let arr = this.formContent.column.filter(item=> item.prop == val)
        this.firstQuestionItem = arr[0].dicData
        let chooseItem = this.secondQuestionOptions.findIndex(item => item.prop ==val)
        this.secondQuestionOptions.forEach(item => item.disabled = false)
        this.secondQuestionOptions[chooseItem].disabled = true
      },
      // 修改双选第二题
      changeSecondQuestion(val){
        let arr = this.formContent.column.filter(item=> item.prop == val)
        this.secondQuestionItem = arr[0].dicData
        let chooseItem = this.firstQuestionOptions.findIndex(item => item.prop ==val)
        this.firstQuestionOptions.forEach(item => item.disabled = false)
        this.firstQuestionOptions[chooseItem].disabled = true
      },
      // 添加省份城市筛选规则的 省市表格
      addCityList() {
        if(!this.cityId) {
          this.addCityData.cityName = '全选'
        }
        if(this.addProvinceCityTableData.length < 1) {
          this.addProvinceCityTableData.push(this.addCityData)
        } else {
          // 校验省份时 数组长度为0则是没有重复
          let isSameProvince = this.addProvinceCityTableData.filter(item => item.province === this.addCityData.province)
          // 校验用户位于/不位于
          let isSameLocal = this.addProvinceCityTableData.filter(item => item.here === this.addCityData.here)
          // 用户位于的情况不同 但是 省份重复 则互斥
          if(!isSameLocal.length && isSameProvince.length){
            this.PUBLIC_Methods.apiNotify("已添加该省份", "warning")
            return
          }
          // 用户位于情况相同 身份重复 已有规则
          if(isSameLocal.length && isSameProvince.length){
            this.PUBLIC_Methods.apiNotify("已添加该省份", "warning")
            return
          }
          // 用户位于的情况相同 但是 省份不重复 则添加
          if(isSameLocal.length && !isSameProvince.length || !isSameLocal.length && !isSameProvince.length){
            this.addProvinceCityTableData.push(this.addCityData)
          }
        }
        
        this.addCityData = {
          seq: 1,
          type: 1,
          here: this.here,
          province: '',
          provinceName: '',
          city: '',
          cityName: '',
        }
        this.province = ""
        this.city = ""
        this.cityId = ""
      },
      removeCityList(index) {
        this.addProvinceCityTableData.splice(index, 1)
      },
      removeRuleList(index) {
        this.filterRulesData.splice(index, 1)
        this.form.filterRules = []
        this.form.filterRules.push(...this.filterRulesData)
      },
      // 生成筛选规则列表
      async addRuleList() {
        // 省份城市规则
        if(this.type == 1) {
          this.addProvinceCityTableData.forEach((item)=>{
            let index = item.here ? 0 : 1
            item.ruleDetail = `${this.filterRuleCityOptions[index].label} ${item.provinceName}(${item.cityName}) 时，将答卷标记为无效答卷`
          })
          this.form.filterRules.push(...this.addProvinceCityTableData)
          this.addProvinceCityTableData = []
        }
        // 填写时间规则
        if(this.type == 2) {
          this.addTimeArr = []
          this.addTimeArr.push({
            seq: 2,
            type: 2,
            gt: this.filterRuleTimeOptions[this.gt].id,
            time: this.filterRuletime,
            ruleDetail: `${this.filterRuleTimeOptions[Number(this.gt)].label} ${this.filterRuletime}秒时，将答卷标记为无效答卷`
          })
          this.form.filterRules.push(...this.addTimeArr)
          this.filterRuletime = ''
        }
        // 单题规则
        if(this.type == 3) {
          let arr = []
          this.addSingleQuestionArr = []
          for(var i=0;i<this.options.length;i++){
            arr.push(`${(this.filterSingleOptions.filter(item => item.prop == this.question))[0].label}的${this.quetionItem[`${this.options[i]}`].label}`)
          }
          let selectIndex = this.select ? 0 : 1
          this.addSingleQuestionArr.push({
            seq: 3,
            type: 3,
            question: this.question,
            options: this.options,
            select: this.select,
            and: this.and,
            ruleDetail:`${this.userSelectOptions[selectIndex].label}${arr.join(`${this.and ? '与':'或'}`)}时，将答卷标记为无效答卷`
          })
          this.form.filterRules.push(...this.addSingleQuestionArr)
          this.options = []
        }
        // 双题规则
        if(this.type == 4) {
          let arr1 = []
          for(let j=0;j<this.firstQuestion.options.length;j++){
            arr1.push(`${(this.firstQuestionOptions.filter(item => item.prop == this.firstQuestion.question))[0].label}的${this.firstQuestionItem[`${this.firstQuestion.options[j]}`].label}`)
          }
          let arr2 = []
          for(let i=0;i<this.secondQuestion.options.length;i++){
            arr2.push(`${(this.secondQuestionOptions.filter(item => item.prop == this.secondQuestion.question))[0].label}的${this.secondQuestionItem[`${this.secondQuestion.options[i]}`].label}`)
          }
          this.addDdoubleQuestionArr = []
          this.addDdoubleQuestionArr.push({
            //双题规则
            seq: 4,
            type: 4,
            firstQuestion: {
              "question": this.firstQuestion.question,
              "options":this.firstQuestion.options,
              "and": this.firstQuestion.and                           
            },
            secondQuestion: {
              "question": this.secondQuestion.question,
              "options": this.secondQuestion.options,
              "and": this.secondQuestion.and
            },
            ruleDetail: `当用户选择${arr1.join(`${this.firstQuestion.and ? '与':'或'}`)},同时选择了${arr2.join(`${this.secondQuestion.and ? '与':'或'}`)}时，将答卷标记为无效答卷`
          })
          this.form.filterRules.push(...this.addDdoubleQuestionArr)
          this.firstQuestion.options = []
          this.secondQuestion.options = []
        }
        // 校验接口请求成功之后执行makeRuleCheck
        let params = {
          templateId: this.form.templateId,
          userId: this.$store.getters.info.userId,
          username: this.$store.getters.info.userName,
          filterRules: this.form.filterRules
        }
        const res = await this.api.makeRuleCheck(params)
        if(res.status == 200) {
          this.filterRulesData = []
          this.filterRulesData.push(...this.form.filterRules)
        } else {
          this.PUBLIC_Methods.apiNotify(`${res.message}`, "warning")
          this.form.filterRules.splice(this.form.filterRules.length-1)
        }
        
      },
      // 校验数值
      validateNumber() {
        // 0-1000000
        if(this.filterRuletime<0){
          this.filterRuletime = 0
        }else if(this.filterRuletime>1000000){
          this.filterRuletime = 1000000
        }else if(this.filterRuletime.indexOf('.')>0){
          this.filterRuletime = Math.floor(this.filterRuletime)
        }
      },
      // 改变配额表单题目
      changeQuotaQuestion(val) {
        let arr = this.formContent? this.formContent.column.filter(item=> item.prop == val):[]
        this.quotaDetailData = []
        if(arr.length>0){
          let arrdicdata = arr[0].dicData
          let dicData = []
          let currentQuestion = (this.quotaQuestionOptions.filter(item=> item.prop === val))[0].label
          for(let i=0;i<arrdicdata.length;i++){
            dicData.push({option: arrdicdata[i].value,optionStr: `${currentQuestion}的${arrdicdata[i].label}`,num: '', show: true})
          }
          this.quotaDetailData.push(...dicData)
        }
      },
      // 1校验配额数量1-50000
      validateQuotaNum(scope){
        let num = scope.row.count
        if(num<=0){
          scope.row.count = ''
        }else if(num>50000){
          scope.row.count = 50000
        }else if(num.indexOf('.')>0){
          scope.row.count = Math.floor(num)
        }
      },
      // 修改配额规则选项
      changeQuotaType(val) {
        this.form.quotaRuleType = val
        this.quotaQuestion = ''
        this.form.quotaRules = []
        this.quotaDetailData = []
        this.quotaRulesData = []
      },
      // 省份
      bindQuotaProvince(val){
        this.quotaCityId = ''
        this.addQuotaCityData.province = val.model.id
        this.addQuotaCityData.provinceName = val.model.shortName
        this.api.getMedsciTreeAddress({levelType: 2}).then( response => {
          if(response.status === 200 && response.data && response.data.length > 0) {
            this.allQuotaCityOptions = []
            this.allQuotaCityOptions.push(...(response.data[0].child.filter(item => item.id === val.model.id)[0].child))
          }
        })
      },
      // 城市
      bindQuotaCity(val){
        this.addQuotaCityData.city = val
        this.addQuotaCityData.cityName = this.allQuotaCityOptions.length > 0 ? (this.allQuotaCityOptions.filter(item => item.id === val))[0].name : ''
      },
      // 添加城市
      addQuotaCityList() {
        if(!this.quotaCityId) {
          this.addQuotaCityData.cityName = '全选'
        }
        if(this.addQuotaProvinceCityData.length < 1) {
          this.addQuotaProvinceCityData.push(this.addQuotaCityData)
        } else {
          // 校验省份时 数组长度为0则是没有重复
          let isSameProvince = this.addQuotaProvinceCityData.filter(item => item.province === this.addQuotaCityData.province)
          // 校验用户位于/不位于
          let isSameLocal = this.addQuotaProvinceCityData.filter(item => item.here === this.addQuotaCityData.here)
          // 用户位于的情况不同 但是 省份重复 则互斥
          if(!isSameLocal.length && isSameProvince.length){
            this.PUBLIC_Methods.apiNotify("已添加该省份", "warning")
            return
          }
          // 用户位于情况相同 身份重复 已有规则
          if(isSameLocal.length && isSameProvince.length){
            this.PUBLIC_Methods.apiNotify("已添加该省份", "warning")
            return
          }
          // 用户位于的情况相同 但是 省份不重复 则添加
          if(isSameLocal.length && !isSameProvince.length || !isSameLocal.length && !isSameProvince.length){
            this.addQuotaProvinceCityData.push(this.addQuotaCityData)
          }
        }
        this.addQuotaCityData = {
          type: 1,
          province: '',
          provinceName: '',
          city: '',
          cityName: '',
          count:'',
          show: true
        }
        this.quotaProvince = ""
        this.quotaCity = ""
        this.quotaCityId = ""
      },
      // 生成配额规则
      async addQuotaRuleList(){
        let isHaveTip = this.form.quotaRuleType ? `且提示${this.form.quotaFull?`"${this.form.quotaFull}"`:'"配额已满"'}`:''
        this.form.quotaFull = this.form.quotaRuleType ? `${this.form.quotaFull?`${this.form.quotaFull}` : '配额已满'}`: ''
        if(this.quotaQuestion != 'quotaquestionlast'){
          for(let i=0;i<this.quotaDetailData.length;i++){
            if(this.quotaDetailData[i].count){
              this.addSecondQuotaArr.push({
                "type": 2,
                "question":this.quotaQuestion, //题目编码（prop）
                "option": this.quotaDetailData[i].option, //选项value
                "count": this.quotaDetailData[i].count, //配额数量
                ruleDetail: `当用户选择${this.quotaDetailData[i].optionStr}的问卷达${this.quotaDetailData[i].count}份后，后续问卷无法提交${isHaveTip}`
              })
              this.form.quotaRules.push(...this.addSecondQuotaArr)
              this.addSecondQuotaArr = []
            }
          }
          this.quotaDetailData = []
        }else{
          if(this.addQuotaProvinceCityData.length == 1&&!this.addQuotaProvinceCityData[0].count){
            this.PUBLIC_Methods.apiNotify("请填写配额数量", "warning")
            return
          }
          for(let i=0;i<this.addQuotaProvinceCityData.length;i++){
            if(this.addQuotaProvinceCityData[i].count){
              //当用户IP地址是浙江省杭州市的问卷达24份后，后续问卷无法提交且提示“配额已满”
              this.addQuotaProvinceCityData[i].ruleDetail= `当用户IP地址是${this.addQuotaProvinceCityData[i].provinceName}${this.addQuotaProvinceCityData[i].cityName}的问卷达${this.addQuotaProvinceCityData[i].count}份后，后续问卷无法提交${isHaveTip}`
              this.form.quotaRules.push(this.addQuotaProvinceCityData[i])
            }
          }
          this.addQuotaProvinceCityData = []
        }
        this.quotaQuestion = ''
        // 校验成功之后执行
        let params = {
          templateId: this.form.templateId,
          userId: this.$store.getters.info.userId,
          username: this.$store.getters.info.userName,
          quotaRules: this.form.quotaRules,
          quotaFull: this.form.quotaRuleType ? `${this.form.quotaFull?`${this.form.quotaFull}` : '配额已满'}`: '',
          quotaRuleType: this.form.quotaRuleType
        }
        const resp = await this.api.makeRuleCheck(params)
        if(resp.status == 200){
          this.quotaRulesData = []
          this.quotaRulesData.push(...this.form.quotaRules)
        }else{
          this.PUBLIC_Methods.apiNotify(`${resp.message}`, "warning")
          this.form.quotaRules.splice(this.form.quotaRules.length-1)
        }
        
      },
      // 删除生成配额原则
      removeQuotaRuleList(index){
        this.quotaRulesData.splice(index, 1)
        this.form.quotaRules = []
        this.form.quotaRules.push(...this.quotaRulesData)
      },
      // 获取表单
      getOptionData(val) {
        // 接口获取数据
        let params = {
          formName : val ? val : '',
          currentPageNo: 1,
          status: 'PASS',
          pageSize: 20
        }
        this.api.formService_getFormList(params).then(response => {
          if (response.code === 'SUCCESS') {
            // 表单属性赋值
            this.formContent = response.data.content[0].formContent
            this.question = ''
            this.quetionItem = []
            // 跟表单绑定的题目选项
            this.filterSingleOptions = []
            if(this.formContent) {
              this.filterSingleOptions.push(...this.formContent.column.filter(item => item.type == 'radio'))
              this.filterSingleOptions.push(...this.formContent.column.filter(item => item.type == 'checkbox'))
              this.filterSingleOptions.push(...this.formContent.column.filter(item => item.type == 'select'))
            }
            this.firstQuestionOptions = this.secondQuestionOptions = this.filterSingleOptions
            this.quotaQuestionOptions.unshift(...this.filterSingleOptions)
          }
        })
      },
      async init() {
          const { id } = this.$route.query;
          if (id) {
              const res = await this.api.getCustomFormById({ id })
              if (res.status !== 200) {
                  return this.PUBLIC_Methods.apiNotify(res.message || '请求出错', 'warning')
              }
              this.form = {
                ...res.data,
                filterRules: res.data.filterRules ? res.data.filterRules : [],
                quotaRules: res.data.quotaRules ? res.data.quotaRules : [],
                formCategoryList: res.data.formCategoryList ? res.data.formCategoryList : [],
                orientationParamRequest: res.data.orientationParamRequest ? res.data.orientationParamRequest : [],
                cssContent: this.PUBLIC_Methods.unexcapeHtml(res.data.cssContent),
                surveyIntroduce: this.PUBLIC_Methods.unexcapeHtml(res.data.surveyIntroduce)
              }
              this.filterRulesData = []
              this.filterRulesData.push(...this.form.filterRules)
              this.quotaRulesData = []
              this.quotaRulesData.push(...this.form.quotaRules)
              this.templateSurveyName = res.data.templateSurveyName
              this.getOptionData(this.templateSurveyName)
              this.isPage = res.data.isPage
              this.respEquipmentMaxNum = res.data.respEquipmentMaxNum
              this.ipAddressMaxNum = res.data.ipAddressMaxNum
              if (res.data.openStartTime && res.data.openEndTime) {
                  this.time = [parseTime(res.data.openStartTime, '{y}-{m}-{d} {h}:{i}:{s}'), parseTime(res.data.openEndTime, '{y}-{m}-{d} {h}:{i}:{s}')]
              }
          }
      },
      // 保存
      async handleSave() {
        this.buttonLoading = true;
        // 如果为投票
        let params = {}
        if(this.form.drainageInfoDtoList && this.form.drainageInfoDtoList.length != 0 && this.form.drainageInfoDtoList[0].categoryName){
          this.form.drainageInfoDtoList = [{
            categoryId:this.form.drainageInfoDtoList[0].categoryId,
            switchDrainage:1,
            projectName:this.form.drainageInfoDtoList[0].categoryName,
            url:this.form.drainageInfoDtoList[0].url+'/templatePage/',
          }]
          if(this.form.drainageInfoDtoList[0].url.indexOf('https://') == -1 && this.form.drainageInfoDtoList[0].url.indexOf('http://') == -1){
            this.form.drainageInfoDtoList[0].url = 'https://' + this.form.drainageInfoDtoList[0].url
          }
        }else{
          this.form.drainageInfoDtoList = []
        }
        if(this.form.isOpenResult===1){
          // 投票属性
          this.form.cssContent = this.PUBLIC_Methods.excapeHtml(this.form.cssContent)
          this.form.filterRules = []
          this.form.filterRules.push(...this.filterRulesData)
          this.form.quotaRules = []
          this.form.quotaRules.push(...this.quotaRulesData)
          params = {
            formCategoryList: this.form.formCategoryList,
            isSubmitOne:1,
            isOpenResult:this.form.isOpenResult,
            templateSurveyName:this.form.templateSurveyName,
            templateName:this.form.templateName,
            isLogin:this.form.isLogin,
            formType:this.form.formType,
            surveyIntroduce:this.PUBLIC_Methods.excapeHtml(this.form.surveyIntroduce),
            surveyIntroduceRemovePic:this.form.surveyIntroduceRemovePic,
            shareCopyWriting:this.form.shareCopyWriting,
            surveyCover:this.form.surveyCover,
            pollResults:this.form.pollResults,
            loginAuthority:this.form.loginAuthority,
            bonusPoints:this.form.bonusPoints,
            // rewardUsers:this.form.rewardUsers,
            showMedsci:this.form.showMedsci,
            openStartTime:this.form.openStartTime,
            openEndTime:this.form.openEndTime,
            templateId:this.form.templateId,
            userId: this.$store.getters.info.userId,
            username: this.$store.getters.info.userName,
            drainageInfoDtoList: this.form.drainageInfoDtoList,
          }
        }else{
          this.form.cssContent = this.PUBLIC_Methods.excapeHtml(this.form.cssContent)
          this.form.filterRules = []
          this.form.filterRules.push(...this.filterRulesData)
          this.form.quotaRules = []
          this.form.quotaRules.push(...this.quotaRulesData)
           params = {
              ...this.form,
              surveyIntroduce:this.PUBLIC_Methods.excapeHtml(this.form.surveyIntroduce),
              surveyIntroduceRemovePic:this.form.surveyIntroduceRemovePic,
              shareCopyWriting:this.form.shareCopyWriting,
              surveyCover:this.form.surveyCover,
              pollResults:this.form.pollResults,
              loginAuthority:this.form.loginAuthority,
              userId: this.$store.getters.info.userId,
              username: this.$store.getters.info.userName,
          }
        }
          const { id } = this.$route.query;
          if (id) {
              // 编辑拼加密id
              if(params.drainageInfoDtoList.length != 0 ){
                params.drainageInfoDtoList[0].url = this.form.drainageInfoDtoList[0].url + this.form.encryptionId
              }
              const res = await this.api.updateFormMaterial(Object.assign(params, {id}))
              if (res.status !== 200) {
                  this.PUBLIC_Methods.apiNotify(res.message || '请求出错', 'warning')
                  this.buttonLoading = false;
                  return
              }
          } else {
              const res = await this.api.saveFormMaterial(params)
              if (res.status !== 200) {
                  this.PUBLIC_Methods.apiNotify(res.message || '请求出错', 'warning')
                  this.buttonLoading = false;
                  return
              }
          }
          this.buttonLoading = false;
          this.$router.back()
      },
      // 按钮
      info_operation(val) {
        switch (val) {
            case "save":
                this.$refs.form.validate(async (valid) => {
                  console.log('??');
                    if (valid) {
                      if(!this.form.formCategoryList.length && this.form.formType === 2) {
                        this.activeName = "category"
                        this.PUBLIC_Methods.apiNotify('请选择对应项目')
                      }else {
                        this.handleSave()
                      }
                    } else {
                      this.activeName = "design"
                    }
                })
                break
            case "back":
                this.$router.back()
                break
            default:
                break
        }
      },
      changeResult(e){
        this.form = {
              isSubmitOne: 1, //  是否只能提交一次,0否,1是
              isPage: 0, // 是否分页
              // isPcVisible: 1, // PC是否可见(0.不可见,1.可见)
              // isAppVisible: 1, // APP是否可见(0.不可见,1.可见)
              isOpenResult: e, // 是否公开结果(0,不公开,1公开)
              openStartTime: "", // 开放时间start
              openEndTime: "", // 开放时间end
              // rewardUsers: "", // 奖励用户数
              bonusPoints: "", // 积分奖励
              formCategoryList: [], // 调研分类
              basisNumber: undefined, //在线观看基础人数
              multipleNumber: undefined, //在线观看人数倍数
              // isRecommend: null, //调研角标 0.不推荐 1.推荐
              surveyIntroduce:'',
              surveyIntroduceRemovePic:'',
              shareCopyWriting: '', // => 分享文案 (string)
              surveyCover: '', // => 调研封面 （string）
              pollResults:1, //投票结果 1.提交后显示 2.投放结束后显示
              loginAuthority: 1, //参与权限  1.免登录 2.登录 3.完善信息
              formType: 1, //调研属性 1.平台调研 2.商业调研
              orientationParamRequest: [], //定向群发参数集合
              targetedSwitch: null, //定向医院开关 1-开启 0-关闭
              templateId: null, //模板id
              templateName: null, //调研name
              templateSurveyName: null, //模版name

              filterRuleSwitch: 0, // 筛选规则
              filterRules: [],//所有筛选规则数据集合

              quotaRuleSwitch: 0, //配额规则开关 (0:关闭 1:开启)
              quotaRules: [], //配额规则集合
              quotaRuleType: 1, // 0->隐式配额,1->显示配额
              quotaFull: '',// 显示配额的提示文案

              isLogin: 1, //默认需要登录
              isPerfectInfo: 1, //默认打开完善信息
              showMedsci: 1, //默认主站展示
              wordReactUrl: "", //跳转地址
              wordReactText: "", //跳转提示
              respEquipmentMaxNum: 0, //作答设备控制
              ipAddressMaxNum: 0, //ip地址限制
              temporarySave: 0, //临时保存
            }
        this.templateSurveyName = null,
        this.time = null,
        this.$refs.form.resetFields()
      }
    },
}
</script>

<style scoped lang="scss">
.form-design-style {
    width: 100%;
    height: calc(100vh - 230px);
    border-radius: 4px;
    border: 1px solid #DCDFE6;
    /deep/ .ant-form-item-control {
        text-align: left;
    }
    /deep/ label {
        font-weight: 500;
    }
    /deep/ iframe {
      display: none;
    }
}
.flex-item {
  /deep/ .el-form-item__content {
    display: flex;
    flex-direction: row;
  }
}
/deep/ .prise-editor pre{
  margin: 0 0 30px 0 !important;
  padding: 5px 15px 15px;
  min-height: 150px;
  height: 150px;
  border-radius: 4px;
}
.tips {
  color:red;
  font-size: 12px;
  padding-left:20px;
}
.inlines {
  display: flex;
  align-items: center;
  // width:20px
}

.inputs {
  margin: 0 10px;
  width: 20px;
}

/deep/ .inlines .el-input__inner{
    padding: 0;
    width: 41px;
}
/deep/ .inlines {
  .el-input--mini{
   padding-left: 12px;
  }
  .el-input-number__increase{
    display: none;
  }
  .el-input-number__decrease{
    display: none;
  }
  .el-input-number--mini{
    width: 57px;
  }
}
</style>

