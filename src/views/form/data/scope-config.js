// import serveUrl from '@/store/data/serveUrl.js'

const scopeConfig = {
  show: {
    approvalStatus: () => {
      return {
        type: 'status',
        rule: {
          0: { label: '待审核', background: '#A7ADBD' },
          1: { label: '审核通过', background: '#40A23F' },
          2: { label: '草稿', background: '#E6A23C' }
        }
      }
    },
    isOpenResult: () => {
      return {
        type: 'status-char',
        rule: {
          1: { label: '投票' },
          0: { label: '调研问卷' }
        },
      }
    },
    bonus: () => {
      return {
        type: 'status',
        rule: {
          0: { label: '无', background: '#A7ADBD' },
          1: { label: '积分', background: '#40A23F' },
          2: { label: '梅花', background: '#E6A23C' }
        }
      }
    },
    publishedTime: () => {
      return {
        type: 'formatTime',
        cFormat: '{y}-{m}-{d} {h}:{i}'
      }
    },
    createdTime: () => {
      return {
        type: 'formatTime',
        cFormat: '{y}-{m}-{d} {h}:{i}'
      }
    },
    templateName: (row) => {
      let color =  row.isSticky == 1 && row.isRecommend == 1? '#67C23A':(row.isSticky == 1?'rgb(230, 162, 60)' : (row.isRecommend == 1 ? 'red' : ''))
      // let colorType =  row.isSticky == 1 && row.isRecommend == 1? 'all':(row.isSticky == 1?'isSticky' : (row.isRecommend == 1 ? 'isRecommend' : ''))
      return {
        type: 'other',
        config: {
          way: 'link',
          pathKey: 'url',
          color: color
        }
      }
    },
    fields: () => {
      return {
        type: 'fidd',
        fields: [
          {name: 'allHits', way: 'text'},
          {name: 'userHits', way: 'page', path: 'form-details',params: ['id'], operation: 'getSurveyUvRecordPage'},
          {name: 'joinCount', way: 'page', path: 'form-details',params: ['id'], operation: 'getSurveyRecordPage'}
        ]
      }
    },
    channel: () => {
      return {
        type: 'fidd',
        fields: [
          {name: 'projectName', way: 'text'},
          {name: 'moduleName', way: 'text'}
        ],
        connector: '-'
      }
    },
    ipFields: () => {
      return {
        type: 'fidd',
        fields: [
          {name: 'ipProvince', way: 'text'},
          {name: 'ipCity', way: 'text'}
        ],
      }
    },
  },
  headerShow: {
    // fields: () => {
    //   return {
    //     type: 'dropdown',
    //     icon: '',
    //     options: [
    //       { label: '默认排序', value: null },
    //       { label: '按总点击量', value: 1 },
    //       { label: '按APP点击量', value: 2 },
    //       { label: '按PC点击量', value: 3 },
    //       { label: '按点赞量', value: 4 },
    //       { label: '按分享量', value: 5 },
    //       { label: '按评论量', value: 6 },
    //     ],
    //     operation: 'query',
    //     params: 'sortType'
    //   }
    // },
    approvalStatus: () => {
      return {
        type: 'dropdown',
        icon: 'icon-funnel',
        options: [
          { label: '全部', value: 2 },
          { label: '待审核', value: 0 },
          { label: '审核通过', value: 1 }
        ],
        operation: 'query'
      }
    },
    bonus: () => {
      return {
        type: 'dropdown',
        icon: 'icon-funnel',
        options: [
          { label: '全部', value: null },
          { label: '无', value: 0 },
          { label: '积分', value: 1 },
          { label: '梅花', value: 2 }
        ],
        operation: 'query'
      }
    }
  }
}

export default scopeConfig;
