// import store from '@/store'

const domConfig = {
  listSearch: [
    {
      label: '标题',
      placeholder: '请输入',
      model: 'value',
      component: 'ms-input'
    },
    {
      label: '创建人',
      placeholder: '请选择',
      model: 'createdBy',
      component: 'ms-createby-search'
    },
    {
      label: '发布时间',
      placeholder: '请选择时间段',
      model: 'createTime',
      component: 'ms-picker',
      type: 'daterange',
      width: '240px'
    }
  ],
  tableHeader: [
    { label: 'ID', property: 'id', sortable: true, width: '60' },
    { label: '标题', property: 'templateName', width: '180' },
    { label: '属性', property: 'isOpenResult', width: '100' },
    { label: '创建人', property: 'createdName' },
    { label: '发布时间', property: 'publishedTime', sortable: true,  width: '130' },
    { label: '奖励', property: 'bonus' },
    { label: 'PV/UV/已提交', property: 'fields', width: '100' },
    { label: '状态', property: 'approvalStatus' },
  ],
  tableButtons: [
    {
      label: '编辑',
      icon: '',
      role: '',
      operation: 'edit',
      component: 'form-operation',
      way: 'page',
      type: 'primary',
      path: 'form-operation',
      params: ['id'],
    },
    {
      label: '',
      icon: '',
      role: '',
      operation: 'editStatus',
      component: 'msFormOperation',
      way: 'dialog',
      field: 'approvalStatus',
      rule: {
        1: { label: '去审', type: '', operation: 'toreview' },
        0: { label: '审核', type: 'success', operation: 'approval' }
      }
    },
    {
      label: '删除',
      way: 'page',
      type: 'danger',
      operation: 'delete',
      component: 'msFormOperation'
    },
    {
      label: '更多',
      operation: 'more',
      type: 'info',
      children: [
        {
          label: '',
          way: 'recommend',
          field: 'isRecommend',
          rule: {
            1: { label: '取消推荐'},
            0: { label: '推荐'}
          }
        },
        {
          label: '',
          way: 'sticky',
          field: 'isSticky',
          rule: {
            1: { label: '取消固顶'},
            0: { label: '固顶'}
          }
        },
        {
          label: '导出',
          way: 'export'
        }
    //     {
    //       label: '推至',
    //       icon: '',
    //       role: '',
    //       operation: 'edit',
    //       component: 'msArticleProject',
    //       way: 'dialog',
    //       title: '推至其他项目',
    //       type: 'warning',
    //       width: '60%',
    //       identify: 'push_project'
    //     },
      ]
    },
    {
      label: '统计',
      way: 'page',
      type: 'default',
      path: 'form-statistics',
      component: 'form-statistics',
      params: ['id','templateId','templateName','type'],
    },
  ],

  soltButtons: [
    { 
      label: '调研添加', 
      type: 'primary', 
      icon: 'el-icon-plus',
      operation: 'created',
      component: 'form-operation',
      way: 'page',
      path: 'form-operation',
      params: ['id'],
    },
    { 
      label: '批量审核', 
      type: 'primary',
      operation: 'approval',
      component: 'msFormOperation',
      way: 'batch',
    },
    { 
      label: '批量去审', 
      type: 'primary',
      operation: 'toreview',
      component: 'msFormOperation',
      way: 'batch',
    },
    { 
      label: '批量删除',
      type: 'info',
      icon: 'el-icon-close',
      operation: 'delete',
      component: 'msFormOperation',
      way: 'batch',
    },
    {
      label: '回收站',
      type: 'info',
      operation: '',
      component: 'msFormRecycle',
      way: 'dialog',
      title: '调研回收站',
      width: '70%',
    }
  ]
}

export default domConfig;
