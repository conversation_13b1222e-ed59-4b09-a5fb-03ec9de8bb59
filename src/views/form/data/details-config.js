// import store from '@/store'

const domConfig = {
  listSearch: [
    {
      label: '用户名',
      placeholder: '请输入',
      model: 'userName',
      component: 'ms-input'
    },
    {
      label: '手机号码',
      placeholder: '请输入',
      model: 'userMobile',
      component: 'ms-input'
    },
    {
      label: '医院',
      placeholder: '请输入',
      model: 'hospitalName',
      component: 'ms-input'
    },
  ],
  tableHeader: [
    { label: 'ID', property: 'id', sortable: true, width: '60' },
    { label: '姓名', property: 'userName'},
    { label: '手机号', property: 'userMobile'},
    { label: '省份', property: 'provinceName'},
    { label: '城市', property: 'cityName'},
    { label: '城市级别', property: 'cityLevel'},
    { label: '医院（单位）', property: 'hospitalName'},
    { label: '医院级别', property: 'hospitalLevel'},
    { label: '科室（部门）', property: 'departmentName'},
    { label: '职称（职位）', property: 'professionalCatName'},
    { label: '浏览时间', property: 'browseTime', sortable: true,  width: '130'  },
    { label: '来源渠道', property: 'channel' },
    { label: '企业', property: 'businessName'},
    { label: '学校', property: 'universityName'},
  ],
  tableHeaderIp: [
    { label: 'ID', property: 'id', sortable: true, width: '60' },
    { label: '来自IP', property: 'ipAddress', width: '120'},
    { label: 'IP所属地', property: 'ipFields'},
    { label: '姓名', property: 'userName'},
    { label: '手机号', property: 'userMobile'},
    { label: '省份', property: 'provinceName'},
    { label: '城市', property: 'cityName'},
    { label: '城市级别', property: 'cityLevel'},
    { label: '医院（单位）', property: 'hospitalName'},
    { label: '医院级别', property: 'hospitalLevel'},
    { label: '科室（部门）', property: 'departmentName'},
    { label: '职称（职位）', property: 'professionalCatName'},
    { label: '提交时间', property: 'submitTime', sortable: true,  width: '130'  },
    { label: '来源渠道', property: 'channel' },
    { label: '企业', property: 'businessName'},
    { label: '学校', property: 'universityName'},
  ],
}

export default domConfig;
