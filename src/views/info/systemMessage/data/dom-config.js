const domConfig = {
  // 搜索配置
  listSearch: [
    {
      label: '创建人',
      placeholder: '请输入创建人',
      model: 'createdName',
      component: 'ms-input'
    },
    {
      label: '创建时间',
      placeholder: '请选择时间段',
      model: 'createTime',
      component: 'ms-picker',
      type: 'daterange',
      width: '240px'
    }
  ],

  // 表头配置
  tableHeader: [
    { label: 'ID', property: 'id', width: '80' },
    { label: '对象类型', property: 'objType' },
    { label: '对象ID', property: 'objId' },
    { label: '阅读量', property: 'readCount', sortable: true },
    { label: '创建人', property: 'createdName' },
    { label: '创建时间', property: 'createdTime' },
    { label: '状态', property: 'status' }
  ],

  // 行内列表按钮配置
  tableButtons: [
    {
      label: '',
      icon: '',
      role: '',
      operation: 'editStatus',
      component: 'msSystemOperation',
      way: 'dialog',
      field: 'status',
      rule: {
        1: { label: '去审', type: '', operation: 'toreview' },
        0: { label: '审核', type: 'success', operation: 'approval' }
      },
    },
    {
      label: '删除',
      way: 'dialog',
      type: 'danger',
      operation: 'delete',
      component: 'msSystemOperation'
    }
  ],

  // 新建项目按钮
  soltButtons: [
    { 
      label: '添加系统消息', 
      icon: 'el-icon-plus',
      type: 'primary', 
      operation: 'created',
      component: 'msSystemEdit',
      way: 'dialog',
      title: '添加系统消息',
      width: '45%'
    },
    { 
      label: '批量删除', 
      type: 'info', 
      icon: 'el-icon-close',
      operation: 'delete',
      component: 'msSystemOperation',
      way: 'batch'
    }
  ]
}
export default domConfig