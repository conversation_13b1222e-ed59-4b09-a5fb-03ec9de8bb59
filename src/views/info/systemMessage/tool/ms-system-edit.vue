<template>
  <ms-operation-dialog>
    <template slot="content">
      <el-form ref="submitRef"
               class="rule-form"
               :model="submitData"
               :rules="formConfig.rules"
               label-width="115px"
               v-loading="getLoading">
        <el-row>
          <el-col :span="22">
            <el-form-item prop="objType" label="对象类型">
              <ms-dictionary-search :model.sync='submitData.objType' type="system_message_type" style="width: 100%;"></ms-dictionary-search>
            </el-form-item>
          </el-col>
          <el-col :span="22">
            <el-form-item prop="objId" :label="objIdLabel[submitData.objType] || '对象ID'">
              <el-input v-model="submitData.objId" :placeholder="objIdPlaceholder[submitData.objType] || '请粘贴对应模块Id'">
                <el-button slot="append" icon="el-icon-d-arrow-right" @click="goModel()">前往模块</el-button>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="22">
            <el-form-item prop="message" label="通知消息">
              <el-input v-model="submitData.message" placeholder="请输入" type="textarea" :autosize="{ minRows: 3, maxRows: 5}"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </template>
    <template slot="footer">
      <el-button @click="submitForm"
                 :loading="loading"
                 size="mini"
                 type="primary">确 定</el-button>
      <el-button @click="$emit('close')"
                 size="mini">取 消</el-button>
    </template>
  </ms-operation-dialog>
</template>

<script>
import formConfig from '../data/form-config'
import msDictionarySearch from '@/components/MsCommon/ms-dictionary-search'
import { mapGetters } from "vuex";
export default {
	name: "ms-system-edit",
	data () {
		return {
      loading: false,
      getLoading: false,
      formConfig: formConfig,
      submitData: {},

      //common
      operationLocal: "",
      objIdLabel: {
        "article": '资讯ID',
        "guider": '指南ID',
        "video": '视频ID',
        "tool_impact_factor": '期刊ID',
        "topic": '话题ID',
        "meeting": '会议ID',
        "nsfc": '基金ID'
      },
      objIdPlaceholder: {
        "article": '请粘贴资讯ID',
        "guider": '请粘贴指南ID',
        "video": '请粘贴视频ID',
        "tool_impact_factor": '请粘贴期刊ID',
        "topic": '请粘贴话题ID',
        "meeting": '请粘贴会议ID',
        "nsfc": '请粘贴基金ID'
      },
      objIdPath: {
        "article": 'article',
        "guider": 'guider',
        "video": 'video',
        "tool_impact_factor": 'journal',
        "topic": 'topickey',
        "series": 'video',
        "meeting": 'meeting',
        "nsfc": 'nsfc'
      }
		}
	},
	props: {
		model: Object,
		operation: String
  },
  computed: {
    ...mapGetters(["info"])
  },
  components: {
    msDictionarySearch
  },
	methods: {
		submitForm () {
      this.$refs["submitRef"].validate(valid => {
        if(valid) {
          this.loading = true;
          this.submitAdd()
        }
      })
    },

    goModel() {
      if (this.submitData.objType) {
        this.$router.push({
          path: this.objIdPath[this.submitData.objType]
        })
      } else {
        this.$message({
          message: '请选择对象类型',
          type: 'warning'
        });
      }
    },
    
    submitAdd() {
      let params = {
        // userId: this.info.userId,
        // username: this.info.userName,
        createdId: this.info.userId,
        ...this.submitData
      }
      this.api.releaseSystemMessage(params).then(response => {
        if(response.status === 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
        this.$emit('up-date')
        this.loading = false
      }).catch( () => {
        this.loading = false;
        this.$emit('close')
      })
    },
	}
}
</script>
