<template>
	<ms-table
		:currentPage="searchParams.pageIndex"
		:loading="loading"
		:operationButtons="domConfig.tableButtons"
		:pageSize="searchParams.pageSize"
		:scopeConfig="scopeConfig.show"
    :scopeHeader="scopeConfig.headerShow"
		:tableData="list"
		:tableHeader="domConfig.tableHeader"
		:total="total"
    :showSelection="true"
		@current-change="current_change"
		@operation-change="operation_change"
		@size-change="size_change"
    @header-operation="header_operation"
    @handleSelectionChange="handleSelectionChange"
	>
    <!-- 列表搜索去区域插槽 -->
		<template slot="ms-table-header">
			<div class="slot-search">
				<template v-for="(searchItem, key) in domConfig.listSearch">
					<component
						:index="searchItem.index || ''"
						:is="searchItem.component"
						:key="key"
            :width="searchItem.width || '150px'"
            :model.sync="searchParams[searchItem.model]"
            :label="searchItem.label"
						:operation="searchItem.operation || ''"
						:options="searchItem.options || []"
						:placeholder="searchItem.placeholder || ''"
						:type="searchItem.type || ''"
            :multiple="searchItem.multiple"
            :disabled="searchItem.disabled" 
					></component>
				</template>
        <div class="inlineBlock">
          <el-button @click="handleClick('query')" type="primary" plain icon="el-icon-search">查询</el-button>
          <el-button @click="handleClick('reset')">重置</el-button>
        </div>
        
			</div>
			<div class="slot-button-article clearfix">
				<template v-for="(item, index) in domConfig.soltButtons">
					<el-button :key="index" 
                     :type="item.type" 
                     size="mini"
                     :icon="item.icon"
                     @click="operation_change({operation: item})"
                     plain>{{ item.label }}</el-button>
				</template>
			</div>
			<el-dialog :visible.sync="dialog" 
                 closeable 
                 show-close
                 :close-on-click-modal="false"
                 :width="dialogWidth"
                 :title="dialogTitle">
				<component :is="dialogComponent" 
                   :model="scopeInfo" 
                   :operation="dialogOperation" 
                   @close="dialog = !dialog" 
                   @up-date="init" 
                   v-if="dialog"></component>
			</el-dialog>

      <ms-right-dialog :visible.sync="r_dialog" :width="dialogWidth" :title="dialogTitle">
        <component
          :is="dialogComponent"
          :model="scopeInfo"
          :operation="dialogOperation"
          @close="r_dialog = !r_dialog"
          @up-date="init"
          v-if="r_dialog"
        ></component>
      </ms-right-dialog>
		</template>
	</ms-table>
</template>

<script>
import VideoSeriesMixin from "./video-series-mixin"
import tableMixins  from "../../../common/mixins/table"
import msVideoSeriesCategory from "./tool/ms-video-series-category"
export default {
  name: "ms-video-series",
  mixins: [tableMixins,VideoSeriesMixin],
  data () {
    return {
      //seriesSearchParams: { // => 列表查询传参
        // title: "",
        // createdBy: "",
        // createTime: [],
        // startBeginAt: "",
        // startStopAt: "", 
        // classify: "",
        // status: null
      //} 
    }
  },
  components: {
    msVideoSeriesCategory
  },
  methods: {
    apiInit (params) {
      let searchParams = {...params}
      this.$emit('getSearchParams', {
        searchParams: {...params},
        label: 'series'
      })
      if (searchParams.createTime) {
        searchParams.startBeginAt = searchParams.createTime[0] || ''
        searchParams.startStopAt = searchParams.createTime[1] || ''
      }
      searchParams.moduleType = 0
      this.api.getVideosPage(searchParams).then(response => {
        this.loading = false
        this.total = response.totalSize || 0;
        this.list = response.data || []
        if (response.status !== 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
      }).catch(() => this.loading = false)
    }
  }
};
</script>
