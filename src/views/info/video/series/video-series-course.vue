<template>
  <section>
    <el-row :gutter="20" class="course-pad">
      <el-col :span="7">
        <series-course-chapter ref="chapterRef" @getCourseInfo="getCourseInfo"></series-course-chapter>
      </el-col>
      <el-col :span="17">
        <series-course-video ref="videoRef"></series-course-video>
      </el-col>
    </el-row>
    <!-- 提交按钮 -->
    <footer-tool-bar v-loading="buttonLoading">
      <template slot="tool-content">
        <el-button type="primary" @click="info_operation('save')">保存排序</el-button>
        <el-button type="info" @click="info_operation('back')">返回</el-button>
      </template>
    </footer-tool-bar>
  </section>
</template>

<script>
import FooterToolBar from '@/components/ToolBar/footer-tool-bar'
import seriesCourseChapter from './components/series-course-chapter'
import seriesCourseVideo from './components/series-course-video'
export default {
  name: "video-series-operation",
	data () {
		return {
      videoId: this.$route.query.id ? this.$route.query.id : 0,
      buttonLoading: false
		}
  },
  components: {
    seriesCourseChapter,
    seriesCourseVideo,
    FooterToolBar
  },
  created() {
    if (!this.videoId) {
      this.$router.back()
    }
  },
  methods: {
    getCourseInfo(val) {
      this.$store.dispatch('SetChapterId', val.id)
      this.$refs.videoRef.init()
    },
    info_operation(val) {
      switch (val) {
        case 'save': 
          var newList = []
          var list = this.$refs.videoRef.courseData || []
          list.forEach( (v,k) => {
            newList.push({
              id: v.additionId,
              rank: k
            })
          });
          if (newList.length === 0) return this.PUBLIC_Methods.apiNotify('请添加课程', 'warning')
          var params = {
            seriesList: newList
          } 
          this.buttonLoading = true;
          this.api.updateVideoAdditionRank(params).then(response => {
            this.buttonLoading = false;
            if(response.status === 200) {
              this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
              this.$refs.videoRef.init()
            } else {
              this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
            }
          })
          break;
        case 'back':
          this.$router.back();
          break;
        default: break;
      }
    }
  }
}
</script>
<style scoped>
  .course-pad {
    padding-bottom: 40px;
  }
</style>
