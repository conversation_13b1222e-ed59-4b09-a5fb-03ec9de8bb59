<template>
  <div class="form-tab">
    <el-tabs v-model="activeName" @tab-click="tab_click">
      <el-tab-pane label="单节课程" name="single"></el-tab-pane>
      <el-tab-pane label="系列课程" name="series"></el-tab-pane>
      <el-tab-pane label="专题课程" name="special"></el-tab-pane>
      <el-tab-pane label="视频课程" name="media"></el-tab-pane>
      <!-- <el-tab-pane label="合集课程" name="failarmy"></el-tab-pane> -->
    </el-tabs>
    <component :is="videoComponent" @getSearchParams="getSearchParams"></component>
  </div>
</template>

<script>
import { mapGetters } from "vuex";

import MsVideoSingle from './single/ms-video-single'
import MsVideoSeries from './series/ms-video-series'
import MsVideoSpecial from './special/ms-video-special'
import MsVideoFailarmy from './failarmy/ms-video-failarmy'
import MsVideoMedia from './media/ms-video-media'
export default {
  name: "ms-video",
  data () {
    return {
      // activeName: 'media',
      // videoComponent: 'MsVideoMedia',
      activeName: 'media',
      videoComponent: 'MsVideoSingle',
      singleSearchParams: {},
      seriesSearchParams: {},
      specialSearchParams: {},
      failarmySearchParams: {},
      mediaSearchParams: {}
    }
  },
  components: {
    MsVideoSingle,
    MsVideoSeries,
    MsVideoFailarmy,
    MsVideoSpecial,
    MsVideoMedia
  },
  computed: {
    ...mapGetters(["videoActive", "listSearchParams"])
  },
  created() {
    // this.activeName = this.videoActive ? this.videoActive : 'media'
    this.activeName = this.videoActive ? this.videoActive : 'media'
    if (this.listSearchParams) {
      this[`${this.activeName}SearchParams`] = {...this.listSearchParams}
    }
    this.tab_click()
  },
  methods: {
    tab_click() {
      if (this.activeName === 'single') {
        if (this.singleSearchParams) {
          this.$store.dispatch('SetSearchParams', this.singleSearchParams)
        }
        this.videoComponent = 'MsVideoSingle'
      } else if (this.activeName === 'series') {
        if (this.seriesSearchParams) {
          this.$store.dispatch('SetSearchParams', this.seriesSearchParams)
        }
        this.videoComponent = 'MsVideoSeries'
      } else if (this.activeName === 'special') {
        if (this.specialSearchParams) {
          this.$store.dispatch('SetSearchParams', this.specialSearchParams)
        }
        this.videoComponent = 'MsVideoSpecial'
      } else if (this.activeName === 'failarmy') {
        if (this.singleSearchParams) {
          this.$store.dispatch('SetSearchParams', this.failarmySearchParams)
        }
        this.videoComponent = 'MsVideoFailarmy'
      } else if (this.activeName === 'media') {
        if (this.mediaSearchParams) {
          this.$store.dispatch('SetSearchParams', this.mediaSearchParams)
        }
        this.videoComponent = 'MsVideoMedia'
      }
      this.$store.dispatch('SetVideoActive', this.activeName)
    },
    getSearchParams(params) {
      if(params.label === 'single') {
        this.singleSearchParams = {...params.searchParams}
      } else if (params.label === 'series') {
        this.seriesSearchParams = {...params.searchParams}
      } else if (params.label === 'special') {
        this.specialSearchParams = {...params.searchParams}
      } else if (params.label === 'failarmy') {
        this.failarmySearchParams = {...params.searchParams}
      }
    }
  }
}
</script>
