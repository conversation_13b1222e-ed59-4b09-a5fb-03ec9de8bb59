<template>
  <ms-operation-dialog :title="title">
    <template slot="content">
      <el-tag v-for="(item, index) in tagArr" 
              :key="index" 
              type="info" 
              style="margin: 0 5px 5px 0">{{item}}</el-tag>
    </template>
    <template slot="footer">
      <el-button @click="submitForm"
                 :loading="loading"
                 size="mini"
                 type="primary">确 定</el-button>
      <el-button @click="$emit('close')"
                 size="mini">取 消</el-button>
    </template>
  </ms-operation-dialog>
</template>

<script>
export default {
	name: "ms-video-media-operation",
	data () {
		return {
      userInfo: {},
      dealType: null,
      tagArr: [],
      ids: [],
      title: '',
      loading: false
		}
	},
	props: [
		"model",
		"operation"
  ],
  created() {
    this.userInfo = this.$store.getters.info || {}
    this.init()
  },
	methods: {
    init() {
      let arr = []
      let ids = []
      let way = this.operation || this.$route.query.operation
      this.dealType = way === 'approval' ? 1 : way === 'toreview' ? 2 : way === 'delete' ? 3 : null
      this.title = `是否${this.dealType === 1 ? '审核' : this.dealType === 2 ? '去审' : this.dealType === 3 ? '删除' : ''}产品信息`
      if (this.PUBLIC_Methods.isArrayFn(this.model)) {
        this.model.forEach(item => {
          arr.push(item.title)
          ids.push(item.id)
        });
      } else {
        arr.push(this.model.title)
        ids.push(this.model.id)
      }
      this.tagArr = arr
      this.ids = ids
    },
		submitForm () {
      if(this.dealType === 3) {
        this.$confirm('此操作将永久删除，是否继续', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.apiOperationDelete()
        })
      } else {
        this.apiOperationAudit()
      }
      
    },
    apiOperationDelete() {
      this.loading = true;
      let params = {
        userId: this.userInfo.userId,
        username: this.userInfo.userName,
        ids: this.ids,
        type: 3
      }
      this.api.videoMedsciCourseOperateCourse(params).then(response => {
        if(response.status === 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
        this.$emit('up-date')
        this.loading = false
      }).catch( () => {
        this.loading = false;
        this.$emit('close')
      })
    },
    apiOperationAudit() {
      let params = {
        userId: this.userInfo.userId,
        username: this.userInfo.userName,
        type: this.dealType,
        ids: this.ids
      }
      this.loading = true;
      this.api.videoMedsciCourseOperateCourse(params).then(response => {
        if(response.status === 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
        this.$emit('up-date')
        this.loading = false
      }).catch( () => {
        this.loading = false;
        this.$emit('close')
      })
    }
	}
}
</script>
