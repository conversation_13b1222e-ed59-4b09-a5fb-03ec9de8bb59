<template>
  <section>
    <el-form :model="submitData"
             ref="submitRef"
             class="rule-form info-form"
             :rules="rules"
             label-width="70px">
      <el-row :gutter="20">
        <el-col :span="18">
          <el-row>
            <el-col :span="24">
              <el-form-item label="标题" prop="title">
                <el-input v-model="submitData.title" style="width: 100%"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="详情介绍" prop="content">
                <ms-editor v-model="submitData.content"></ms-editor>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="摘要" prop="summary">
                <el-input v-model="submitData.summary" type="textarea" :rows="3" @keyup.native="summaryKeyUp = true" maxlength="20" show-word-limit></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <!-- <div class="editor-info" v-show="editorDisplay">
            <div class="info-li">
              <ms-picker :model.sync="submitData.publishedTime" type="datetime" width="180px" label="发布时间" labelWidth="60px"></ms-picker>
            </div>
            <div class="info-li">
              <span class="label">最后更新人：</span><span>{{submitData.editor}}</span>
            </div>
            <div class="info-li">
              <span class="label">最后更新时间：</span><span>{{submitData.editorTime | parseTime}}</span>
            </div>
            <div class="info-li">
              <span class="label">视频创建时间：</span><span>{{submitData.createdTime | parseTime}}</span>
            </div>
          </div> -->
        </el-col>
        <!-- <el-col :span="6" class="info-form-right">
          <el-row>
            <el-col :span="24">
              <el-form-item label="显示选项">
                <el-row type="flex" >
                  <el-checkbox v-model="submitData.recommend" style="width: 45%" :true-label="1" :false-label="0">推荐</el-checkbox>
                  <ms-picker :model.sync="submitData.recommendEndTime" type="datetime"></ms-picker>
                </el-row>
                <el-row type="flex" style="margin-top: 5px;" >
                  <el-checkbox v-model="submitData.sticky" style="width: 45%" :true-label="1" :false-label="0">固顶</el-checkbox>
                  <ms-picker :model.sync="submitData.stickyEndTime" type="datetime"></ms-picker>
                </el-row>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="权限" prop="opened">
                <el-select v-model="submitData.opened" style="width: 100%">
                  <el-option :value="1" label="开放阅读"></el-option>
                  <el-option :value="0" label="登录阅读"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="24" v-show="+submitData.opened === 0">
              <el-form-item label="支付方式">
                <el-checkbox v-model="submitData.paymentCash" :true-label="1" :false-label="0">现价</el-checkbox>
                <el-checkbox v-model="submitData.paymentIntegralBase" :true-label="1" :false-label="0">积分</el-checkbox>
                <el-checkbox v-model="submitData.paymentIntegralUme" :true-label="1" :false-label="0">梅花</el-checkbox>
              </el-form-item>
            </el-col>
            <el-col :span="24" v-show="+submitData.opened === 0 && +submitData.paymentCash === 1">
              <el-form-item label="原价">
                <el-input-number v-model="submitData.costPrice" class="w100" :min="0" controls-position="right"></el-input-number>
              </el-form-item>
              <el-form-item label="现价">
                <el-input-number v-model="submitData.presentPrice" class="w100" :min="0" controls-position="right"></el-input-number>
              </el-form-item>
            </el-col>
            <el-col :span="24" v-show="+submitData.opened === 0 && +submitData.paymentIntegralBase === 1">
              <el-form-item label="积分">
                <el-input-number v-model="submitData.integralBase" class="w100" :min="0" controls-position="right"></el-input-number>
              </el-form-item>
            </el-col>
            <el-col :span="24" v-show="+submitData.opened === 0 && +submitData.paymentIntegralUme === 1">
              <el-form-item label="梅花">
                <el-input-number v-model="submitData.integralUme" class="w100" :min="0" controls-position="right"></el-input-number>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="关键词" prop="tagList">
                <MsTagSearch v-model="submitData.tagList" :notMul="false"></MsTagSearch>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="封面图片" prop="cover">
                <ms-single-image v-model="submitData.cover"></ms-single-image>
              </el-form-item>
            </el-col>
            <el-col :span="24" style="text-align: left">
              <el-form-item label="自动水印" prop="waterMark" style="display: inline-block;">
                  <el-switch v-model="submitData.waterMark" :disabled="formatDisabled" :active-value="1" :inactive-value="0"></el-switch>
              </el-form-item>
            </el-col>
          </el-row>
        </el-col> -->
      </el-row>
    </el-form>
  </section>
</template>

<script>
import MsEditor from '@/components/MsEditor'
// import MsTagSearch from '@/components/MsCommon/ms-tag-search'
import { getEditContent } from '@/utils/index'
import { mapGetters } from "vuex";
export default {
	name: "ms-video-failarmy-content",
	data () {
		return {
      testLoading: false,
      rules: {
        title: [
          { required: true, message: "请填写文章标题", trigger: 'blur' }
        ],
        content: [
          { required: true, message: "请填写文章内容", trigger: 'change' }
        ]
      },
      formatDisabled: false,
      editorDisplay: false,
      summaryKeyUp: false  
		}
  },
  components: {
    MsEditor,
    // MsTagSearch
  },
  computed: {
    ...mapGetters(["submitData", "info", "localCode"])
  },
  watch: {
    'submitData.id': function (val) {
      if (val) {
        this.formatDisabled = true
        this.editorDisplay = true
      } 
    },
    'submitData.content': function (val) {
      if (val) {
        if (!this.submitData.id && !this.summaryKeyUp) {
          this.submitData.summary = this.PUBLIC_Methods.unexcapeHtml(getEditContent(val))
        }
      }
    }
  },
  created() {
    // this.submitData.editor = this.info.userName
    console.log(this.submitData, 'this.submitData');
  },
	methods: {
    getNodeData(val) {
      this.submitData.videoId = val.id
      this.submitData.title = val.title
      this.submitData.lecturerName = val.lecturerName
    },
    // 数据校验
    validateData() {
      let params = {
        ...this.submitData
      }
      return new Promise((resolve) => {
        this.$refs["submitRef"].validate((valid) => {
          if(valid) {
            this.$store.dispatch('SetSubmitData', params);
            resolve()
          }
        })
      })
    }
	}
}
</script>

<style scope lang="scss">
.info-form{
  /deep/ .el-checkbox {
    margin-right: 0px;
  }
  &-right {
    /deep/ .el-form-item {
      margin-bottom: 16px;
    }
  }
  .expand {
    .label {
      color: #829FFF; 
      font-size: 12px;
      width: 40px;
      display: inline-block;
      text-align: right;
    }
    /deep/ .el-form-item {
      padding: 4px 0;
      margin-bottom: 14px;
      margin-top: -4px;
      background-color: #E5E6E9;
      border-radius: 4px;
    }
  }
}
</style>
