<template>
  <ms-operation-dialog >
    <template slot="content">
		<p class="content-top">
			<span class="top-label">视频名称：</span><span class="top-content">{{model.title}}</span>
		</p>
		<p class="content-top">
			<span class="top-label">视频时长：</span><span class="top-content">{{model.duration}}</span>
		</p>
        <video controls="controls" width="80%" >
            <source :src="model.videoKey" />
        </video>
    </template>
  </ms-operation-dialog>
</template>

<script>
export default {
	name: "ms-video-preview",
	data () {
		return {}
	},
	props: {
		model: Object
  },
  mounted() {
    var hm = document.createElement("script");
    hm.src = "https://hm.baidu.com/hm.js?410ec554592b4d7b85b3e6cdc413fa52";
    var s = document.getElementsByTagName("script")[0];
    s.parentNode.insertBefore(hm, s);
    // eslint-disable-next-line
    if (Hls.isSupported()) {
        let video = document.getElementsByTagName('video'); // 获取 video 标签
        for (let i = 0; i < video.length; i++) {
            let sources = document.getElementsByTagName('source')[i].src;
            if(sources.includes('.m3u8')) {
              // eslint-disable-next-line
                let hls = new Hls(); // 实例化 Hls 对象
                hls.loadSource(sources); // 传入路径
                hls.attachMedia(video[i]);
            }
        }
    }
  }
}
</script>

<style>
	.content-top {
		margin-bottom: 10px;
		font-size: 14px;
		text-align: left;
	}
	.content-top .top-label {
		font-weight: bold;
		margin-right: 12px;
		display: inline-block;
	}
</style>
