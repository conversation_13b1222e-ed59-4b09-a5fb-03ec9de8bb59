<template>
  <section>
    <main>
    <el-form :model="submitData"
      ref="submitRef"
      class="rule-form info-form"
      :rules="rules"
      label-width="90px">
        <el-row>
          <el-col :span="14">
            <el-form-item label="关键词" prop="tags">
              <MsTagSearch v-model="submitData.tags" :notMul="false"></MsTagSearch>
            </el-form-item>
          </el-col>
          <el-col :span="14">
            <el-form-item label="显示选项">
              <el-row type="flex" >
                <el-checkbox v-model="submitData.isPc" style="width: 60px" :true-label="1" :false-label="0">官网首页</el-checkbox>
                <el-checkbox v-model="submitData.isApp" style="width: 60px" :true-label="1" :false-label="0">APP</el-checkbox>
              </el-row>
            </el-form-item>
          </el-col>
          <el-col :span="14">
            <el-form-item label="运营选项">
              <el-row type="flex" >
                <el-checkbox v-model="submitData.recommend" style="width: 80px" :true-label="1" :false-label="0">推荐</el-checkbox>
                <el-date-picker style="width: 100%;" @change="changeTime('recommend')"
                  v-model="submitData.recommendTime"
                  format="yyyy-MM-dd HH:mm:ss"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期">
                </el-date-picker>
              </el-row>
              <el-row type="flex" style="margin-top: 5px;" >
                <el-checkbox v-model="submitData.isUp" style="width: 80px" :true-label="1" :false-label="0">固顶</el-checkbox>
                <el-date-picker style="width: 100%;" @change="changeTime('up')"
                  v-model="submitData.upTime"
                  format="yyyy-MM-dd HH:mm:ss"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期">
                </el-date-picker>
              </el-row>
            </el-form-item>
          </el-col>
          <el-col :span="14">
            <el-form-item label="定时上线" prop="publishTime">
              <el-date-picker
                  @change="change"
                  v-model="submitData.publishTime"
                  type="datetime"
                  value-format="yyyy-MM-dd HH:mm"
                  :picker-options="pickerOptions"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  style="width:100%"
                >
                </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="14">
            <el-form-item label="权限" prop="permission">
              <el-select v-model="submitData.permission" style="width: 100%">
                <el-option :value="1" label="开放阅读"></el-option>
                <el-option :value="2" label="登录阅读"></el-option>
                <el-option :value="3" label="付费阅读"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="14" style="text-align: left" v-if="+submitData.permission === 3">
            <el-col :span="8">
              <el-form-item label="付费会员专属" prop="isPrivilege" style="display: inline-block;">
                <el-switch @change="isPrivilege" v-model="submitData.isPrivilege" :disabled="formatDisabled" :active-value="1" :inactive-value="0"></el-switch>
              </el-form-item>
            </el-col>
            <el-col :span="16" v-if="submitData.isPrivilege == 1">
              <el-form-item label=" " label-width="20px" :prop="submitData.isPrivilege == 1 ? 'memberCardIds' : ''" style="display: inline-block; width: 100%">
                <el-select v-model="submitData.memberCardIds" clearable placeholder="请选择会员" multiple style="width: 100%">
                  <el-option  v-for="(item,index) in memberList" :key="index" :label="item.cardName" :value="item.id"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-col>
          <el-col :span="24" v-if="+submitData.permission === 3">
            <el-form-item label="支付方式" :prop="submitData.isPrivilege == 1 ? '' : 'payType'">
              <!-- <el-checkbox v-model="submitData.payType" :true-label="1" :false-label="0" style="width: 80px" @change="changePayType">现金</el-checkbox> -->
              <el-radio-group v-model="submitData.payType">
                <el-radio :label="1">现金</el-radio>
                <!-- <el-radio :label="2" :disabled="submitData.isPrivilege == 1">积分</el-radio> -->
                <el-radio :label="2">积分</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-col :span="12" :offset="2" v-if="submitData.payType == 1 || submitData.payType == 2">
              <el-form-item :label="submitData.payType  == 1 ? '价格': '积分'" prop="price">
                <el-input
                  v-model.trim="submitData.price"
                  @keyup.native="proving('price')"
                  type="number"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </el-form-item>
              <el-form-item label="活动价">
                <el-input
                  v-model.trim="submitData.activityPrice"
                  @change="changeActivityPrice"
                  @keyup.native="proving('activityPrice')"
                  type="number"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </el-form-item>
              <el-form-item label="活动时间" :prop="submitData.activityPrice !== '' ? 'activityTime' : ''">
                <el-date-picker style="width: 100%;" @change="changeTime('activity')"
                  v-model="submitData.activityTime"
                  format="yyyy-MM-dd HH:mm:ss"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期">
                </el-date-picker>
              </el-form-item>
            </el-col>
          </el-col>
          <el-col :span="14">
            <el-form-item :label="submitData.permission  == 3 ? '基础人数': '基础次数'">
              <el-input
                v-model.trim="submitData.customBaseNum"
                @keyup.native="provingNumber('customBaseNum')"
                type="number"
                placeholder="请输入"
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item :label="submitData.permission  == 3 ? '人数倍数': '次数倍数'">
              <el-input
                v-model.trim="submitData.customMultiple"
                @keyup.native="proving('customMultiple')"
                type="number"
                placeholder="请输入"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="课程封面" clearable prop="cover">
              <ms-single-image v-model="submitData.cover" :upFileSize="0.5"></ms-single-image>
              <p class="hint">支持扩展名：jpg,jpeg,png</p>
              <p class="hint">数量：1张</p>
              <p class="hint">尺寸：904*508</p>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </main>
  </section>
</template>

<script>
import { mapGetters } from "vuex";
import MsTagSearch from '@/components/MsCommon/ms-tag-search'
import { getSession } from "@/utils/auth";
import moment from 'moment'
export default {
	name: "ms-video-media-set",
	data () {
    var validatePass = (rule, value, callback) => {
      if (this.submitData.activityPrice != '' && this.submitData.activityPrice != null && (!this.submitData.activityTime)) {
        callback(new Error('请选择'));
      } else {
        callback();
      }
    };
    var validateType = (rule, value, callback) => {
      if (this.submitData.permission == 3 && this.submitData.isPrivilege == 0 && !this.submitData.payType) {
        callback(new Error('请选择'));
      } else {
        callback();
      }
    };
    var validateMember = (rule, value, callback) => {
      if (this.submitData.permission == 3 && this.submitData.isPrivilege == 1 && this.submitData.memberCardIds.length < 1) {
        callback(new Error('请选择'));
      } else if (this.submitData.memberCardIds.length && this.compareArray(this.submitData.memberCardIds,this.zhinanList)) {
        callback(new Error('指南付费会员专属，必须勾选全部指南会员，否则会导致部分会员用户下载不成功！'));
      } else {
        callback();
      }
    };
		return {
      formatDisabled: false,
      rules: {
        cover: [
          { required: true, message: "请选择", trigger: 'change' }
        ],
        payType: [
          { required: true, validator: validateType, trigger: 'change' }
        ],
        activityTime: [
          { required: true, validator: validatePass, trigger: 'change' }
        ],
        price: [
          { required: true, message: "请选择", trigger: 'change' }
        ],
        memberCardIds: [
          { required: true, validator: validateMember, trigger: 'change' }
        ],
      },
      memberList: [],
      zhinanList: [],
      pickerOptions: {
        disabledDate: time => {
          let delay = new Date().getTime() - 86400000
          if(delay){
            // 小于接口返回的值得日期全部禁选
            return time.getTime() < delay;
          }
        }
      }
		}
  },
  components: {
    MsTagSearch
  },
  computed: {
    ...mapGetters(["submitData", "info", "localCode"])
  },
  watch: {
    'submitData.permission': function(val) {
      if(!this.submitData.payType && val == 3 && this.submitData.isPrivilege == 0) {
        this.$set(this.submitData, 'payType', 1)
      }
      if(val == 1 || val == 2) {
        delete this.submitData.payType
        this.$set(this.submitData, 'isPrivilege', 0)
        this.changePayType(0)
      }
      
    },
    // 'submitData.isPrivilege': function(val) {
    //   console.log(this.submitData.payType);
      
    //   if(val == 1 && (this.submitData.payType == 0)) {
    //     console.log(9999999);
    //     delete this.submitData.payType
    //     this.changePayType(0)
    //   }
    //   if(val != 1 ){
    //     console.log(88888888);
    //     this.$set(this.submitData, 'payType', 1)
    //   }
    // }
  },
  created() {
    this.initMemberList()
  },
	methods: {
    isPrivilege(value){
      if(value == 1 ) {
        delete this.submitData.payType
        this.changePayType(0)
      }
      if(value != 1 ){
        this.$set(this.submitData, 'payType', 1)
      }
    },
    initMemberList() {
      let searchParams = {
        pageIndex: 1,
        pageSize: 99,
        status: 1,
        propertyName: '课程会员'
      }
      this.api.memberCardQuery(searchParams).then(response => {
        this.memberList = response.data || []
        if(this.memberList.length) {
          this.memberList.forEach(el => {
            if(el.cardName.includes('指南会员')) {
              this.zhinanList.push(el.id)
            }
          })
        }
        if (response.status !== 200) {
          this.PUBLIC_Methods.apiNotify(
            response.message || "请求出错",
            "warning"
          )
        }
      })
      .catch((err) => (console.log(err)))
    },
    // b有元素包含在a中，b也有元素不包含在a中，返回ture，反之false
    compareArray(a,b) {
      let result = false;
      if(b.length > 1) {
        let oldM = '';
        b.forEach((m) => {
          if(oldM && a.includes(oldM) != a.includes(m)) {
            result = true
          }
          oldM = m;
        })
      }
      return result
    },
    change(val){
      if(!val){
        this.submitData.publishTime = val
      } else {
        let time = moment(val).format("YYYY-MM-DD HH:mm") + ':59'
        if(new Date(time).getTime()<new Date().getTime()){
          this.$message({
            message: '不能选择当前时间之前的时间',
            type: 'warning'
          })
          this.submitData.publishTime = getSession('video-publishTime')
        }else {
          this.submitData.publishTime = moment(val).format("YYYY-MM-DD HH:mm") + ':00'
        }
      }
    },
    // 推荐，固顶，活动时间变化
    changeTime(type) {
      switch(true) {
        case type === 'recommend':
          this.submitData.recommendStartTime = this.submitData.recommendTime ? moment(this.submitData.recommendTime[0]).format("YYYY-MM-DD HH:mm:ss") : '';
          this.submitData.recommendEndTime = this.submitData.recommendTime ? moment(this.submitData.recommendTime[1]).format("YYYY-MM-DD HH:mm:ss") : '';
          break;
        case type === 'up':
          this.submitData.upStartTime = this.submitData.upTime ? moment(this.submitData.upTime[0]).format("YYYY-MM-DD HH:mm:ss") : '';
          this.submitData.upEndTime = this.submitData.upTime ? moment(this.submitData.upTime[1]).format("YYYY-MM-DD HH:mm:ss") : '';
          break;
        case type === 'activity':
          this.submitData.activityStartTime = this.submitData.activityTime ? moment(this.submitData.activityTime[0]).format("YYYY-MM-DD HH:mm:ss") : '';
          this.submitData.activityEndTime = this.submitData.activityTime ? moment(this.submitData.activityTime[1]).format("YYYY-MM-DD HH:mm:ss") : '';
          break;
      }
    },
    // 活动价格变化
    changeActivityPrice(val) {
      if(val === '') {
        this.submitData.activityTime = '';
        this.submitData.activityStartTime = '';
        this.submitData.activityEndTime = '';
      }
    },
    // 支付方式变化
    changePayType(val) {
      if(val === 0) {
        this.submitData.price = '';
        this.submitData.activityPrice = '';
        this.submitData.activityTime = '';
        this.submitData.activityStartTime = '';
        this.submitData.activityEndTime = '';
      }
    },
    // 数据校验
    validateData() {
      let params = {
        ...this.submitData,
        userId: this.info.userId,
        username: this.info.userName
      }
      return new Promise((resolve) => {
        this.$refs["submitRef"].validate((valid) => {
          if(valid) {
            this.$store.dispatch('SetSubmitData', params);
            resolve()
          }
        })
      })
    },
    // 限制小数并保留2位
    proving(e) {
      // 先把非数字的都替换掉，除了数字和
      this.submitData[e] = this.submitData[e].replace(/[^\d.]/g, '');
      // 必须保证第一个为数字而不是.
      this.submitData[e] = this.submitData[e].replace(/^\./g, '');
      // 保证只有出现一个.而没有多个.
      this.submitData[e] = this.submitData[e].replace(/\.{2,}/g, '');
      // 保证.只出现一次，而不能出现两次以上
      this.submitData[e] = this.submitData[e].replace('.', '$#$').replace(/\./g, '').replace('$#$', '.');
      let index = -1
      for (let i in this.submitData[e]) {
          if (this.submitData[e][i] === '.') {
              index = i
          }
          if (index !== -1) {
              if (i - index > 2) {
                  this.submitData[e] = this.submitData[e].substring(0, this.submitData[e].length - 1)
              }
          }
      }
    },
    provingNumber(e) {
       // 先把非数字的都替换掉，除了数字和
      this.submitData[e] = this.submitData[e].replace(/[^\d.]/g, '');
      // 必须保证第一个为数字而不是.
      this.submitData[e] = this.submitData[e].replace(/^\./g, '');
      // 保证只有出现一个.而没有多个.
      this.submitData[e] = this.submitData[e].replace(/\.{2,}/g, '');
      // 保证.只出现一次，而不能出现两次以上
      this.submitData[e] = this.submitData[e].replace('.', '$#$').replace(/\./g, '').replace('$#$', '.');
      let index = -1
      for (let i in this.submitData[e]) {
          if (this.submitData[e][i] === '.') {
              index = i
          }
          if (index !== -1) {
              if (i - index > 0) {
                  this.submitData[e] = this.submitData[e].substring(0, this.submitData[e].length - 1)
              }
          }
      }
      this.submitData[e] = Number(this.submitData[e])
    }
	}
}
</script>

<style scope lang="scss">
main{
  margin-bottom: 20px;
}
.hint{
  color: rgba(0,0,0,0.4);
}
</style>
