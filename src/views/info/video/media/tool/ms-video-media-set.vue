<template>
  <section>
    <main>
      <el-collapse v-model="activeNames">
        <draggable v-model="dataList" group="章节" animation="300">
          <transition-group>
            <el-collapse-item :name="i" v-for="(item, i) in dataList" :key="'p' + item.title">
              <template slot="title">
                <div class="period-name">
                  <span>第{{i+1}}章：</span>
                  <div @click="stopPgn" class="period-name-right">
                    <span class="period-name-value">{{item.title}}</span>
                    <el-button class="btn" type="primary" @click="addPeriodOrVideo('视频', i)" icon="el-icon-plus" plain>视频</el-button>
                    <el-button class="btn" type="primary" @click="editPeriodOrVideo(item, i)" plain>编辑</el-button>
                    <el-button class="btn" type="danger" @click="delPeriodOrVideo('章节', item.id, i)" plain>删除</el-button>
                  </div>
                </div>
              </template>
              <draggable v-model="item.singleInfoReqList" group="视频" animation="300">
                <transition-group>
                    <div v-for="(element, j) in item.singleInfoReqList" :key="'v' + element.id" class="video-wrap">
                      <p class="video-wrap-left" @click="previewPeriodOrVideo(element, i, j)">
                        <i class="el-icon-video-play icon-play" plain></i>
                        <span class="video-name">第{{j+1}}节：{{element.title}}</span>
                        <span class="video-time">{{element.duration}}</span>
                        <span class="video-status">{{element.isTrial == 1 ? '试看' : ''}}</span>
                      </p>
                      <el-button class="btn" type="primary" @click="editPeriodOrVideo(element, i, j)" plain>编辑</el-button>
                      <el-button class="btn" type="danger" @click="delPeriodOrVideo('视频', item.id, i, element.id, j)" plain>删除</el-button>
                    </div>
                </transition-group>
              </draggable>
            </el-collapse-item>
          </transition-group>
        </draggable>
      </el-collapse>
    </main>
    <el-button
      type="primary"
      icon="el-icon-plus"
      @click="addPeriodOrVideo('章节')"
      plain
      >章节</el-button>
    <div class="buttons">
      <el-button type="primary" @click="submit">保存</el-button>
      <el-button type="info" @click="close">返回</el-button>
    </div>
    <el-dialog
      v-if="dialogVisible"
      :title="openDialog.type + openDialog.label"
      append-to-body
      :visible.sync="dialogVisible"
      width="40%">
      <el-form :label-position="labelPosition" label-width="90px" :model="form">
        <template v-if="openDialog.label === '章节'">
          <el-form-item label="章节名称：">
            <el-input v-model="form.title"></el-input>
          </el-form-item>
        </template>
        <template v-if="openDialog.label === '视频'">
          <el-form-item label="选择视频：">
              <ms-video-search :model.sync="submitData.videoTitle" @getNodeData="getNodeData"></ms-video-search>
          </el-form-item>
          <el-form-item label="视频名称：">
            <el-input v-model="form.title"></el-input>
          </el-form-item>
          <el-form-item label="视频时长：">
            <el-input v-model="form.duration" readonly></el-input>
          </el-form-item>
          <el-form-item label="允许试看：">
            <el-switch v-model="form.isTrial" :disabled="formatDisabled" :active-value="1" :inactive-value="0">
            </el-switch>
          </el-form-item>
        </template>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <!-- <el-button @click="dialogVisible = false">取 消</el-button> -->
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog 
          v-if="dialog"
          :visible.sync="dialog"
          closeable 
          show-close
          append-to-body
          :close-on-click-modal="false"
          width="60%"
          title="视频课程预览">
          <component :is="msVideoPreview" 
                    :model="perviewInfo" 
                    @close="dialog = !dialog" 
          ></component>
    </el-dialog>
  </section>
</template>

<script>
import { mapGetters } from "vuex";
import draggable from 'vuedraggable';
import MsVideoSearch from '@/components/MsCommon/ms-video-search'
import msVideoPreview from './ms-video-preview'
export default {
	name: "ms-video-media-set",
	data () {
		return {
      formatDisabled: false,
      activeNames: [0],
      dataList:[],
      dialogVisible: false,
      openDialog: {},
      labelPosition: 'right',
      form: {},
      dialog: false,
      perviewInfo:{},
      msVideoPreview
		}
  },
  computed: {
    ...mapGetters(["submitData", "info"])
  },
	props: [
		"model",
		"operation"
  ],
  components: {
    draggable,
    MsVideoSearch
  },
  watch: {
  },
  created() {
    this.init()
  },
	methods: {
    init() {
      let params = {
        id: this.model.id
      };
      this.api.videoMedsciCourseTreeList(params).then( response => {
        if(response.status === 200) {
          this.dataList = response.data;
          this.dataList.forEach(item=>{
            if(item.singleInfoReqList&&item.singleInfoReqList.length) {
              item.singleInfoReqList.forEach(row=>{
                delete row.description
              })
            }
          })
        } else {
          this.dataList = [];
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
      }).catch(() => {
      })
    },
    // 打开添加章节或视频弹窗
    addPeriodOrVideo(label, index) {
      this.dialogVisible = true;
      this.openDialog = {
        label: label,
        type: '添加',
        periodIndex: index != undefined ? index : ''
      }
      if(label === '章节') {
        this.form = {
          title: ''
        };
      } else {
        this.form = {
          title: '',
          duration: '',
          isTrial: ''
        };
      }
    },
    // 删除章节视频
    delPeriodOrVideo(type, i, periodIndex, j, videoIndex) {
      // 删除章节
      if(type == '章节') {
        if(i) {
          this.deleteId(i)
        } else {
          this.dataList.splice(periodIndex, 1);
        }
      }
      // 删除视频
      if(type == '视频') {
        if(j) {
          this.deleteId(j)
        } else {
          this.dataList[periodIndex].singleInfoReqList.splice(videoIndex, 1);
        }
      }
    },
    deleteId(id) {
      let params = {
        userId: this.info.userId,
        userName: this.info.userName,
        ids: [id]
      }
      this.api.videoMedsciCourseDeletedCourse(params).then(response => {
        if (response.status === 200) {
          this.init();
          this.PUBLIC_Methods.apiNotify(
            response.message || "删除成功",
            "success"
          )
        } else {
          this.PUBLIC_Methods.apiNotify(
            response.message || "添加失败",
            "warning"
          )
        }
      })
    },
    // 打开编辑章节或视频弹窗
    editPeriodOrVideo(item, period, video) {
      this.openDialog = {
        label: item.singleInfoReqList ? '章节' : '视频',
        periodIndex: period,
        videoIndex: video,
        type: '修改'
      }
      if(item.singleInfoReqList) {
        this.form = {
          title: item.title
        };
      } else {
        this.form = {
          title: item.title,
          duration: item.duration,
          isTrial: item.isTrial
        };
      }
      this.dialogVisible = true;
    },
    // 预览视频
    previewPeriodOrVideo(item) {
      this.perviewInfo = {
        title: item.title,
        duration: item.duration,
        videoKey:item.videoKey
      }
      this.dialog = true;
    },
    // 暂时保存章节视频
    submitForm() {
      let period = {};
      let currentPeriodIndex = this.openDialog.periodIndex;
      let video = {};
      let currentVideoIndex = this.openDialog.videoIndex;
      if(this.openDialog.label === '章节') {
        if(this.openDialog.type === '添加') {
          period = {
            id: '',
            title: this.form.title,
            singleInfoReqList: []
          }
          this.dataList.push(period);
        }
        if(this.openDialog.type === '修改') {
          currentPeriodIndex = this.openDialog.periodIndex;
          this.$set(this.dataList[currentPeriodIndex], 'title', this.form.title);
        }
      }
      if(this.openDialog.label === '视频') {
        if(this.openDialog.type === '添加') {
          video = this.form;
          this.dataList[currentPeriodIndex]['singleInfoReqList'].push(video);
          // 如果添加的是视频，则判断该视频所在章节是否打开，未打开则打开展示
          if(!this.activeNames.includes(currentPeriodIndex)) {
            this.activeNames.push(currentPeriodIndex);
          }
        }
        if(this.openDialog.type === '修改') {
          video = Object.assign({}, this.dataList[currentPeriodIndex].singleInfoReqList[currentVideoIndex], {
            duration: this.form.duration,
            isTrial: this.form.isTrial,
            title: this.form.title,
            materialId: this.form.materialId,
            videoKey: this.form.videoKey,
            speakerId: this.form.speakerId,
          });
          this.dataList[currentPeriodIndex].singleInfoReqList.splice(currentVideoIndex, 1, video);
        }
      }
      this.dialogVisible = false;
    },
    // 获取选择视频的参数
    getNodeData(val) {
      this.form.cover = val.cover;
      this.form.title = val.title;
      this.form.speakerId = val.lecturerId;
      this.form.videoKey = val.videoKey;
      this.form.speakerName = val.lecturerName;
      this.form.materialId = val.id;
      this.form.duration = val.duration;
    },
    // 上传视频设置
    videoDataDeal (data) {
        this.form.videoKey = data.key
        if (!data.key) {return false}
        // let videoUrl = 'https://' + this.PUBLIC_Methods.dealCosUrl(data.key).m3u8Url
        let videoUrl = 'https://ali-video.medsci.cn/' + this.PUBLIC_Methods.dealCosUrl(data.key).m3u8Url
        // let videoUrl = `https://${data.key}`
        // let videoUrl = `https://ali-video.medsci.cn/${data.key}`
        if (!this.form.title) {
            this.form.title = data.name
        }
        if (!this.form.cover) {
            // this.form.cover = `${videoUrl}?x-oss-process=video/snapshot,t_7000,f_jpg,w_0,h_0,m_fast`
            // this.form.cover = 'https://' + this.PUBLIC_Methods.dealCosUrl(data.key).snapshotUrl
            this.form.cover = 'https://ali-video.medsci.cn/' + this.PUBLIC_Methods.dealCosUrl(data.key).snapshotUrl
        }
        // 获取视频时长
        let videoEle = document.createElement('video');
        videoEle.setAttribute('src', videoUrl);
        videoEle.onloadedmetadata = ( () => {
            if (videoEle.duration) {
                let v_hour = Math.floor(videoEle.duration/(60*60)%24);
                let v_minute = Math.floor(videoEle.duration/60%60);
                let v_second = Math.floor(videoEle.duration%60);
                var vH = v_hour >= 10 ?  v_hour : '0'+ v_hour;
                var vM = v_minute >= 10 ?  v_minute : '0'+ v_minute;
                var vS = v_second >= 10 ?  v_second : '0'+ v_second;
                this.form.duration = `${vH}:${vM}:${vS}`
                videoEle = null;
            }
            
        })
    },
    // 阻止事件冒泡
    stopPgn(e) {
      e.stopPropagation();
    },
    // 判断章节下没有视频，不允许保存
    judgeVideo(arr) {
      return arr.find(el => el.singleInfoReqList.length == 0)
    },
    // 保存课程信息
    submit() {
      let params = {
        userId: this.info.userId,
        userName: this.info.userName,
        courseId: this.model.id,
        courseInfoList: this.dataList
      }
      if(this.judgeVideo(this.dataList)) {
          this.PUBLIC_Methods.apiNotify(
            "请添加视频",
            "warning"
          )
          return false;
      }
      this.api.videoMedsciCourseEditCourseInfo(params).then(response => {
        if (response.status === 200) {
          this.PUBLIC_Methods.apiNotify(
            response.message || "添加成功",
            "success"
          )
          this.close()
          this.$emit("up-date")
        } else {
          this.PUBLIC_Methods.apiNotify(
            response.message || "添加失败",
            "warning"
          )
        }
      })
    },
    // 关闭课程弹层，返回
    close() {
      this.$emit("close")
    }
	}
}
</script>

<style scoped lang="scss">
main{
  margin-bottom: 20px;
  .btn{
    flex: none;
    margin-left: 10px;
  }
  .period-name{
    width: calc(100% - 30px);
    display: flex;
    align-items: center;
    span{
      flex: none;
      width: 60px;
      text-align: right;
    }
    .period-name-right{
      width: 100%;
      display: flex;
      align-items: center;
      .period-name-value{
        width: calc(100% - 215px);
        text-align: left;
      }
    }
  }
  .video-wrap{
    width: calc(100% - 30px);
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    .video-wrap-left{
      width: calc(100% - 132px);
      display: flex;
      align-items: center;
      cursor: pointer;
      /deep/ .icon-play {
        margin-left:10px;
        font-size:20px;
        color: #67c23a;
      }
      .video-name{
        width: 70%;
        padding-left: 10px;
        box-sizing: border-box;
        display: inline-block;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .video-time{
        display: inline-block;
        width: 15%;
      }
      .video-status{
        display: inline-block;
        width: 15%;
      }
    }
    .video-wrap-left:hover /deep/ .icon-play,.video-wrap-left:hover .video-name,.video-wrap-left:hover .video-time {
      color: #409EFF;
    }
  }
  .el-collapse-item__content{
    min-height: 5px;
    div{
      min-height: 5px;
      span{
        display: block;
        min-height: 5px;
      }
    }
  }
}
.buttons {
  position: fixed;
  bottom: 20px;
  right: 20px;
}
</style>
