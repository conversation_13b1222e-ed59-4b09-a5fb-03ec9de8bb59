<template>
  <section class="form-tab" v-loading="getLoading">
    <el-tabs v-model="activeName">
      <el-tab-pane label="视频课程信息" name="content">
        <ms-video-media-content ref="contentTemp" @handle-click="handle_click"></ms-video-media-content>
      </el-tab-pane>
      <el-tab-pane label="课程设置" name="make">
        <ms-video-media-make ref="contentMake" @handle-click="handle_click"></ms-video-media-make>
      </el-tab-pane>
      <el-tab-pane label="显示栏目" name="category">
        <ms-info-setting :categoryModel.sync="submitData.categorys" :drainageModel.sync="submitData.drainageInfoDtoList" :projectIdModel.sync="submitData.projectId" headerShow :categoryConfigChild="{moduleName: 'video'}"></ms-info-setting>
      </el-tab-pane>
    </el-tabs>

    <!-- 弹出层 -->
    <el-dialog :visible.sync="dialog" 
               closeable 
               :show-close="false"
               :close-on-click-modal="false"
               width="50%"
               :title="dialogTitle">
      <component :is="dialogComponent" 
                   :model="dialogInfo" 
                   :operation="dialogOperation" 
                   :isNeedApi="false"
                   @close="dialog = !dialog" 
                   @up-date="init" 
                   v-if="dialog"></component>
    </el-dialog>
    <!-- 提交按钮 -->
    <footer-tool-bar v-loading="buttonLoading">
      <template slot="tool-content">
        <!-- <el-button plain>提交</el-button> -->
        <!-- <el-button plain v-show="videoId" @click="info_operation('approval')">{{form.status === 1 ? '去审' : '审核'}}</el-button> -->
        <el-button type="primary" @click="info_operation('save')">保存</el-button>
        <el-button type="info" @click="info_operation('back')">返回</el-button>
      </template>
    </footer-tool-bar>
  </section>
</template>

<script>
import msVideoMediaContent from './tool/ms-video-media-content'
import msVideoMediaMake from './tool/ms-video-media-make'
import msInfoSetting from '@/components/MsCommon/ms-info-setting'
import FooterToolBar from '@/components/ToolBar/footer-tool-bar'
import { mapGetters } from "vuex";
import { setSession } from "@/utils/auth";
export default {
  name: "video-failarmy-operation",
	data () {
		return {
      activeName: 'content',
      videoId: this.$route.query.id ? this.$route.query.id : 0,
      buttonLoading: false,
      getLoading: false,
      form: { // => userId、username、integral、waterMark、formatted 、expandRead (保存添加属性)
        userId: '', // => 用户Id
        username: '', // => 用户名称 (string)
        title: '', // => 标题 (string)
        content: '', // => 文章内容 (string)
        summary: '', // => 摘要 (string)
        isApp: '', // app是否显示（integer）
        isPc: '', // pc是否显示（integer）
        recommend: 0, // => 推荐（0.不推荐 1.推荐）
        recommendTime: '', //推荐时间范围
        recommendStartTime: '', //推荐开始时间
        recommendEndTime: '', //推荐结束时间
        isUp: 0, // => 置顶（0.不置顶 1.置顶）
        upTime: '', // => 置顶结束时间范围
        upStartTime: '', // => 置顶开始时间 (date-time)
        upEndTime: '', // => 置顶结束时间 (date-time)
        permission: 1, // => 观看权限 (1.开放 2.登录 3.付费)
        isPrivilege: 0, // 付费会员专属 (0.否,1.是)
        payType: 0, // => 是否支持现金 (0.不支持 1.支持)
        activityTime: '', // => 活动时间范围
        activityStartTime: '', // => 活动开始时间 (date-time)
        activityEndTime: '', // => 活动结束时间 (date-time)
        price: '', // 价格（integer）
        activityPrice: '', // 活动价格（integer）
        cover: '', // 课程封面 (string)
        tags: [], // => 关键字集合
        categorys: [], // => 专题分类集合
        // projectId: 1,
        memberCardIds: [], //付费会员ids
        drainageInfoDtoList: []
      },
      dialogComponent: '',
      dialog: false,
      dialogTitle: '',
      dialogOperation: '',
      dialogInfo: {}
		}
  },
  created() {
    this.init()
  },
  computed: {
    ...mapGetters(["submitData", "info", "localCode"])
  },
  components: {
    msVideoMediaContent,
    msVideoMediaMake,
    msInfoSetting,
    FooterToolBar
  },
  methods: {
    init() {
      let id = this.videoId
      this.dialog = false;
      this.$store.dispatch('SetSubmitData', this.form);
      if(id !== 0) {
        this.getLoading = true;
        this.form.id = id;
        let params = {
          id: id
        }
        this.api.videoMedsciCourseDetails(params).then( response => {
          this.getLoading = false;
          if(response.status === 200) {
            let res = response.data;
            if(res.recommendStartTime) {
              res.recommendTime = [res.recommendStartTime, res.recommendEndTime]
            }
            if(res.upStartTime) {
              res.upTime = [res.upStartTime, res.upEndTime]
            }
            if(res.activityStartTime) {
              res.activityTime = [res.activityStartTime, res.activityEndTime]
            }
            if(!res.categorys) {
              res.categorys = [];
            }
            // if(!res.projectId) {
            //   res.projectId = 1;
            // }
            if(!res.memberCards) {
              res.memberCardIds = []
            }else if (res.memberCards.length){
              res.memberCardIds = res.memberCards.map(i=> {return i.id})
            }
            this.form = {
              ...this.form,
              ...res,
              content: this.PUBLIC_Methods.unexcapeHtml(res.content)
            }
            setSession('video-publishTime', this.form.publishTime)
          } else {
            this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
          }
          this.$store.dispatch('SetSubmitData', this.form)
        }).catch(() => {
          this.getLoading = false;
          this.$store.dispatch('ClearSubmitData')
        })
      }
    },
    info_operation(val) {
      switch (val) {
        case 'save': 
          try {
            Promise.all([
              this.$refs['contentTemp'].validateData(),
              this.$refs['contentMake'].validateData()
            ]).then(() => {
              this.videoId ? this.updateData() : this.createData();
            });
          } catch (error) {
            return;
          }
          break;
        case 'back':
          this.$router.back();
          break;
        case 'approval':
          var params = {
            way: 'dialog',
            operation: this.form.status === 1 ? 'toreview' : 'approval',
            component: 'msVideoFailarmyOperation',
            data: this.form
          }
          this.handle_click(params)
          break;
        default: break;
      }
    },
    createData() {
      this.buttonLoading = true;
      if(this.submitData.drainageInfoDtoList && this.submitData.drainageInfoDtoList.length != 0 && this.submitData.drainageInfoDtoList[0].categoryName){
        this.submitData.drainageInfoDtoList = [{
          categoryId:this.submitData.drainageInfoDtoList[0].categoryId,
          switchDrainage:1,
          projectName:this.submitData.drainageInfoDtoList[0].categoryName,
          url:this.submitData.drainageInfoDtoList[0].url+'/class/detail/',
        }]
        if(this.submitData.drainageInfoDtoList[0].url.indexOf('https://') == -1 && this.submitData.drainageInfoDtoList[0].url.indexOf('http://') == -1){
          this.submitData.drainageInfoDtoList[0].url = 'https://' + this.submitData.drainageInfoDtoList[0].url
        }
      }else{
        this.submitData.drainageInfoDtoList = []
      }
      let paramsData = {
        ...this.submitData,
        memberCardIds: this.submitData.isPrivilege == 1 ? this.submitData.memberCardIds : [],
        content: this.PUBLIC_Methods.excapeHtml(this.submitData.content)
      }
      this.api.videoSaveCourse(paramsData).then(response => {
        this.buttonLoading = false
        if(response.status === 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
          window.history.go(-1);
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
      }).catch(() => this.buttonLoading = false)
    },
    updateData() {
      this.buttonLoading = true;
      if(this.submitData.drainageInfoDtoList && this.submitData.drainageInfoDtoList.length != 0 && this.submitData.drainageInfoDtoList[0].categoryName){
        this.submitData.drainageInfoDtoList = [{
          categoryId:this.submitData.drainageInfoDtoList[0].categoryId,
          switchDrainage:1,
          projectName:this.submitData.drainageInfoDtoList[0].categoryName,
          url:this.submitData.drainageInfoDtoList[0].url+'/class/detail/'+this.submitData.encryptionId,
        }]
        if(this.submitData.drainageInfoDtoList[0].url.indexOf('https://') == -1 && this.submitData.drainageInfoDtoList[0].url.indexOf('http://') == -1){
          this.submitData.drainageInfoDtoList[0].url = 'https://' + this.submitData.drainageInfoDtoList[0].url
        }
      }else{
        this.submitData.drainageInfoDtoList = []
      }
      let paramsData = {
        ...this.submitData,
        memberCardIds: this.submitData.isPrivilege == 1 ? this.submitData.memberCardIds : [],
        content: this.PUBLIC_Methods.excapeHtml(this.submitData.content)
      }
      this.api.videoMedsciCourseEditCourse(paramsData).then(response => {
        this.buttonLoading = false
        if(response.status === 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
          window.history.go(-1);
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
      }).catch(() => this.buttonLoading = false)
    },
    handle_click(val) {
      switch (val.way) {
        case 'dialog': 
          this.dialog = true;
          this.dialogInfo = val.data
          this.dialogOperation = val.operation;
          this.dialogComponent = val.component;
          this.dialogTitle = val.title;
          break;
        default: break;
      }
    }
  }
}
</script>
