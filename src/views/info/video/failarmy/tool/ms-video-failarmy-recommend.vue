<template>
  <ms-operation-dialog>
    <template slot="content">
      <el-form ref="submitRef"
               class="rule-form"
               style="padding: 20px 0 15px;"
               :model="submitData"
               :rules="rules"
               label-width="80px">
        <el-row type="flex" justify="space-around">
          <el-col :span="16">
            <el-form-item prop="endTime"
                      label="结束时间">
              <el-date-picker v-model="submitData.endTime"
                              type="date"
                              placeholder="请选择时间点"
                              style="width: 100%;"
                              value-format="yyyy-MM-dd HH:mm:ss"
                              :picker-options="pickerOptions">
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </template>
    <template slot="footer">
      <el-button @click="submitForm"
                 :loading="loading"
                 size="mini"
                 type="primary">确 定</el-button>
      <el-button @click="close"
                 size="mini">取 消</el-button>
    </template>
  </ms-operation-dialog>
</template>

<script>
import {ms_rule_date} from "@/utils/form-rule.js";
import {parseTime} from "@/utils"
export default {
	name: "ms-video-failarmy-recommend",
	data () {
		return {
      loading: false,
      userInfo: {},
      dealType: null,
      submitData: {
        id: '',
        endTime: ''
      },
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now();
        },
        shortcuts: [{
          text: '3天后',
          onClick(picker) {
            const date = new Date();
            date.setTime(date.getTime() + 3 * 3600 * 1000 * 24);
            picker.$emit('pick', date);
          }
        }, {
          text: '5天后',
          onClick(picker) {
            const date = new Date();
            date.setTime(date.getTime() + 5 * 3600 * 1000 * 24);
            picker.$emit('pick', date);
          }
        }, {
          text: '一周后',
          onClick(picker) {
            const date = new Date();
            date.setTime(date.getTime() + 7 * 3600 * 1000 * 24);
            picker.$emit('pick', date);
          }
        }, {
          text: '一个月后',
          onClick(picker) {
            const date = new Date();
            date.setTime(date.getTime() + 30 * 3600 * 1000 * 24);
            picker.$emit('pick', date);
          }
        }]
      },
      rules: {
        endTime: [
          { required: true, message: "请选择结束时间", trigger: 'blur' },
          { validator: ms_rule_date, trigger: 'blur' }
        ],
      }
		}
	},
	props: {
		model: Object,
    operation: String,
    isNeedApi: {
      type: Boolean,
      default: true
    }
  },
  created() {
    this.userInfo = this.$store.getters.info || {}
    let operationLocal = this.operation || this.$route.query.operation
    let temp = parseTime(new Date().getTime() + (7 * 24 * 60 * 60 * 1000))
    if (operationLocal === 'recommend') {
      this.dealType = 0
      this.submitData.endTime = this.model.recommendEndTime || temp
    } else if (operationLocal === 'sticky') {
      this.dealType = 1
      this.submitData.endTime = this.model.stickyEndTime || temp
    } 
    this.submitData.id = this.model.id || 0
  },
	methods: {
    submitForm () {
      let params = {
        userId: this.userInfo.userId,
        username: this.userInfo.userName,
        type: this.dealType,
        ...this.submitData
      }
      this.$refs["submitRef"].validate(valid => {
        if(valid) {
          if(this.isNeedApi) {
            this.loading = true
            this.api.recommendOrSticky(params).then( response => {
              if(response.status === 200) {
                this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
              } else {
                this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
              }
              this.$emit('up-date')
              this.loading = false
            }).catch( () => {
              this.loading = false;
              this.$emit('close')
            })
          } else {
            this.$emit('return-data', {...this.submitData,operation: this.operation})
          }
        }
      })
    },
    close() {
      this.$emit('close', {operation: this.operation})
    }
	}
}
</script>
