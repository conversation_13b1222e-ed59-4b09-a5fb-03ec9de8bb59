<template>
  <section class="form-tab" v-loading="getLoading">
    <el-tabs v-model="activeName">
      <el-tab-pane label="合集课程信息" name="content">
        <ms-video-failarmy-content ref="contentTemp" @handle-click="handle_click"></ms-video-failarmy-content>
      </el-tab-pane>
      <el-tab-pane label="显示栏目" name="category">
        <ms-info-setting :categoryModel.sync="submitData.categoryList" headerShow :categoryConfigChild="{moduleName: 'video'}"></ms-info-setting>
      </el-tab-pane>
    </el-tabs>

    <!-- 弹出层 -->
    <el-dialog :visible.sync="dialog" 
               closeable 
               :show-close="false"
               :close-on-click-modal="false"
               width="50%"
               :title="dialogTitle">
      <component :is="dialogComponent" 
                   :model="dialogInfo" 
                   :operation="dialogOperation" 
                   :isNeedApi="false"
                   @close="dialog = !dialog" 
                   @up-date="init" 
                   v-if="dialog"></component>
    </el-dialog>
    <!-- 提交按钮 -->
    <footer-tool-bar v-loading="buttonLoading">
      <template slot="tool-content">
        <!-- <el-button plain>提交</el-button> -->
        <el-button plain v-show="videoId" @click="info_operation('approval')">{{submitData.status === 1 ? '去审' : '审核'}}</el-button>
        <el-button type="primary" @click="info_operation('save')">保存</el-button>
        <el-button type="info" @click="info_operation('back')">返回</el-button>
      </template>
    </footer-tool-bar>
  </section>
</template>

<script>
import msVideoFailarmyContent from './tool/ms-video-failarmy-content'
import msInfoSetting from '@/components/MsCommon/ms-info-setting'
import VideoFailarmyMixin from "./video-failarmy-mixin"
import FooterToolBar from '@/components/ToolBar/footer-tool-bar'
export default {
  name: "video-failarmy-operation",
  mixins: [VideoFailarmyMixin],
	data () {
		return {
      activeName: 'content',
      videoId: this.$route.query.id ? this.$route.query.id : 0,
      buttonLoading: false,
      getLoading: false,

      submitData: { // => userId、username、integral、waterMark、formatted 、expandRead (保存添加属性)
        userId: '', // => 用户Id
        username: '', // => 用户名称 (string)
        title: '', // => 标题 (string)
        recommend: 0, // => 推荐（0.不推荐 1.推荐）
        recommendEndTime: '', // => 推荐结束时间 
        sticky: 0, // => 置顶（0.不置顶 1.置顶）
        stickyEndTime: '', // => 置顶结束时间 (date-time)
        opened: 1, // => 开放阅读 (0.不开放 1.开放)
        paymentCash: 0, // => 是否支持现金 (0.不支持 1.支持)
        costPrice: 0, // => 原价
        presentPrice: 0, // => 现价
        paymentIntegralBase: 0, // => 是否支持积分 (0.不支持 1.支持)
        integralBase: 0,
        paymentIntegralUme: 0, // => 是否支持梅花 (0.不支持 1.支持) 
        integralUme: 0,
        content: '', // => 文章内容 (string)
        summary: '', // => 摘要 (string)
        waterMark: 0, // 自动水印 (0.无水印,1.加水印)
        formatted: 0, // 去复杂格式 (0.不去格式,1.去格式)
        cover: '', // 封面 (string)
        attachmentList: [], // => 附件
        tagList: [], // => 标签集合
        categoryList: [], // => 专题分类集合
        status: 0,
        moduleType: 1
      },

      dialogComponent: '',
      dialog: false,
      dialogTitle: '',
      dialogOperation: '',
      dialogInfo: {}
		}
  },
  created() {
    this.init()
  },
  components: {
    msVideoFailarmyContent,
    msInfoSetting,
    FooterToolBar
  },
  methods: {
    init() {
      let id = this.videoId
      this.dialog = false;
      this.$store.dispatch('SetSubmitData', this.submitData)
      if(id !== 0) {
        this.getLoading = true;
        this.submitData.id = id;
        let params = {
          moduleType: 1,
          seriesId: id
        }
        this.api.getVideos(params).then( response => {
          this.getLoading = false;
          if(response.status === 200) {
            let res = response.data
            this.submitData = {
              ...this.submitData,
              ...res
            }
          } else {
            this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
          }
          this.$store.dispatch('SetSubmitData', this.submitData)
        }).catch(() => {
          this.getLoading = false;
          this.$store.dispatch('ClearSubmitData')
        })
      }
    },
    info_operation(val) {
      switch (val) {
        case 'save': 
          this.$refs['contentTemp'].validateData(this.videoId ? this.updateData: this.createData)
          break;
        case 'back':
          this.$router.back();
          break;
        case 'approval':
          var params = {
            way: 'dialog',
            operation: this.submitData.status === 1 ? 'toreview' : 'approval',
            component: 'msVideoFailarmyOperation',
            data: this.submitData
          }
          this.handle_click(params)
          break;
        default: break;
      }
    },
    createData(params) {
      this.buttonLoading = true;
      let paramsData = {
        ...params,
        categoryList: this.submitData.categoryList,
        moduleType: 1
      }
      this.api.saveVideos(paramsData).then(response => {
        this.buttonLoading = false
        if(response.status === 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
          this.$router.back()
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
      }).catch(() => this.buttonLoading = false)
    },
    updateData(params) {
      this.buttonLoading = true;
      let paramsData = {
        ...params,
        categoryList: this.submitData.categoryList,
        moduleType: 1
      }
      this.api.updateVideos(paramsData).then(response => {
        this.buttonLoading = false
        if(response.status === 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
          this.$router.back()
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
      }).catch(() => this.buttonLoading = false)
    },
    handle_click(val) {
      switch (val.way) {
        case 'dialog': 
          this.dialog = true;
          this.dialogInfo = val.data
          this.dialogOperation = val.operation;
          this.dialogComponent = val.component;
          this.dialogTitle = val.title;
          break;
        default: break;
      }
    }
  }
}
</script>
