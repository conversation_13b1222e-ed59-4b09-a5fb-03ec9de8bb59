const domConfig = {
  listSearch: [
    {
      label: '标题',
      placeholder: '请输入',
      model: 'title',
      component: 'ms-input'
    },
    {
      label: '创建人',
      placeholder: '请选择',
      model: 'createdBy',
      component: 'ms-createby-search'
    },
    {
      label: '发布时间',
      placeholder: '请选择时间段',
      model: 'createTime',
      component: 'ms-picker',
      type: 'daterange',
      width: '240px'
    },
    {
      label: '专题',
      placeholder: '请选择',
      model: 'classify',
      component: 'ms-category-cascader'
    }
  ],
  tableHeader: [
    { label: 'ID', property: 'id', sortable: true, width: 80 },
    { label: '标题', property: 'title', width: 250 },
    { label: '创建人', property: 'createdName' },
    { label: '发布时间', property: 'publishedTime', width: 130, sortable: true },
    { label: '创建时间', property: 'createdTime', width: 130, sortable: true },
    { label: '状态', property: 'status'}
  ],
  tableButtons: [
    {
      label: '编辑',
      icon: '',
      role: '',
      operation: 'edit',
      component: 'video-manage-failarmy',
      way: 'page',
      type: 'primary',
      path: 'video-manage-failarmy',
      params: ['id']
    },
    {
      label: '课程',
      icon: '',
      role: '',
      operation: 'edit',
      component: 'video-manage-failarmy-course',
      way: 'page',
      type: 'primary',
      path: 'video-manage-failarmy-course',
      params: ['id']
    },
    {
      label: '',
      icon: '',
      role: '',
      operation: 'editStatus',
      component: 'msVideoFailarmyOperation',
      way: 'dialog',
      field: 'status',
      rule: {
        1: { label: '去审', type: '', operation: 'toreview' },
        0: { label: '审核', type: 'success', operation: 'approval' }
      }
    },
    {
      label: '删除',
      icon: '',
      role: '',
      operation: 'delete',
      component: 'msVideoFailarmyOperation',
      way: 'dialog',
      type: 'danger'
    }
  ],
  soltButtons: [
    { 
      label: '添加课程', 
      type: 'primary', 
      icon: 'el-icon-plus',
      operation: 'created',
      component: 'video-manage-failarmy',
      way: 'page',
      path: 'video-manage-failarmy',
      params: ['id']
    },
    { 
      label: '批量审核', 
      type: 'primary',
      operation: 'approval',
      component: 'msVideoFailarmyOperation',
      way: 'batch'
    },
    { 
      label: '批量去审', 
      type: 'primary',
      operation: 'toreview',
      component: 'msVideoFailarmyOperation',
      way: 'batch'
    },
    { 
      label: '批量删除',
      type: 'info',
      icon: 'el-icon-close',
      operation: 'delete',
      component: 'msVideoFailarmyOperation',
      way: 'batch'
    }
  ]
}

export default domConfig;
