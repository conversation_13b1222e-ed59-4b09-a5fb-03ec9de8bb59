<template>
  <section>
    <el-row :span="20">
      <el-col :span="24">
        <el-table ref="dragTable" 
                  v-loading="getLoading" 
                  :data="chapterData" 
                  :header-cell-style="headerCellStyle" 
                  :header-row-style="headerRowStyle" 
                  highlight-current-row
                  @current-change="handleCurrentChange"
                  style="width: 100%;" 
                  class="table-style">
          <el-table-column align="center" label="章节列表">
            <template slot-scope="{row}">
              <template v-if="row.edit">
                <el-input v-model="row.title" placeholder="请输入标题"></el-input>
              </template>
              <span v-else>{{ row.title }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" label="" min-width="70px">
            <template slot-scope="{row, $index}">
              <div style="text-align: right">
                <template v-if="row.edit">
                  <el-tooltip effect="dark" content="确定" placement="bottom">
                    <span @click.stop="confirmEdit(row)">
                      <svg-icon class-name="drag-handler" icon-class="icon-queren" />
                    </span>
                    </el-tooltip>
                    <el-tooltip effect="dark" content="取消" placement="bottom">
                      <span @click.stop="cancelEdit(row, $index)">
                        <svg-icon class-name="drag-handler" icon-class="icon-cancel" />
                      </span>
                    </el-tooltip>
                </template>
                <template v-else>
                  <el-tooltip effect="dark" content="编辑" placement="bottom">
                    <span @click.stop="row.edit=!row.edit">
                      <svg-icon class-name="drag-handler" icon-class="icon-bianji-copy-copy" />
                    </span>
                  </el-tooltip>
                </template>
                <el-tooltip effect="dark" content="删除" placement="bottom" v-show="$index !== 0 && row.id !== 0">
                  <span @click.stop="deleteRow(row)">
                    <svg-icon class-name="drag-handler" icon-class="icon-shanchu" />
                  </span>
                </el-tooltip>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <button class="add-course"  @click="createChapter" type="button">
          <i class="el-icon-plus"></i> 添加章节
        </button>
      </el-col>
    </el-row>
  </section>
</template>

<script>
import { mapGetters } from "vuex";
export default {
  name: "special-course-chapter",
	data () {
		return {
      videoId: this.$route.query.id ? this.$route.query.id : 0,
      getLoading: false,
      chapterData: [],

      headerCellStyle: {
        "text-align": "center",
        "background-color": "#EBEEF5"
      },
      headerRowStyle: {
        "text-align": "center",
        "background-color": "#EBEEF5"
      }
		}
  },
  computed: {
    ...mapGetters(["info"])
  },
  created() {
    this.init(0)
  },
  methods: {
    /**
     * params: index => 判断是否为第一次执行接口
     */
    init(index) {
      let id = this.videoId
      if (id) {
        let params = {
          seriesId: id
        }
        this.getLoading = true
        this.api.getVideoSeriesChapterList(params).then(response => {
          this.getLoading = false
          if (response.status === 200 && response.data) {
            if (index === 0) {
              this.$emit('getCourseInfo', response.data[0])
            }
            this.chapterData = response.data.map(v => {
              this.$set(v, 'edit', false)
              return v
            })
          } 
        }).catch(() => { this.getLoading = false;})
      }
    },
    // 单选选中章节
    handleCurrentChange(val) {
      if (val && !val.edit) {
        this.$emit('getCourseInfo', val)
      }
    },
    // 取消章节编辑
    cancelEdit(row, index) {
      if (row.id === 0) {
        this.chapterData.splice(index, 1)
      } else if (row.id) {
        row.edit = !row.edit
      }
    },
    // dom添加章节
    createChapter() {
      this.chapterData.push({
        id: 0,
        title: '',
        edit: true  
      })
    },
    // 确认添加章节
    confirmEdit(row) {
      if (row.title) {
        if (row.id) {
          let params = {
            userId: this.info.userId,
            username: this.info.userName,
            id: row.id,
            title: row.title
          }
          this.api.updateVideoSeriesChapter(params).then(response => {
            if (response.status === 200) {
              this.init();
            } else {
              this.PUBLIC_Methods.apiNotify(response.message || '更新失败', 'warning')
            }
          })
        } else if (row.id === 0) {
          let params = {
            userId: this.info.userId,
            username: this.info.userName,
            seriesId: this.videoId,
            title: row.title
          }
          this.api.saveVideoSeriesChapter(params).then(response => {
            if (response.status === 200) {
              this.init();
            } else {
              this.PUBLIC_Methods.apiNotify(response.message || '添加失败', 'warning')
            }
          })
        }
      } else {
        this.PUBLIC_Methods.apiNotify('请输入章节标题', 'warning')
      }
    },
    deleteRow(row) {
      this.$confirm('此操作将永久删除专题章节，是否继续', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.api.delVideoSeriesChapter({id: row.id}).then(response => {
          if(response.status === 200) {
            this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
            this.init()
          } else {
            this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
          }
        })
      }).catch(() => {})
    }
  }
}
</script>

<style>
.drag-handler {
  margin: 7px 6px;
  cursor: pointer;
  width: 20px !important;
  height: 20px !important;
  color: rgba(31, 38, 62, 0.31);
}
.add-course {
  cursor: pointer;
  width: 100%;
  height: 40px;
  line-height: 39px;
  border: 1px dashed #EBEEF5;
  border-radius: 4px;
  text-align: center;
  font-size: 14px;
  color: #333;
  outline: none;
  margin-top: 10px;
  background-color: rgba(0,0,0, 0);
}
</style>
