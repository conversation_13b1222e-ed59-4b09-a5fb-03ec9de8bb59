<template>
  <section>
    <el-row :span="20">
      <el-col :span="24">
        <el-table ref="dragTable" 
                  v-loading="getLoading" 
                  row-key="additionId" 
                  :data="courseData" 
                  :header-cell-style="headerCellStyle" 
                  :header-row-style="headerRowStyle" 
                  style="width: 100%;" 
                  class="table-style">
          <el-table-column align="center" label="课程标题" min-width="300px">
            <template slot-scope="scope">
              <span>{{ scope.row.title }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" label="讲师" min-width="100px">
            <template slot-scope="scope">
              <span>{{ scope.row.lecturerName }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" label="时长" min-width="100px">
            <template slot-scope="scope">
              <span>{{ scope.row.duration }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" label="操作" min-width="150px">
            <template slot-scope="{row}">
              <el-button size="mini" class="btn-ope" @click="videoHaveLook(row)" :type="row.haveLook === 1 ? 'primary' : 'info'" v-text="row.haveLook === 1 ? '试看' : '非试看'"></el-button>
              <el-tooltip effect="dark" content="排序" placement="bottom">
                <svg-icon class-name="drag-handler" icon-class="icon-jiantou-tuodong" />
              </el-tooltip>
              <el-tooltip effect="dark" content="删除" placement="bottom">
                <span @click="deleteRow(row)">
                  <svg-icon class-name="drag-handler" icon-class="icon-shanchu" />
                </span>
              </el-tooltip>
            </template>
          </el-table-column>
        </el-table>
        <button class="add-course" v-show="!isAddCourse" @click="isAddCourse = true;" type="button">
          <i class="el-icon-plus"></i> 添加专题课程
        </button>
        <el-row style="margin-top: 10px;" v-show="isAddCourse">
          <el-col :span="16">
            <el-select size="medium"
                       v-model="videoResult" 
                       clearable 
                       filterable 
                       remote
                       style="width:100%;"
                       :remote-method="filterMethod"
                       :loading="courseLoading"
                       value-key="id">
                <el-option v-for="(item,index) in optionSearch"
                          :label="item.title"
                          :key="index"
                          :value="item">
                  <span v-text="`${item.title} ---- 讲师：${item.lecturerName}`"></span>
                </el-option>
            </el-select>
          </el-col>
          <el-col :span="8" style="text-align: right;">
            <el-button size="medium" type="primary" @click="confirmCreate" :loading="buttonLoading">确定</el-button>
            <el-button size="medium" plain @click="isAddCourse = false">取消</el-button>
          </el-col>
        </el-row>
      </el-col>
    </el-row>
  </section>
</template>

<script>
import Sortable from 'sortablejs'
import { mapGetters } from "vuex";
export default {
  name: "video-failarmy-video",
	data () {
		return {
      videoId: this.$route.query.id ? this.$route.query.id : 0,
      getLoading: false,
      courseLoading: false,
      buttonLoading: false,
      isAddCourse: false,
      optionSearch: [],
      courseData: [],
      total: 0,
      videoResult: {},

      headerCellStyle: {
        "text-align": "center",
        "background-color": "#EBEEF5"
      },
      headerRowStyle: {
        "text-align": "center",
        "background-color": "#EBEEF5"
      }
		}
  },
  computed: {
    ...mapGetters(["info", "chapterId"])
  },
  methods: {
    init() {
      let params = {
        chapterId: this.chapterId
      }
      this.getLoading = true
      this.api.getVideoAdditionList(params).then(response => {
        this.getLoading = false
        if (response.status === 200 && response.data) {
          this.courseData = response.data;
          this.total = response.data.length
          this.$nextTick(() => {
            this.setSort()
          })
        } else {
          this.courseData = []
          this.total = 0
        }
      }).catch(() => { this.getLoading = false;})
    },
    setSort() {
      const el = this.$refs.dragTable.$el.querySelectorAll('.el-table__body-wrapper > table > tbody')[0]
      this.sortable = Sortable.create(el, {
        ghostClass: 'sortable-ghost', 
        setData: function(dataTransfer) {
          dataTransfer.setData('Text', '')
        },
        onEnd: evt => {
          let drapObj = this.courseData[evt.oldIndex]
          this.courseData.splice(evt.oldIndex, 1)
          this.courseData.splice(evt.newIndex, 0, drapObj)
        }
      })
    },
    filterMethod(val) {
      if (val) {
        this.courseLoading = true;
        // 接口获取数据
        let params = {
          title : val
        }
        this.api.getVideoListLikeTitle(params).then(response => {
          this.courseLoading = false
          if (response.status === 200) {
            this.optionSearch = response.data
          }
        })
      }
    },
    deleteRow(row) {
      this.$confirm('此操作将删除章节课程，是否继续', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.api.delVideoAddition({additionId: row.additionId}).then(response => {
          if(response.status === 200) {
            this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
            this.init();
          } else {
            this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
          }
        })
      }).catch(() => {})
    },
    confirmCreate() {
      let chVal = this.videoResult
      if (!chVal.id) {
        return this.PUBLIC_Methods.apiNotify('请选择单节、专题信息', 'warning')
      }
      let params = {
        userId: this.info.userId,
        username: this.info.userName,
        chapterId: this.chapterId,
        videoId: chVal.id,
        rank: this.total + 1
      }
      this.buttonLoading = true;
      this.api.saveVideoAddition(params).then(response => {
        this.buttonLoading = false;
        this.isAddCourse = false;
        this.videoResult = {}
        if (response.status === 200) {
          this.init();
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '添加失败', 'warning')
        }
      }).catch(() => this.buttonLoading = false)
    },
    videoHaveLook(row) {
      let params = {
        userId: this.info.userId,
        username: this.info.userName,
        dealType: row.haveLook === 0 ? 2 : 1,
        additionId: row.additionId
      }
      this.api.updateVideoAdditionHaveLook(params).then(response => {
        if (response.status === 200) {
          this.init();
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '更新失败', 'warning')
        }
      })
    }
  }
}
</script>

<style>
.btn-ope {
  height: 28px !important;
  position: relative;
  top: 3px;
}
.sortable-ghost{
  opacity: .1;
  color: #fff!important;
  background: #409EFF!important;
}
.drag-handler {
  margin: 7px 10px;
  cursor: pointer;
  width: 20px !important;
  height: 20px !important;
  color: rgba(31, 38, 62, 0.31);
}
.add-course {
  cursor: pointer;
  width: 100%;
  height: 40px;
  line-height: 39px;
  border: 1px dashed #EBEEF5;
  border-radius: 4px;
  text-align: center;
  font-size: 14px;
  color: #333;
  outline: none;
  margin-top: 10px;
  background-color: rgba(0,0,0, 0);
}
</style>
