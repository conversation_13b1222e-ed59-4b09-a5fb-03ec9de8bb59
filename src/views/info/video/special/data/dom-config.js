const domConfig = {
  listSearch: [
    {
      label: '标题',
      placeholder: '请输入',
      model: 'title',
      component: 'ms-input'
    },
    {
      label: '创建人',
      placeholder: '请选择',
      model: 'createdBy',
      component: 'ms-createby-search'
    },
    {
      label: '发布时间',
      placeholder: '请选择时间段',
      model: 'createTime',
      component: 'ms-picker',
      type: 'daterange',
      width: '240px'
    },
    {
      label: '专题',
      placeholder: '请选择',
      model: 'categoryId',
      component: 'ms-category-cascader'
    }
  ],
  tableHeader: [
    { label: 'ID', property: 'id', sortable: true, width: 80 },
    { label: '标题', property: 'title', width: 250 },
    { label: '创建人', property: 'createdName' },
    { label: '发布时间', property: 'publishedTime', width: 130, sortable: true },
    { label: '创建时间', property: 'createdTime', width: 130, sortable: true },
    { label: '状态', property: 'status' }
  ],
  tableButtons: [
    {
      label: '编辑',
      icon: '',
      role: '',
      operation: 'edit',
      component: 'video-manage-special',
      way: 'page',
      type: 'primary',
      path: 'video-manage-special',
      params: ['id']
    },
    {
      label: '课程',
      icon: '',
      role: '',
      operation: 'edit',
      type: 'primary',
      component: 'msVideoSpecialCourse',
      way: 'dialog',
      position: 'right',
      title: '专题课程编辑',
      width: '60%'
    },
    {
      label: '',
      icon: '',
      role: '',
      operation: 'editStatus',
      component: 'msVideoSpecialOperation',
      way: 'dialog',
      field: 'status',
      rule: {
        1: { label: '去审', type: '', operation: 'toreview' },
        0: { label: '审核', type: 'success', operation: 'approval' }
      }
    },
    {
      label: '删除',
      icon: '',
      role: '',
      operation: 'delete',
      component: 'msVideoSpecialOperation',
      way: 'dialog',
      type: 'danger'
    }
  ],
  soltButtons: [
    {
      label: '添加课程',
      type: 'primary',
      icon: 'el-icon-plus',
      operation: 'created',
      component: 'video-manage-special',
      way: 'page',
      path: 'video-manage-special',
      params: ['id']
    },
    {
      label: '批量审核',
      type: 'primary',
      operation: 'approval',
      component: 'msVideoSpecialOperation',
      way: 'batch'
    },
    {
      label: '批量去审',
      type: 'primary',
      operation: 'toreview',
      component: 'msVideoSpecialOperation',
      way: 'batch'
    },
    {
      title: '批量添加专题',
      label: '批量添加专题',
      type: 'success',
      operation: 'add',
      component: 'msVideoSpecialCategory',
      way: 'batch',
      identify: 'batch_add_category',
      width: '70%'
    },
    {
      title: '批量移除专题',
      label: '批量移除专题',
      type: 'success',
      operation: 'delete',
      component: 'msVideoSpecialCategory',
      way: 'batch',
      identify: 'batch_delete_category',
      width: '70%'
    },
    {
      label: '批量删除',
      type: 'info',
      icon: 'el-icon-close',
      operation: 'delete',
      component: 'msVideoSpecialOperation',
      way: 'batch'
    }
  ]
}

export default domConfig;
