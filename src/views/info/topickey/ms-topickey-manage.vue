<template>
	<ms-table
		:currentPage="searchParams.pageIndex"
		:loading="loading"
		:operationButtons="domConfig.tableButtons"
		:pageSize="searchParams.pageSize"
		:scopeConfig="scopeConfig.show"
    :scopeHeader="scopeConfig.headerShow"
		:tableData="list"
		:tableHeader="domConfig.tableHeader"
		:total="total"
    :showSelection="true"
		@current-change="current_change"
		@operation-change="operation_change"
		@size-change="size_change"
    @header-operation="header_operation"
    @handleSelectionChange="handleSelectionChange"
	>
    <!-- 列表搜索去区域插槽 -->
		<template slot="ms-table-header">
			<div class="slot-search">
				<template v-for="(searchItem, key) in domConfig.listSearch">
					<component
						:index="searchItem.index || ''"
						:is="searchItem.component"
						:key="key"
            :width="searchItem.width || '150px'"
            :model.sync="searchParams[searchItem.model]"
            :label="searchItem.label"
						:operation="searchItem.operation || ''"
						:options="searchItem.options || []"
						:placeholder="searchItem.placeholder || ''"
						:type="searchItem.type || ''"
            :multiple="searchItem.multiple"
            :disabled="searchItem.disabled" 
					></component>
				</template>
        <div class="inlineBlock">
          <el-button @click="handleClick('query')" type="primary" plain icon="el-icon-search">查询</el-button>
          <el-button @click="handleClick('reset')">重置</el-button>
        </div>
        <div class="slot-button" style="margin-bottom: 0px;">
          <template v-for="(item, index) in domConfig.soltButtons">
            <el-button :key="index" :type="item.type" :icon="item.icon" @click="operation_change({operation: item})" plain>{{ item.label }}</el-button>
          </template>
        </div>
			</div>
      
      <el-dialog :visible.sync="dialog" 
                 closeable 
                 show-close
                 :close-on-click-modal="false"
                 :width="dialogWidth"
                 :title="dialogTitle">
				<component :is="dialogComponent" 
                   :model="scopeInfo" 
                   :operation="dialogOperation" 
                   @close="dialog = !dialog" 
                   @up-date="init" 
                   v-if="dialog"></component>
			</el-dialog>
		</template>
	</ms-table>
</template>

<script>
import tableMixins  from "../../common/mixins/table"
import domConfig from "./data/dom-config"
import scopeConfig from "./data/scope-config"
import msTopickeyStatus from './tool/ms-topickey-status'
import msTopickeyDelete from './tool/ms-topickey-delete'
import msTopickeyDepartment from './tool/ms-topickey-department'
import msTopickeyEdit from './tool/ms-topickey-edit'
import MsDepartmentSearch from '@/components/MsCommon/ms-department-search'
export default {
  name: "ms-topickey-manage",
  mixins: [tableMixins],
  data () {
    return {
      domConfig: domConfig,
      scopeConfig: scopeConfig, 
      searchParams: { // => 列表查询传参
        topicName: "",
        startTime: "",
        endTime: "",
        status: null,
        createTime: []
      }
    }
  },
  components: {
    msTopickeyStatus,
    msTopickeyDelete,
    msTopickeyEdit,
    MsDepartmentSearch,
    msTopickeyDepartment
  },
  methods: {
    init () {
      this.loading = true;
      this.dialog = false;
      if (this.searchParams.createTime) {
        this.searchParams.startTime = this.searchParams.createTime[0] || ''
        this.searchParams.endTime = this.searchParams.createTime[1] || ''
      }
      let searchParams = {...this.searchParams}
      this.api.getTopicKeywordsPage(searchParams).then(response => {
        this.loading = false
        this.total = response.totalSize || 0;
        this.list = response.data || []
        if (response.status !== 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
      }).catch(() => this.loading = false)
    },
    operation_change_module(val) {
        if (val.operation.way === 'update') {
            let params = {
                userId: this.$store.getters.info.userId,
                username: this.$store.getters.info.userName,
                id: [val.model.id],
                operatorType: val.model.isSecret
            }
            this.api.updateTopicSecret(params).then(response => {
                if(response.status === 200) {
                    this.init()
                } else {
                    this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
                }
            }).catch()
        }
    }
  }
};
</script>
