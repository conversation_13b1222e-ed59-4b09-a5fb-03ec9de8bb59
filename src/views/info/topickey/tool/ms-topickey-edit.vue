<template>
  <ms-operation-dialog>
    <template slot="content">
      <el-form
        ref="submitRef"
        class="rule-form"
        :model="submitData"
        :rules="rules"
        label-width="80px"
        v-loading="getLoading"
      >
        <el-row>
          <el-col :span="12" style="float: right;">
            <el-form-item label="显示选项">
              <el-row type="flex" >
                <el-checkbox v-model="submitData.pcVisible" style="width: 60px" :true-label="1" :false-label="0">PC显示</el-checkbox>
                <el-checkbox v-model="submitData.appVisible" style="width: 60px" :true-label="1" :false-label="0">APP显示</el-checkbox>
              </el-row>
            </el-form-item>
          </el-col>
          <template v-for="(item, index) in formConfig.formField">
            <el-col :key="index" :span="item.colSpan">
              <el-form-item :prop="item.prop" :label="item.label">
                <component
                  :is="item.component"
                  v-model="submitData[item.prop]"
                  :model.sync="submitData[item.prop]"
                  :modelName.sync="submitData[item.propName]"
                  :width="item.width || '100%'"
                  :disabled="item.disabled"
                  :type="item.type || 'text'"
                  :active="item.active"
                  :inactive="item.inactive"
                >
                </component>
              </el-form-item>
            </el-col>
          </template>
        </el-row>
      </el-form>
      <p class="banner-h5">上传图片尺寸推荐：750*430</p>
    </template>
    <template slot="footer">
      <el-button
        @click="submitForm"
        :loading="loading"
        size="mini"
        type="primary"
        >确 定</el-button
      >
      <el-button @click="$emit('close')" size="mini">取 消</el-button>
    </template>
  </ms-operation-dialog>
</template>

<script>
import MsTagSearch from "@/components/MsCommon/ms-tag-search";
import MsUserSearch from "@/components/MsCommon/ms-user-search";
import formConfig from "../data/form-config";
import { mapGetters } from "vuex";
export default {
  name: "ms-pagekey-edit",
  data() {
    return {
      loading: false,
      getLoading: false,
      formConfig: formConfig,
      submitData: {
        topicName: "",
        url: "",
        priority: null,
        introduction: "",
        image: "",
        bannerImg: "",
        bannerImgH5: "",
        status: 1,
        isSecret: 0,
        topicTags: [],
        adminId: 0,
        adminName: "",
        showAppHits: 0,
        pcVisible:0,
        appVisible:0
      },

      //common
      operationLocal: "",
      rules: {
        topicName: [
          { required: true, message: "请输入话题名称", trigger: "blur" },
        ],
        introduction: [
          { required: true, message: "请输入话题简介", trigger: "blur" },
        ],
        adminId: [
          {
            validator: (rule, value, callback) => {
              if (this.submitData.isSecret === 1 && !value) {
                callback("请选择话题创建人");
              } else {
                callback();
              }
            },
            trigger: "change",
          },
        ],
      },
    };
  },
  props: {
    model: Object,
    operation: String,
  },
  computed: {
    ...mapGetters(["info"]),
  },
  components: {
    MsTagSearch,
    MsUserSearch,
  },
  created() {
    this.init();
  },
  methods: {
    init() {
      this.operationLocal = this.operation || this.$route.query.operation;
      if (this.operationLocal === "edit" && this.model.id) {
        this.getDetail(this.model.id);
      }
    },
    getDetail(id) {
      this.getLoading = true;
      this.api
        .getTopicKeyWords({ id: id })
        .then((response) => {
          this.getLoading = false;
          if (response.status === 200) {
            this.submitData = { ...response.data };
          } else {
            this.PUBLIC_Methods.apiNotify(
              response.message || "请求出错",
              "warning"
            );
          }
        })
        .catch(() => (this.getLoading = false));
    },
    submitForm() {
      this.$refs["submitRef"].validate((valid) => {
        if (valid) {
          this.loading = true;
          if (this.operationLocal === "edit") {
            this.submitEdit();
          } else {
            this.submitAdd();
          }
        }
      });
    },
    submitEdit() {
      let params = {
        userId: this.info.userId,
        username: this.info.userName,
        ...this.submitData,
        adminId: this.submitData.adminId || 0,
      };
      this.api
        .updateTopicKeyWords(params)
        .then((response) => {
          if (response.status === 200) {
            this.PUBLIC_Methods.apiNotify(
              response.message || "请求成功",
              "success"
            );
          } else {
            this.PUBLIC_Methods.apiNotify(
              response.message || "请求出错",
              "warning"
            );
          }
          this.$emit("up-date");
          this.loading = false;
        })
        .catch(() => {
          this.loading = false;
          this.$emit("close");
        });
    },
    submitAdd() {
      let params = {
        userId: this.info.userId,
        username: this.info.userName,
        ...this.submitData,
        adminId: this.submitData.adminId || 0,
      };
      this.api
        .insertTopicKeyWords(params)
        .then((response) => {
          if (response.status === 200) {
            this.PUBLIC_Methods.apiNotify(
              response.message || "请求成功",
              "success"
            );
          } else {
            this.PUBLIC_Methods.apiNotify(
              response.message || "请求出错",
              "warning"
            );
          }
          this.$emit("up-date");
          this.loading = false;
        })
        .catch(() => {
          this.loading = false;
          this.$emit("close");
        });
    },
  },
};
</script>

<style scope lang="scss">
  // ::v-deep {
    
  // }
  .ms-dialog-main{
      // background-color: #ff0;
     position: relative;
   } 
   .banner-h5 {
    position: absolute;
    display: inline-block;
    width: 130px;
    bottom: 70px;
    right: 80px;
  }

</style>
