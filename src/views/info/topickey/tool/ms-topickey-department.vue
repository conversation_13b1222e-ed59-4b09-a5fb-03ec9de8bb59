<template>
    <ms-operation-dialog title="科室绑定">
        <template slot="content">
            <el-tag v-for="(item, index) in tagArr" 
                    :key="index" 
                    type="info" 
                    style="margin: 0 5px 5px 0">{{item}}</el-tag>
            <el-row style="padding: 10px 0 20px">
                <el-col :span="12">
                    <ms-category-cascader :model.sync="submitData.departmentId" :modelName.sync="submitData.departmentName"></ms-category-cascader>
                </el-col>
            </el-row>
        </template>
        <template slot="footer">
            <el-button @click="submitForm"
                        :loading="loading"
                        size="mini"
                        type="primary">确 定</el-button>
            <el-button @click="$emit('close')"
                        size="mini">取 消</el-button>
        </template>
    </ms-operation-dialog>
</template>

<script>
export default {
	name: "ms-topickey-department",
	data () {
		return {
            tagArr: [],
            ids: [],
            title: '',
            loading: false,
            submitData: {
                departmentId: 0,
                departmentName: ''
            }
		}
	},
	props: [
		"model",
		"operation"
    ],
    created() {
        this.init()
    },
	methods: {
        init() {
            let arr = []
            let ids = []
            if (this.PUBLIC_Methods.isArrayFn(this.model)) {
                this.model.forEach(item => {
                arr.push(item.topicName)
                ids.push(item.id)
                });
            } else {
                arr.push(this.model.topicName)
                ids.push(this.model.id)
            }
            this.tagArr = arr
            this.ids = ids
        },
		submitForm () {
            this.loading = true;
            let params = {
                ids: this.ids,
                ...this.submitData
            }
            this.api.batchUpdateTopicCategory(params).then(response => {
                if(response.status === 200) {
                    this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
                } else {
                    this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
                }
                this.$emit('up-date')
                this.loading = false
            }).catch( () => {
                this.loading = false;
                this.$emit('close')
            })
        }
	}
}
</script>
