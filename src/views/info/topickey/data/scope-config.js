const scopeConfig = {
  show: {
    status: () => {
      return {
        type: 'status',
        rule: {
          0: { label: '禁用', background: '#A7ADBD' },
          1: { label: '启用', background: '#40A23F' }
        }
      }
    },
    isSecret: () => {
      return {
        type: 'switch',
        activeVal: 0,
        inactiveVal: 1,
        config: {
          way: 'update'
        }
      }
    },
    topicName: () => {
      return {
        type: 'other',
        config: {
          way: 'link',
          pathKey: 'url' 
        }
      }
    },
    adminName: () => {
      return {
          type: 'webLink',
          config: {
            role: '',
            operation: 'edit',
            way: 'page',
            path: 'user-detail',
            params: [{
              keyName: 'id',
              valName: 'adminId'
            }],
          }
      }
    },
    // createdName: () => {
    //   return {
    //       type: 'webLink',
    //       config: {
    //         role: '',
    //         operation: 'edit',
    //         way: 'page',
    //         path: 'user-detail',
    //         params: [{
    //           keyName: 'id',
    //           valName: 'adminId'
    //         }],
    //       }
    //   }
    // },
  },
  headerShow: {
    status: () => {
      return {
        type: 'dropdown',
        icon: 'icon-funnel',
        options: [
          { label: '全部', value: null },
          { label: '禁用', value: 0 },
          { label: '启用', value: 1 }
        ],
        operation: 'query'
      }
    },
    isSecret: () => {
      return {
        type: 'dropdown',
        icon: 'icon-funnel',
        options: [
          { label: '全部', value: null },
          { label: '公开', value: 0 },
          { label: '私密', value: 1 }
        ],
        operation: 'query'
      }
    }
  }
}

export default scopeConfig;
