const formConfig = {
  formField: [
    {
      label: '话题名称',
      prop: 'topicName',
      colSpan: 12,
      component: 'ms-input'
    },
    // {
    //   label: 'URL',
    //   prop: 'url',
    //   colSpan: 12,
    //   component: 'ms-input'
    // },
    {
      label: '关联Tag库',
      prop: 'topicTags',
      colSpan: 24,
      component: 'MsTagSearch'
    },
    {
      label: '优先级',
      prop: 'priority',
      colSpan: 12,
      component: 'ms-input-number',
    },
    {
      label: 'app点击数',
      colSpan: 12,
      component: 'ms-input-number',
      prop: 'showAppHits'
    },
    {
      label: '话题管理员',
      placeholder: '请选择',
      prop: 'adminId',
      propName: 'adminName',
      component: 'ms-user-search',
      colSpan: 12
    },
    {
      label: '公开状态',
      prop: 'isSecret',
      colSpan: 6,
      component: 'ms-switch',
      active: 0,
      inactive: 1
    },
    {
      label: '状态',
      prop: 'status',
      colSpan: 6,
      component: 'ms-switch',
      active: 1,
      inactive: 0
    },
    {
      label: '简介',
      prop: 'introduction',
      colSpan: 24,
      component: 'ms-input',
      type: 'textarea'
    },
    {
      label: '图片',
      prop: 'image',
      colSpan: 24,
      component: 'ms-single-image'
    },
    {
      label: ' banner图 (web)',
      prop: 'bannerImg',
      colSpan: 12,
      component: 'ms-single-image'
    },
    {
      label: ' banner图 (H5)',
      prop: 'bannerImgH5',
      colSpan: 12,
      component: 'ms-single-image'
    }
  ]
}

export default formConfig;
