const domConfig = {
  listSearch: [
    {
      label: '话题名称',
      placeholder: '请输入',
      model: 'topicName',
      component: 'ms-input'
    },
    {
      label: '话题创建人',
      placeholder: '请输入',
      model: 'createdName',
      component: 'ms-input'
    },
    {
      label: '非所属科室',
      placeholder: '请选择',
      model: 'departmentId',
      component: 'ms-category-cascader'
    },
    {
      label: '创建时间',
      placeholder: '请选择时间段',
      model: 'createTime',
      component: 'ms-picker',
      type: 'daterange',
      width: '240px'
    },
    {
      label: '话题管理员',
      placeholder: '请输入',
      model: 'adminName',
      component: 'ms-input'
    }
  ],
  tableHeader: [
    { label: 'ID', property: 'id', sortable: true },
    { label: '话题名称', property: 'topicName' },
    // { label: 'URL', property: 'url', width: 220 },
    { label: '点击数', property: 'allHits', width: 80, sortable: true  },
    { label: 'App点击数', property: 'showAppHits', width: 100, sortable: true  },
    { label: '评论数', property: 'comments', width: 80, sortable: true  },
    { label: '所属科室', property: 'departmentName' },
    { label: '公开状态', property: 'isSecret', width: 80},
    // { label: '状态', property: 'status' },
    { label: '管理员', property: 'adminName' },
    { label: '创建人', property: 'createdName' },
    { label: '创建时间', property: 'createdTime', width: 130, sortable: true  }
  ],
  tableButtons: [
    {
      label: '编辑',
      icon: '',
      role: '',
      operation: 'edit',
      component: 'msTopickeyEdit',
      way: 'dialog',
      type: 'primary',
      title: '关键词编辑',
      width: '60%'
    },
    {
      icon: '',
      role: '',
      operation: 'editStatus',
      component: 'msTopickeyStatus',
      way: 'dialog',
      field: 'status',
      rule: {
        1: { label: '禁用', type: 'info' },
        0: { label: '启用', type: 'success' }
      }
    },
    {
      label: '删除',
      icon: '',
      role: '',
      type: 'danger',
      way: 'dialog',
      operation: 'delete',
      component: 'msTopickeyDelete',
    }
  ],
  soltButtons: [
    { 
      label: '批量设置科室', 
      type: 'primary', 
      icon: '',
      operation: '',
      component: 'msTopickeyDepartment',
      way: 'batch'
    },
    { 
      label: '添加关键词', 
      icon: 'el-icon-plus',
      type: 'primary', 
      operation: 'created',
      component: 'msTopickeyEdit',
      way: 'dialog',
      title: '关键词新建',
      width: '60%'
    },
    { 
      label: '批量删除', 
      type: 'info', 
      icon: 'el-icon-close',
      operation: 'delete',
      component: 'msTopickeyDelete',
      way: 'batch'
    }
  ]
}

export default domConfig;
