<template>
  <ms-operation-dialog>
    <template slot="content">
      <el-form ref="submitRef"
               label-width="85px"
               class="rule-form">
        <el-row>
          <el-col :span="18" :offset="2">
            是否新建一个mcpToken？
          </el-col>
        </el-row>
      </el-form>
    </template>
    <template slot="footer">
      <el-button @click="submitForm"
                 :loading="loading"
                 size="mini"
                 type="primary">确 定</el-button>
      <el-button @click="$emit('close')"
                 size="mini">取 消</el-button>
    </template>
  </ms-operation-dialog>
</template>

<script> 
export default {
	name: "mcpToken-create",
	data () {
		return {
      loading: false,
    }
  },
	methods: {
    submitForm() {
      this.api.mcpGenerate().then(response => {
        if(response.status === 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
        this.$emit('up-date')
        this.loading = false
      }).catch( () => {
        this.loading = false;
        this.$emit('close')
      })
    },
	}
}
</script>
