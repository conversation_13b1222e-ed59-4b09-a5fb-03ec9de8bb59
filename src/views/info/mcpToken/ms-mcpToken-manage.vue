<template>
    <ms-table
        :operationButtons="domConfig.tableButtons"
        :tableData="list"
        :tableHeader="domConfig.tableHeader"
        @operation-change="operation_change"
        class="table-simple"
    >
      <!-- 列表搜索去区域插槽 -->
      <template slot="ms-table-header">
			<div class="slot-button-article clearfix">
				<template v-for="(item, index) in domConfig.soltButtons">
					<el-button :key="index" 
                     :type="item.type" 
                     size="mini"
                     :icon="item.icon"
                     @click="operation_change({operation: item})"
                     plain>{{ item.label }}</el-button>
				</template>
			</div>
			<el-dialog :visible.sync="dialog" 
                 closeable 
                 show-close
                 :close-on-click-modal="false"
                 :width="dialogWidth"
                 :title="dialogTitle">
				<component :is="dialogComponent" 
                   :model="scopeInfo" 
                   :operation="dialogOperation" 
                   @close="dialog = !dialog" 
                   @up-date="init" 
                   v-if="dialog"></component>
			</el-dialog>
		</template>
    </ms-table>
</template>

<script>
import mcpTokenMixin from "./mcpToken-mixin"
import tableMixins  from "../../common/mixins/table"
export default {
  name: "ms-webinit-manage",
  mixins: [tableMixins,mcpTokenMixin],
  data () {
    return {
    }
  },
  methods: {
    apiInit () {
      this.api.mcpTokenList().then(response => {
        this.loading = false
        this.total = response.totalSize || 0;
        this.list = response.data || []
        if (response.status !== 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
      }).catch(() => this.loading = false)
    },
  }
};
</script>
