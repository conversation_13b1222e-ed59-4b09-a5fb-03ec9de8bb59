const domConfig = {
  
  tableHeader: [
    { label: 'id', property: 'id',  width: 80 },
    { label: 'token', property: 'token', width: 450 },
    // { label: '创建人', property: 'createdName', width: 100 },
  ],
  soltButtons: [
    {
      label: '新建token',
      type: 'primary',
      icon: 'el-icon-plus',
      title: '新建token',
      operation: 'created',
      component: 'mcpTokenCreate',
      way: 'dialog',
    }
  ]
}

export default domConfig;
