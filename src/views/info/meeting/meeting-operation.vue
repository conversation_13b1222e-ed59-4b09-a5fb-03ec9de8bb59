<template>
  <section class="form-tab" v-loading="getLoading">

    <el-tabs v-model="activeName">
      <el-tab-pane label="会议内容" name="content">
        <el-form :model="submitData"
             ref="submitRef"
             class="rule-form info-form"
             :rules="rules"
             label-width="70px">
          <el-row :gutter="10"> 
            <el-col :span="18">
              <el-row>
                <el-col :span="24">
                  <el-form-item label="会议名称" prop="name">
                    <el-input v-model="submitData.name" style="width: 100%"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="会议城市" prop="cityId">
                    <ms-area-cascader :model.sync="submitData.cityId" :modelName.sync="submitData.city" @bindData="bindData"></ms-area-cascader>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="会议时间" prop="meetingTime">
                    <ms-picker :model.sync="submitData.meetingTime" type="daterange"></ms-picker>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="主办方" prop="sponsor">
                    <el-input v-model="submitData.sponsor" style="width: 100%"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="会议网址" prop="url">
                    <el-input v-model="submitData.url" style="width: 100%"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="会议性质" prop="meetingNature">
                    <ms-select-local :model.sync="submitData.meetingNature" code="meetingNature"></ms-select-local>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="定时上线" prop="publishedTime">
                    <ms-picker :model.sync="submitData.publishedTime" type="datetime"></ms-picker>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="会议内容" prop="content">
                    <ms-editor v-model="submitData.content"></ms-editor>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-col>
            <el-col :span="6" class="info-form-right">
              <el-row>
                <el-col :span="24">
                  <el-form-item label="显示选项" label-width="95px">
                    <el-row type="flex">
                      <el-checkbox v-model="submitData.recommend" style="width: 45%" :true-label="1" :false-label="0">推荐</el-checkbox>
                      <ms-picker :model.sync="submitData.recommendEndTime" type="datetime"></ms-picker>
                    </el-row>
                    <el-row type="flex" style="margin-top: 5px;">
                      <el-checkbox v-model="submitData.sticky" style="width: 45%" :true-label="1" :false-label="0">固顶</el-checkbox>
                      <ms-picker :model.sync="submitData.stickyEndTime" type="datetime"></ms-picker>
                    </el-row>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="关键词" prop="tagList" label-width="95px">
                    <MsTagSearch v-model="submitData.tagList" :notMul="false"></MsTagSearch>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="学分" prop="credit" label-width="95px">
                    <el-input v-model="submitData.credit" style="width: 100%"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="承办方" prop="contractor" label-width="95px">
                    <el-input v-model="submitData.contractor" style="width: 100%"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="协办方" prop="coSponsor" label-width="95px">
                    <el-input v-model="submitData.coSponsor" style="width: 100%"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="会议详细地址" prop="address" label-width="95px">
                    <el-input v-model="submitData.address" style="width: 100%"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="报名结束时间" prop="applyEndTime" label-width="95px">
                    <ms-picker :model.sync="submitData.applyEndTime" type="datetime"></ms-picker>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="联系人" prop="contact" label-width="95px">
                    <el-input v-model="submitData.contact" style="width: 100%"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="联系地址" prop="contactAddress" label-width="95px">
                    <el-input v-model="submitData.contactAddress" style="width: 100%"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="电话" prop="contactNumber" label-width="95px">
                    <el-input v-model="submitData.contactNumber" style="width: 100%"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="邮箱" prop="contactEmail" label-width="95px">
                    <el-input v-model="submitData.contactEmail" style="width: 100%"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="传真" prop="contactFax" label-width="95px">
                    <el-input v-model="submitData.contactFax" style="width: 100%"></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-col>
          </el-row>
        </el-form>
      </el-tab-pane>
      <el-tab-pane label="显示栏目" name="category">
        <ms-info-setting :categoryModel.sync="submitData.categoryList" :categoryConfigChild="{moduleName: 'meeting'}" headerShow></ms-info-setting>
      </el-tab-pane>
    </el-tabs>
    <!-- 表单内容 -->
    
    <!-- 提交按钮 -->
    <footer-tool-bar v-loading="buttonLoading">
      <template slot="tool-content">
        <el-button type="primary" @click="info_operation('save')">保存</el-button>
        <el-button type="info" @click="info_operation('back')">返回</el-button>
      </template>
    </footer-tool-bar>
  </section>
</template>

<script>
import FooterToolBar from '@/components/ToolBar/footer-tool-bar'
import MsEditor from '@/components/MsEditor'
import MsTagSearch from '@/components/MsCommon/ms-tag-search'
import MsAreaCascader from '@/components/MsCommon/ms-area-cascader'
import msInfoSetting from '@/components/MsCommon/ms-info-setting'
import {ms_rule_url} from "@/utils/form-rule.js";
export default {
  name: "journal-operation",
	data () {
		return {
      buttonLoading: false,
      getLoading: false,
      dataId: this.$route.query.id ? this.$route.query.id : 0,
      rules: {
        name: [
          { required: true, message: "请填写会议名称", trigger: 'blur' }
        ],
        cityId: [
          { required: true, message: "请选择会议城市", trigger: 'change' }
        ],
        meetingTime: [
          { required: true, message: "请选择会议时间", trigger: 'change' }
        ],
        sponsor: [
          { required: true, message: "请填写主办方", trigger: 'blur' }
        ],
        url: [
          { required: true, message: "请填写会议网址", trigger: 'blur' },
          { validator: ms_rule_url, trigger: 'blur' }
        ],
        meetingNature: [
          { required: true, message: "请选择会议性质", trigger: 'change' }
        ],
        categoryId: [
          { required: true, message: "请选择学科领域", trigger: 'change' }
        ],
        content: [
          { required: true, message: "请填写会议内容", trigger: 'change' }
        ]
        // address: [
        //   { required: true, message: "请填写会议地址", trigger: 'change' }
        // ],
        // applyEndTime: [
        //   { required: true, message: "请填写会议报名结束时间", trigger: 'change' }
        // ]
      },
      submitData: { 
        recommend: 0,
        sticky: 0,
        meetingTime: null,
        categoryList: []
      },
      activeName: 'content'
		}
  },
  components: {
    MsAreaCascader,
    MsTagSearch,
    FooterToolBar,
    MsEditor,
    msInfoSetting
  },
  created() {
    this.init()
  },
  methods: {
    init() {
      this.dialog = false
      let id = this.dataId
      if(id !== 0) {
        this.getLoading = true;
        this.submitData.id = id;
        this.api.getMedsciMeetingById({id: id}).then( response => {
          this.getLoading = false;
          if(response.status === 200) {
            let res = response.data
            this.submitData = {
              ...this.submitData,
              ...res,
              content: this.PUBLIC_Methods.unexcapeHtml(res.content),
              meetingTime: res.startTime && res.endTime ? [res.startTime, res.endTime] : null
            }
          } else {
            this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
          }
        }).catch(() => {
          this.getLoading = false;
        })
      } 
    },
    bindData(params) {
      this.submitData.provinceId = params.model.parentId
      this.submitData.provinceName = params.model.parentName
    },
    info_operation(val) {
      switch (val) {
        case 'save': 
          this.$refs["submitRef"].validate( valid => {
            if (valid) {
              if (this.submitData.meetingTime) {
                this.submitData.startTime = this.submitData.meetingTime[0] || ''
                this.submitData.endTime = this.submitData.meetingTime[1] || ''
              }
              this.submitData.content = this.PUBLIC_Methods.excapeHtml(this.submitData.content)
              this.dataId ? this.updateForm() : this.createForm()
            }
          })
          break;
        case 'back':
          this.$router.back();
          break;
        default: break;
      }
    },
    createForm() {
      this.buttonLoading = true;
      let params = {
        userId: this.$store.getters.info.userId,
        username: this.$store.getters.info.userName,
        ...this.submitData
      }
      this.api.saveMedsciMeetingAdministrator(params).then(response => {
        this.buttonLoading = false
        if(response.status === 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
          this.$router.back()
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
      }).catch(() => this.buttonLoading = false)
    },
    updateForm() {
      this.buttonLoading = true;
      let params = {
        ...this.submitData
      }
      this.api.updateMedsciMeetingById(params).then(response => {
        this.buttonLoading = false
        if(response.status === 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
          this.$router.back()
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
      }).catch(() => this.buttonLoading = false)
    }
  }
}
</script>
