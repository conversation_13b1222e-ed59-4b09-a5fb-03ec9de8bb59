import serveUrl from '@/store/data/serveUrl.js'

const scopeConfig = {
  show: {
    status: () => {
      return {
        type: 'status',
        rule: {
          0: { label: '待审核', background: '#A7ADBD' },
          1: { label: '审核通过', background: '#40A23F' }
        }
      }
    },
    fields: () => {
      return {
        type: 'fidd',
        fields: [
          {name: 'allHits', way: 'text'},
          {name: 'appHits', way: 'text'},
          {name: 'pcHits', way: 'text'},
        ]
      }
    },
    name: () => {
      return {
        type: 'preview',
        config: {
          field: 'status',
          pageUrl: `${serveUrl['meeting']}`,
          previewName: 'MsMeetingPreview'
        }
      }
    },
    startTime: () => {
      return {
        type: 'formatTime',
        cFormat: '{y}-{m}-{d} {h}:{i}'
      }
    }
  },
  headerShow: {
    fields: () => {
      return {
        type: 'dropdown',
        icon: '',
        options: [
          { label: '默认排序', value: null },
          { label: '按总点击量', value: 1 },
          { label: '按APP点击量', value: 2 },
          { label: '按PC点击量', value: 3 }
        ],
        operation: 'query',
        params: 'sortType'
      }
    },
    status: () => {
      return {
        type: 'dropdown',
        icon: 'icon-funnel',
        options: [
          { label: '全部', value: null },
          { label: '待审核', value: 0 },
          { label: '审核通过', value: 1 }
        ],
        operation: 'query'
      }
    }
  }
}

export default scopeConfig;
