const domConfig = {
  listSearch: [
    {
      label: '会议名称',
      placeholder: '请输入',
      model: 'name',
      component: 'ms-input'
    },
    {
      label: '会议开始时间',
      placeholder: '请选择时间段',
      model: 'createTime',
      component: 'ms-picker',
      type: 'daterange',
      width: '240px'
    },
    {
      label: '专题',
      placeholder: '请选择',
      model: 'categoryId',
      component: 'ms-category-cascader',
      config: {
        moduleName: 'meeting'
      }
    }
  ],
  tableHeader: [
    { label: 'ID', property: 'id', sortable: true, width: '60' },
    { label: '标题', property: 'name', width: '250'},
    { label: '总/APP/PC', property: 'fields', width: '120' },
    { label: '会议开始时间', property: 'startTime', sortable: true, width: '130' },
    { label: '创建人', property: 'createdName' },
    { label: '状态', property: 'status'}
  ],
  tableButtons: [
    {
      label: '编辑',
      icon: '',
      role: '',
      operation: 'edit',
      component: 'meeting-operation',
      way: 'page',
      type: 'primary',
      path: 'meeting-operation',
      params: ['id'],
      identify: 'edit'
    },
    {
      label: '',
      icon: '',
      role: '',
      operation: 'editStatus',
      component: 'msMeetingOperation',
      way: 'dialog',
      field: 'status',
      rule: {
        1: { label: '去审', type: '', operation: 'toreview' },
        0: { label: '审核', type: 'success', operation: 'approval' }
      },
      identify: 'status'
    },
    {
      label: '更多',
      operation: 'more',
      type: 'info',
      children: [
        // {
        //   label: '推荐',
        //   way: 'dialog',
        //   operation: 'recommend',
        //   component: 'msGuiderRecommend',
        //   title: '推荐文章',
        //   identify: 'recommend'
        // },
        // {
        //   label: '固顶',
        //   way: 'dialog',
        //   operation: 'sticky',
        //   component: 'msGuiderRecommend',
        //   title: '固定文章',
        //   identify: 'sticky'
        // },
        {
          label: '删除',
          way: 'dialog',
          operation: 'delete',
          component: 'msMeetingOperation',
          identify: 'single_delete'
        },
      ]
    }
  ],
  soltButtons: [
    { 
      label: '手工添加', 
      type: 'primary', 
      icon: 'el-icon-plus',
      operation: 'created',
      component: 'meeting-operation',
      way: 'page',
      path: 'meeting-operation',
      params: ['id'],
      identify: 'created'
    },
    { 
      label: '批量审核', 
      type: 'primary',
      operation: 'approval',
      component: 'msMeetingOperation',
      way: 'batch',
      identify: 'batch_approval'
    },
    { 
      label: '批量去审', 
      type: 'primary',
      operation: 'toreview',
      component: 'msMeetingOperation',
      way: 'batch',
      identify: 'batch_toreview'
    },
    { 
      label: '批量设置分类',
      title: '批量设置会议分类',
      type: 'success',
      operation: '',
      component: 'msMeetingCategory',
      way: 'batch',
      width: '70%',
      identify: 'batch_category'
    },
    { 
      label: '批量删除',
      type: 'info',
      icon: 'el-icon-close',
      operation: 'delete',
      component: 'msMeetingOperation',
      way: 'batch',
      identify: 'batch_delete'
    }
  ]
}

export default domConfig;
