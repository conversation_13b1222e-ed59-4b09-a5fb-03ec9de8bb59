<template>
  <ms-operation-dialog>
    <template slot="content">
		<p class="content-top">
			<span class="top-label"><i class="el-icon-document"></i></span><span class="top-content">会议名称: {{model.name}}</span>
		</p>
		<p class="content-top">
			<span class="top-label"><i class="el-icon-paperclip"></i></span><span class="top-content">会议地址: {{model.webLink}}</span>
		</p>
		<p class="content-top">
			<span class="top-label"><i class="el-icon-time"></i></span><span class="top-content">会议开始时间: {{model.startTime | parseTime('{y}-{m}-{d}')}}</span>
		</p>
        <div v-html="PUBLIC_Methods.unexcapeHtml(model.content)"></div>
    </template>
  </ms-operation-dialog>
</template>

<script>
export default {
	name: "ms-meeting-preview",
	data () {
		return {
		}
	},
	props: {
		model: Object,
		operation: String
    }
}
</script>

<style scoped>
	.content-top {
		margin-bottom: 10px;
		font-size: 14px;
		text-align: left;
	}
	.content-top .top-label {
		font-weight: bold;
		margin-right: 12px;
		display: inline-block;
	}
</style>
