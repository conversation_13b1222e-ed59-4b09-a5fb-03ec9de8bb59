<template>
	<ms-table
		:currentPage="searchParams.pageIndex"
		:loading="loading"
		:operationButtons="domConfig.tableButtons"
		:pageSize="searchParams.pageSize"
		:scopeConfig="scopeConfig.show"
    :scopeHeader="scopeConfig.headerShow"
		:tableData="list"
		:tableHeader="domConfig.tableHeader"
		:total="total"
    :showSelection="true"
		@current-change="current_change"
		@operation-change="operation_change"
		@size-change="size_change"
    @header-operation="header_operation"
    @handleSelectionChange="handleSelectionChange"
    :rowColor="rowColor"
	>
    <!-- 列表搜索去区域插槽 -->
		<template slot="ms-table-header">
			<div class="slot-search">
				<template v-for="(searchItem, key) in domConfig.listSearch">
					<component
						:index="searchItem.index || ''"
						:is="searchItem.component"
						:key="key"
            :width="searchItem.width || '150px'"
            :model.sync="searchParams[searchItem.model]"
            :label="searchItem.label"
						:operation="searchItem.operation || ''"
						:options="searchItem.options || []"
						:placeholder="searchItem.placeholder || ''"
						:type="searchItem.type || ''"
            :multiple="searchItem.multiple"
            :disabled="searchItem.disabled" 
            :projectName="searchParams.projectName"
					></component>
				</template>
        <div class="inlineBlock">
          <el-button @click="handleClick('query')" type="primary" plain icon="el-icon-search">查询</el-button>
          <el-button @click="handleClick('reset')">重置</el-button>
        </div>
			</div>
      <div class="slot-button-article clearfix">
				<template v-for="(item, index) in domConfig.soltButtons">
					<el-button :key="index" 
                     :type="item.type" 
                     size="mini"
                     :icon="item.icon"
                     :disabled="item.roleDisabled"
                     @click="operation_change({operation: item})"
                     plain>{{ item.label }}</el-button>
				</template>
			</div>
      <ms-right-dialog :visible.sync="r_dialog" :width="dialogWidth" :title="dialogTitle">
        <component
          :is="dialogComponent"
          :model="scopeInfo"
          :operation="dialogOperation"
          @close="r_dialog = !r_dialog"
          @up-date="init"
          v-if="r_dialog"
        ></component>
      </ms-right-dialog>
      <el-dialog :visible.sync="dialog" :width="dialogWidth" :title="dialogTitle">
        <component
          :is="dialogComponent"
          :model="scopeInfo"
          :operation="dialogOperation"
          @close="dialog = !dialog"
          @up-date="init"
          v-if="dialog"
        ></component>
      </el-dialog>

		</template>
    
	</ms-table>
</template>

<script>
import tableMixins  from "../../common/mixins/table"
import domConfig from "./data/dom-config"
import scopeConfig from "./data/scope-config"
import MsPostInfo from "./tool/ms-post-info"
import postAprroveOpera from "./tool/post-aprrove-opera"
import msPostRecommend from "./tool/ms-post-recommend"
import msPostSticky from "./tool/ms-post-sticky"
import msPostProjectSearch from '@/components/MsCommon/ms-postProject-search'

export default {
  name: "ms-post-manage",
  mixins: [tableMixins],
  data () {
    return {
      domConfig: domConfig,
      scopeConfig: scopeConfig
    }
  },
  components: {
    MsPostInfo,
    postAprroveOpera,
    msPostRecommend,
    msPostSticky,
    msPostProjectSearch
  },
  methods: {
    rowColor({row}) {
      if (row.recommend === 1) {
        return 'recommend-row';
      } else if (row.sticky === 1) {
        return 'sticky-row';
      } else if (row.recommend !== 1 && row.sticky !== 1 && row.pcVisible === 0) {
        return 'pc-visible'
      }
      return '';
    },
    init () {
      this.loading = true;
      this.dialog = false;
      if (this.searchParams.createTime) {
        this.searchParams.createdStartTime = this.searchParams.createTime[0] || "";
        this.searchParams.createdEndTime = this.searchParams.createTime[1] || "";
      }
      let searchParams = {...this.searchParams}
      this.api.medsciPostPage(searchParams).then(response => {
        this.loading = false
        this.total = response.totalSize || 0;
        if (response.status == 200) {
          this.list = response.data.map(v => {
            return {
              ...v
            }
          })
        } else {
          this.list = []
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
      }).catch(() => this.loading = false)
    },
    operation_change_module(val) {
      switch (val.operation.way) {
        case "batch-confirm":
          if (this.selectList.length > 0) {
            let ids = this.selectList.map(v => {
              return v.id
            }) 
            this.postsOpear(ids, val.operation.operation)
          } else {
            this.$message.warning('请选择至少一条数据')
          }
          break;
        case "confirm":
          this.postsOpear([val.model.id], val.operation.operation)
          break;
        default: break;
      }
    },
    postsOpear(ids, operation) {
      this.$confirm(`是否${operation === 'approval' ? '审核' : operation === 'toreview' ? '去审' : operation === 'delete' ? '删除' : ''}帖子`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let params = {
          "userId": this.info.userId,
          "username": this.info.userName,
          "remark": "",
          "type": operation === 'approval' ? 1 : operation === 'toreview' ? 2 : 3,
          "ids": ids
        }
        this.api.medsciBanchPosts(params).then(response => {
          if (response.status === 200) {
            this.init()
          }
        })
      }).catch(() => {});
    },
  }
};
</script>
