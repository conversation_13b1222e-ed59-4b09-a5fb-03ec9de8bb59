<template>
      <el-form
        ref="submitRef"
        class="rule-form comment-form"
        label-width="80px"
        v-loading="getLoading"
      >
        <el-row>
          <el-col :span="12">
            <el-form-item label="创建人:">
              <span>{{submitData.createdName}}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="创建时间:">
              <span class="comment-info" v-text="submitData.createdTime"></span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="项目名称:">
              <span class="comment-info" v-text="submitData.projectName"></span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="标题:">
              <span class="comment-info" v-text="submitData.title"></span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="帖子正文:">
              <span class="comment-info" v-html="submitData.content"></span>
            </el-form-item>
          </el-col>
        </el-row>
        <div v-if="submitData.status!=2">
           <el-row>
            <el-col :span="24">
              <el-form-item label="审核:">
                <el-radio-group v-model="checkDetail.status">
                  <el-radio :label="1">通过审核</el-radio>
                  <el-radio :label="2">去除审核</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="说明:">
                <el-input type="textarea" v-model="checkDetail.remark"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item>
                <el-button @click="cancel">取消</el-button>
                <el-button type="primary" @click="confirm">确认</el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-form>
</template>

<script>
import { mapGetters } from "vuex";
export default {
  name: "ms-post-info",
  data() {
    return {
      getLoading: false,
      checkDetail: {
        status: ''
      },
    };
  },
  computed: {
    ...mapGetters(["info"])
  },
  props: {
    model: Object,
    operation: String
  },
  created() {
    this.getDetail();
  },
  methods: {
    getDetail() {
      this.submitData = this.model
    },
    cancel() {
      this.$emit("close");
    },
    confirm() {
      let params = {
        "ids": [this.model.id],
        "remark": this.checkDetail.remark,
        "type": this.checkDetail.status,
        "userId": this.info.userId,
        "username": this.info.userName
      }
      this.api
        .medsciBanchPosts(params)
        .then(response => {
          if (response.status === 200) {
            this.PUBLIC_Methods.apiNotify(
              response.message || "请求成功",
              "success"
            );
          } else {
            this.PUBLIC_Methods.apiNotify(
              response.message || "请求出错",
              "warning"
            );
          }
          this.$emit("up-date");
          this.$emit("close");
          this.loading = false;
        })
        .catch(() => {
          this.loading = false;
          this.$emit("close");
        });
    }
  }
};
</script>

<style>
.comment-form .el-form-item__label {
  color: #8b949d;
}
.comment-form .comment-info {
  color: #606266;
}
</style>
