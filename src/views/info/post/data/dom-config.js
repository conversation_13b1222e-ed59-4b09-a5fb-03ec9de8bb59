const domConfig = {
  listSearch: [
    {
      label: '项目名称',
      placeholder: '请选择',
      model: 'sortProjectId',
      component: 'ms-postProject-search'
    },
    {
      label: '帖子标题',
      placeholder: '请输入帖子标题',
      model: 'title',
      component: 'ms-input',
      width: '130px'
    },
    {
      label: '创建人',
      placeholder: '请选择',
      model: 'createdBy',
      component: 'ms-createby-search'
    },
    {
      label: '创建时间',
      placeholder: '请选择时间段',
      model: 'createTime',
      component: 'ms-picker',
      format: 'yyyy-MM-dd HH:mm:ss',
      type: 'datetimerange',
      width: '240px'
    }
  ],
  tableHeader: [
    { label: 'ID', property: 'id', width: '60' },
    { label: '项目名称', property: 'projectName', width: '80' },
    { label: '标题', property: 'title', width: '160'},
    { label: 'PV/赞/评/藏', property: 'fields', width: '100' },
    { label: '状态', property: 'status', width: '100' },
    { label: '创建人', property: 'createdName', width: '130' },
    { label: '创建时间', property: 'createdTime', sortable: true, width: '130' }
  ],
  tableButtons: [
    {
      label: '查看',
      icon: '',
      role: '',
      operation: 'edit',
      component: 'MsPostInfo',
      position: 'right',
      way: 'dialog',
      type: 'primary',
      title: '查看详情',
      width: '45%'
    },
    {
      icon: '',
      role: '',
      operation: 'edit',
      component: 'postAprroveOpera',
      title: "审核操作",
      way: 'dialog',
      field: 'status',
      rule: {
        0: { label: '审核', type: 'success' },
        3: { label: '审核', type: 'success' },
        1: { label: '去审', type: 'info' },
      },
      showCallback: (val) => {
        if (val.status === 2) {
          return false
        } else {
          return true
        }
      }
    },
    {
      label: '更多',
      operation: 'more',
      type: 'info',
      children: [
        {
          label: '推荐',
          way: 'dialog',
          operation: 'recommend',
          component: 'msPostRecommend',
          title: '推荐帖子',
          identify: 'recommend'
        },
        {
          label: '固顶',
          way: 'dialog',
          operation: 'sticky',
          component: 'msPostSticky',
          title: '固定帖子',
          identify: 'sticky'
        }
      ],
      showCallback: (val) => {
        if (val.status === 2) {
          return false
        } else {
          return true
        }
      }
    }
  ],
  soltButtons: [
    {
      label: '批量审核',
      type: 'primary',
      operation: 'approval',
      way: 'batch-confirm',
    },
    {
      label: '批量去审',
      type: 'primary',
      operation: 'toreview',
      way: 'batch-confirm',
    },
    {
      label: '批量删除',
      type: 'info',
      icon: 'el-icon-close',
      operation: 'delete',
      way: 'batch-confirm',
    },
  ]
}

export default domConfig;
