import serveUrl from '@/store/data/serveUrl.js'
const scopeConfig = {
  show: {
    status: () => {
      return {
        type: 'status',
        rule: {
          0: { label: '待审核', background: '#A7ADBD' },
          1: { label: '审核通过', background: '#40A23F' },
          2: { label: '用户删除', background: '#F56C6C' },
          3: { label: '未通过', background: '#ccc' },
        }
      }
    },
    fields: () => {
      return {
        type: 'fidd',
        fields: [
          {name: 'pvCount', way: 'text'},
          {name: 'likeCount', way: 'text'},
          {name: 'commentCount', way: 'text'},
          {name: 'collectCount', way: 'text'}
        ]
      }
    },
    title: () => {
      return {
        type: 'preview',
        config: {
          field: 'status',
          noCheck: true,
          pageUrl: `${serveUrl['post']}`
        }
      }
    }
  },
  headerShow: {
    status: () => {
      return {
        type: 'dropdown',
        icon: 'icon-funnel',
        options: [
          { label: '全部', value: null },
          { label: '待审核', value: 0 },
          { label: '审核通过', value: 1 },
          { label: '用户删除', value: 2 },
          { label: '未通过', value: 3 }
        ],
        operation: 'query'
      }
    }
  }
}

export default scopeConfig;
