<template>
  <section class="form-tab" v-loading="getLoading">
    <ms-live-content ref="contentTemp" @handle-click="handle_click"></ms-live-content>
    <!-- 弹出层 -->
    <el-dialog :visible.sync="dialog" 
               closeable 
               :close-on-click-modal="false"
               :width="dialogWidth || '50%'"
               :title="dialogTitle">
      <component :is="dialogComponent" 
                   :model="dialogInfo" 
                   :operation="dialogOperation" 
                   :isNeedApi="false"
                   @close="closeDialog" 
                   @up-date="init" 
                   @return-data="returnData"
                   v-if="dialog"></component>
    </el-dialog>
    <!-- 提交按钮 -->
    <footer-tool-bar v-loading="buttonLoading">
      <template slot="tool-content">
        <template v-if="dataId">
          <el-popover placement="top" width="500" trigger="manual" v-model="popVisible">
            <el-row :gutter="10" v-loading="popLoading">
              <el-col :span="18" class="live-address">
                <div class="label">观看地址</div>
                <el-input v-model="livePopObj.liveUrl" style="margin: 15px 0;">
                  <el-button slot="append" @click="openUrl(livePopObj.liveUrl)">打开</el-button>
                </el-input>
                <el-button type="primary" @click="copy_text($event,livePopObj.liveUrl)">复制</el-button>
              </el-col>
              <el-col :span="6" class="live-img">
                <div class="label">手机观看二维码</div>
                <div class="img">
                  <img :src="livePopObj.imgCode" v-if="livePopObj.imgCode"/>
                </div>
              </el-col>
            </el-row>
            <el-button slot="reference" @click="openLivePop">预览</el-button>
          </el-popover>
          <el-button plain v-show="dataId" @click="info_operation('approval')">{{submitData.auditStatus === 1 ? '去审' : '审核'}}</el-button>
        </template>
        <el-button type="primary" @click="info_operation('save')">保存</el-button>
        <el-button type="info" @click="info_operation('back')">返回</el-button>
      </template>
    </footer-tool-bar>
  </section>
</template>

<script>
import msLiveContent from './tool/ms-live-content'
import liveMixin from "./live-mixin"
import msLiveFlow from "./tool/ms-live-flow"
import msLiveRecommend from "./tool/ms-live-recommend"
import msLiveSponsor from "./tool/ms-live-sponsor"
import msLiveDisplay from "./tool/ms-live-display"
import FooterToolBar from '@/components/ToolBar/footer-tool-bar'
import { mapGetters } from "vuex";
export default {
  name: "live-operation",
  mixins: [liveMixin],
	data () {
		return {
      dataId: this.$route.query.id ? this.$route.query.id : 0,
      buttonLoading: false,
      getLoading: false,
      popVisible: false,
      popLoading: false,

      submitData: {},
      dialogComponent: '',
      dialog: false,
      dialogTitle: '',
      dialogOperation: '',
      dialogWidth: '',
      dialogInfo: {},

      livePopObj: {
        liveUrl: '',
        imgCode: ''
      }
		}
  },
  created() {
    this.init()
  },
  components: {
    msLiveContent,
    msLiveFlow,
    msLiveRecommend,
    msLiveSponsor,
    msLiveDisplay,
    FooterToolBar
  },
  computed: {
    ...mapGetters(["info"])
  },
  methods: {
    init() {
      let id = this.dataId
      this.dialog = false;
      this.$store.dispatch('SetSubmitData', this.submitData)
      if(id !== 0) {
        this.getLoading = true;
        this.api.getLiveDetails({id: id}).then( response => {
          this.getLoading = false;
          if(response.status === 200) {
            let res = response.data
            let categoryList = []
            if (res.categoryList  && res.categoryList .length > 0) {
              categoryList = res.categoryList.map(v => {
                return {
                  categoryId: v.categoryId,
                  categoryName: v.categoryName
                }
              })
            }
            this.submitData = {
              ...res,
              liveTime: res.liveStartTime && res.liveEndTime ? [res.liveStartTime, res.liveEndTime] : null,
              copyMailbox: res.copyMailbox ? res.copyMailbox.split(";") : [],
              categoryList: categoryList
            }
          } else {
            this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
          }
          this.$store.dispatch('SetSubmitData', this.submitData)
        }).catch(() => {
          this.getLoading = false;
          this.$store.dispatch('ClearSubmitData')
        })
      } else {
        this.$router.back()
      }
    },
    info_operation(val) {
      switch (val) {
        case 'save': 
          this.$refs['contentTemp'].validateData(this.saveLive)
          break;
        case 'back':
          this.$router.back();
          break;
        case 'approval':
          var params = {
            way: 'dialog',
            operation: this.submitData.auditStatus === 1 ? 'toreview' : 'approval',
            component: 'msLiveOperation',
            data: {
              id: this.dataId,
              auditStatus: this.submitData.auditStatus
            }
          }
          this.handle_click(params)
          break;
        default: break;
      }
    },
    saveLive(params) {
      this.buttonLoading = true;
      this.api.saveLive(params).then(response => {
        this.buttonLoading = false
        if(response.status === 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
          this.$router.back()
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
      }).catch(() => this.buttonLoading = false)
    },
    handle_click(val) {
      switch (val.way) {
        case 'dialog': 
          this.dialog = true;
          this.dialogInfo = val.data
          this.dialogOperation = val.operation;
          this.dialogComponent = val.component;
          this.dialogTitle = val.title;
          this.dialogWidth = val.width;
          break;
        default: break;
      }
    },
    returnData(val) {
      this.dialog = false;
      this.$refs['contentTemp'].setEndTime(val)
    },
    closeDialog(val) {
      this.dialog = !this.dialog
      if (val) {
        if (val.operation === 'recommend') {
          this.submitData.recommend = 0
        } else if (val.operation === 'sticky') {
          this.submitData.sticky = 0
        }
        this.$store.dispatch('SetSubmitData', this.submitData)
      }
    },
    openLivePop() {
      let liveUrl = this.$refs.contentTemp.submitData.liveAddress
      if (liveUrl && !this.popVisible) {
        this.popVisible = true
        this.popLoading = true
        let params = {
          liveId: this.dataId,
          liveAddress: liveUrl
        }
        this.api.createLiveQRCode(params).then(response => {
          this.popLoading = false
          if (response.status === 200 && response.data) {
            this.livePopObj.liveUrl = liveUrl
            this.livePopObj.imgCode = response.data.liveQrCodeUrl
          }
        })
      } else {
        this.popVisible = false
      }
    },
    openUrl(url) {
      window.open(url, '_blank')
    },
    copy_text(e, data) {
      let input = document.createElement("input");
      input.value = data
      document.body.appendChild(input)
      input.select()
      document.execCommand("Copy")
      this.$message({
        message: '复制成功',
        type: 'success',
        duration: '2000'
      });
      input.parentNode.removeChild(input)
    }
  }
}
</script>

<style scoped>
  .live-address, .live-img .label{
    font-size: 12px;
  }
  .live-img .label {
    text-align: center;
  }
  .live-img .img {
    padding: 10px;
    text-align: center;
    height: 100px
    
  }
  .live-img .img img{
    width: auto;
    height: 100%;
  }
</style>
