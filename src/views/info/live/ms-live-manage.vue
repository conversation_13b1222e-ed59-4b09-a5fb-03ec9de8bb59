<template>
	<ms-table
		:currentPage="searchParams.pageIndex"
		:loading="loading"
		:operationButtons="domConfig.tableButtons"
		:pageSize="searchParams.pageSize"
		:scopeConfig="scopeConfig.show"
    :scopeHeader="scopeConfig.headerShow"
		:tableData="list"
		:tableHeader="domConfig.tableHeader"
		:total="total"
		@current-change="current_change"
		@operation-change="operation_change"
		@size-change="size_change"
    @header-operation="header_operation"
	>
    <!-- 列表搜索去区域插槽 -->
		<template slot="ms-table-header">
			<div class="slot-search">
				<template v-for="(searchItem, key) in domConfig.listSearch">
					<component
						:index="searchItem.index || ''"
						:is="searchItem.component"
						:key="key"
            :width="searchItem.width || '150px'"
            :model.sync="searchParams[searchItem.model]"
            :label="searchItem.label"
						:operation="searchItem.operation || ''"
						:options="searchItem.options || []"
						:placeholder="searchItem.placeholder || ''"
						:type="searchItem.type || ''"
					></component>
				</template>
        <div class="inlineBlock">
          <el-button @click="handleClick('query')" type="primary" plain icon="el-icon-search">查询</el-button>
          <el-button @click="handleClick('reset')">重置</el-button>
        </div>
			</div>
      <div class="slot-button">
        <template v-for="(item, index) in domConfig.soltButtons">
          <el-button :key="index" :type="item.type" :icon="item.icon" @click="operation_change({operation: item})" plain>{{ item.label }}</el-button>
        </template>
      </div>
			<el-dialog :visible.sync="dialog" 
                 closeable 
                 show-close
                 :close-on-click-modal="false"
                 :width="dialogWidth"
                 :title="dialogTitle">
				<component :is="dialogComponent" 
                   :model="scopeInfo" 
                   :operation="dialogOperation" 
                   @close="dialog = !dialog" 
                   @up-date="init" 
                   v-if="dialog"></component>
			</el-dialog>
		</template>
	</ms-table>
</template>

<script>
import liveMixin from "./live-mixin"
import tableMixins  from "../../common/mixins/table"
import msLecturerSearch from '@/components/MsCommon/ms-lecturer-search'
export default {
  name: "ms-live-manage",
  mixins: [tableMixins,liveMixin],
  data () {
    return {
      // searchParams: { // => 列表查询传参
      //   liveTitle: "",
      //   columnId: null,
      //   teacherId: null,
      //   liveStatus: null,
      //   auditStatus: null
      // } 
    }
  },
  components: {
    msLecturerSearch
  },
  methods: {
    apiInit (params) {
      let searchParams = {...params}
      this.api.getLivePage(searchParams).then(response => {
        this.loading = false
        this.total = response.totalSize || 0;
        this.list = response.data || []
        if (response.status !== 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
      }).catch(() => this.loading = false)
    },
    operation_change (val) {
      switch (val.operation.way) {
        case "dialog":
          this.dialog = true;
          this.scopeInfo = val.model ? val.model : {};
          this.dialogOperation = val.operation.operation;
          this.dialogComponent = val.operation.component;
          this.dialogTitle = val.operation.title;
          this.dialogWidth = val.operation.width || '40%';
          break;
        case "page":
          var paramsObjs = {}
          if (val.operation.params) {
            val.operation.params.forEach(item => {
              let keyName = item.keyName || item
              let valName = item.valName || item
              paramsObjs[keyName] = val.model ? val.model[valName] : ""
            });
          }
          this.$router.push({
            path: val.operation.path,
            query: {
              operation: val.operation.operation,
              component: val.operation.component,
              ...paramsObjs
            }
          });
          break;
        case "delete": 
          this.$confirm('此操作将永久删除直播信息，是否继续', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.api.deleteLive({id: val.model.id}).then(response => {
              if(response.status === 200) {
                this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
                this.init()
              } else {
                this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
              }
            })
          }).catch(() => {})
          break;
        default: break;
      }
    },
  }
};
</script>
