<template>
  <ms-operation-dialog>
    <template slot="content">
      <el-row v-loading="getLoading" style="padding-bottom: 30px;">
        <el-col :span="showBase ? 11 : 24">
          <el-table :data="list" 
                style="width: 100%"
                ref="sponsorRef"
                highlight-current-row
                @current-change="handleCurrentChange">
            <el-table-column label="企业名称" align="center" min-width="200px">
              <template slot-scope="{row}">
                <el-input v-model="row.companyName" disabled></el-input>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="140px" align="center">
              <template slot-scope="{row}">
                <div class="table-operation">
                  <el-button @click="handleCurrentChange(row)" type="primary" class="scope-btn">查看</el-button>
                  <el-button @click="delete_sponsor(row)" type="danger" class="scope-btn">删除</el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
          <button class="add-flow" v-show="!isAddCompany"  @click="isAddCompany = true;" type="button">
            <i class="el-icon-plus"></i> 添加赞助企业
          </button>
          <el-row v-show="isAddCompany" class="add-row">
            <el-col :span="14"><msCompanySearch :model.sync="createCompany" :multiple="true"></msCompanySearch></el-col>
            <el-col :span="10" style="text-align: right;">
              <el-button type="primary" @click="confirmCreate" :loading="loading">确定</el-button>
              <el-button plain @click="isAddCompany = false">取消</el-button>
            </el-col>
          </el-row>
        </el-col>
        <el-col :span="12" :offset="1" v-if="showBase">
          <el-table :data="textList" 
                style="width: 100%"
                v-loading="rightLoading">
            <el-table-column label="显示文本" align="center" min-width="150px">
              <template slot-scope="{row}">
                <el-input v-model="row.text" placeholder="请输入文本"></el-input>
              </template>
            </el-table-column>
            <el-table-column label="超链接" align="center" min-width="150px">
              <template slot-scope="{row}">
                <el-input v-model="row.textLink" placeholder="请输入超链接"></el-input>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="70px" align="center">
              <template slot-scope="{row, $index}">
                <div class="table-operation">
                  <el-button @click="delete_sponsor_detail(row, $index)" type="danger" class="scope-btn">删除</el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
          <button class="add-flow"  @click="add_introduce" type="button">
            <i class="el-icon-plus"></i> 添加企业介绍
          </button>
        </el-col>
      </el-row>
    </template>
    <template slot="footer">
      <el-button @click="submitForm"
                 :loading="loading"
                 size="mini"
                 type="primary"
                 v-if="showBase">确 定</el-button>
      <el-button @click="$emit('close')"
                 size="mini">取 消</el-button>
    </template>
  </ms-operation-dialog>
</template>

<script>
import msCompanySearch from '@/components/MsCommon/ms-company-search'
import { mapGetters } from "vuex";
export default {
	name: "ms-live-flow",
	data () {
		return {
      getLoading: false,
      loading: false,
      textLoading: false,
      rightLoading: false,
      list: [],
      textList: [],
      createCompany: [],
      isAddCompany: false, 
      sponsorshipId: 0,

      showBase: false
		}
	},
	props: {
		model: Object
  },
  components: {
    msCompanySearch
  },
  computed: {
    ...mapGetters(["info"])
  },
  created() {
    this.init()
  },
	methods: {
    init() {
      this.getLoading = true;
      this.showBase = false;
      this.createCompany = [];
      this.list = [];
      this.textList = [];
      this.isAddCompany = false;
      this.api.getLiveSponsorshipEnterprisesList({liveId: this.model.id}).then( response => {
        this.getLoading = false;
        if(response.status === 200 && response.data) {
          this.list = response.data.map((v) => {
            return {
              ...v.getLiveSponsorshipEnterprisesResponse,
              sponsorDetail: v.getLiveSponsorshipEnterprisesDetailsResponseList
            }
          })
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '查询失败', 'warning')
        }
      }).catch(() => this.getLoading = false)
    },
    confirmCreate() {
      let params = []
      if (this.createCompany.length > 0) {
        params = this.createCompany.map(v => {
          return {
            liveId: this.model.id,
            userId: this.info.userId,
            username: this.info.userName,
            companyId: v
          }  
        })
        this.loading = true;
        this.api.addLiveSponsorshipEnterprises(params).then(response => {
          this.loading = false;
          if (response.status === 200) {
            this.init()
          } else {
            this.PUBLIC_Methods.apiNotify(response.message || '添加失败', 'warning')
          }
        })
      } else {
        this.PUBLIC_Methods.apiNotify('请选择公司信息', 'warning')
      }
    },
    delete_sponsor(row) {
      this.getLoading = true;
      this.api.deleteLiveSponsorshipEnterprises({id: row.id}).then(response => {
        this.getLoading = false;
        if (response.status === 200) {
          this.init()
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '删除失败', 'warning')
        }
      })
    },
    handleCurrentChange(val) {
      this.textList = []
      if (val) {
        this.showBase = true
        this.sponsorshipId = val.id
        this.textList = val.sponsorDetail || []
      }
    },
    add_introduce() {
      this.textList.push({
        sponsorshipId: this.sponsorshipId,
        userId: this.info.userId,
        username: this.info.userName,
        id: 0,
        text: '',
        textLink: ''
      })
    },
    delete_sponsor_detail(row,index) {
      if (row.id === 0) {
        this.textList.splice(index, 1)
      } else {
        this.rightLoading = true;
        this.api.deleteLiveSponsorshipEnterprisesDetail({id: row.id}).then(response => {
          this.rightLoading = false
          if (response.status === 200) {
            this.textList.splice(index, 1)
          } else {
            this.PUBLIC_Methods.apiNotify(response.message || '删除失败', 'warning')
          }
        })
      }
    },
    submitForm() {
      let params = this.textList.map(v => {
        return v
      })
      this.loading = true
      this.api.saveLiveSponsorshipEnterprisesDetails(params).then(response => {
        if(response.status === 200) {
          this.init()
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
        this.loading = false
      }).catch( () => {
        this.loading = false;
        this.$emit('close')
      })
    }
	}
}
</script>

<style lang="scss" scoped>
  .add-row {
    margin: 10px 0 30px;
  }
  .add-flow {
    cursor: pointer;
    margin: 10px 0 30px;
    width: 100%;
    height: 30px;
    line-height: 28px;
    border: 1px dashed #DCDFE6;
    border-radius: 4px;
    text-align: center;
    font-size: 12px;
    color: #333;
    outline: none;
    margin-top: 10px;
    background-color: rgba(0,0,0, 0);
  }
</style>
