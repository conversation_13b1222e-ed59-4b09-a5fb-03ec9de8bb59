<template>
  <ms-operation-dialog>
    <template slot="content">
      <el-table :data="list" 
                v-loading="getLoading" 
                style="width: 100%">
        <el-table-column label="推荐视频" align="center" min-width="200px">
          <template slot-scope="{row}">
            <msVideoSearch :model.sync="row.videoTitle" :modelName.sync="row.videoId"></msVideoSearch>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="70px" align="center">
          <template slot-scope="{row, $index}">
            <div class="table-operation">
              <el-button @click="delete_flow(row, $index)" type="danger" class="scope-btn">删除</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <button class="add-flow" @click="add_flow" type="button">添加推荐视频</button>
    </template>
    <template slot="footer">
      <el-button @click="submitForm"
                 :loading="loading"
                 size="mini"
                 type="primary">确 定</el-button>
      <el-button @click="$emit('close')"
                 size="mini">取 消</el-button>
    </template>
  </ms-operation-dialog>
</template>

<script>
import msVideoSearch from '@/components/MsCommon/ms-video-search'
import { mapGetters } from "vuex";
export default {
	name: "ms-live-flow",
	data () {
		return {
      getLoading: false,
      loading: false,
      list: []
		}
	},
	props: {
		model: Object
  },
  components: {
    msVideoSearch
  },
  computed: {
    ...mapGetters(["info"])
  },
  created() {
    this.init()
  },
	methods: {
    init() {
      this.getLoading = true;
      this.api.getLiveRecommendedVideoGlList({liveId: this.model.id}).then( response => {
        this.getLoading = false;
        if(response.status === 200 && response.data) {
          this.list = response.data
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '查询失败', 'warning')
        }
      }).catch(() => this.getLoading = false)
    },
    add_flow() {
      this.list.push({
        id: 0,
        liveId: this.model.id,
        userId: this.info.userId,
        username: this.info.userName,
        videoId: 0
      })
    },
    delete_flow(row,index) {
      if (row.id) {
        this.api.deleteLiveRecommendedVideo({id: row.id}).then(response => {
          if (response.status === 200 && response.data) {
            this.list.splice(index, 1)
          } else {
            this.PUBLIC_Methods.apiNotify(response.message || '删除失败', 'warning')
          }
        })
      } else {
        this.list.splice(index, 1)
      }
    },
    submitForm() {
      let params = this.list.map(v => {
        return {
          id: v.id,
          liveId: v.liveId,
          userId: v.userId,
          username: v.username,
          videoId: v.videoId
        }
      })
      this.loading = true
      this.api.saveLiveRecommendedVideo(params).then(response => {
        if(response.status === 200) {
          this.$emit('up-date')
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
        this.loading = false
      }).catch( () => {
        this.loading = false;
        this.$emit('close')
      })
    }
	}
}
</script>

<style lang="scss" scoped>
  .add-flow {
    cursor: pointer;
    margin: 10px 0 30px;
    width: 100%;
    height: 30px;
    line-height: 28px;
    border: 1px dashed #DCDFE6;
    border-radius: 4px;
    text-align: center;
    font-size: 12px;
    color: #333;
    outline: none;
    margin-top: 10px;
    background-color: rgba(0,0,0, 0);
  }
</style>
