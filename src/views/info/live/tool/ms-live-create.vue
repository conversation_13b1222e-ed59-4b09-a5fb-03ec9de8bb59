<template>
  <ms-operation-dialog>
    <template slot="content">
      <el-form ref="submitRef"
               :model="submitData"
               :rules="rules"
               label-width="85px"
               class="rule-form">
        <el-row>
          <el-col :span="18" :offset="3">
            <el-form-item label="直播标题" prop="liveTitle">
              <el-input v-model="submitData.liveTitle" placeholder="请输入"></el-input>
            </el-form-item>
            <el-form-item label="直播地址" prop="liveAddress">
              <el-input v-model="submitData.liveAddress" placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </template>
    <template slot="footer">
      <el-button @click="submitForm"
                 :loading="loading"
                 size="mini"
                 type="primary">编辑详情</el-button>
      <el-button @click="$emit('close')"
                 size="mini">取 消</el-button>
    </template>
  </ms-operation-dialog>
</template>

<script> 
import { mapGetters } from "vuex";
export default {
	name: "ms-live-create",
	data () {
		return {
      loading: false,
      submitData: {
        liveTitle: "",
        liveAddress: ""
      },
      rules: {
        liveTitle: [
          { required: true, message: "请输入直播标题", trigger: 'blur' }
        ],
        liveAddress: [
          { required: true, message: "请输入直播地址", trigger: 'blur' }
        ]
      },
    }
  },
	props: {
		model: Object,
		operation: String
  },
  computed: {
    ...mapGetters(["info"])
  },
	methods: {
		submitForm() {
      this.$refs.submitRef.validate( valid => {
        if(valid) {
          let params = {
            userId: this.info.userId,
            username: this.info.userName,
            ...this.submitData
          }
          this.loading = true;
          this.api.addInitialLive(params).then( response => {
            if(response.status === 200 && response.data && response.data.id) {
              this.$router.push({
                path: 'live-manage-operation',
                query: {
                  operation: 'edit',
                  component: 'live-manage-operation',
                  id: response.data.id
                }
              });
            } else {
              this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
            }
            this.loading = false
          }).catch( () => {
            this.loading = false;
            this.$emit('close')
          })
        }
      })
    }
	}
}
</script>
