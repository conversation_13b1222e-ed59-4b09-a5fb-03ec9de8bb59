<template>
  <section>
    <el-form :model="submitData"
             ref="submitRef"
             class="rule-form info-form"
             :rules="rules"
             label-width="70px">
      <el-row :gutter="20"> 
        <el-col :span="18">
          <el-row>
            <el-col :span="12">
              <el-form-item label="直播标题" prop="liveTitle">
                <el-input v-model="submitData.liveTitle" style="width: 100%"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="讲师" prop="teacherDicRequests">
                <el-select v-model="submitData.teacherDicRequests" multiple value-key="teacherId" class="w100" disabled>
                  <el-option v-for="(item,index) in submitData.teacherDicRequests || []" 
                             :key="index"
                             :label="item.teacherName"
                             :value="item"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="直播地址" prop="liveAddress">
                <el-input v-model="submitData.liveAddress" style="width: 100%"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="直播时间">
                <el-date-picker v-model="submitData.liveTime" type="datetimerange" clearable 
                                class="w100"
                                value-format="yyyy-MM-dd HH:mm:ss"
                                start-placeholder="开始时间"
                                end-placeholder="结束时间">
                </el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="6">
              <el-form-item label="权限" prop="opened">
                <el-select v-model="submitData.opened" style="width: 100%">
                  <el-option :value="1" label="开放阅读"></el-option>
                  <el-option :value="0" label="登录阅读"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6" v-show="+submitData.opened === 0">
              <el-form-item label="积分">
                <el-input-number v-model="submitData.integralBase" class="w100" :min="0" controls-position="right"></el-input-number>
              </el-form-item>
            </el-col>
            <el-col :span="6" v-show="+submitData.opened === 0">
              <el-form-item label="原价">
                <el-input-number v-model="submitData.costPrice" class="w100" :min="0" controls-position="right"></el-input-number>
              </el-form-item>
            </el-col>
            <el-col :span="6" v-show="+submitData.opened === 0">
              <el-form-item label="现价">
                <el-input-number v-model="submitData.presentPrice" class="w100" :min="0" controls-position="right"></el-input-number>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="详情介绍" prop="details">
                <ms-editor v-model="submitData.details" :height="280"></ms-editor>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="摘要" prop="remark">
                <el-input v-model="submitData.remark" type="textarea" :rows="4" @keyup.native="summaryKeyUp = true"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <div class="editor-info" v-show="editorDisplay">
            <div class="info-li">
              <span class="label">最后更新人：</span><span>{{submitData.updatedName}}</span>
            </div>
            <div class="info-li">
              <span class="label">最后更新时间：</span><span>{{submitData.updatedTime | parseTime}}</span>
            </div>
            <div class="info-li">
              <span class="label">直播创建时间：</span><span>{{submitData.createdTime | parseTime}}</span>
            </div>
          </div>
        </el-col>
        <el-col :span="6" class="info-form-right">
          <el-row>
            <el-col :span="24">
              <el-form-item label="状态">
                <el-radio v-model="submitData.liveStatus" :label="0" style="width: 45%;margin-right: 0px">未开始</el-radio>
                <el-radio v-model="submitData.liveStatus" :label="1" style="width: 45%;margin-right: 0px">直播中</el-radio>
                <el-radio v-model="submitData.liveStatus" :label="2" style="width: 45%;margin-right: 0px">已结束</el-radio>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="显示选项">
                <el-checkbox v-model="submitData.isApp" style="width: 45%" :true-label="1" :false-label="0">APP显示</el-checkbox>
                <el-checkbox v-model="submitData.isPc" style="width: 45%" :true-label="1" :false-label="0">PC显示</el-checkbox>
                <el-checkbox v-model="submitData.isTj" style="width: 45%" :true-label="1" :false-label="0" @change="endTimeCh(submitData.isTj,'推荐结束时间', 'recommend')">推荐</el-checkbox>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="显示栏目" prop="categoryList">
                <ms-category-cascader :model.sync="submitData.categoryList" :multiple="true"></ms-category-cascader>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="模板" prop="templateId">
                <msDictionarySearch :model.sync="submitData.templateId" :modelName.sync="submitData.templateName" type="template_type"></msDictionarySearch>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="直播流程">
                <el-link type="primary" @click="dialog_open('flow')" class="link-pos">设置</el-link>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="赞助企业">
                <el-link type="primary" @click="dialog_open('sponsor')" class="link-pos">设置</el-link>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="相关推荐">
                <el-link type="primary" @click="dialog_open('recommend')" class="link-pos">设置</el-link>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="封面图片" prop="coverUrl">
                <ms-single-image v-model="submitData.coverUrl" :upFileSize="0.5"></ms-single-image>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="背景" prop="backgroundUrl">
                <ms-single-image v-model="submitData.backgroundUrl"></ms-single-image>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="抄送邮箱">
                <template v-for="(item,index) in submitData.copyMailbox">
                  <el-input v-model="submitData.copyMailbox[index]" :key="index" style="margin-top: 5px;">
                    <el-button slot="append" icon="el-icon-delete" @click="deleteMail(index)" v-if="index !== 0"></el-button>
                  </el-input>
                </template>
                <button class="add-mail" @click="addMail" type="button">添加邮箱</button>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="客户名称" prop="cover">
                <msCompanySearch :model.sync="submitData.customerId" :modelName.sync="submitData.customerName" :multiple="false"></msCompanySearch>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="直播执行" prop="cover">
                <msExecutorSearch :model.sync="submitData.liveImplementDicRequests"></msExecutorSearch>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="销售负责" prop="salesResponsibility">
                <el-input v-model="submitData.salesResponsibility" style="width: 100%"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="ERP订单号" prop="erpNo">
                <el-input v-model="submitData.erpNo" style="width: 100%"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-col>
      </el-row>
    </el-form>
  </section>
</template>

<script>
import MsEditor from '@/components/MsEditor'
import msExecutorSearch from '@/components/MsCommon/ms-executor-search'
import msCompanySearch from '@/components/MsCommon/ms-company-search'
import msDictionarySearch from '@/components/MsCommon/ms-dictionary-search'
import { getEditContent } from '@/utils/index'
import { mapGetters } from "vuex";
export default {
	name: "ms-live-content",
	data () {
		return {
      rules: {
        liveTitle: [
          { required: true, message: "请填写直播标题", trigger: 'blur' }
        ],
        liveAddress: [
          { required: true, message: "请填写直播地址", trigger: 'blur' }
        ],
        details: [
          { required: true, message: "请填写文章内容", trigger: 'change' }
        ]
      },
      editorDisplay: false,
      summaryKeyUp: false,
      detailsRemark: false 
		}
  },
  components: {
    MsEditor,
    msDictionarySearch,
    msCompanySearch,
    msExecutorSearch
  },
  computed: {
    ...mapGetters(["submitData", "info"])
  },
  watch: {
    'submitData.id': function (val) {
      if (val) {
        this.editorDisplay = true
        if (!this.submitData.details) {
          this.detailsRemark = true
        }
      } 
    },
    'submitData.details': function (val) {
      if (val) {
        if ((!this.submitData.id || this.detailsRemark) && !this.summaryKeyUp) {
          this.submitData.summary = this.PUBLIC_Methods.unexcapeHtml(getEditContent(val))
        }
      }
    }
  },
  created() {
    this.submitData.editor = this.info.userName
  },
	methods: {
    // 数据校验
    validateData(callback) {
      if (this.submitData.liveTime) {
        this.submitData.liveStartTime = this.submitData.liveTime[0] || ''
        this.submitData.liveEndTime = this.submitData.liveTime[1] || ''
      }
      // if (this.submitData.categoryList && this.submitData.categoryList.length > 0) {
      //   this.submitData.categoryList = this.submitData.categoryList.map(v => {
      //     return {
      //       categoryId: v.id,
      //       categoryName: v.name
      //     }
      //   })
      // }
      let params = {
        ...this.submitData,
        copyMailbox: this.submitData.copyMailbox.join(';'),
        userId: this.info.userId,
        username: this.info.userName,
      }
      this.$refs["submitRef"].validate( valid => {
        if (valid) {
          callback(params)
        }
      })
    },
    addMail() {
      this.submitData.copyMailbox.push("")
    },
    deleteMail(index) {
      this.submitData.copyMailbox.splice(index, 1)
    },
    dialog_open(name) {
      let params = {
        way: 'dialog',
        data: {id: this.submitData.id}
      }
      if (name === 'flow') {
        params = {
          ...params,
          title: '直播流程设置',
          component: 'msLiveFlow',
          width: '70%'
        }
      } else if (name === 'recommend') {
        params = {
          ...params,
          title: '视频推荐设置',
          component: 'msLiveRecommend',
          width: '60%'
        }
      } else if (name === 'sponsor') {
        params = {
          ...params,
          title: '赞助企业设置',
          component: 'msLiveSponsor',
          width: '80%'
        }
      }
      this.$emit('handle-click', params)
    },
    setEndTime(val) {
      if (val.endTime) {
        this.submitData[`recommendTime`] = val.endTime
      } else {
        this.submitData[val.operation] = 0
      }
    },
    endTimeCh(val, title, operation) {
      let params = {
        way: 'dialog',
        title: title,
        operation: operation,
        component: 'msLiveDisplay',
        data: {
          recommendEndTime: this.submitData.recommendTime
        }
      }
      if(val === 1) {
        this.$emit('handle-click', params)
      }
    }
	}
}
</script>

<style scope lang="scss">
.add-mail {
  cursor: pointer;
  width: 100%;
  height: 30px;
  line-height: 28px;
  border: 1px dashed #DCDFE6;
  border-radius: 4px;
  text-align: center;
  font-size: 12px;
  color: #333;
  outline: none;
  margin-top: 10px;
  background-color: rgba(0,0,0, 0);
}
.link-pos {
  position: relative;
  top: -2px;
}
.info-form{
  /deep/ .el-checkbox {
    margin-right: 0px;
  }
  &-right {
    /deep/ .el-form-item {
      margin-bottom: 16px;
    }
  }
}
</style>
