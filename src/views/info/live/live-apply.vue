<template>
	<ms-table
		:currentPage="searchParams.pageIndex"
		:loading="loading"
		:pageSize="searchParams.pageSize"
		:scopeConfig="scopeShow"
    :scopeHeader="scopeHeaderShow"
		:tableData="list"
		:tableHeader="tableHeader"
		:total="total"
		@current-change="current_change"
		@operation-change="operation_change"
		@size-change="size_change"
    @header-operation="header_operation"
	>
    <!-- 列表搜索去区域插槽 -->
		<template slot="ms-table-header">
      <div class="slot-button" v-loading="buttonLoading">
        <template v-for="(item, index) in soltButtons">
          <el-button :key="index" :type="item.type" :icon="item.icon" :disabled="btn_disabled(item.way)" @click="operation_change({operation: item})">{{ item.label }}</el-button>
        </template>
      </div>
		</template>
	</ms-table>
</template>

<script>
import tableMixins  from "../../common/mixins/table"
export default {
  name: "live-apply",
  mixins: [tableMixins],
  data () {
    return {
      searchParams: { // => 列表查询传参
        registrationType: null
      },
      buttonLoading: false,
      operaDisabled: true,
      tableHeader: [
        { label: '报名ID', property: 'id', sortable: true },
        { label: '平台账号', property: 'userName' },
        { label: '真实姓名', property: 'realName' },
        { label: '手机号', property: 'mobile' },
        { label: '邮箱', property: 'email' },
        { label: '报名渠道', property: 'registrationType' },
        { label: '报名时间', property: 'registrationTime' },
      ],
      scopeShow: {
        registrationType: () => {
          return {
            type: 'code',
            rule: {
              0: { label: 'PC' },
              1: { label: 'APP' },
              2: { label: '其他' }
            }
          }
        }
      },
      scopeHeaderShow: {
        registrationType: () => {
          return {
            type: 'dropdown',
            icon: 'icon-funnel',
            options: [
              { label: '全部', value: null },
              { label: 'PC', value: 0 },
              { label: 'APP', value: 1 },
              { label: '其他', value: 2 }
            ],
            operation: 'query'
          }
        }
      },
      soltButtons: [
        {
          label: '导出报名数据', 
          type: 'primary', 
          way: 'export'
        },
        {
          label: '返回', 
          type: 'info', 
          way: 'back'
        }
      ]
    }
  },
  methods: {
    init () {
      this.loading = true;
      this.dialog = false;
      let searchParams = {
        ...this.searchParams,
        liveId: this.$route.query.id || 0
      }
      this.api.getLiveRegistrationPage(searchParams).then(response => {
        this.loading = false
        if (response.status === 200 && response.data && response.data.length > 0) {
          this.total = response.totalSize
          this.list = response.data
          this.operaDisabled = false
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
      }).catch(() => this.loading = false)
    },
    operation_change (val) {
      switch (val.operation.way) {
        case "back":
          this.$router.back()
          break;
        case "export":
          this.buttonLoading = true;
          this.api.exportRegistrationExcel().then(response => {
            if (response.status === 200) {
              window.location.href = response.data
            } else {
              this.PUBLIC_Methods.apiNotify('导出数据失败', 'warning')
            }
            this.buttonLoading = false
          }).catch(() => this.buttonLoading = false)
          break;
        default: break;
      }
    },
    btn_disabled(way) {
      if (way !== 'back' && this.operaDisabled) {
        return true
      } else {
        return false
      }
    }
  }
};
</script>
