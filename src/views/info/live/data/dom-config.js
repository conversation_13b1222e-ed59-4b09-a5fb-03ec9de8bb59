const domConfig = {
  listSearch: [
    {
      label: '直播标题',
      placeholder: '请输入',
      model: 'liveTitle',
      component: 'ms-input'
    },
    {
      label: '栏目',
      placeholder: '请选择',
      model: 'columnId',
      component: 'ms-category-cascader'
    },
    {
      label: '讲师',
      placeholder: '请选择',
      model: 'teacherId',
      component: 'msLecturerSearch'
    },
    {
      label: '创建人',
      placeholder: '请输入',
      model: 'createdName',
      component: 'ms-input'
    },
  ],
  tableHeader: [
    { label: 'ID', property: 'id', sortable: true, width: 80 },
    { label: '直播标题', property: 'liveTitle', width: 250 },
    { label: '直播状态', property: 'liveStatus'},
    { label: '创建人', property: 'createdName'},
    { label: '直播开始时间', property: 'liveStartTime', sortable: true, width: 130 },
    { label: '最后更新时间', property: 'updatedTime', sortable: true, width: 130 },
    { label: '审核状态', property: 'auditStatus' }
  ],
  tableButtons: [
    {
      label: '编辑',
      icon: '',
      role: '',
      operation: 'edit',
      component: 'live-manage-operation',
      way: 'page',
      type: 'primary',
      path: 'live-manage-operation',
      params: ['id']
    },
    {
      label: '',
      icon: '',
      role: '',
      operation: 'editStatus',
      component: 'msLiveOperation',
      way: 'dialog',
      field: 'auditStatus',
      rule: {
        1: { label: '去审', type: '', operation: 'toreview' },
        0: { label: '审核', type: 'success', operation: 'approval' }
      }
    },
    {
      label: '删除',
      icon: '',
      role: '',
      operation: 'delete',
      way: 'delete',
      type: 'danger'
    },
    {
      label: '更多',
      operation: 'more',
      type: 'info',
      children: [
        {
          label: '报名管理',
          way: 'page',
          operation: 'query',
          component: 'live-manage-apply',
          path: 'live-manage-apply',
          params: ['id']
        }
      ]
    }
  ],
  soltButtons: [
    { 
      label: '添加直播', 
      type: 'primary', 
      icon: 'el-icon-plus',
      title: '添加直播',
      operation: 'created',
      component: 'msLiveCreate',
      way: 'dialog',
    }
  ]
}

export default domConfig;
