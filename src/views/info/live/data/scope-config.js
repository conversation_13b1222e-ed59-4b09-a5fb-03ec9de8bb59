const scopeConfig = {
  show: {
    auditStatus: () => {
      return {
        type: 'status',
        rule: {
          0: { label: '待审核', background: '#A7ADBD' },
          1: { label: '审核通过', background: '#40A23F' },
          2: { label: '审核未通过', background: '#FF1F1F' }
        }
      }
    },
    liveStatus: () => {
      return {
        type: 'status',
        rule: {
          0: { label: '未开始', background: '#A7ADBD' },
          1: { label: '直播中', background: '#40A23F' },
          2: { label: '已结束', background: '#FF1F1F' }
        }
      }
    },
  },
  headerShow: {
    auditStatus: () => {
      return {
        type: 'dropdown',
        icon: 'icon-funnel',
        options: [
          { label: '全部', value: null },
          { label: '待审核', value: 0 },
          { label: '审核通过', value: 1 }
        ],
        operation: 'query'
      }
    },
    liveStatus: () => {
      return {
        type: 'dropdown',
        icon: 'icon-funnel',
        options: [
          { label: '全部', value: null },
          { label: '未开始', value: 0 },
          { label: '直播中', value: 1 },
          { label: '已结束', value: 2 }
        ],
        operation: 'query'
      }
    }
  }
}

export default scopeConfig;
