const scopeConfig = {
  show: {
    type: () => {
      return {
        type: 'code',
        rule: {
          'sci': { label: '数据评估' },
          'article': { label: '文章评估' }
        }
      }
    },
    goal: () => {
      return {
        type: 'code',
        rule: {
          1: { label: '高分SCI文章' },
          2: { label: '普通SCI文章' },
          3: { label: '中文文章' }
        }
      }
    },
    status: () => {
      return {
        type: 'render',
        render: (h, scope) => {
          const status = scope.row.status
          const statusConfig = {
            0: { label: '待评估', color: '#E6A23C' },
            1: { label: '已评估', color: '#67C23A' }
          }

          const config = statusConfig[status] || { label: '未知', color: '#909399' }

          return h('span', {
            style: {
              color: config.color,
              fontWeight: '500'
            }
          }, config.label)
        }
      }
    },
    rating: () => {
      return {
        type: 'render',
        render: (h, scope) => {
          const rating = scope.row.rating || 0
          const stars = []
          for (let i = 1; i <= 5; i++) {
            stars.push(h('i', {
              class: i <= rating ? 'el-icon-star-on' : 'el-icon-star-off',
              style: { color: i <= rating ? '#ff9900' : '#dcdfe6', marginRight: '2px' }
            }))
          }
          return h('div', {
            style: {
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'flex-start',
              width: '100%'
            }
          }, stars)
        }
      }
    },
    feedback: () => {
      return {
        type: 'render',
        render: (h, scope) => {
          const feedback = scope.row.feedback || ''
          if (!feedback || feedback.trim() === '') {
            return h('span', { style: { color: '#999', fontSize: '12px' } }, '暂无反馈')
          }

          // 使用Tooltip组件显示完整内容
          return h('el-tooltip', {
            props: {
              content: feedback,
              placement: 'top',
              effect: 'dark',
              disabled: feedback.length <= 50 // 内容较短时不显示tooltip
            }
          }, [
            h('div', {
              style: {
                fontSize: '12px',
                color: '#606266',
                lineHeight: '1.4',
                maxHeight: '2.8em', // 约2行的高度
                overflow: 'hidden',
                display: '-webkit-box',
                WebkitLineClamp: 2,
                WebkitBoxOrient: 'vertical',
                wordBreak: 'break-all',
                cursor: feedback.length > 50 ? 'help' : 'default'
              }
            }, feedback)
          ])
        }
      }
    },
    dataFilePath: () => {
      return {
        type: 'render',
        render: (h, scope) => {
          if (!scope.row.dataFilePath) {
            return h('span', { style: { color: '#999', fontSize: '12px' } }, '暂无文件')
          }

          return h('el-button', {
            props: {
              type: 'primary',
              size: 'mini',
              icon: 'el-icon-download'
            },
            on: {
              click: () => {
                const link = document.createElement('a')
                link.href = scope.row.dataFilePath
                link.download = scope.row.dataFilePath.split('/').pop() || 'data'
                link.target = '_blank'
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link)
              }
            }
          }, '下载')
        }
      }
    }
  },
  headerShow: {
    type: () => {
      return {
        type: 'dropdown',
        icon: 'icon-funnel',
        options: [
          { label: '全部', value: null },
          { label: '数据评估', value: 'sci' },
          { label: '文章评估', value: 'article' }
        ],
        operation: 'query'
      }
    }
  }
}

export default scopeConfig
