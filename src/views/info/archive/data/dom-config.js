const domConfig = {
  listSearch: [
    {
      label: '课题名',
      placeholder: '请输入课题名称',
      model: 'title',
      component: 'ms-input'
    }
  ],
  tableHeader: [
    { label: '课题名称', property: 'title', width: 200 },
    { label: '数据下载', property: 'dataFilePath', width: 100 },
    { label: '样本量', property: 'samples', width: 100 },
    { label: '成果目标', property: 'goal', width: 120 },
    { label: '类型', property: 'type', width: 100 },
    { label: '状态', property: 'status', width: 100 },
    { label: '评估人', property: 'editorName', width: 120 },
    { label: '评估时间', property: 'evaluationTime', width: 150 },
    { label: '用户评价', property: 'rating', width: 100 },
    { label: '用户反馈', property: 'feedback', width: 150 }
  ],
  tableButtons: [
    {
      label: '上传报告',
      operation: 'uploadReport',
      type: 'primary',
      tdWidth: 80
    },
    {
      label: '下载报告',
      operation: 'downloadReport',
      type: 'primary',
      tdWidth: 80,
      disabled: (row) => {
        return !row.reportFilePath || row.reportFilePath.trim() === ''
      }
    }
  ],
  soltButtons: []
}

export default domConfig
