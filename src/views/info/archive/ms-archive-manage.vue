<template>
  <div class="archive-manage">
    <ms-table
      :currentPage="searchParams.pageIndex"
      :loading="loading"
      :operationButtons="domConfig.tableButtons"
      :pageSize="searchParams.pageSize"
      :scopeConfig="scopeConfig.show"
      :scopeHeader="scopeConfig.headerShow"
      :tableData="list"
      :tableHeader="domConfig.tableHeader"
      :total="total"
      :showSelection="false"
      @current-change="current_change"
      @operation-change="operation_change"
      @size-change="size_change"
      @header-operation="header_operation"
      @scope-click="scope_click"
      class="table-svg high-row-none"
    >
      <!-- 列表搜索区域插槽 -->
      <template slot="ms-table-header">
        <div class="slot-search">
          <template v-for="(searchItem, key) in domConfig.listSearch">
            <component
              :index="searchItem.index || ''"
              :is="searchItem.component"
              :key="key"
              :width="searchItem.width || '150px'"
              :model.sync="searchParams[searchItem.model]"
              :label="searchItem.label"
              :operation="searchItem.operation || ''"
              :options="searchItem.options || []"
              :placeholder="searchItem.placeholder || ''"
              :type="searchItem.type || ''"
              :multiple="searchItem.multiple"
              :disabled="searchItem.disabled"
              :code="searchItem.code"
            ></component>
          </template>
          <div class="inlineBlock">
            <el-button @click="handleClick('query')" type="primary" plain icon="el-icon-search">查询</el-button>
            <el-button @click="handleClick('reset')">重置</el-button>
          </div>
        </div>

        <div class="slot-button clearfix">
          <el-alert class="archive-total" :title="`共搜索到${total}条评估记录`" type="info" show-icon></el-alert>
        </div>
      </template>
    </ms-table>

    <!-- 上传报告对话框 -->
    <ms-upload-report
      :visible="uploadDialogVisible"
      :recordId="currentRecordId"
      @close="handleUploadClose"
      @upload-success="handleUploadSuccess"
    />
  </div>
</template>

<script>
import tableMixins from '../../common/mixins/table'
import domConfig from './data/dom-config'
import scopeConfig from './data/scope-config'
import msUploadReport from './tool/ms-upload-report'
import msDownloadButton from './tool/ms-download-button'
import msRatingDisplay from './tool/ms-rating-display'

export default {
  name: 'ms-archive-manage',
  mixins: [tableMixins],
  data() {
    return {
      domConfig: domConfig,
      scopeConfig: scopeConfig,
      searchParams: {
        title: '',
        type: '',
        pageIndex: 1,
        pageSize: 20
      },
      uploadDialogVisible: false,
      currentRecordId: null
    }
  },
  created() {
    // 确保按钮权限正确设置
    this.domConfig.tableButtons = this.domConfig.tableButtons.map(btn => ({
      ...btn,
      roleDisabled: false
    }))
  },
  components: {
    msUploadReport,
    msDownloadButton,
    msRatingDisplay
  },
  methods: {
    // 初始化数据
    apiInit(params) {
      this.loading = true

      // 添加roleId参数
      const userInfo = this.$store.getters.info || {}
      const requestParams = {
        ...params,
        roleId: userInfo.roleId
      }

      // 真实API调用
      this.api.getEvaluationRecordPage(requestParams).then(response => {
        this.loading = false
        if (response.status === 200) {
          this.total = response.totalSize || 0
          this.list = response.data || []
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
      }).catch(() => {
        this.loading = false
        this.PUBLIC_Methods.apiNotify('网络请求失败', 'error')
      })
    },

    // 处理操作按钮点击
    operation_change(val) {
      const { operation, model } = val

      switch (operation.operation) {
        case 'uploadReport':
          this.handleUploadReport(model)
          break
        case 'downloadReport':
          this.handleDownloadReport(model)
          break
        default:
          break
      }
    },

    // 处理上传报告
    handleUploadReport(row) {
      this.currentRecordId = row.id
      this.uploadDialogVisible = true
    },

    // 处理下载报告
    handleDownloadReport(row) {
      if (!row.reportFilePath) {
        this.$message.warning('暂无报告文件')
        return
      }

      // 直接打开文件链接进行下载
      window.open(row.reportFilePath, '_blank')
    },

    // 上传成功回调
    handleUploadSuccess() {
      this.$message.success('报告上传成功')
      this.handleUploadClose()
      this.init() // 刷新列表
    },

    // 关闭上传对话框
    handleUploadClose() {
      this.uploadDialogVisible = false
      this.currentRecordId = null
    },

    // 处理表格内点击事件
    scope_click(val) {
      // 可以在这里处理其他点击事件
      console.log('scope click:', val)
    }
  }
}
</script>

<style scoped>
.slot-search {
  margin-bottom: 15px;
}

.slot-button {
  margin-bottom: 15px;
}

.archive-total {
  float: right;
  width: auto;
}

.inlineBlock {
  display: inline-block;
  margin-left: 10px;
}
</style>
