# 数据评估功能

## 功能概述

数据评估功能是一个用于管理和展示评估记录的模块，支持数据评估和文章评估两种类型。

## 主要功能

### 1. 分页列表展示
- 课题名称、样本量、成果目标、类型、评估人、评估时间
- 用户评价星级显示（1-5星）
- 数据文件下载按钮
- 操作列：上传报告、下载报告

### 2. 查询功能
- 按课题名称搜索
- 按类型筛选（数据评估/文章评估）

### 3. 文件操作
- **数据下载**：直接通过dataFilePath字段的URL下载，支持权限控制
- **报告上传**：支持真实文件上传到云存储，自动获取文件URL并保存到数据库
- **报告下载**：直接通过reportFilePath字段的URL下载

### 4. 权限控制
- **数据下载权限**：已移除前端权限控制，由后端根据roleId控制数据返回
- **报告下载控制**：当reportFilePath为空时，下载报告按钮禁用
- **按钮状态**：无文件时按钮显示为禁用状态

## 数据字段说明

### 成果目标 (goal)
- 1：高分SCI文章
- 2：普通SCI文章
- 3：中文文章

### 评估类型 (type)
- sci：数据评估
- article：文章评估

### 用户评价 (rating)
- 1-5星评价，使用星星图标显示（不显示文字描述）

### 用户反馈 (feedback)
- 用户的文字反馈内容
- 最多显示2行文字，超出部分自动截断
- 鼠标悬停时显示气泡提示框展示完整内容
- 无反馈时显示"暂无反馈"

### 评估状态 (status)
- 0：待评估（黄色字体显示）
- 1：已评估（绿色字体显示）
- 状态字段仅用于显示，权限控制由后端处理

## 文件结构

```
src/views/info/archive/
├── ms-archive-manage.vue          # 主页面组件
├── data/
│   ├── dom-config.js             # 表格和搜索配置
│   └── scope-config.js           # 列显示配置
├── tool/
│   ├── ms-upload-report.vue      # 上传报告组件
│   ├── ms-download-button.vue    # 下载按钮组件
│   └── ms-rating-display.vue     # 星级评价显示组件
└── README.md                     # 说明文档
```

## API接口

### 1. 获取评估记录列表
- **接口**: `/medsci-mgr-center-service/evaluationRecord/getEvaluationRecordPage`
- **方法**: POST
- **参数**:
  - title: 课题名称（可选）
  - type: 类型（可选，sci/article）
  - pageIndex: 页码
  - pageSize: 每页数量
  - roleId: 用户角色ID（自动获取）

### 2. 上传报告文件
- **接口**: `/medsci-mgr-center-service/evaluationRecord/uploadReportFile`
- **方法**: POST
- **参数**:
  - id: 记录ID
  - editorId: 评估人ID
  - editorName: 评估人名称
  - reportFilePath: 报告文件路径

## 使用说明

1. **访问页面**: 在菜单中选择"信息配置" -> "数据评估"
2. **查询数据**: 使用搜索框输入课题名称或选择类型进行筛选
3. **下载数据**: 点击"下载数据"按钮，直接下载dataFilePath对应的文件
4. **上传报告**: 点击"上传报告"按钮，选择本地文件进行上传，系统会自动上传到云存储并保存文件URL
5. **下载报告**: 点击"下载报告"按钮，直接下载reportFilePath对应的文件

### 上传报告功能特性

- **支持格式**: .xls, .xlsx, .zip, .rar, .doc, .docx, .txt, .csv, .pdf
- **文件大小**: 最大支持50MB
- **上传进度**: 实时显示上传进度条
- **云存储**: 文件自动上传到腾讯云COS存储
- **安全性**: 使用临时密钥，确保上传安全

## 注意事项

1. 下载功能直接使用文件路径，不通过后端API处理
2. 上传报告功能已升级为真实文件上传，支持多种文档格式
3. 已启用真实API调用，连接后端服务获取数据
4. 星级评价使用Element UI的图标显示
5. 文件上传需要有效的云存储访问凭证
6. 上传报告时自动使用当前登录用户作为评估人

## 开发说明

### API调用状态
- ✅ 列表查询已启用真实API调用
- ✅ 上传报告已启用真实API调用
- ✅ 自动获取当前用户信息作为评估人

### 自定义配置
- 修改 `dom-config.js` 调整表格列和搜索条件
- 修改 `scope-config.js` 调整列显示样式，特别是render函数部分

### API参数说明
**上传报告接口参数：**
- `id`: 评估记录ID
- `editorId`: 当前登录用户ID（自动获取）
- `editorName`: 当前登录用户名（自动获取）
- `reportFilePath`: 上传后的文件URL（自动转换为CDN地址）

### URL处理机制
**文件URL转换：**
- 上传到云存储：`https://medsci-open-files-1253188136.cos.ap-shanghai.myqcloud.com/path/file.pdf`
- 转换为CDN地址：`https://img.medsci.cn/path/file.pdf`
- 遵循项目标准的URL处理逻辑
