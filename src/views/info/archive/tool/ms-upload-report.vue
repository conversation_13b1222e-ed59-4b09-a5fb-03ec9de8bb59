<template>
  <div class="upload-report-dialog">
    <el-dialog
      title="上传报告"
      :visible.sync="dialogVisible"
      width="600px"
      :close-on-click-modal="false"
      @close="handleClose"
    >
      <div class="upload-content">
        <el-upload
          class="report-uploader"
          :http-request="upLoad"
          :before-upload="beforeUpload"
          :on-remove="handleRemove"
          :file-list="fileList"
          :disabled="uploading"
          :limit="1"
          action=""
        >
          <el-button
            type="primary"
            icon="el-icon-upload"
            :loading="uploading"
            plain
          >
            {{ uploading ? '上传中...' : '选择文件' }}
          </el-button>
          <div slot="tip" class="el-upload__tip">
            支持格式：.xls, .xlsx, .zip, .rar, .doc, .docx, .txt, .csv, .pdf，单个文件不超过50MB
          </div>
        </el-upload>

        <el-progress
          v-show="showProgress"
          :percentage="progress"
          :status="progressStatus || undefined"
          class="upload-progress"
        />

        <div v-if="uploadedFile" class="uploaded-file-info">
          <i class="el-icon-document"></i>
          <span class="file-name">{{ uploadedFile.name }}</span>
          <span class="file-size">({{ formatFileSize(uploadedFile.size) }})</span>
        </div>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button
          type="primary"
          @click="submitReport"
          :loading="submitting"
          :disabled="!uploadedFile"
        >
          确 定
        </el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import upload from '@/components/UpFile/upload'
import COS from "cos-js-sdk-v5"

export default {
  name: 'ms-upload-report',
  mixins: [upload],
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    recordId: {
      type: [String, Number],
      required: false,
      default: null
    }
  },
  data() {
    return {
      dialogVisible: false,
      uploading: false,
      submitting: false,
      showProgress: false,
      progress: 0,
      progressStatus: undefined,
      fileList: [],
      uploadedFile: null,
      upFileFormat: 'document', // 文档类型
      upFileSize: 50, // 50MB限制
      uploadTimeout: 60 * 1000, // 60秒超时
      fileSuffix: 'evaluation-reports', // 文件存储路径
      acceptTypes: ['.xls', '.xlsx', '.zip', '.rar', '.doc', '.docx', '.txt', '.csv', '.pdf'],
      cosUrl: 'img.medsci.cn' // CDN域名
    }
  },
  watch: {
    visible(val) {
      this.dialogVisible = val
      if (val) {
        this.resetUpload()
      }
    }
  },
  methods: {
    beforeUpload(file) {
      // 检查文件格式
      const fileSuffix = file.name.substr(file.name.lastIndexOf('.')).toLowerCase()
      const isValidType = this.acceptTypes.includes(fileSuffix)

      if (!isValidType) {
        this.$message.error('请上传支持的文件格式：' + this.acceptTypes.join(', '))
        return false
      }

      // 检查文件大小
      const isLtSize = file.size / 1024 / 1024 < this.upFileSize
      if (!isLtSize) {
        this.$message.error(`上传文件大小不能超过 ${this.upFileSize}MB!`)
        return false
      }

      // 返回Promise来获取上传凭证
      return this.getUploadToken()
    },

    async getUploadToken() {
      try {
        this.uploading = true
        const response = await this.api.getToken({ type: 1 }) // 文档类型
        this.dataObj = {
          region: 'oss-cn-shanghai',
          accessKeyId: response.data.accessKeyId,
          accessKeySecret: response.data.accessKeySecret,
          bucket: response.data.publicBucketName,
          stsToken: response.data.securityToken,
          timeout: this.uploadTimeout
        }
        return true
      } catch (error) {
        this.uploading = false
        this.$message.error('获取上传凭证失败')
        return false
      }
    },

    async upLoad(file) {
      const files = file.file
      const fileSize = files.size
      const fileName = files.name
      const suffix = fileName.substr(fileName.lastIndexOf('.'))
      const timestamp = new Date().getTime()
      const userId = this.$store.getters.info.userId

      // 生成文件路径
      const fileKey = `${this.fileSuffix}/${timestamp}_${userId}${suffix}`

      this.showProgress = true
      this.progress = 0
      this.progressStatus = undefined

      try {
        const cos = new COS({
          SecretId: this.dataObj.accessKeyId,
          SecretKey: this.dataObj.accessKeySecret,
          XCosSecurityToken: this.dataObj.stsToken,
        })

        await new Promise((resolve, reject) => {
          cos.uploadFile({
            Bucket: this.dataObj.bucket,
            Region: "ap-shanghai",
            Key: fileKey,
            Body: files,
            onProgress: (progressData) => {
              this.progress = Math.round(progressData.percent * 100)
            }
          }, (err, data) => {
            if (err) {
              this.progressStatus = 'exception'
              this.$message.error('上传失败：' + (err.message || '网络错误'))
              reject(err)
            } else {
              this.progressStatus = 'success'
              this.uploadedFile = {
                name: fileName,
                size: fileSize,
                key: fileKey,
                url: `https://${data.Location}`
              }
              this.$message.success('文件上传成功')
              resolve(data)
            }
          })
        })
      } catch (error) {
        console.error('上传失败:', error)
      } finally {
        this.uploading = false
        setTimeout(() => {
          this.showProgress = false
        }, 1000)
      }
    },

    handleRemove() {
      this.uploadedFile = null
      this.fileList = []
      this.showProgress = false
      this.progress = 0
    },

    async submitReport() {
      if (!this.uploadedFile) {
        this.$message.warning('请先上传文件')
        return
      }

      if (!this.recordId) {
        this.$message.warning('记录ID不能为空')
        return
      }

      this.submitting = true

      try {
        // 获取当前用户信息作为评估人
        const userInfo = this.$store.getters.info

        // 处理URL前缀，将云存储URL转换为CDN URL
        const reportFilePath = this.convertToCdnUrl(this.uploadedFile.url)

        const params = {
          id: this.recordId,
          editorId: userInfo.userId,
          editorName: userInfo.userName,
          reportFilePath: reportFilePath
        }

        const response = await this.api.uploadEvaluationReportFile(params)

        if (response.status === 200) {
          this.$message.success('报告上传成功')
          this.$emit('upload-success')
          this.handleClose()
        } else {
          this.$message.error(response.message || '保存失败')
        }
      } catch (error) {
        console.error('上传报告失败:', error)
        this.$message.error('保存失败，请重试')
      } finally {
        this.submitting = false
      }
    },

    resetUpload() {
      this.uploadedFile = null
      this.fileList = []
      this.showProgress = false
      this.progress = 0
      this.uploading = false
      this.submitting = false
      this.progressStatus = undefined
    },

    handleClose() {
      this.dialogVisible = false
      this.resetUpload()
      this.$emit('close')
    },

    formatFileSize(bytes) {
      if (bytes === 0) return '0 B'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    },

    /**
     * 将云存储URL转换为CDN URL
     * 遵循项目标准，使用与upload.js相同的处理逻辑
     * @param {string} originalUrl - 原始云存储URL
     * @returns {string} - 转换后的CDN URL
     */
    convertToCdnUrl(originalUrl) {
      if (!originalUrl) return ''

      // 使用项目标准的URL处理方式
      // 将云存储域名替换为项目CDN域名
      const urlParts = originalUrl.split('/')
      if (urlParts.length > 0) {
        // 使用全局配置的cosUrl
        return originalUrl.replace(urlParts[0] + '//' + urlParts[2], `https://${this.cosUrl}`)
      }

      return originalUrl
    }
  }
}
</script>

<style scoped>
.upload-content {
  padding: 20px 0;
}

.report-uploader {
  text-align: center;
}

.report-uploader .el-upload__tip {
  color: #606266;
  font-size: 12px;
  margin-top: 7px;
  line-height: 1.4;
}

.upload-progress {
  margin-top: 15px;
}

.uploaded-file-info {
  margin-top: 15px;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.uploaded-file-info .el-icon-document {
  color: #409eff;
  font-size: 16px;
}

.file-name {
  font-weight: 500;
  color: #303133;
  flex: 1;
  word-break: break-all;
}

.file-size {
  color: #909399;
  font-size: 12px;
}

.dialog-footer {
  text-align: right;
}
</style>
