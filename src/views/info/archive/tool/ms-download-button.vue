<template>
  <div class="download-button">
    <el-button 
      v-if="filePath"
      type="primary" 
      size="mini" 
      icon="el-icon-download"
      @click="handleDownload"
      :loading="downloading"
    >
      {{ buttonText }}
    </el-button>
    <span v-else class="no-file">暂无文件</span>
  </div>
</template>

<script>
export default {
  name: 'ms-download-button',
  props: {
    filePath: {
      type: String,
      default: ''
    },
    downloadType: {
      type: String,
      default: 'data', // 'data' 或 'report'
      validator: value => ['data', 'report'].includes(value)
    },
    recordId: {
      type: [String, Number],
      required: true
    },
    buttonText: {
      type: String,
      default: '下载'
    }
  },
  data() {
    return {
      downloading: false
    }
  },
  methods: {
    handleDownload() {
      if (!this.filePath) {
        this.$message.warning('文件路径不存在')
        return
      }

      this.downloading = true

      try {
        // 直接使用文件路径下载
        this.downloadByUrl(this.filePath)
      } catch (error) {
        console.error('下载失败:', error)
        this.$message.error('下载失败，请重试')
      } finally {
        this.downloading = false
      }
    },
    
    downloadBlob(blob, filename) {
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = filename
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
    },
    
    downloadByUrl(url) {
      const link = document.createElement('a')
      link.href = url
      link.download = this.getFileName()
      link.target = '_blank'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    },
    
    getFileName() {
      if (this.filePath) {
        const pathParts = this.filePath.split('/')
        return pathParts[pathParts.length - 1] || `download_${Date.now()}`
      }
      return `download_${Date.now()}`
    }
  }
}
</script>

<style scoped>
.download-button {
  display: inline-block;
}

.no-file {
  color: #999;
  font-size: 12px;
}
</style>
