<template>
  <div class="rating-display">
    <el-rate
      v-model="ratingValue"
      :max="maxRating"
      :allow-half="false"
      :show-text="showText"
      :show-score="showScore"
      :text-color="textColor"
      :score-template="scoreTemplate"
      disabled
      class="rating-stars"
    >
    </el-rate>
    <span v-if="showRatingText" class="rating-text">{{ getRatingText() }}</span>
  </div>
</template>

<script>
export default {
  name: 'ms-rating-display',
  props: {
    rating: {
      type: [Number, String],
      default: 0
    },
    maxRating: {
      type: Number,
      default: 5
    },
    showText: {
      type: Boolean,
      default: false
    },
    showScore: {
      type: Boolean,
      default: false
    },
    showRatingText: {
      type: Boolean,
      default: true
    },
    textColor: {
      type: String,
      default: '#ff9900'
    },
    scoreTemplate: {
      type: String,
      default: '{value}分'
    }
  },
  computed: {
    ratingValue() {
      const value = Number(this.rating)
      return isNaN(value) ? 0 : Math.min(Math.max(value, 0), this.maxRating)
    }
  },
  methods: {
    getRatingText() {
      const value = this.ratingValue
      if (value === 0) {
        return '暂无评价'
      }
      
      const ratingTexts = {
        1: '很差',
        2: '较差', 
        3: '一般',
        4: '良好',
        5: '优秀'
      }
      
      return ratingTexts[value] || `${value}星`
    }
  }
}
</script>

<style scoped>
.rating-display {
  display: flex;
  align-items: center;
  gap: 8px;
}

.rating-stars {
  display: inline-block;
}

.rating-text {
  font-size: 12px;
  color: #666;
  white-space: nowrap;
}

/* 自定义星星样式 */
.rating-display /deep/ .el-rate__item {
  margin-right: 2px;
}

.rating-display /deep/ .el-rate__icon {
  font-size: 14px;
}

.rating-display /deep/ .el-icon-star-on {
  color: #ff9900;
}

.rating-display /deep/ .el-icon-star-off {
  color: #dcdfe6;
}
</style>
