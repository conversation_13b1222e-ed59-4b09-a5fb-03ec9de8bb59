const domConfig = {
    listSearch: [
      {
        label: ' ',
        placeholder: '请输入',
        model: 'keyword',
        component: 'ms-input'
      },
    ],
    tableHeader: [
      { label: '音频名称', property: 'audioTitle', width: '100' },
      { label: 'URL', property: 'audioUrl', width: '200'},
      { label: '创建人', property: 'createdName' },
      { label: '创建时间', property: 'createdTime' },
      { label: '关键字', property: 'keyword' },
    ],
    tableButtons: [
      {
        label: '编辑',
        icon: '',
        role: '',
        operation: 'edit',
        component: 'msAudioEdit',
        way: 'dialog',
        type: 'primary',
        title: '图片素材编辑',
        width: '50%'
      },
      {
        label: '删除',
        icon: '',
        role: '',
        type: 'danger',
        way: 'dialog',
        operation: 'delete',
        component: 'msAudioDelete',
      }
    ],
    soltButtons: [
      { 
        label: '添加', 
        // icon: 'el-icon-plus',
        type: 'primary', 
        operation: 'created',
        component: 'msAudioEdit',
        way: 'dialog',
        title: '添加图片素材',
        width: '50%'
      },
      { 
        label: '批量删除', 
        type: 'info', 
        // icon: 'el-icon-close',
        operation: 'delete',
        component: 'msAudioDelete',
        way: 'batch'
      },
      {
        label: '回收站',
        type: 'info',
        operation: '',
        component: 'msAudioRecycle',
        way: 'dialog',
        title: '回收站（彻底删除资源将无法访问，请谨慎操作）',
        width: '60%',
      },
    ]
  }
  
  export default domConfig;
  