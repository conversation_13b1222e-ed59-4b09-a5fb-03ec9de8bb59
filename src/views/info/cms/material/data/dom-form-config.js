// import store from '@/store'

const domConfig = {
  listSearch: [
    {
      label: '标题',
      placeholder: '请输入',
      model: 'formName',
      component: 'ms-input'
    },
    {
      label: '创建人',
      placeholder: '请选择',
      model: 'createUserId',
      component: 'ms-createby-search'
    },
    {
      label: '创建时间',
      placeholder: '请选择时间段',
      model: 'createTime',
      component: 'ms-picker',
      type: 'daterange',
      width: '240px'
    }
  ],
  tableHeader: [
    { label: 'ID', property: 'id', sortable: true, width: '60' },
    { label: '标题', property: 'formName', width: '200'},
    { label: '创建人', property: 'createUserName' },
    { label: '创建时间', property: 'updateAt', sortable: true,  width: '130'  },
    { label: '状态', property: 'status'},
  ],
  tableButtons: [
    {
      label: '编辑',
      icon: '',
      role: '',
      operation: 'edit',
      component: 'form-operation',
      way: 'page',
      type: 'primary',
      path: 'material-operation-form',
      params: ['formId'],
      disabled: (val) => {
        if (val.formVersion === 1) {
          return true
        } else {
          return false
        }
      }
    },
    {
      label: '',
      icon: '',
      role: '',
      operation: 'editStatus',
      component: 'msFormOperation',
      way: 'dialog',
      field: 'status',
      rule: {
        'PASS': { label: '去审', type: '', operation: 'toreview' },
        'WAITING': { label: '审核', type: 'success', operation: 'approval' }
      },
    },
    {
      label: '删除',
      way: 'dialog',
      type: 'danger',
      operation: 'delete',
      component: 'msFormOperation',
    },
    {
      label: '导出',
      way: 'export',
      disabled: (val) => {
        if (val.formVersion === 1) {
          return true
        } else {
          return false
        }
      }
    },
  ],
  soltButtons: [
    { 
      label: '表单添加', 
      type: 'primary', 
      icon: 'el-icon-plus',
      operation: 'created',
      component: 'form-operation',
      way: 'page',
      path: 'material-operation-form',
      params: ['formId'],
    },
    { 
      label: '批量审核', 
      type: 'primary',
      operation: 'approval',
      component: 'msFormOperation',
      way: 'batch',
    },
    { 
      label: '批量去审', 
      type: 'primary',
      operation: 'toreview',
      component: 'msFormOperation',
      way: 'batch',
    },
    { 
      label: '批量删除',
      type: 'info',
      icon: 'el-icon-close',
      operation: 'delete',
      component: 'msFormOperation',
      way: 'batch',
    },
  ]
}

export default domConfig;
