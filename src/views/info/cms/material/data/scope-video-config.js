const scopeConfig = {
    show: {
      cover: () => {
        return {
          type: 'video',
          config: {
            component: 'msVideoPreview',
            way: 'dialog',
            title: '视频素材预览',
            width: '60%'
          }
        }
      },
      createdTime: () => {
        return {
          type: 'formatTime',
          cFormat: '{y}-{m}-{d} {h}:{i}'
        }
      },
    },
    headerShow: {
      
    }
  }
  
  export default scopeConfig;
  