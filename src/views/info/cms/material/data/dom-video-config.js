const domConfig = {
    listSearch: [
        {
            label: '标题',
            placeholder: '请输入',
            model: 'title',
            component: 'ms-input'
        },
        {
            label: '讲师名',
            placeholder: '请输入',
            model: 'speakerName',
            component: 'ms-input'
        }
    ],
    tableHeader: [
        { label: '视频', property: 'cover', width: '100' },
        { label: 'URL', property: 'videoUrl', width: '200'},
        { label: '标题', property: 'title' },
        { label: '时长', property: 'duration' },
        { label: '讲师名', property: 'lecturerName' },
        { label: '创建人', property: 'createdName' },
        { label: '创建时间', property: 'createdTime' }
    ],
    tableButtons: [
        {
            label: '编辑',
            icon: '',
            role: '',
            type: 'primary',
            operation: 'edit',
            component: 'video-operation',
            way: 'page',
            path: 'material-operation-video',
            params: ['id'],
        },
        {
            label: '',
            icon: '',
            role: '',
            operation: 'status',
            component: 'msVideoDelete',
            way: 'dialog',
            field: 'status',
            rule: {
              1: { label: '去审', type: '', operation: 'toreview' },
              0: { label: '审核', type: 'success', operation: 'approval' }
            },
        },
        {
            label: '删除',
            icon: '',
            role: '',
            type: 'danger',
            way: 'dialog',
            operation: 'delete',
            component: 'msVideoDelete',
        }
    ],
    soltButtons: [
        { 
            label: '添加视频素材', 
            icon: 'el-icon-plus',
            type: 'primary', 
            operation: 'created',
            component: 'video-operation',
            way: 'page',
            path: 'material-operation-video',
        },
        { 
            label: '批量删除', 
            type: 'info', 
            icon: 'el-icon-close',
            operation: 'delete',
            component: 'msVideoDelete',
            way: 'batch'
        },
        {
            label: '回收站',
            type: 'info',
            operation: '',
            component: 'msVideoRecycle',
            way: 'dialog',
            title: '回收站（彻底删除资源将无法访问，请谨慎操作）',
            width: '60%',
          },
    ]
  }
  
  export default domConfig;
  