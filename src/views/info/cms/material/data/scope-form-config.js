// import serveUrl from '@/store/data/serveUrl.js'

const scopeConfig = {
  show: {
    status: () => {
      return {
        type: 'status',
        rule: {
          'WAITING': { label: '待审核', background: '#A7ADBD' },
          'PASS': { label: '审核通过', background: '#40A23F' },
          'FAIL': { label: '审核失败', background: '#E6A23C' }
        }
      }
    },
    updateAt: () => {
      return {
        type: 'formatTime',
        cFormat: '{y}-{m}-{d} {h}:{i}'
      }
    },
    createdTime: () => {
      return {
        type: 'formatTime',
        cFormat: '{y}-{m}-{d} {h}:{i}'
      }
    },
  },
  headerShow: {
    fields: () => {
      return {
        type: 'dropdown',
        icon: '',
        options: [
          { label: '默认排序', value: null },
          { label: '按总点击量', value: 1 },
          { label: '按APP点击量', value: 2 },
          { label: '按PC点击量', value: 3 },
          { label: '按点赞量', value: 4 },
          { label: '按分享量', value: 5 },
          { label: '按评论量', value: 6 },
        ],
        operation: 'query',
        params: 'sortType'
      }
    },
    status: () => {
      return {
        type: 'dropdown',
        icon: 'icon-funnel',
        options: [
          { label: '全部', value: null },
          { label: '待审核', value: 'WAITING' },
          { label: '审核通过', value: 'PASS' }
        ],
        operation: 'query'
      }
    }
  }
}

export default scopeConfig;
