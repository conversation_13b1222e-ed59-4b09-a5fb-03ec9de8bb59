const domConfig = {
  listSearch: [
    {
      label: '名称、关键词',
      placeholder: '请输入',
      model: 'title',
      component: 'ms-input'
    },
    {
      label: '图片库',
      placeholder: '请输入',
      model: 'isShare',
      component: 'ms-select-local',
      options: [
        { label: '项目', value: 0 },
        { label: '公共', value: 1 },
      ],
      clearable: 'clearable'
    }
  ],
  tableHeader: [
    { label: '图片', property: 'fileUrl', width: '100' },
    { label: 'URL', property: 'fileUrl_s', width: '200'},
    { label: '名称', property: 'title' },
    { label: '关键字', property: 'tagName' },
    { label: '创建人', property: 'createdName' },
    { label: '创建时间', property: 'createdTime' }
  ],
  tableButtons: [
    {
      label: '编辑',
      icon: '',
      role: '',
      operation: 'edit',
      component: 'msImageEdit',
      way: 'dialog',
      type: 'primary',
      title: '图片素材编辑',
      width: '50%'
    },
    {
      label: '删除',
      icon: '',
      role: '',
      type: 'danger',
      way: 'dialog',
      operation: 'delete',
      component: 'msImageDelete',
    }
  ],
  soltButtons: [
    { 
      label: '添加图片素材', 
      icon: 'el-icon-plus',
      type: 'primary', 
      operation: 'created',
      component: 'msImageEdit',
      way: 'dialog',
      title: '添加图片素材',
      width: '50%'
    },
    { 
      label: '批量删除', 
      type: 'info', 
      icon: 'el-icon-close',
      operation: 'delete',
      component: 'msImageDelete',
      way: 'batch'
    },
    {
      label: '回收站',
      type: 'info',
      operation: '',
      component: 'msImageRecycle',
      way: 'dialog',
      title: '回收站（彻底删除资源将无法访问，请谨慎操作）',
      width: '60%',
    },
  ]
}

export default domConfig;
