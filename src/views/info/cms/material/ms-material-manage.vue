<template>
  <section class="form-tab">
    <el-tabs v-model="activeName">
      <el-tab-pane label="图片" name="image">
        <image-manage></image-manage>
      </el-tab-pane>
      <el-tab-pane label="音频" name="audio">
        <audio-manage></audio-manage>
      </el-tab-pane>
      <el-tab-pane label="视频" name="video">
        <video-manage></video-manage>
      </el-tab-pane>
      <el-tab-pane label="表单" name="form">
        <form-manage></form-manage>
      </el-tab-pane>
    </el-tabs>
  </section>
</template>

<script>
import imageManage from './image-manage'
import videoManage from './video-manage'
import formManage from './form-manage'
import audioManage from './audio-manage'
export default {
  name: "ms-material-manage",
	data () {
		return {
      activeName: 'image'
		}
  },
  components: {
    imageManage,
    videoManage,
    formManage,
    audioManage
  },
  beforeRouteEnter (to, from, next) {
    next(vm => {
        if (from.path === '/material-operation-video') {
          vm.activeName = 'video'
        }else if (from.path === '/material-operation-form') {
          vm.activeName = 'form'
        }
    })
},
}
</script>
