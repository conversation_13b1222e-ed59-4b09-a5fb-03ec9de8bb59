<template>
	<ms-table
		:currentPage="searchParams.pageIndex"
		:loading="loading"
		:operationButtons="domConfig.tableButtons"
		:pageSize="searchParams.pageSize"
		:scopeConfig="scopeConfig.show"
    :scopeHeader="scopeConfig.headerShow"
		:tableData="list"
		:tableHeader="domConfig.tableHeader"
		:total="total"
    :showSelection="true"
		@current-change="current_change"
		@operation-change="operation_change"
		@size-change="size_change"
    @header-operation="header_operation"
    @handleSelectionChange="handleSelectionChange"
	>
    <!-- 列表搜索去区域插槽 -->
		<template slot="ms-table-header">
			<div class="slot-search">
        <template v-for="(searchItem, key) in domConfig.listSearch">
					<component
						:index="searchItem.index || ''"
						:is="searchItem.component"
						:key="key"
            :width="searchItem.width || '150px'"
            :model.sync="searchParams[searchItem.model]"
            :label="searchItem.label"
						:operation="searchItem.operation || ''"
						:options="searchItem.options || []"
						:placeholder="searchItem.placeholder || ''"
						:type="searchItem.type || ''"
            :multiple="searchItem.multiple"
            :disabled="searchItem.disabled"
            :clearable="searchItem.clearable"
					></component>
				</template>
        <div class="inlineBlock">
            <el-button @click="handleClick('query')">查询</el-button>
            <el-button @click="handleClick('reset')">重置</el-button>
        </div>
			</div>
      <div class="slot-button">
          <template v-for="(item, index) in domConfig.soltButtons">
              <el-button :key="index" :type="item.type" :icon="item.icon" @click="operation_change({operation: item})" plain>{{ item.label }}</el-button>
          </template>
      </div>
      <el-dialog :visible.sync="dialog" 
                  closeable 
                  show-close
                  :close-on-click-modal="false"
                  :width="dialogWidth"
                  :title="dialogTitle">
                  <component :is="dialogComponent" 
                  :model="scopeInfo" 
                  :operation="dialogOperation" 
                  @close="dialog = !dialog" 
                  @up-date="init" 
                  v-if="dialog"></component>
      </el-dialog>
      <!--大图--> 
      <el-image-viewer
        v-if="showImageViewer"
        :on-close="closeImageViewer"
        :url-list="viewerImgList_c"
      />
		</template>
	</ms-table>
</template>

<script>
import ElImageViewer from 'element-ui/packages/image/src/image-viewer'
import tableMixins  from "../../../common/mixins/table"
import domConfig from "./data/dom-audio-config"
import scopeConfig from "./data/scope-audio-config"
import msAudioEdit from './tool/ms-audio-edit'
import msAudioDelete from './tool/ms-audio-delete'
import msAudioRecycle from './tool/ms-audio-recycle'
export default {
    name: "audio-manage",
    mixins: [tableMixins],
    data () {
        return {
            domConfig: domConfig,
            scopeConfig: scopeConfig, 
            searchParams: { // => 列表查询传参
                keyword: "",
                // isShare: 0,
            },
            showImageViewer: false,
            viewerImgList: [],
            viewerImgList_c: [],
            searchCacheFlag: false,
            isFirst: true
        }
    },
  components: {
    msAudioEdit,
    ElImageViewer,
    msAudioDelete,
    msAudioRecycle
  },
  methods: {
    init () {
      this.loading = true;
      this.dialog = false;
      let searchParams = {...this.searchParams}
      this.api.getAudioMaterialListByProject(searchParams).then(response => {
        this.loading = false
        this.total = response.totalSize || 0;
        this.list = []
        this.viewerImgList = []
        let arr = response.data || []
        let list = []
        let imgList = []
        if (response.status === 200) {
          arr.forEach((v, k) => {
            list.push({
              ...v,fileUrl_s: v.fileUrl, _index: k
            })
            imgList.push(v.fileUrl)
          });
          this.list = list;
          this.viewerImgList = imgList;
        } 
        else {
          if (!this.isFirst) {
            this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
          }
        }
        this.isFirst = false;
      }).catch(() => this.loading = false)
    },
    operation_change_module(val) {
      if (val.operation.way === 'image-preview') {
        this.viewerImgList_c = this.viewerImgList.slice(val.model._index).concat(this.viewerImgList.slice(0,val.model._index))
        this.showImageViewer = true;
      }
    },
    closeImageViewer () {
      this.showImageViewer = false;
    }
  }
};
</script>
