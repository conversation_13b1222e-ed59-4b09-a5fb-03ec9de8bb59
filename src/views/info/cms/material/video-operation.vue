<template>
    <section class="form-tab" v-loading="getLoading">
        <el-form :model="submitData"
            ref="submitRef"
            class="rule-form info-form"
            :rules="rules"
            label-width="70px">
            <el-row :gutter="10"> 
                <el-col :span="18">
                    <el-row>
                        <el-col :span="12">
                            <el-form-item label="视频标题" prop="title">
                                <el-input v-model="submitData.title" style="width: 100%"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                          <el-col :span="12">
                              <el-form-item label="讲师" prop="lecturerName">
                                  <ms-lecturer-search :model.sync="submitData.lecturerId" :modelName.sync="submitData.lecturerName"></ms-lecturer-search>
                              </el-form-item>
                          </el-col>
                          <el-col :span="12">
                              <el-form-item label="讲师ID" prop="lecturerId">
                                <el-input v-model="submitData.lecturerId" disabled></el-input>
                              </el-form-item>
                          </el-col>
                        </el-col>
                        <el-col :span="24">
                            <el-form-item label="视频内容" prop="content">
                                <ms-editor v-model="submitData.content"></ms-editor>
                            </el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <el-form-item label="摘要" prop="summary">
                                <el-input v-model="submitData.summary" type="textarea" :rows="4" maxlength="100" show-word-limit></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row v-show="dataId">
                        <el-col :span="8" >
                            <el-form-item label="编辑者：" label-width="100px">
                                <span class="font-12">{{submitData.createdName}}</span>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="创建时间：" label-width="80px">
                                <span class="font-12">{{submitData.createdTime | parseTime}}</span>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="更新时间" label-width="80px">
                                <span class="font-12">{{submitData.updatedTime | parseTime}}</span>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-col>
                <el-col :span="6" class="info-form-right">
                    <el-row>
                        <el-col :span="24">
                            <el-form-item prop="videoKey" label="视频">
                                <ms-video-upload v-model="submitData.videoKey" buttonLabel="视频上传" :fileSuffix="videoSpace" @bindData="videoDataDeal" v-if="!getLoading" upFileFormat="video"></ms-video-upload>
                            </el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <el-form-item prop="duration" label="视频时长">
                                <el-time-picker
                                    v-model="submitData.duration"
                                    value-format="HH:mm:ss"
                                    placeholder="请选择视频时长"
                                    style="width: 100%"
                                    :clearable="false">
                                </el-time-picker>
                            </el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <el-form-item prop="attachmentKey" label="附件">
                                <ms-video-upload v-model="submitData.attachmentKey" :fileSuffix="videoFileSpace" @bindData="fileDataDeal" v-if="!getLoading"></ms-video-upload>
                            </el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <el-form-item label="视频封面" prop="cover">
                                <ms-single-image v-model="submitData.cover" :loadingData = "isLoading" :isVideoOperation = "isVideo" :upFileSize="0.5"></ms-single-image>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-col>
            </el-row>
        </el-form>
        
        <!-- 提交按钮 -->
        <footer-tool-bar v-loading="buttonLoading">
            <template slot="tool-content">
                <el-button type="primary" @click="info_operation('save')">保存</el-button>
                <el-button type="info" @click="info_operation('back')">返回</el-button>
            </template>
        </footer-tool-bar>
    </section>
</template>

<script>
import FooterToolBar from '@/components/ToolBar/footer-tool-bar'
import MsEditor from '@/components/MsEditor'
import MsLecturerSearch from '@/components/MsCommon/ms-lecturerpaas-search'
import MsVideoUpload from '@/components/UpFile/ms-video-upload'
export default {
    name: "video-operation",
	data () {
		return {
            isLoading:false,
            isVideo:true,
            buttonLoading: false,
            getLoading: true,
            dataId: this.$route.query.id ? this.$route.query.id : 0,
            rules: {
                title: [
                    { required: true, message: "请填写视频标题", trigger: 'blur' }
                ],
                lecturerName: [
                    { required: true, message: "请选择讲师", trigger: 'blur' }
                ],
                videoKey: [
                    { required: true, message: "请上传视频", trigger: 'change' }
                ]
            },
            submitData: { 
                "attachmentKey": "",
                "attachmentName": "",
                "content": "",
                "cover": "",
                "duration": '00:00:00',
                "formatted": 0,
                "lecturerId": null,
                "lecturerName": "",
                "summary": "",
                "title": "",
                "videoKey": ""
            },
            videoSpace: `/up/${new Date().getFullYear()}-${new Date().getMonth() + 1 < 10 ? '0'+(new Date().getMonth() + 1): new Date().getMonth() + 1}`,
            videoFileSpace: `/upload/content/day_${new Date().getFullYear()}${new Date().getMonth() + 1}${new Date().getDate()}`
		}
    },
    components: {
        FooterToolBar,
        MsEditor,
        MsLecturerSearch,
        MsVideoUpload
    },
    created() {
        this.init()
    },
    methods: {
        init() {
            this.dialog = false
            let id = this.dataId
            if(id !== 0) {
                this.getLoading = true;
                this.api.getVideoMaterialById(id).then( response => {
                this.getLoading = false;
                if(response.status === 200) {
                    let res = response.data
                    this.submitData = {
                    ...this.submitData,
                    ...res,
                    content: this.PUBLIC_Methods.unexcapeHtml(res.content)
                    }
                } else {
                    this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
                }
                }).catch(() => {
                this.getLoading = false;
                })
            } else {
                this.getLoading = false;
            }
        },
        videoDataDeal (data) {
            // this.submitData.videoKey = data.key
            this.submitData.videoKey = this.PUBLIC_Methods.dealCosUrl(data.key).m3u8Url
            if (!data.key) {return false}
            // let videoUrl = `https://${data.key}`
            let videoUrl = 'https://ali-video.medsci.cn/' + this.PUBLIC_Methods.dealCosUrl(data.key).m3u8Url
            // let videoUrl = 'https://' + this.PUBLIC_Methods.dealCosUrl(data.key).m3u8Url
            if (!this.submitData.title) {
                this.submitData.title = data.name
            }
            if (!this.submitData.cover) {
                this.isLoading = true
                // this.submitData.cover = `${videoUrl}?x-oss-process=video/snapshot,t_7000,f_jpg,w_0,h_0,m_fast`
                setTimeout(() => {
                    this.submitData.cover = 'https://ali-video.medsci.cn/' + this.PUBLIC_Methods.dealCosUrl(data.key).snapshotUrl
                    this.isLoading = false
                    // console.log(this.submitData.cover,'this.submitData.cover');
                }, 4000);
            }
            
            // 获取视频时长
            let videoEle = document.createElement('video');
            videoEle.setAttribute('src', videoUrl);
            videoEle.onloadedmetadata = ( () => {
                if (videoEle.duration) {
                    let v_hour = Math.floor(videoEle.duration/(60*60)%24);
                    let v_minute = Math.floor(videoEle.duration/60%60);
                    let v_second = Math.floor(videoEle.duration%60);

                    var vH = v_hour >= 10 ?  v_hour : '0'+ v_hour;
                    var vM = v_minute >= 10 ?  v_minute : '0'+ v_minute;
                    var vS = v_second >= 10 ?  v_second : '0'+ v_second;

                    this.submitData.duration = `${vH}:${vM}:${vS}`
                    videoEle = null;
                }
                
            })
        },
        fileDataDeal (data) {
            this.submitData.attachmentKey = data.key;
            this.submitData.attachmentName = data.name;
        },
        info_operation(val) {
            switch (val) {
                case 'save': 
                    this.$refs["submitRef"].validate( valid => {
                        if (valid) {
                            this.submitData.content = this.PUBLIC_Methods.excapeHtml(this.submitData.content)
                            this.dataId ? this.updateForm() : this.createForm()
                        }
                    })
                break;
                case 'back':
                this.$router.back();
                break;
                default: break;
            }
        },
        createForm() {
            this.buttonLoading = true;
            let params = {
                userId: this.$store.getters.info.userId,
                username: this.$store.getters.info.userName,
                ...this.submitData
            }
            this.api.saveVideoMaterial(params).then(response => {
                this.buttonLoading = false
                if(response.status === 200) {
                this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
                this.$router.back()
                } else {
                this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
                }
            }).catch(() => this.buttonLoading = false)
        },
        updateForm() {
            this.buttonLoading = true;
            let params = {
                userId: this.$store.getters.info.userId,
                username: this.$store.getters.info.userName,
                ...this.submitData
            }
            this.api.updateVideoMaterial(params).then(response => {
                this.buttonLoading = false
                if(response.status === 200) {
                this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
                this.$router.back()
                } else {
                this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
                }
            }).catch(() => this.buttonLoading = false)
        }
    }
}
</script>
