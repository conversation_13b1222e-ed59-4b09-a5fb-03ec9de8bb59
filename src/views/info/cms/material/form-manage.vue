<template>
	<ms-table
		:currentPage="searchParams.pageIndex"
		:loading="loading"
		:operationButtons="domConfig.tableButtons"
		:pageSize="searchParams.pageSize"
		:scopeConfig="scopeConfig.show"
    :scopeHeader="scopeConfig.headerShow"
		:tableData="list"
		:tableHeader="domConfig.tableHeader"
		:total="total"
    :showSelection="true"
		@current-change="current_change"
		@operation-change="operation_change"
		@size-change="size_change"
    @header-operation="header_operation"
    @handleSelectionChange="handleSelectionChange"
    class="table-svg"
	>
    <!-- 列表搜索去区域插槽 -->
		<template slot="ms-table-header">
			<div class="slot-search">
				<template v-for="(searchItem, key) in domConfig.listSearch">
					<component
						:index="searchItem.index || ''"
						:is="searchItem.component"
						:key="key"
            :width="searchItem.width || '150px'"
            :model.sync="searchParams[searchItem.model]"
            :label="searchItem.label"
						:operation="searchItem.operation || ''"
						:options="searchItem.options || []"
						:placeholder="searchItem.placeholder || ''"
						:type="searchItem.type || ''"
            :multiple="searchItem.multiple"
            :disabled="searchItem.disabled"
            :code="searchItem.code"
					></component>
				</template>
        <div class="inlineBlock">
          <el-button @click="handleClick('query')" type="primary" plain icon="el-icon-search">查询</el-button>
          <el-button @click="handleClick('reset')">重置</el-button>
        </div>
        
			</div>
			<div class="slot-button-article clearfix">
				<template v-for="(item, index) in domConfig.soltButtons">
					<el-button :key="index" 
                     :type="item.type" 
                     size="mini"
                     :icon="item.icon"
                     @click="operation_change({operation: item})"
                     :disabled="item.roleDisabled"
                     plain>{{ item.label }}</el-button>
				</template>
        <el-alert class="article-total" :title="`共搜索到${total}个表单`" type="info" show-icon></el-alert>
			</div>
			<el-dialog :visible.sync="dialog" 
                 closeable 
                 show-close
                 :close-on-click-modal="false"
                 :width="dialogWidth"
                 :title="dialogTitle">
				<component :is="dialogComponent" 
                   :model="scopeInfo" 
                   :operation="dialogOperation" 
                   @close="dialog = !dialog" 
                   @up-date="init" 
                   v-if="dialog"></component>
			</el-dialog>
		</template>
	</ms-table>
</template>

<script>
import formMixin from "./form-mixin"
import tableMixins  from "../../../common/mixins/table"
export default {
  name: "ms-article-manage",
  mixins: [tableMixins,formMixin],
  data () {
    return {
      searchParams: {},
    }
  },
  methods: {
    apiInit (params) {
      let searchParams = {...params}
      if (searchParams.createTime && searchParams.createTime.length) {
        searchParams.startTime =  searchParams.createTime[0]
        searchParams.endTime = searchParams.createTime[1]
      }
      searchParams.currentPageNo = searchParams.pageIndex
      delete searchParams.createTime
      this.api.formService_getFormList(searchParams).then(response => {
        this.loading = false
        this.total = response.data.total || 0;
        this.list = response.data.content || []
        if (response.code != 'SUCCESS') {
          this.PUBLIC_Methods.apiNotify(response.msg || '请求出错', 'warning')
        }
      }).catch(() => this.loading = false)
    },
    operation_change_module(val) {
      switch (val.operation.way) {
        case "export": 
          var data = {
            id: val.model.id,
            formId: val.model.formId,
            dimension: 1,
            userId: this.$store.getters.info.userId,
            userName: this.$store.getters.info.userName
          }
          this.api.excelExportSync(data).then(res => {
            if(res.status === 200) {
              this.excelExportSyncQuery(res.data.taskId)
              this.PUBLIC_Methods.apiNotify('导出数据处理中，请耐心等待', 'success')
            } else {
              this.PUBLIC_Methods.apiNotify(res.message || '请求出错', 'warning')
            }
          })
          break;
        default: break;
      }
    },
    excelExportSyncQuery(taskId) {
      this.api.excelExportSyncQuery(taskId).then(response => {
        if(response.status === 200) {
          window.location.href = response.data
          this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
        } else if (response.status === 102) {
          let _this = this
          setTimeout(() => {
            _this.excelExportSyncQuery(taskId)
          }, 2000);
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
      })
    }
  }
};
</script>
