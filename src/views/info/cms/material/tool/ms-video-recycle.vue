<template>
    <ms-operation-dialog>
        <template slot="content">
            <el-table :data="tableData" 
                    v-loading="getLoading" 
                    style="width: 100%">
                <el-table-column align="left" label="视频">
                    <template slot-scope="scope">
                        <div class="table-video-cell">
                            <el-image :src="scope.row.cover" fit="cover" style="width: 100px; height: 60px;padding: 6px 0 0;text-align: left; cursor: pointer;" @click="viewVideo(scope.row.videoUrl)"  />
                            <i class="el-icon-video-play" style="position:absolute; left: calc(50% - 15px); top: 28%;color: rgba(255,255,255,.7);font-size: 30px;cursor: pointer;" @click="viewVideo(scope.row.videoUrl)"></i>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column prop="title" label="标题" align="left" min-width="120"></el-table-column>
                <el-table-column prop="createdName" label="创建人" align="left" min-width="80"></el-table-column>
                <el-table-column prop="createdTime" label="创建日期" align="left"></el-table-column>
                <el-table-column label="操作" width="160" align="center">
                    <template slot-scope="scope">
                        <div class="table-operation">
                            <el-button @click="recycle_operation(scope.row, 'recover',scope.$index)" type="primary" class="scope-btn">恢复</el-button>
                            <el-button @click="recycle_operation(scope.row, 'remove',scope.$index)" type="danger" class="scope-btn">彻底删除</el-button>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
        </template>
        <template slot="footer">
            <el-pagination
                style="text-align: center; margin-top: 10px;"
                background
                layout="prev, pager, next"
                :current-page.sync="pageIndex"
                :page-size="pageSize"
                :total="total"
                @current-change="current_change">
            </el-pagination>
        </template>
    </ms-operation-dialog>
</template>

<script>
import {parseTime} from "@/utils"
export default {
	name: "ms-image-recycle",
	data () {
		return {
            getLoading: false,
            tableData: [],
            pageIndex: 1,
            pageSize: 10,
            total: 0,

            //common
            operationLocal: "",
		}
	},
	props: {
		model: Object,
		operation: String
    },
    created() {
        this.init()
    },
	methods: {
        init() {
            this.getLoading = true;
            this.api.getVideoMaterialRecycle({pageIndex: this.pageIndex, pageSize: this.pageSize}).then( response => {
                this.getLoading = false;
                if(response.status === 200) {
                    this.total = response.totalSize
                    this.tableData = response.data.map(v => {
                        return {
                            ...v,
                            createdTime: parseTime(v.createdTime, '{y}-{m}-{d} {h}:{i}')
                        }
                    })
                }
            }).catch(() => this.getLoading = false)
        },
        current_change (val) {
            this.pageIndex = val;
            this.init()
        },
        viewVideo (url) {
            window.open(url, '__blank');
        },
        recycle_operation(model, type, index) {
            let params = {
                ids: [model.id],
                userId: this.$store.getters.info.userId,
                username: this.$store.getters.info.userName
            }
            if(type === 'recover') {
                params.type = 3;
                this.getLoading = true;
                this.api.videoMaterialOpera(params).then( response => {
                    if(response.status === 200) {
                        this.tableData.splice(index, 1)
                    } else {
                        this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
                    }
                    this.getLoading = false;
                })
            } else if (type === 'remove') {
                this.$confirm('是否永久删除视频', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    params.type = 4;
                    this.getLoading = true;
                    this.api.videoMaterialOpera(params).then( response => {
                        if(response.status === 200) {
                            this.tableData.splice(index, 1)
                        } else {
                            this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
                        }
                        this.getLoading = false;
                    })
                })
                
            }
        },
	}
}
</script>

<style>
.scope-header-btn {
  position: absolute;
  top: -7px;
  right: 0px;
  font-size: 16px !important;
  border: none !important;
}
.scope-header-btn .delete-btn {
  width: 1.2em;
}
.table-video-cell{
    position: relative;
    display: inline-block;
}
</style>
