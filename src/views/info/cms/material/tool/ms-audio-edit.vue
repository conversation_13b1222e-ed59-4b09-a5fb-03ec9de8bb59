<template>
  <ms-operation-dialog>
    <template slot="content">
      <el-form ref="submitRef"
               class="rule-form"
               :model="submitData"
               :rules="rules"
               label-width="90px"
               v-loading="getLoading">
        <el-row>
          <el-col :span="24">
            <el-form-item label="标题" prop="audioTitle" style="width: 80%;">
              <el-input v-model="submitData.audioTitle"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="关键字" prop="audioExplain" style="width: 80%;">
              <el-input v-model="submitData.keyword"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="音频内容" >
              <el-upload
                :http-request="upLoad"
                :on-remove="handleRemove"
                :before-upload="beforeUpload"
                :show-file-list="false"
                action=""
              >
                <el-progress
                  type="circle"
                  v-show="showProgress"
                  :percentage="imgprogress"
                  class="avatar-progress"
                  :width="110"
                ></el-progress>
                <el-button style="background-color: #f2f2f2;" size="medium">上传音频</el-button>
                <span v-if="audioContext && audioContext.length != 0"> {{audioContext[0].url}} </span>
              </el-upload>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      
      <div>
        <ul>
          <li>格式支持mp3、wma、wav、amr、m4a</li>
          <li>文件大小不超过100M，时长不超过2小时</li>
        </ul>
      </div>

    </template>
    <template slot="footer">
      <el-button @click="chanelDialog" size="medium">取 消</el-button>
      <el-button @click="submitForm"
                 :loading="loading"
                 size="medium"
                 type="primary">确 定</el-button>
    </template>
  </ms-operation-dialog>
</template>

<script>
import { mapGetters } from "vuex";
import COS from "cos-js-sdk-v5";
export default {
  name: "ms-image-edit",
	data () {
		return {
      loading: false,
      getLoading: false,
      showProgress: false, //进度条
      imgprogress: 0,//进度条
      audioContext: [], //上传音频的内容
      audioRes:{},
      audioSize:0,//上传音频大小
      submitData: {
        "audioKey": "",
        "audioSize": "",
        "audioUrl": "",
        "keyword": '',
        "audioTitle": "",
        "duration": 0,
      },
      // 图片上传限制
      upFileFormat: "audio",
      upFileSize: 100,
      uploadTimeout: 30 * 1000,
      title: '',
      dataObj: {},
      buttonLoading: false,
      rules: {
        // fileUrl: [
        //   { required: true, message: "请上传图片", trigger: 'change' }
        // ],
        audioTitle: [
          { required: true, message: "请填写标题", trigger: 'blur' }
        ],
        // audioExplain: [
        //   { required: true, message: "请选择关键字", trigger: 'blur' }
        // ],
      },
		}
	},
	props: {
		model: Object,
		operation: String
  },
  computed: {
    ...mapGetters(["info"])
  },
  created() {
    this.init()
  },
	methods: {
    init() {
      if(this.model.id) {
        // this.submitData = {...this.model}
        this.submitData = Object.assign(this.submitData, this.model)
      } 
    },

    bindData (params) {
      this.submitData.audioKey = params.key;
      this.submitData.audioSize = params.size;
      this.submitData.audioUrl = params.url;
      this.submitData.title = params.name;
    },

		submitForm () {
      this.$refs["submitRef"].validate(valid => {
        if(valid) {
          this.loading = true;
          if(this.model.id) {
            this.submitEdit()
          } else {
            this.submitAdd()
          }
        }
      })
    },
    // 编辑
    submitEdit() {
      let params = {
        userId: this.info.userId,
        username: this.info.userName,
        id: this.submitData.id,
        duration: this.submitData.duration,
        audioUrl: this.submitData.audioUrl,
        audioTitle: this.submitData.audioTitle,
        audioSize: this.submitData.audioSize,
        audioKey: this.submitData.audioKey,
        keyword: this.submitData.keyword
      }
      this.api.updateAudioMaterial(params).then(response => {
        this.loading = false;
        if(response.status === 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
          this.$emit('up-date')
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
        
      }).catch( () => {
        this.loading = false;
      })
    },
    // 添加
    submitAdd() {
      let params = {
        userId: this.info.userId,
        username: this.info.userName,
        ...this.submitData
      }
      this.api.saveAudioMaterial(params).then(response => {
        this.loading = false;
        if(response.status === 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
          this.$emit('up-date')
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
      }).catch( () => {
        this.loading = false;
      })
    },
    chanelDialog () {
      this.$emit('close')
    },
    // 上传
    async upLoad(file) {
      let files = file.file,
          point = files.name.lastIndexOf('.'),
          suffix = files.name.substr(point),
          nDate = new Date(),
          year = nDate.getFullYear(),
          mouth = nDate.getMonth() < 10 ? '0' + (new Date().getMonth() + 1) : new Date().getMonth() + 1,
          date = nDate.getDate() < 10 ? '0' + new Date().getDate() : new Date().getDate(),
          timestamp = nDate.getTime(),
          fileNames = ''
          if(window.location.href.includes('Bioon')){
            fileNames = this.fileSuffix ? `bioon-com/${this.fileSuffix}/${timestamp}${suffix}` : `bioon-com/${year}${mouth}${date}/${timestamp}${suffix}`;
          }else {
            fileNames = this.fileSuffix ? `${this.fileSuffix}/${timestamp}${suffix}` : `${year}${mouth}${date}/${timestamp}${suffix}`;
          }

          const cos = new COS({
                SecretId: this.dataObj.accessKeyId,
                SecretKey: this.dataObj.accessKeySecret,
                XCosSecurityToken: this.dataObj.securityToken,
              });
              console.log( this.dataObj)
          await new Promise((resolve, reject) => {
                cos.uploadFile(
                  {
                    Bucket: this.dataObj.bucket,
                    Region: "ap-shanghai",
                    Key: fileNames,
                    Body:  file.file,
                  },
                  (err, data) => {
                    if (err) {
                      let message = err.message
                        if (err.code == 'ConnectionTimeoutError') {
                            message = "上传超时，请检查网络"
                        }
                        this.buttonLoading = false;
                        //this.uploadFiled(message)
                      reject(err);
                    } else {
                      this.buttonLoading = false;
                      this.submitData.audioKey = data.Location
                      this.submitData.audioSize = file.file.size
                      if(this.submitData.audioTitle == ''){
                        this.submitData.audioTitle = file.file.name
                      }
                      if(window.location.href.includes('Bioon')){
                        // this.submitData.audioUrl = `https://${data.Location}`;
                        this.submitData.audioUrl = "https://" + data.Location.replace(data.Location.split('/')[0], this.cosUrl)
                      }else {
                        // this.submitData.audioUrl = `https://${data.Location}`;
                        this.submitData.audioUrl = "https://" + data.Location.replace(data.Location.split('/')[0], this.cosUrl)
                      }
                      this.audioContext[0] = { url: file.file.name }
                      this.showProgress = false;
                      this.imgprogress = 0;
                      resolve();
                    }
                  }
                );
              });
      
    },
    beforeUpload(file) {
      let fileSuffix = file.name.substr(file.name.lastIndexOf('.')).toLowerCase()
      // 危险文件类型校验
      if (fileSuffix === '.bat' || fileSuffix === '.sh') {
          this.$message({
              message: '警告！您上传的文件存在风险',
              type: 'warning'
          })
          return false;
      }
       // 校验音频格式
      if (this.upFileFormat === 'audio' && !/\.(mp3|wma|wav|amr|m4a)$/.test(fileSuffix)) {
          this.$message({
              message: '请上传MP3、wma、wav、amr、m4a类型文件',
              type: 'warning'
          })
          return false;
      }
      // 校验文件大小
      if (this.upFileSize > 0 && file.size / 1024 / 1024 > this.upFileSize) {
          this.$message({
              message: `上传文件大小不能超过${this.upFileSize}MB`,
              type: 'warning'
          })
          return false;
      }
      return new Promise(async (resolve, reject) => {
         // 校验音频时长
         
         this.submitData.duration = await this.getAudioDuration(file)
          if(this.submitData.duration > 7200){
            this.$message({
              message: '音频时长不能超过2小时',
              type: 'warning'
            })
            return reject()
          }
          //getToken 获取OSS秘钥的接口地址
          this.buttonLoading = true;
          this.api.getToken({ type:  1 }).then(response => {
              this.dataObj = {
                  accessKeyId: response.data.accessKeyId,
                  accessKeySecret: response.data.accessKeySecret,
                  bucket: response.data.publicBucketName,
                  securityToken: response.data.securityToken,
                  timeout: this.uploadTimeout || 50 * 1000
              }
              
              resolve(true)
          }).catch(() => {
              reject(false)
          })
      })
    },
        // 音频文件时长
    getAudioDuration(file){
      return new Promise(function(resolve){
        const url = URL.createObjectURL(file);
        const filelement = new Audio(url);
        let duration = 0
        filelement.addEventListener("loadedmetadata",function () {
          duration = filelement.duration; // 得到视频或音频的时长，单位秒
          resolve(duration)
        })
      })
    },
    handleRemove() {
      // 因为目前都是单图上传，所以直接清空图片list
      this.audioLi = [];
    },
    // 进度条
    progressOpe(p) {
      this.showProgress = true;
      this.imgprogress = Math.floor(p * 100);
    },
	}
}
</script>

<!-- <script scoped lang="scss">

  .tip {
    margin-left: 50px;
    font-size: 14px;
    position: relative;
    top: 10px;
    color: #d7d7d7;
    li{
      list-style-type: disc;
    }
  }
</script> -->

