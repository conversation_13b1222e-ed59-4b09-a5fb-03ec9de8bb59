<template>
    <section>
        <el-tabs v-model="activeName">
            <el-tab-pane label="表单内容" name="design">
                <el-form
                    ref="formname"
                    style="text-align: left;"
                    :model="form"
                >
                    <el-form-item prop="formName"
                        :rules="[
                            { required: true, message: '请输入调研  / 问卷 / 表单标题', trigger: 'blur' },
                        ]"
                    >
                        <el-input v-model="form.formName" placeholder="调研 / 问卷 / 表单标题"></el-input>
                    </el-form-item>
                </el-form>
                <div class="form-design-style">
                    <avue-form-design ref="avue" :options="options" :includeFields="includeFields"></avue-form-design>
                </div>
            </el-tab-pane>
        </el-tabs>

        <!-- 提交按钮 -->
        <footer-tool-bar v-loading="buttonLoading">
            <template slot="tool-content">
                <el-button type="primary" @click="info_operation('save')"
                    >保存</el-button
                >
                <el-button type="info" @click="info_operation('back')"
                    >返回</el-button
                >
            </template>
        </footer-tool-bar>
    </section>
</template>
<script>
import FooterToolBar from "@/components/ToolBar/footer-tool-bar"
import "@/plugins/avue-form-design"
import { mapGetters } from "vuex";

export default {
    name: "formMenu",
    data() {
        return {
            // 所选tab
            activeName: "design",
            buttonLoading: false,
            // 表单属性
            form: {
                formName: "", // 名字
                content: {},
            },
            options: {},
            // includeFields: ['group', 'dynamic', 'title', 'input', 'password', 'textarea', 'number', 'url', 'array', 'img', 'radio', 'checkbox', 'select', 'cascader', 'upload', 'year', 'month', 'week', 'date', 'time', 'datetime', 'daterange', 'timerange', 'datetimerange', 'icon', 'switch', 'rate', 'slider', 'color']
            includeFields: ['dynamic', 'title', 'input', 'password', 'textarea', 'number', 'url', 'array', 'img', 'radio', 'checkbox', 'select', 'cascader', 'upload', 'signature', 'region', 'sort', 'proportion', 'year', 'month', 'week', 'date', 'time', 'datetime', 'daterange', 'timerange', 'datetimerange', 'icon', 'switch', 'rate', 'slider', 'color']
        }
    },
    computed: {
      ...mapGetters(["info"])
    },
    components: {
        FooterToolBar,
    },
    mounted() {
        this.init()
    },
    methods: {
        async init() {
            const { formId } = this.$route.query;
            if (formId) {
                const res = await this.api.formService_formDetails(formId)
                if (res.code != 'SUCCESS') {
                    return this.PUBLIC_Methods.apiNotify(res.msg || '请求出错', 'warning')
                }
                this.form = {
                  formName: res.data.formName,
                  content: res.data.formContent ? res.data.formContent : {}
                }
                String.prototype.toFunction = function() {
                    return eval('(' + this + ')')
                }
                let json = res.data.formContent ? res.data.formContent : {}
                if(json.column && json.column.length) {
                  json.column.forEach(item => {
                    if(item.change) {
                        item.change = item.change.toFunction()
                    }
                    if(item.click) {
                        item.click = item.click.toFunction()
                    }
                    if(item.focus) {
                        item.focus = item.focus.toFunction()
                    }
                    if(item.blur) {
                        item.blur = item.blur.toFunction()
                    }
                  })
                }
                if(json.group && json.group.length) {
                  json.group.forEach(row => {
                    if(row.column && row.column.length) {
                      row.column.forEach(item => {
                        if(item.change) {
                          item.change = item.change.toFunction()
                          }
                          if(item.click) {
                              item.click = item.click.toFunction()
                          }
                          if(item.focus) {
                              item.focus = item.focus.toFunction()
                          }
                          if(item.blur) {
                              item.blur = item.blur.toFunction()
                          }
                      })
                    }
                  })
                }
                this.options = json
            }
        },
        // 保存
        async handleSave(values) {
            if (!values.column.length && !values.group) {
                this.$message.warning("请先设计模板！")
                return
            }
            this.buttonLoading = true;
            let json = values
            if(json.column && json.column.length) {
              json.column.forEach(item => {
                if(item.change) {
                    item.change = item.change.toString()
                }
                if(item.click) {
                    item.click = item.click.toString()
                }
                if(item.focus) {
                    item.focus = item.focus.toString()
                }
                if(item.blur) {
                    item.blur = item.blur.toString()
                }
              })
            }
             if(json.group && json.group.length) {
               json.group.forEach(row => {
                 if(row.column && row.column.length) {
                   row.column.forEach(item => {
                    if(item.change) {
                      item.change = item.change.toString()
                      }
                      if(item.click) {
                          item.click = item.click.toString()
                      }
                      if(item.focus) {
                          item.focus = item.focus.toString()
                      }
                      if(item.blur) {
                          item.blur = item.blur.toString()
                      }
                  })
                 }
              })
             }
            const params = {
                ...this.form,
                content: json,
                createdId: this.$store.getters.info.userId,
                createdName: this.$store.getters.info.userName
            }
            const { formId } = this.$route.query;
            if (formId) {
                const res = await this.api.formService_updateForm(Object.assign({...params,formId:formId}))
                if (res.code != 'SUCCESS') {
                    this.PUBLIC_Methods.apiNotify(res.msg || '请求出错', 'warning')
                    return
                }
            } else {
                const res = await this.api.formService_saveForm(params)
                if (res.code != 'SUCCESS') {
                    this.PUBLIC_Methods.apiNotify(res.msg || '请求出错', 'warning')
                    return
                }
            }
            this.buttonLoading = false;
            this.$router.back()
        },
        // 按钮
         info_operation(val) {
            switch (val) {
              case "save":
                  this.$refs.formname.validate(async (valid) => {
                      if (valid) {
                        const values = await this.$refs.avue.getData()
                        await this.handleSave(values)
                      }
                  })
                  break
              case "back":
                  this.$router.back()
                  break
              default:
                  break
            }
        },
    },
}
</script>

<style scoped lang="scss">
.form-design-style {
    width: 100%;
    height: calc(100vh - 230px);
    border-radius: 4px;
    border: 1px solid #DCDFE6;
    text-align: left;
    /deep/ .ant-form-item-control {
        text-align: left;
    }
    /deep/ label {
        font-weight: 500;
    }
    /deep/ iframe {
      display: none;
    }
}
</style>
