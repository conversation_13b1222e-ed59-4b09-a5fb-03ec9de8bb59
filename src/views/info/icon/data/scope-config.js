const scopeConfig = {
  show: {
    status: () => {
      return {
        type: 'status',
        rule: {
          0: { label: '禁用', background: '#A7ADBD' },
          1: { label: '正常', background: '#40A23F' }
        }
      }
    },
    pid: () => {
      return {
        type: 'code',
        rule: {
          14: { label: '临床工具'},
          21: { label: '科研工具'},
          170: { label: 'H5'},
          26: { label: '其他'}
        }
      }
    },
    createdTime: () => {
      return {
        type: 'formatTime',
        cFormat: '{y}-{m}-{d} {h}:{i}'
      }
    },
    updatedTime: () => {
      return {
        type: 'formatTime',
        cFormat: '{y}-{m}-{d} {h}:{i}'
      }
    }
  },
  headerShow: {

  }
}

export default scopeConfig;
