const domConfig = {
  tableHeader: [
    { label: '序号', property: 'sort', sortable: true, width: '60' },
    { label: '图标名称', property: 'name'},
    { label: '所属分类', property: 'pid', width: '120' },
    { label: '创建时间', property: 'createdTime', width: '130' },
    { label: '更新时间', property: 'updatedTime', width: '130' },
    { label: '状态', property: 'status'}
  ],
  tableButtons: [
    {
      label: '编辑',
      icon: '',
      role: '',
      operation: 'edit',
      component: 'icon-operation',
      way: 'page',
      type: 'primary',
      path: 'icon-operation',
      params: ['id'],
      identify: 'edit'
    },
    {
      label: '',
      icon: '',
      role: '',
      operation: 'editStatus',
      component: 'msIconOperation',
      way: 'dialog',
      field: 'status',
      rule: {
        1: { label: '去审', type: '', operation: 'toreview' },
        0: { label: '审核', type: 'success', operation: 'approval' }
      },
      identify: 'status'
    },
    // {
    //   label: '删除',
    //   way: 'dialog',
    //   type: 'danger',
    //   operation: 'delete',
    //   component: 'msIconOperation',
    //   identify: 'single_delete'
    // }
  ],
  soltButtons: [
    { 
      label: '添加图标', 
      type: 'primary', 
      icon: 'el-icon-plus',
      operation: 'created',
      component: 'icon-operation',
      way: 'page',
      path: 'icon-operation',
      params: ['id'],
      identify: 'created'
    },
    { 
      label: '批量审核', 
      type: 'primary',
      operation: 'approval',
      component: 'msIconOperation',
      way: 'batch',
      identify: 'batch_approval'
    },
    { 
      label: '批量去审', 
      type: 'primary',
      operation: 'toreview',
      component: 'msIconOperation',
      way: 'batch',
      identify: 'batch_toreview'
    },
    // { 
    //   label: '批量删除',
    //   type: 'info',
    //   icon: 'el-icon-close',
    //   operation: 'delete',
    //   component: 'msIconOperation',
    //   way: 'batch',
    //   identify: 'batch_delete'
    // }
  ]
}

export default domConfig;
