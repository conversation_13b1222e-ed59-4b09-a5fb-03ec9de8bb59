<template>
  <section class="form-tab" v-loading="getLoading">

    <el-tabs v-model="activeName">
      <el-tab-pane label="图标信息" name="content">
        <el-form :model="submitData"
             ref="submitRef"
             class="rule-form info-form"
             :rules="rules"
             label-width="70px">
          <el-row :gutter="10"> 
            <el-col :span="12">
              <el-form-item label="图标名称" prop="name">
                <el-input v-model="submitData.name" style="width: 100%" placeholder="请输入4个字以内的图标名称" maxlength="4"></el-input>
              </el-form-item>
              <el-form-item label="跳转链接" prop="url">
                <el-input v-model="submitData.url" style="width: 100%"  placeholder="请输入图标跳转链接"></el-input>
              </el-form-item>
              <el-form-item label="所属分类" prop="pid" v-if="!dataId || (dataId && [14,21,26].includes(submitData.pid))">
                <el-select v-model="submitData.pid" placeholder="请选择" style="width: 100%">
                  <el-option label="临床工具" :value="14"></el-option>
                  <el-option label="科研工具" :value="21"></el-option>
                  <el-option label="H5" :value="170"></el-option>
                  <el-option label="其他" :value="26"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="排序" prop="sort">
                <el-input-number v-model="submitData.sort" :step="1" step-strictly :min="1" placeholder="请输入排序"></el-input-number>
              </el-form-item>
              <el-form-item label="图片icon" prop="pic">
                <ms-single-image v-model="submitData.pic" :upFileSize="0.5"></ms-single-image>
              </el-form-item>
              <el-form-item label="推荐" prop="recommend">
                <el-switch v-model="submitData.recommend" :active-value="1" :inactive-value="0"></el-switch>
              </el-form-item>
              <el-form-item label="需要登录" prop="isLogin">
                <el-switch v-model="submitData.isLogin" :active-value="1" :inactive-value="0"></el-switch>
              </el-form-item>
              <el-form-item label="支持分享" prop="isCanShare">
                <el-switch v-model="submitData.isCanShare" :active-value="1" :inactive-value="0"></el-switch>
              </el-form-item>
              <el-form-item label="是否固定" prop="isFixed">
                <el-switch v-model="submitData.isFixed" :active-value="1" :inactive-value="0"></el-switch>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-tab-pane>
      <el-tab-pane label="显示栏目" name="categoryDtoList">
        <ms-info-setting :categoryModel.sync="submitData.categoryDtoList" :categoryConfigChild="{moduleName: 'applicationTool'}" headerShow></ms-info-setting>
      </el-tab-pane>
    </el-tabs>
    <!-- 表单内容 -->
    
    <!-- 提交按钮 -->
    <footer-tool-bar v-loading="buttonLoading">
      <template slot="tool-content">
        <el-button type="primary" @click="info_operation('save')">保存</el-button>
        <el-button type="info" @click="info_operation('back')">返回</el-button>
      </template>
    </footer-tool-bar>
  </section>
</template>

<script>
import FooterToolBar from '@/components/ToolBar/footer-tool-bar'
import msInfoSetting from '@/components/MsCommon/ms-info-setting'
import {ms_rule_url} from "@/utils/form-rule.js";
export default {
  name: "icon-operation",
	data () {
		return {
      buttonLoading: false,
      getLoading: false,
      dataId: this.$route.query.id ? this.$route.query.id : 0,
      rules: {
        name: [
          { required: true, message: "请输入图标名称", trigger: 'blur' }
        ],
        url: [
          { required: true, message: "请输入跳转链接", trigger: 'blur' },
          { validator: ms_rule_url, trigger: 'blur' }
        ],
        sort: [
          { required: true, message: "请输入排序", trigger: 'blur' }
        ],
        pic: [
          { required: true, message: "请上传图标icon", trigger: 'change' }
        ],
      },
      submitData: { 
        name: '',
        url: '',
        pic: '',
        pid: '',
        sort: '',
        isCanShare: 0,
        isLogin: 0,
        isFixed: 0,
        recommend: 0,
        categoryDtoList: []
      },
      activeName: 'content'
		}
  },
  components: {
    FooterToolBar,
    msInfoSetting
  },
  created() {
    this.init()
  },
  methods: {
    init() {
      this.dialog = false
      let id = this.dataId
      if(id !== 0) {
        this.getLoading = true;
        this.submitData.id = id;
        this.api.detailToolApplication({id: id}).then( response => {
          this.getLoading = false;
          if(response.status === 200) {
            let res = response.data
            this.submitData = {
              ...this.submitData,
              ...res,
              categoryDtoList: res.categoryDtoList ? res.categoryDtoList : []
            }
          } else {
            this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
          }
        }).catch(() => {
          this.getLoading = false;
        })
      } 
    },
    info_operation(val) {
      switch (val) {
        case 'save': 
          this.$refs["submitRef"].validate( valid => {
            if (valid) {
              this.dataId ? this.updateForm() : this.createForm()
            }
          })
          break;
        case 'back':
          this.$router.back();
          break;
        default: break;
      }
    },
    createForm() {
      this.buttonLoading = true;
      let params = {
        userId: this.$store.getters.info.userId,
        username: this.$store.getters.info.userName,
        ...this.submitData
      }
      this.api.addToolApplication(params).then(response => {
        this.buttonLoading = false
        if(response.status === 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
          this.$router.back()
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
      }).catch(() => this.buttonLoading = false)
    },
    updateForm() {
      this.buttonLoading = true;
      let params = {
        ...this.submitData
      }
      this.api.updateToolApplication(params).then(response => {
        this.buttonLoading = false
        if(response.status === 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
          this.$router.back()
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
      }).catch(() => this.buttonLoading = false)
    }
  }
}
</script>
