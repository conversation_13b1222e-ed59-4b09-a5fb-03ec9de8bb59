<template>
  <ms-operation-dialog>
    <template slot="content">
      <el-table :data="tableData" 
                v-loading="getLoading" 
                style="width: 100%;"
                border
                @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align='center'>
        </el-table-column>
        <el-table-column prop="name" label="项目名称" align="center" show-overflow-tooltip></el-table-column>
      </el-table>
    </template>
    <template slot="footer">
      <el-button @click="submitForm"
                 :loading="loading"
                 size="mini"
                 type="primary">确 定</el-button>
      <el-button @click="$emit('close')"
                 size="mini">取 消</el-button>
    </template>
  </ms-operation-dialog>
</template>

<script>
import { mapGetters } from "vuex";
export default {
	name: "ms-article-project",
	data () {
		return {
      getLoading: false,
      loading: false,
      tableData: [],
      selectData: []
		}
	},
	props: {
		model: Object,
		operation: String
  },
  computed: {
    ...mapGetters(["info"])
  },
  created() {
    this.init()
  },
	methods: {
    init() {
      this.getLoading = true;
      this.api.getProjectDicList({}).then( response => {
        this.getLoading = false;
        if(response.status === 200) {
          this.tableData = response.data
        }
      }).catch(() => this.getLoading = false)
    },
    handleSelectionChange(val) {
      this.selectData = val
    },
     submitForm () {
      if (!this.selectData || !(this.selectData.length > 0)) {
        return this.$message.warning('请选择项目')
      }
      let projectIds = this.selectData.map(v => {
        return v.projectId
      })
      let params = {
        userId: this.info.userId,
        username: this.info.userName,
        id: this.model.id,
        projectId: this.info.projectId,
        otherProjectIds: projectIds
      }
      this.api.pushToOtherProjects(params).then( response => {
        if(response.status === 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
          this.$emit('up-date')
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
        this.loading = false
      }).catch( () => {
        this.loading = false;
        this.$emit('close')
      })
    }
	}
}
</script>
