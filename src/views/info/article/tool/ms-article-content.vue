<template>
  <section ref="articleContentTemp">
    <el-form :model="submitData"
             ref="submitRef"
             class="rule-form info-form"
             :rules="rules"
             label-width="70px">
      <el-row :gutter="20">
        <el-col :span="18">
          <el-row>
            <el-col :span="24">
              <el-form-item label="标题中文" prop="title">
                <el-input v-model="submitData.title" style="width: 100%"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="外向链接" prop="linkOutUrl">
                <el-input v-model="submitData.linkOutUrl" style="width: 100%"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="创作选项">
                <el-checkbox-group v-model="submitData.creationTypeList">
                  <el-checkbox :label=1 >原创</el-checkbox>
                  <el-checkbox :label=2 style="margin-left: 5px;">软文</el-checkbox>
                </el-checkbox-group>
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="来源" prop="articleFrom">
                <el-input v-model="submitData.articleFrom" style="width: 100%" placeholder="非原创请直接填写来源"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="作者" prop="author">
                <el-input v-model="submitData.author" style="width: 100%"></el-input>
              </el-form-item>
            </el-col>
            <!-- <el-col :span="12">
              <el-form-item label="原始链接" prop="originalUrl">
                <el-input v-model="submitData.originalUrl" style="width: 100%"></el-input>
              </el-form-item>
            </el-col> -->
            <el-col :span="24">
              <el-form-item label="内容" prop="content">
                <ms-editor :height="360" v-model="submitData.content"></ms-editor>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="摘要" prop="summary">
                <el-input v-model="submitData.summary" type="textarea" :rows="4" @keyup.native="summaryKeyUp = true" maxlength="100" show-word-limit></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row v-if="categoryConfigChild.moduleName != 'article'" class="dingshi">
            <el-col :span="8">
              <el-form-item prop="publishedTime" label="定时上线">
                <el-date-picker
                  @change="change"
                  v-model="submitData.publishedTime"
                  type="datetime"
                  format="yyyy-MM-dd HH:mm"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期">
              </el-date-picker>
<!--                :picker-options="pickerOptions"-->
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="发布者：" label-width="70px" prop="editorId">
                <span class="font-12"><ms-createby-search :model.sync="submitData.editorId" :modelName.sync="submitData.editor"></ms-createby-search></span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="编辑者：" label-width="100px">
                <span class="font-12"><ms-createby-search :model.sync="submitData.editorId" :modelName.sync="submitData.editor"></ms-createby-search></span>
              </el-form-item>
            </el-col>
            <!-- <el-col :span="8" v-show="formatDisabled">
              <el-form-item label="创建人：" label-width="80px">
                <span class="font-12">{{submitData.createdName}}</span>
              </el-form-item>
            </el-col>
            <el-col :span="8" v-show="formatDisabled">
              <el-form-item label="创建时间：" label-width="80px">
                <span class="font-12">{{submitData.createdTime | parseTime}}</span>
              </el-form-item>
            </el-col> -->
          </el-row>
          <el-row v-if="categoryConfigChild.moduleName != 'article'" class="dingshi">
            <el-col :span="8" v-show="formatDisabled">
              <el-form-item label="创建人：" label-width="80px">
                <span class="font-12">{{submitData.createdName}}</span>
              </el-form-item>
            </el-col>
            <el-col :span="8" v-show="formatDisabled">
              <el-form-item label="创建时间：" label-width="80px">
                <span class="font-12">{{submitData.createdTime | parseTime}}</span>
              </el-form-item>
            </el-col>
          </el-row>
        </el-col>
        <el-col :span="6" class="info-form-right">
          <el-row>
            <el-col :span="24">
              <el-form-item label="显示选项" >
                <div style="display:flex">
                  <el-checkbox v-model="submitData.appVisible" style="width: 45%" :true-label="1" :false-label="0">APP显示</el-checkbox>
                  <el-checkbox v-model="submitData.pcVisible" style="width: 45%" :true-label="1" :false-label="0">PC显示</el-checkbox>
                </div>
                <div>
                  <el-row type="flex" style="margin-top: 5px;" v-permission="['/article','recommend']">
                  <el-checkbox v-model="submitData.recommend" style="width: 45%" :true-label="1" :false-label="0">推荐</el-checkbox>
                  <ms-picker :model.sync="submitData.recommendEndTime" type="datetime"></ms-picker>
                </el-row>
                <el-row type="flex" style="margin-top: 5px;" v-permission="['/article','sticky']">
                  <el-checkbox v-model="submitData.sticky" style="width: 45%" :true-label="1" :false-label="0">固顶</el-checkbox>
                  <ms-picker :model.sync="submitData.stickyEndTime" type="datetime"></ms-picker>
                </el-row>
                </div>
                <!-- <el-row type="flex" style="margin-top: 5px;" v-permission="['/article','sticky']">
                  <el-checkbox v-model="submitData.diamonds" style="width: 45%" :true-label="1" :false-label="0">钻石位</el-checkbox>
                  <ms-picker :model.sync="submitData.diamondsEndTime" type="datetime"></ms-picker>
                </el-row> -->
              </el-form-item>
            </el-col>
            <!-- <el-col :span="24">
              <el-form-item label="短视频">
                <el-select
                  style="width: 100%"
                  v-model="submitData.pointTitle"
                  filterable
                  remote
                  clearable
                  reserve-keyword
                  placeholder="请选择视频"
                  :remote-method="remoteMethod"
                  @change="handleSelect"
                  :loading="loading">
                  <el-option
                    v-for="item in videoOptions"
                    :key="item.id"
                    :label="item.title"
                    :value="item.id">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="">
                <el-select
                  style="width: 100%"
                  v-model="submitData.courseTitle"
                  filterable
                  remote
                  clearable
                  reserve-keyword
                  placeholder="请选择对应公开课/精品课"
                  :remote-method="remoteMethodTwo"
                  @change="handleChange"
                  :loading="loadingTwo">
                  <el-option
                    v-for="item in urlOptions"
                    :key="item.id"
                    :label="item.title"
                    :value="item.id">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col> -->
            <!-- <el-col :span="24">
              <el-form-item label="附件" :prop="submitData.attachmentList.length ? '' : 'fileUrl'">
                <el-input v-model="submitData.fileUrl" style="width: 100%;" placeholder="输入指南资源地址"></el-input>
              </el-form-item>
              <el-form-item prop="attachmentList">
                <ms-file-upload v-model="submitData.attachmentList" buttonLabel="手动上传资源"></ms-file-upload>
              </el-form-item>
            </el-col> -->
            <el-col :span="24">
              <el-form-item label="附件" :prop="submitData.attachmentList.length ? '' : 'fileUrl'">
                <el-input v-model="submitData.fileUrl" style="width: 100%;" placeholder="输入资讯资源地址"></el-input>
              </el-form-item>
              <el-form-item prop="attachmentList">
                <ms-file-upload  v-model="submitData.attachmentList" :limit="10" accept=".doc, .docx, .pdf, .pub, .vip" :upFileSize="50" buttonLabel="手动上传资源"></ms-file-upload>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="权限" prop="opened">
                <el-select v-model="submitData.opened" style="width: 100%">
                  <el-option :value="1" label="开放阅读"></el-option>
                  <el-option :value="0" label="登录阅读"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="24" v-if="categoryConfigChild.moduleName != 'article'">
              <el-form-item label="积分" prop="integral" v-show="+submitData.opened === 0">
                <el-input-number v-model="submitData.paymentAmount" :min="0" style="width: 100%"></el-input-number>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="关键词" prop="tagList">
                <MsTagSearch v-model="submitData.tagList" :notMul="false"></MsTagSearch>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="关联医院" prop="hospitalTagList">
                <ms-hospital-search v-model="submitData.hospitalTagList" :placeholder="'请输入医院名称搜索，并选择'" style="width:100%"></ms-hospital-search>
              </el-form-item>
            </el-col>
            <!-- <el-col :span="24">
              <el-form-item label="关联医院" >
                <MsHospitalSearch v-model="submitData.tagList" :notMul="false"></MsHospitalSearch>
              </el-form-item>
            </el-col> -->
            <el-col :span="24">
              <el-form-item label="关联专家" prop="professorTagList">
                <ms-speaker-search v-model="submitData.professorTagList" :placeholder="'请输入专家名称搜索，并选择'"></ms-speaker-search>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="所属期刊" prop="journalId">
                <MsJournalSearch v-model="submitData.journalId"></MsJournalSearch>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="关联调研" prop="professorTagList">
                <ms-survey-search v-model="submitData.surveyId" :model.sync="submitData.surveyName" @bindData="bindForm" :placeholder="'请输入关键词搜索，并选择'" :isOpenResult="0" style="width:100%"></ms-survey-search>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="资讯拓展">
                <el-row>
                  <el-col :span="18"><MsTagSearch v-model="submitData.articleKeyword" style="width: 95%;display: inline-block;" placeholder="资讯标题关键词" :modelId.sync="submitData.articleKeywordId" :notMul="true"></MsTagSearch></el-col>
                  <el-col :span="6"><el-input placeholder="数量" style="width: 100%" v-model="submitData.articleKeywordNum"></el-input></el-col>
                </el-row>
              </el-form-item>
              <el-form-item label="指南拓展">
                <el-row>
                  <el-col :span="18"><MsTagSearch v-model="submitData.guiderKeyword" style="width: 95%;display: inline-block;" placeholder="指南标题关键词" :modelId.sync="submitData.guiderKeywordId" :notMul="true"></MsTagSearch></el-col>
                  <el-col :span="6"><el-input placeholder="数量" style="width: 100%" v-model="submitData.guiderKeywordNum"></el-input></el-col>
                </el-row>
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item label="封面图片" prop="cover">
                <ms-single-image v-model="submitData.cover" :upFileSize="0.5"></ms-single-image>
              </el-form-item>
            </el-col>
            <el-col :span="24" style="text-align: left">
              <el-form-item label="自动水印" prop="waterMark" style="display: inline-block;">
                  <el-switch v-model="submitData.waterMark" :disabled="formatDisabled" :active-value="1" :inactive-value="0"></el-switch>
              </el-form-item>
            </el-col>
            <el-col :span="24" style="text-align: left">
              <el-form-item label="引导下载APP开关" prop="guideDownload" style="display: inline-block;">
                  <el-switch v-model="submitData.guideDownload" :active-value="1" :inactive-value="0"></el-switch>
              </el-form-item>
            </el-col>
            <el-col :span="24" v-if="categoryConfigChild.moduleName == 'article'">
              <el-form-item prop="publishedTime" label="定时上线">
                <el-date-picker
                  style="width: 100%;"
                  @change="change"
                  v-model="submitData.publishedTime"
                  type="datetime"
                  format="yyyy-MM-dd HH:mm"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期">
                </el-date-picker>
<!--                :picker-options="pickerOptions"-->
              </el-form-item>
            </el-col>
            <!-- <el-col :span="24">
              <el-form-item label="发布者：" label-width="70px" prop="editorId">
                <span class="font-12"><ms-createby-search :model.sync="submitData.editorId" :modelName.sync="submitData.editor"></ms-createby-search></span>
              </el-form-item>
            </el-col> -->
            <el-col :span="24">
              <el-form-item label="短视频">
                <el-select
                  style="width: 100%"
                  v-model="submitData.pointTitle"
                  filterable
                  remote
                  clearable
                  reserve-keyword
                  placeholder="请选择视频"
                  :remote-method="remoteMethod"
                  @change="handleSelect"
                  :loading="loading">
                  <el-option
                    v-for="item in videoOptions"
                    :key="item.id"
                    :label="item.title"
                    :value="item.id">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="">
                <el-select
                  style="width: 100%"
                  v-model="submitData.courseTitle"
                  filterable
                  remote
                  clearable
                  reserve-keyword
                  placeholder="请选择对应公开课/精品课"
                  :remote-method="remoteMethodTwo"
                  @change="handleChange"
                  :loading="loadingTwo">
                  <el-option
                    v-for="item in urlOptions"
                    :key="item.id"
                    :label="item.title"
                    :value="item.id">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-col>
      </el-row>
    </el-form>
  </section>
</template>

<script>
import MsEditor from '@/components/MsEditor'
import MsTagSearch from '@/components/MsCommon/ms-tag-search'
import MsJournalSearch from '@/components/MsCommon/ms-journal-search'
import { getEditContent } from '@/utils/index'
import { mapGetters } from "vuex";
import msHospitalSearch from "@/components/MsCommon/ms-hospital-search-article"
import msSpeakerSearch from '@/components/MsCommon/ms-speaker-search2'
import MsSurveySearch from '@/components/MsCommon/ms-survey-search'
import {ms_rule_url, ms_not_zero, ms_rule_upload_url_article} from "@/utils/form-rule.js";
// import { getSession } from "@/utils/auth";
import moment from 'moment'
export default {
	name: "ms-article-content",
  props: {
    categoryConfigChild: {
      type: Object,
      default: () => {
        return {}
      }
    },
  },
	data () {
		return {
      hospital: '',
      speakers: '',
      speakerData: {
        speakerId: '',
        speakerName: '',
      },
      addData: {
        hospitalId: '',
        hospitalName: '',
      },
      loading: false,
      videoOptions: [],
      urlOptions: [],
      loadingTwo: false,
      rules: {
        title: [
          { required: true, message: "请填写文章标题", trigger: 'blur' }
        ],
        content: [
          { required: true, message: "请填写文章内容", trigger: 'change' }
        ],
        articleFrom: [
          { required: true, message: "请填写文章来源", trigger: 'change' }
        ],
        linkOutUrl: [
          { validator: ms_rule_url, trigger: 'blur' }
        ],
        originalUrl: [
          { validator: ms_rule_url, trigger: 'blur' }
        ],
        summary: [
          { required: true, message: "请填写摘要", trigger: 'blur' }
        ],
        editorId: [
          { validator: ms_not_zero, trigger: 'change' }
        ],
        fileUrl: [
          { required: false,validator: ms_rule_upload_url_article, trigger: 'blur' }
        ]
      },
      formatDisabled: false,
      summaryKeyUp: false,
      pickerOptions: {
        disabledDate: time => {
          let delay = new Date().getTime() - 86400000
          if(delay){
            // 小于接口返回的值得日期全部禁选
            return time.getTime() < delay
          }
        }
      }
		}
  },
  updated() {
    var that = this;
    setTimeout(function() {
      let heightT = that.$refs.articleContentTemp.clientHeight;
      window.sessionStorage.setItem('otherDeptTop', heightT);
      console.log(that.$refs.articleContentTemp.clientHeight, 'this.$refs.articleContentTemp.clientHeight')
    }, 1000)
  },
  components: {
    MsEditor,
    MsTagSearch,
    MsJournalSearch,
    msHospitalSearch,
    msSpeakerSearch,
    MsSurveySearch
  },
  computed: {
    ...mapGetters(["submitData", "info", "editorImgArr"]),
  },
  watch: {
    'submitData.creationTypeList': function(val,oldVal) {
      if (val.includes(1)) {
        this.submitData.articleFrom = 'MedSci原创'
        this.submitData.copyright = '原创'
      } else if (oldVal.includes(1)){
        this.submitData.articleFrom = ''
        this.submitData.copyright = '转发'
      }
    },
    'submitData.id': function (val) {
      if (val) {
        this.formatDisabled = true
      }
    },
    'submitData.recommend': function (val) {
      if (val === 1) {
        this.submitData.sticky = 0
      }
    },
    'submitData.sticky': function (val) {
      if (val === 1) {
        this.submitData.recommend = 0
      }
    },
    'submitData.content': function (val) {
      console.log(val,'val');
      if (val) {
        if (!this.submitData.id && !this.summaryKeyUp) {
          this.submitData.summary = this.PUBLIC_Methods.unexcapeHtml(getEditContent(val))
        }
      }
    },
    editorImgArr: function(val) {
      if (val && val.length > 0 && !this.submitData.id && !this.submitData.cover) {
        this.submitData.cover = val[0]
      }
    }
  },
  created() {
    this.submitData.editor = this.info.userName
    this.submitData.guideDownload = 1
    // console.log(this.submitData, '222222');
  },
  mounted() {
    var that = this;
    setTimeout(function() {
      if(that.$refs.articleContentTemp){
        let heightT = that.$refs.articleContentTemp.clientHeight;
        window.sessionStorage.setItem('otherDeptTop', heightT);
        console.log(that.$refs.articleContentTemp.clientHeight, 'this.$refs.articleContentTemp.clientHeight')
      }
    }, 1000)
    // console.log(this.submitData, '8888888');
  },
	methods: {  
    bindForm(val) {
      this.submitData.surveyId = val.model.id
      this.submitData.surveyName = val.model.templateName
    },
    bindHospital(val) {
        this.addData.hospitalId = val.model.id
        this.addData.hospitalName = val.model.name
      },
      bindSpeakers(val) {
        this.speakerData.speakerId = val.model.id
        this.speakerData.speakerName = val.model.name
      },
    // 远程搜索 实时请求
    remoteMethod(query) {
      if (query !== '') {
        this.loading = true
        this.getVideoLIst(query)
      } else {
        this.videoOptions.length=0;
      }
    },
    // 获取素材list
    getVideoLIst(query) {
      let params = {
        pageIndex: 1,
        pageSize: 30,
        projectId: 1,
        projectIds: [],
        title: query,
        speakerName: ""
      }
      this.videoOptions.length = []
      this.api.getVideoMaterialList(params).then(res => {
        if(res.status == 200){
          this.loading = false
          this.videoOptions = res.data
        }
      })
    },
    handleSelect(val) {
      if(val){
        // 视频id
        let newList = this.videoOptions.filter((item)=>{
          return val == item.id
        })
        this.submitData.pointId = newList[0].id
        this.submitData.videoUrl = newList[0].videoUrl
        this.submitData.pointTitle = newList[0].title
        this.submitData.speakerInfo = newList[0].speakerInfo
      } else {
        this.submitData.pointId = null
        this.submitData.videoUrl = ''
        this.submitData.pointTitle = ''
        this.submitData.speakerInfo = ''
        this.submitData.courseId = null
        this.submitData.courseType = ''
        this.submitData.courseTitle = ''
      }
    },
    remoteMethodTwo(query) {
      if (query !== '') {
        this.loadingTwo = true
        this.getUrlList(query)
      } else {
        this.UrlOptions.length=0;
      }
    },
    // 获取课程地址
    getUrlList(query) {
      let params = {
        "pageIndex": 1,
        "pageSize": 30,
        "projectId": 1,
        "sort": 0,
        "status": 1,
        "title": query
      }
      this.urlOptions.length = 0
      this.api.videoPageList(params).then((res)=>{
        if(res.status == 200) {
          this.loadingTwo = false
          this.urlOptions = res.data
        }
      })
    },
    handleChange(val){
      // 课程id
      if(val){
        let urlNewList = this.urlOptions.filter((item)=>{
          return val == item.id
        })
        this.submitData.courseId = urlNewList[0].id
        this.submitData.courseType = urlNewList[0].type
        this.submitData.courseTitle = urlNewList[0].title
      } else {
        this.submitData.courseId = null
        this.submitData.courseType = ''
        this.submitData.courseTitle = ''
      }
    },
    change(val){
      // if(!val){
      //   this.submitData.publishedTime = val
      // } else {
      //   let time = moment(val).format("YYYY-MM-DD HH:mm") + ':59'
      //   if(new Date(time).getTime()<new Date().getTime()){
      //     this.$message({
      //       message: '不能选择当前时间之前的时间',
      //       type: 'warning'
      //     })
      //     this.submitData.publishedTime = getSession('artical-publishedTime')
      //   } else {
      //     this.submitData.publishedTime = moment(val).format("YYYY-MM-DD HH:mm") + ':00'
      //   }
      // }
      this.submitData.publishedTime = moment(val).format('YYYY-MM-DD HH:mm') + ':00'
      console.log( this.submitData.publishedTime,'===')
    },
    // 数据校验
    // validateData(callback, isDraft = false) {
    //   let multiCover = []
    //   if (this.submitData.cover) {
    //     multiCover[0] = this.submitData.cover
    //   }
    //   this.submitData.content.replace(/<img [^>]*src=['"]([^'"]+)[^>]*>/g, function (match, capture) {
    //       multiCover.push(capture)
    //   });

    //   let params = {
    //     ...this.submitData,
    //     userId: this.info.userId,
    //     username: this.info.userName,
    //     editorId: this.submitData.editorId || this.info.userId,
    //     editor: this.submitData.editor || this.info.userName,
    //     content: this.PUBLIC_Methods.excapeHtml(this.submitData.content),
    //     creationType: this.submitData.articleFrom === 'MedSci原创' ? 1 : this.submitData.creationType === 2 ? 2 : 0,
    //     approvalStatus: isDraft ? 2 : this.submitData.approvalStatus === 2 ? 0 : this.submitData.approvalStatus,
    //     multiCover: multiCover
    //   }

    //   this.$refs['submitRef'].validate(valid => {
    //     if (valid) {
    //       callback(params)
    //     }
    //   })
    // }
    validateData(callback, isDraft = false) {
      
      return new Promise((resolve) => {
        
        // let fileList = []
        // if (this.submitData.attachmentList.length > 0) {
        //   console.log(this.submitData.attachmentList,'55555555');
        //   this.submitData.attachmentList.forEach(v => {
        //     fileList.push({
        //       attachmentKey: v.fileKey,
        //       attachmentUrl: v.url,
        //       fileName: v.name
        //     })
        //   });
        // }
        // this.submitData.attachmentList = fileList
        this.submitData.approvalStatus = isDraft ? 2 : this.submitData.approvalStatus === 2 ? 0 : this.submitData.approvalStatus
        console.log(this.submitData.summary)
        this.$refs["submitRef"].validate(valid => {
          if (valid) {
            resolve()
          } 
          // else {
            
          //   this.$emit('changeTab', 'content')
          // }
        })
        let multiCover = []
      if (this.submitData.cover) {
        multiCover[0] = this.submitData.cover
      }
      this.submitData.content.replace(/<img [^>]*src=['"]([^'"]+)[^>]*>/g, function (match, capture) {
          multiCover.push(capture)
      });

      let params = {
        ...this.submitData,
        userId: this.info.userId,
        username: this.info.userName,
        editorId: this.submitData.editorId || this.info.userId,
        editor: this.submitData.editor || this.info.userName,
        content: this.PUBLIC_Methods.excapeHtml(this.submitData.content),
        creationType: this.submitData.articleFrom === 'MedSci原创' ? 1 : this.submitData.creationType === 2 ? 2 : 0,
        approvalStatus: isDraft ? 2 : this.submitData.approvalStatus === 2 ? 0 : this.submitData.approvalStatus,
        multiCover: multiCover
      }

      this.$refs['submitRef'].validate(valid => {
        if (valid) {
          callback(params)
        }
      })
      })
    }
	}
}
</script>
<style scope>
.ms-radio-con {
  margin-right: 5px !important;
}
</style>
<style scope lang="scss">
.info-form{
  /deep/ .el-checkbox {
    margin-right: 0px;
  }
  &-right {
    /deep/ .el-form-item {
      margin-bottom: 16px;
    }
  }
  .expand {
    .label {
      color: #409EFF;
      font-size: 12px;
      width: 40px;
      display: inline-block;
      text-align: right;
    }
    /deep/ .el-form-item {
      padding: 4px 0;
      margin-bottom: 14px;
      margin-top: -4px;
      background-color: #E5E6E9;
      border-radius: 4px;
    }
  }
   .dingshi /deep/ .el-input__inner {
    width: 164px !important
  }
}
</style>
