<template>
  <ms-operation-dialog>
    <template slot="content">
      <el-row type="flex" style="padding-bottom: 8px;">
        <el-col :span="showVisible ? 12 : 24"> 
          <el-table :data="tableData" 
                v-loading="getLoading" 
                style="width: 100%">
            <el-table-column align="left" label="标题" :min-width="showVisible ? 150 : 300">
              <template slot-scope="scope">
                <el-link @click="setContentData(scope.$index,scope.row)" :type="scope.$index === chooseIndex ? 'primary' : 'default'">{{scope.row.title}}</el-link>
              </template>
            </el-table-column>
            <el-table-column prop="createdName" label="创建人" align="left"></el-table-column>
            <el-table-column prop="createdTime" label="创建日期" align="left"></el-table-column>
            <el-table-column label="操作" width="160" align="center">
              <template slot-scope="scope">
                <div class="table-operation">
                  <el-button @click="recycle_operation(scope.row, 'recover', scope.$index)" type="primary" class="scope-btn">恢复</el-button>
                  <el-button @click="recycle_operation(scope.row, 'remove', scope.$index)" type="danger" class="scope-btn">彻底删除</el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination
            style="text-align: center; margin-top: 10px;"
            background
            layout="prev, pager, next"
            :current-page.sync="pageIndex"
            :page-size="pageSize"
            :total="total"
            @current-change="current_change">
          </el-pagination>
        </el-col>
        <el-col :span="12" v-if="showVisible">
          <el-table style="width: 100%" :data="conData" v-loading="previewLoading">
            <el-table-column>
              <template slot="header" slot-scope="scope">
                文章预览 （点击标题可预览）
                <el-button class="scope-header-btn" @click="closepreview(scope.row)"><svg-icon icon-class="icon-tuichu" class="delete-btn"></svg-icon></el-button>
              </template>
              <template slot-scope="scope">
                <div v-html="scope.row.content"></div>
              </template>
            </el-table-column>
          </el-table>
        </el-col>
      </el-row>
    </template>
  </ms-operation-dialog>
</template>

<script>
import {parseTime} from "@/utils"
export default {
	name: "ms-article-recycle",
	data () {
		return {
      getLoading: false,
      tableData: [],
      userInfo: {},
      conData: [],
      showVisible: false,
      chooseIndex: -1,
      previewLoading: false,
      pageIndex: 1,
      pageSize: 10,
      total: 0,

      //common
      operationLocal: "",
		}
	},
	props: {
		model: Object,
    operation: String,
    isOnce: Boolean
  },
  created() {
    this.userInfo = this.$store.getters.info || {}
    this.init()
  },
	methods: {
    init() {
      this.getLoading = true;
      let params = null;
      if (this.isOnce) {
        params = {
          pageIndex: this.pageIndex, 
          pageSize: this.pageSize,
          fromUserId: this.userInfo.userId
        }
      } else {
        params = {
          pageIndex: this.pageIndex, 
          pageSize: this.pageSize
        }
      }
      this.api.getArticleInTrash(params).then( response => {
        this.getLoading = false;
        if(response.status === 200) {
          this.total = response.totalSize
          this.tableData = response.data.map(v => {
            return {
              ...v,
              createdTime: parseTime(v.createdTime, '{y}-{m}-{d}')
            }
          })
        }
      }).catch(() => this.getLoading = false)
    },
    recycle_operation(model, type,index) {
      let params = {
        userId: this.userInfo.userId,
        username: this.userInfo.userName,
        id: model.id
      }
      if(type === 'recover') {
        this.getLoading = true;
        this.api.recoverArticleInTrashById(params).then( response => {
          if(response.status === 200) {
            this.tableData.splice(index, 1)
            this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
          } else {
            this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
          }
          this.getLoading = false;
        })
      } else if (type === 'remove') {
        this.$confirm('此操作将永久删除该文章，是否继续', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.getLoading = true;
          this.api.removeArticleInTrashById(params).then( response => {
            if(response.status === 200) {
              this.tableData.splice(index, 1)
              this.PUBLIC_Methods.apiNotify('删除成功', 'success')
            } else {
              this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
            }
            this.getLoading = false;
          })
        })
      }
    },
    setContentData(index,item) {
      this.chooseIndex = index
      this.conData = [];
      this.showVisible = true
      this.previewLoading = true;
      this.api.getArticleContentById({id: item.id}).then(response => {
          this.previewLoading = false;
          if (response.status === 200) {
              this.conData.push({content: this.PUBLIC_Methods.unexcapeHtml(response.data.content)})
          }
      })
    },
    closepreview() {
      this.chooseIndex = -1;
      this.conData = [];
      this.showVisible = false;
    },
    current_change(val) {
      this.pageIndex = val;
      this.init()
    }
	}
}
</script>

<style>
.scope-header-btn {
  position: absolute;
  top: -7px;
  right: 0px;
  font-size: 16px !important;
  border: none !important;
}
.scope-header-btn .delete-btn {
  width: 1.2em;
}
</style>
