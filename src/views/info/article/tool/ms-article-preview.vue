<template>
  <ms-operation-dialog >
    <template slot="content">
		<p class="content-top">
			<span class="top-label"><i class="el-icon-document"></i></span><span class="top-content">{{model.title}}</span>
		</p>
		<p class="content-top">
			<span class="top-label"><i class="el-icon-user"></i></span><span class="top-content">{{model.createdName}}</span>
		</p>
		<p class="content-top">
			<span class="top-label"><i class="el-icon-time"></i></span><span class="top-content">{{model.createdTime | parseTime('{y}-{m}-{d}')}}</span>
		</p>
        <div v-html="PUBLIC_Methods.unexcapeHtml(content)" style="min-height: 300px" v-loading="getLoading"></div>
    </template>
  </ms-operation-dialog>
</template>

<script>
export default {
	name: "ms-article-preview",
	data () {
		return {
            getLoading: true,
            content: '' 
		}
	},
	props: {
		model: Object,
		operation: String
    },
    created() {
        this.init()
    },
    methods: {
        init() {
            this.getLoading = true
            this.api.getArticleContentById({id: this.model.id}).then(response => {
                this.getLoading = false;
                if (response.status === 200) {
                    this.content = response.data.content
                }
            })
        }
    }
}
</script>

<style scoped>
	.content-top {
		margin-bottom: 10px;
		font-size: 14px;
		text-align: left;
	}
	.content-top .top-label {
		font-weight: bold;
		margin-right: 12px;
		display: inline-block;
	}
</style>
