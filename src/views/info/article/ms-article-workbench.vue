<template>
	<ms-table
		:currentPage="searchParams.pageIndex"
		:loading="loading"
		:operationButtons="$route.path.includes('Bioon')?bioonConfig.tableButtons:domConfig.tableButtons"
		:pageSize="searchParams.pageSize"
		:scopeConfig="scopeConfig.show"
        :scopeHeader="scopeConfig.headerShow"
		:tableData="list"
		:tableHeader="domConfig.tableHeader"
		:total="total"
        :showSelection="true"
		@current-change="current_change"
		@operation-change="operation_change"
		@size-change="size_change"
        @header-operation="header_operation"
        @handleSelectionChange="handleSelectionChange"
        class="table-svg"
	>
    <!-- 列表搜索去区域插槽 -->
		<template slot="ms-table-header">
			<div class="slot-search">
				<template v-for="(searchItem, key) in domConfig.listSearch">
					<component
						:index="searchItem.index || ''"
						:is="searchItem.component"
						:key="key"
                        :width="searchItem.width || '150px'"
                        :model.sync="searchParams[searchItem.model]"
                        :label="searchItem.label"
						:operation="searchItem.operation || ''"
						:options="searchItem.options || []"
						:placeholder="searchItem.placeholder || ''"
						:type="searchItem.type || ''"
                        :multiple="searchItem.multiple"
                        :disabled="searchItem.disabled"
                        :code="searchItem.code"
                        v-if="key < search_number"
					></component>
				</template>
            <div class="inlineBlock">
                <el-button @click="handleClick('query')" type="primary" plain icon="el-icon-search">查询</el-button>
                <el-button @click="handleClick('reset')">重置</el-button>
                <el-button @click="search_number = search_number === 99 ? 4 : 99">{{search_number === 99 ? '收起' : '展开'}} <i :class="search_number === 99 ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i></el-button>
            </div>
        
			</div>
			<div class="slot-button-article clearfix">
				<template v-for="(item, index) in $route.path.includes('Bioon')?bioonConfig.soltButtons:domConfig.soltButtons">
                    <el-dropdown trigger="click" 
                                size="medium" 
                                v-if="item.children && item.children.length > 0"
                                placement="bottom-start" :key="index" 
                                @command="operation_drop">
                        <span class="el-dropdown-link">
                        <el-button size="mini" class="scope-btn" style="margin-right: 10px;" v-show="!item.roleDisabled" :type="item.type" plain>{{item.label}}</el-button>
                        </span>
                        <el-dropdown-menu slot="dropdown">
                        <el-dropdown-item v-for="(itemC,indexC) in item.children"
                                            :key="indexC"
                                            :command="itemC"
                                            v-show="!itemC.roleDisabled"
                                            v-text="itemC.label">
                        </el-dropdown-item>
                        </el-dropdown-menu>
                    </el-dropdown>
					<el-button :key="index" 
                     :type="item.type" 
                     size="mini"
                     :icon="item.icon"
                     @click="operation_change({operation: item})"
                     v-show="!item.roleDisabled"
                     plain
                     v-else>{{ item.label }}</el-button>
				</template>
                <el-alert class="article-total" :title="`共搜索到${total}篇文章`" type="info" show-icon></el-alert>
			</div>
			<el-dialog :visible.sync="dialog" 
                 closeable 
                 show-close
                 :close-on-click-modal="false"
                 :width="dialogWidth"
                 :title="dialogTitle">
				<component :is="dialogComponent" 
                   :model="scopeInfo" 
                   :operation="dialogOperation" 
                   @close="dialog = !dialog" 
                   @up-date="init"
                   :isOnce="true" 
                   v-if="dialog"></component>
			</el-dialog>
		</template>
	</ms-table>
</template>

<script>
import workbenchMixin from "./workbench-mixin"
import tableMixins  from "../../common/mixins/table"
import msArticleImport from "./tool/ms-article-import"
import msArticleCategory from "./tool/ms-article-category"
export default {
  name: "ms-article-workbench",
  mixins: [tableMixins,workbenchMixin],
  data () {
    return {
      search_number: 4
    }
  },
  components: {
    msArticleImport,
    msArticleCategory
  },
  methods: {
    apiInit (params) {
      let searchParams = {...params}
      if (searchParams.createTime) {
        searchParams.publishedStartTime = searchParams.createTime[0] || ''
        searchParams.publishedEndTime = searchParams.createTime[1] || ''
      }
      searchParams.createdBy = this.$store.getters.info.userId
      searchParams.isTitle = searchParams.isTitle ? '2' : '1'
      this.api.getArticlePage(searchParams).then(response => {
        this.loading = false
        this.total = response.totalSize || 0;
        this.list = response.data || []
        if (response.status !== 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
      }).catch(() => this.loading = false)
    },
    operation_drop(val) {
      this.operation_change({operation: val})
    },
  }
};
</script>
