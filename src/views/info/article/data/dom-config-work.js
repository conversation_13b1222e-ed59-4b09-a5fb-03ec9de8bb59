// import store from '@/store'

const domConfig = {
  listSearch: [
    {
      label: '标题',
      placeholder: '请输入',
      model: 'value',
      component: 'ms-input'
    },
    {
      label: ' ',
      model: 'isTitle',
      component: 'ms-checkbox',
      options: [
        { label: '搜题目' }
      ]
    },
    {
      label: '发布时间',
      placeholder: '请选择时间段',
      model: 'createTime',
      component: 'ms-picker',
      type: 'daterange',
      width: '240px'
    },
    {
      label: '来源',
      placeholder: '请输入',
      model: 'articleFrom',
      component: 'ms-input'
    },
    {
      label: '专题',
      placeholder: '请选择',
      model: 'classify',
      component: 'ms-category-cascader'
    },
    {
      label: '创作选项',
      placeholder: '请选择',
      model: 'creationTypeList',
      component: 'ms-select-local',
      code: 'createType',
      multiple: true
    }
  ],
  tableHeader: [
    // { label: '', property: 'recommend', width: '25' },
    // { label: '', property: 'sticky', width: '25' },
    { label: 'ID', property: 'id', sortable: true, width: '60' },
    { label: '标题', property: 'title', width: '250'},
    { label: '点/APP/PC/赞/享/评', property: 'fields', width: '160' },
    { label: '创建人', property: 'createdName' },
    { label: '项目名称', property: 'articleClasses', width: '60' },
    { label: '发布时间', property: 'publishedTime', sortable: true,  width: '130'  },
    // { label: '创建时间', property: 'createdTime', sortable: true,  width: '130' },
    { label: '状态', property: 'approvalStatus'},
  ],
  tableButtons: [
    {
      label: '编辑',
      icon: '',
      role: '',
      operation: 'edit',
      component:  'workbenchArticle-operation',
      way: 'page',
      type: 'primary',
      path:  'workbenchArticle-operation',
      params: ['id'],
    },
    // {
    //     label: '',
    //     icon: '',
    //     role: '',
    //     operation: 'editStatus',
    //     component: 'msArticleOperation',
    //     way: 'dialog',
    //     field: 'approvalStatus',
    //     rule: {
    //         1: { label: '去审', type: '', operation: 'toreview' },
    //         0: { label: '审核', type: 'success', operation: 'approval' }
    //     },
    //     showCallback: () => {
    //         if (store.getters.info.roleEnglishName === 'inter article pt' ) {
    //             return false
    //         } else {
    //             return true
    //         }
    //     },
    // },
    {
      label: '删除',
      way: 'dialog',
      type: 'danger',
      operation: 'delete',
      component: 'msArticleOperation',
    },
  ],
  soltButtons: [
    { 
      label: '资讯添加', 
      type: 'primary', 
      icon: 'el-icon-plus',
      children: [
        {
          label: '手工添加',
          operation: 'created',
          component: 'workbenchArticle-operation',
          way: 'page',
          path: 'workbenchArticle-operation',
          params: ['id'],
        },
        {
          label: '从PAAS导入',
          way: 'dialog',
          title: '从PAAS导入文章',
          component: 'msArticleImport',
          width: '60%'
        },
      ]
    },
    // { 
    //   label: '批量审核', 
    //   type: 'primary',
    //   operation: 'approval',
    //   component: 'msArticleOperation',
    //   way: 'batch',
    //   identify: 'batch_approval'
    // },
    // { 
    //   label: '批量去审', 
    //   type: 'primary',
    //   operation: 'toreview',
    //   component: 'msArticleOperation',
    //   way: 'batch',
    //   identify: 'batch_toreview'
    // },
    { 
      title: '批量添加专题',
      label: '批量添加专题', 
      type: 'success',
      operation: 'add',
      component: 'msArticleCategory',
      way: 'batch',
      width: '70%'
    },
    { 
      title: '批量移除专题',
      label: '批量移除专题', 
      type: 'success',
      operation: 'delete',
      component: 'msArticleCategory',
      way: 'batch',
      width: '70%'
    },
    { 
      label: '批量删除',
      type: 'info',
      icon: 'el-icon-close',
      operation: 'delete',
      component: 'msArticleOperation',
      way: 'batch',
    },
    {
      label: '回收站',
      type: 'info',
      operation: '',
      component: 'msArticleRecycle',
      way: 'dialog',
      title: '资讯回收站 (仅展示最近一个月资讯信息)',
      width: '90%',
    }
  ]
}

export default domConfig;
