<template>
  <section class="form-tab" v-loading="getLoading">
    <el-tabs v-model="activeName">
      <el-tab-pane label="文章内容" name="content">
        <ms-article-content ref="contentTemp" @handle-click="handle_click"></ms-article-content>
      </el-tab-pane>
      <el-tab-pane label="显示栏目" name="category">
        <ms-info-setting :categoryModel.sync="submitData.categoryList" :categoryConfigChild="{moduleName: 'article'}" headerShow></ms-info-setting>
      </el-tab-pane>
    </el-tabs>
    <!-- 弹出层 -->
    <el-dialog :visible.sync="dialog" 
               closeable 
               :show-close="false"
               :close-on-click-modal="false"
               width="50%"
               :title="dialogTitle">
      <component :is="dialogComponent" 
                   :model="dialogInfo" 
                   :operation="dialogOperation" 
                   :isNeedApi="false"
                   @close="closeDialog" 
                   @up-date="init"
                   v-if="dialog"></component>
    </el-dialog>
    <!-- 提交按钮 -->
    <footer-tool-bar v-loading="buttonLoading">
      <template slot="tool-content">
        <!-- 预览 -->
        <!-- <el-popover placement="top" width="300" trigger="manual" v-model="popVisible" v-show="articleId">
          <el-row :gutter="20" type="flex" justify="center">
            <el-col :span="12">功能开发中~~</el-col>
          </el-row>
          <el-button slot="reference" @click="popVisible=!popVisible">预览</el-button>
        </el-popover> -->

        <!-- 提交草稿 -->
        <!-- <el-button plain v-show="!(articleId && submitData.approvalStatus === 1) && isCanEdit"  v-permission="['/article','draft']" @click="info_operation('draft')">
          存为草稿
          <el-tooltip class="item" effect="light" content="草稿只允许存为草稿、保存、在详情页审核、删除" placement="top"><i class="el-icon-info"></i></el-tooltip>
        </el-button> -->

        <!-- 审核 -->
        <!-- <el-button plain v-show="articleId && isCanEdit" @click="info_operation('approval')" v-permission="['/article','status']">{{submitData.approvalStatus === 1 ? '去审文章' : '审核文章'}}</el-button> -->

        <!-- 保存 -->
        <el-button type="primary" @click="info_operation('save')">保存</el-button>

        <!-- 返回 -->
        <el-button type="info" @click="info_operation('back')">返回</el-button>
      </template>
    </footer-tool-bar>
  </section>
</template>

<script>
import msArticleContent from './tool/ms-article-content'
import msInfoSetting from '@/components/MsCommon/ms-info-setting'
import msMeetingCascader from '@/components/MsCommon/ms-meeting-cascader'
import articleMixin from "./article-mixin"
import FooterToolBar from '@/components/ToolBar/footer-tool-bar'
import {parseTime} from "@/utils"
export default {
  name: "workbench-operation",
  mixins: [articleMixin],
	data () {
		return {
      activeName: 'content',
      articleId: this.$route.query.id ? this.$route.query.id : 0,
      buttonLoading: false,
      getLoading: false,
      popVisible: false,
      isCanEdit: true,

      userInfo: this.$store.getters.info,
      submitData: { // => userId、username、integral、waterMark、formatted 、expandRead (保存添加属性)
        attachmentList: [], // => 附件
        fileUrl: '',
        userId: '', // => 用户Id
        username: '', // => 用户名称 (string)
        title: '', // => 标题 (string)
        authorId: 0, // =>作者id (long)
        author: '', // => 作者 (string)
        opened: 1, // => 开放阅读 (0.不开放 1.开放)
        paymentType: 1, // => 支付方式 1: 积分
        paymentAmount: 0, // => 支付金额 | 积分
        originalUrl: '', // => 原始链接 (string)
        linkOutUrl: '', // => 外向链接 (string)
        pcVisible: 1, // => PC可见 (0.不可见,1.可见)
        appVisible: 1, // => APP可见 (0.不可见,1.可见)
        recommend: 0, // => 推荐（0.不推荐 1.推荐）
        recommendEndTime: '', // => 推荐结束时间 
        sticky: 0, // => 置顶（0.不置顶 1.置顶）
        stickyEndTime: '',// => 置顶结束时间 (date-time)
        articleFrom: '网络', // => 来源 (string)
        journalId: 0, // => 所属期刊id
        content: '', // => 文章内容 (string)
        copyright: '转发', // => 原创|转发 (string)
        summary: '', // => 摘要 (string)
        editorId: 0, // => 编辑人id (long)
        editor: '', // => 编辑人名称 (string)
        publishedTime: parseTime(new Date), // => 发布时间 (date-time)
        waterMark: 0, // 自动水印 (0.无水印,1.加水印)
        formatted: 0, // 去复杂格式 (0.不去格式,1.去格式)
        cover: '', // 封面 (string)
        expandRead: '', // => 拓展阅读
        articleKeywordId: 0, // => 资讯拓展关键词Id
        articleKeyword: '', // => 资讯拓展关键词
        articleKeywordNum: 6, // => 拓展条数
        guiderKeywordId: 0, // => 指南扩张关键词Id
        guiderKeyword: '', // => 指南拓展关键词
        guiderKeywordNum: 6, // => 指南拓展条数
        approvalStatus: 0, // => 发布状态 (0.待审批 1.审批通过, 2.草稿)
        tagList: [], // => 标签集合
        categoryList: [], // => 专题分类集合
        creationTypeList: [] // => 创作选项
      },
      dialogComponent: '',
      dialog: false,
      dialogTitle: '',
      dialogOperation: '',
      dialogInfo: {}
		}
  },
  created() {
    this.init()
  },
  components: {
    msArticleContent,
    msInfoSetting,
    FooterToolBar,
    msMeetingCascader
  },
  methods: {
    init() {
      let id = this.articleId
      this.dialog = false;
      this.$store.dispatch('SetSubmitData', this.submitData)
      if(id !== 0) {
        this.getLoading = true;
        this.submitData.id = id;
        this.api.getArticleById({id: id}).then( response => {
          this.getLoading = false;
          if(response.status === 200) {
            let res = response.data
            let file = []
            if (res.attachmentList && res.attachmentList.length > 0) {
              res.attachmentList.forEach(v => {
                file.push({
                  name: v.fileName || v.attachmentKey,
                  url: v.attachmentUrl,
                  fileKey: v.attachmentKey
                })
              });
            }
            this.submitData = {
              ...this.submitData,
              ...res,
              attachmentList: file,
              content: this.PUBLIC_Methods.unexcapeHtml(res.content),
              creationTypeList: res.creationTypeList || []
            }
            if (this.userInfo.roleLevel === 3 && this.submitData.createdBy !== this.userInfo.userId) { this.isCanEdit = false }
          } else {
            this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
          }
          this.$store.dispatch('SetSubmitData', this.submitData)
        }).catch(() => {
          this.getLoading = false;
          this.$store.dispatch('ClearSubmitData')
        })
      }
    },
    info_operation(val) {
      switch (val) {
        case 'save': 
          this.$refs['contentTemp'].validateData(this.articleId ? this.updateArticle: this.createArticle)
          break;
        case 'draft':
          this.$refs['contentTemp'].validateData(this.articleId ? this.updateArticle: this.createArticle, true)
          break;
        case 'back':
          this.$router.back();
          break;
        case 'approval':
          var params = {
            way: 'dialog',
            operation: this.submitData.approvalStatus === 1 ? 'toreview' : 'approval',
            component: 'msArticleOperation',
            data: {
              id: this.articleId,
              title: this.submitData.title
            }
          }
          this.handle_click(params)
          break;
        default: break;
      }
    },
    createArticle(params) {
      this.buttonLoading = true;
      let paramsData = {
        ...params,
        categoryList: this.submitData.categoryList
      }
      this.api.saveArticle(paramsData).then(response => {
        this.buttonLoading = false
        if(response.status === 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
          this.$router.back()
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
      }).catch(() => this.buttonLoading = false)
    },
    updateArticle(params) {
      this.buttonLoading = true;
      let paramsData = {
        ...params,
        categoryList: this.submitData.categoryList
      }
      this.api.updateArticleById(paramsData).then(response => {
        this.buttonLoading = false
        if(response.status === 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
          this.$router.back()
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
      }).catch(() => this.buttonLoading = false)
    },
    handle_click(val) {
      switch (val.way) {
        case 'dialog': 
          this.dialog = true;
          this.dialogInfo = val.data
          this.dialogOperation = val.operation;
          this.dialogComponent = val.component;
          this.dialogTitle = val.title;
          break;
        default: break;
      }
    },
    closeDialog(val) {
      this.dialog = !this.dialog
      if (val) {
        if (val.operation === 'recommend') {
          this.submitData.recommend = 0
        } else if (val.operation === 'sticky') {
          this.submitData.sticky = 0
        }
        this.$store.dispatch('SetSubmitData', this.submitData)
      }
    }
  }
}
</script>
