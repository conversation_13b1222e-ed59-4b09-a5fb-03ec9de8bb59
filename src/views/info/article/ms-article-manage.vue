<template>
  <ms-table
    :currentPage="searchParams.pageIndex"
    :loading="loading"
    elTableHeight=""
    :operationButtons="$route.path.includes('Bioon')?bioonConfig.tableButtons:domConfig.tableButtons"
    :pageSize="searchParams.pageSize"
    :scopeConfig="scopeConfig.show"
    :scopeHeader="scopeConfig.headerShow"
    :tableData="list"
    :tableHeader="domConfig.tableHeader"
    :total="total"
    :showSelection="true"
    @current-change="current_change"
    @operation-change="operation_change"
    @size-change="size_change"
    @header-operation="header_operation"
    @handleSelectionChange="handleSelectionChange"
    class="table-svg high-row-none"
    :rowColor="rowColor"
  >
    <!-- 列表搜索去区域插槽 -->
    <template slot="ms-table-header">
      <div class="slot-search">
        <template v-for="(searchItem, key) in domConfig.listSearch">
          <component
            :index="searchItem.index || ''"
            :is="searchItem.component"
            :key="key"
            :width="searchItem.width || '150px'"
            :model.sync="searchParams[searchItem.model]"
            :label="searchItem.label"
            :operation="searchItem.operation || ''"
            :options="searchItem.options || []"
            :placeholder="searchItem.placeholder || ''"
            :type="searchItem.type || ''"
            :multiple="searchItem.multiple"
            :disabled="searchItem.disabled"
            :code="searchItem.code"
            v-if="key < search_number"
          ></component>
        </template>
        <div class="inlineBlock">
          <el-button @click="handleClick('query')" type="primary" plain icon="el-icon-search">查询</el-button>
          <el-button @click="handleClick('reset')">重置</el-button>
          <el-button @click="search_number = search_number === 99 ? 4 : 99">{{search_number === 99 ? '收起' : '展开'}} <i
            :class="search_number === 99 ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i></el-button>
        </div>

      </div>
      <div class="slot-button-article clearfix">
        <template v-for="(item, index) in $route.path.includes('Bioon')?bioonConfig.soltButtons:domConfig.soltButtons">
          <el-dropdown trigger="click"
                       size="medium"
                       v-if="item.children && item.children.length > 0"
                       placement="bottom-start" :key="index"
                       @command="operation_drop">
            <span class="el-dropdown-link">
              <el-button size="mini" class="scope-btn" style="margin-right: 10px;" v-show="!item.roleDisabled"
                         :type="item.type" plain>{{item.label}}</el-button>
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item v-for="(itemC,indexC) in item.children"
                                :key="indexC"
                                :command="itemC"
                                v-show="!itemC.roleDisabled"
                                v-text="itemC.label">
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
          <el-button :key="index"
                     :type="item.type"
                     size="mini"
                     :icon="item.icon"
                     @click="operation_change({operation: item})"
                     v-show="!item.roleDisabled"
                     plain
                     v-else>{{ item.label }}
          </el-button>
        </template>
        <el-alert class="article-total" :title="`共搜索到${total}篇文章`" type="info" show-icon></el-alert>
      </div>
      <el-dialog :visible.sync="dialog"
                 closeable
                 show-close
                 :close-on-click-modal="false"
                 :width="dialogWidth"
                 :title="dialogTitle">
        <component :is="dialogComponent"
                   :model="scopeInfo"
                   :operation="dialogOperation"
                   @close="dialog = !dialog"
                   @up-date="init"
                   v-if="dialog"></component>
      </el-dialog>
    </template>
  </ms-table>
</template>

<script>
import articleMixin from './article-mixin'
import tableMixins from '../../common/mixins/table'
import msArticleProject from './tool/ms-article-project'
import msArticleImport from './tool/ms-article-import'
import msArticleCategory from './tool/ms-article-category'

export default {
  name: 'ms-article-manage',
  mixins: [tableMixins, articleMixin],
  data () {
    return {
      notifyInst: null,
      tipFlag: true,
      search_number: 4
    }
  },
  components: {
    msArticleProject,
    msArticleImport,
    msArticleCategory
  },
  created () {
    this.tipFlag = this.$store.getters.infoListTip.articleTip
    if (this.tipFlag) {
      this.notifyInst = this.$notify({
        position: 'bottom-left',
        dangerouslyUseHTMLString: true,
        message: '<div><p style="color:#F56C6C;">红色栏：首页推荐（至多轮播5条）</p><p style="color: #E6A23C">黄色栏：首页固顶（至多置顶5条）</p><p style="color: #50bfff">蓝色栏：PC端不显示</p></div>',
        duration: 10000,
        customClass: 'notify-info'
      })
      this.$store.dispatch('SetInfoTip', 'articleTip')
    }

  },
  beforeDestroy () {
    if (this.tipFlag) this.notifyInst.close()
  },
  methods: {
    rowColor ({ row }) {
      if (row.recommend === 1) {
        return 'recommend-row'
      } else if (row.sticky === 1) {
        return 'sticky-row'
      } else if (row.recommend !== 1 && row.sticky !== 1 && row.pcVisible === 0) {
        return 'pc-visible'
      }
      return ''
    },
    apiInit (params) {
      let searchParams = { ...params }
      if (searchParams.createTime) {
        searchParams.publishedStartTime = searchParams.createTime[0] || ''
        searchParams.publishedEndTime = searchParams.createTime[1] || ''
      }
      // searchParams.isTitle = searchParams.isTitle ? '2' : '1'
      // 默认强制勾选搜题目
      searchParams.isTitle = '2'
      this.api.getArticlePage(searchParams).then(response => {
        this.loading = false
        this.total = response.totalSize || 0
        if (response.data && Array.isArray(response.data)) {
          response.data.forEach(element => {
            if (element.author) {
              element.createdName = `${element.createdName}/${element.author}`
            }
          })
          this.list = response.data
        } else {
          this.list = []
        }
        if (response.status !== 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
      }).catch(() => this.loading = false)
    },
    operation_drop (val) {
      this.operation_change({ operation: val })
    },
    operation_change_module (val) {
      switch (val.operation.way) {
        case 'cache':
          this.api.clearCache({}).then(response => {
            if (response.status) {
              this.$message({
                message: '前台资讯缓存已清除',
                type: 'success'
              })
            }
          })
          break
        case 'send':
          this.api.pushQuickNews({
            id: val.model.id
          }).then(response => {
            console.log(response, 'response')
            if (response.status == 200) {
              this.$message({
                message: '推送成功',
                type: 'success'
              })
            } else {
              this.$message({
                message: response.message,
                type: 'warning'
              })
            }
          })
          break
        default:
          break
      }
    }
  }
}
</script>
<style scoped>
  .slot-search /deep/ .el-radio-group {
    vertical-align: inherit;
  }

  .slot-search /deep/ .el-radio-group .el-radio {
    margin-left: 0 !important;
  }
</style>
