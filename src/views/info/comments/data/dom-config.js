const domConfig = {
  listSearch: [
    {
      label: '评论内容',
      placeholder: '请输入评论内容',
      model: 'content',
      component: 'ms-input',
      width: '130px'
    },
    {
      label: '评论对象',
      placeholder: '请输入评论对象',
      model: 'objectTitle',
      component: 'ms-input',
      width: '130px'
    },
    {
      label: '创建人',
      placeholder: '请输入创建人',
      model: 'createdName',
      component: 'ms-input',
      width: '120px'
    },
    {
      label: '创建时间',
      placeholder: '请选择时间段',
      model: 'createTime',
      component: 'ms-picker',
      format: 'yyyy-MM-dd HH:mm:ss',
      type: 'datetimerange',
      width: '240px'
    }
  ],
  tableHeader: [
    { label: '项目名称', property: 'projectName', width: '100' },
    { label: '对象', property: 'objectTitle', width: '150' },
    { label: '内容', property: 'content', width: '200' },
    { label: '图片', property: 'hasAttachment', width: '60' },
    { label: '举报', property: 'isReport', width: '60' },
    { label: '对象类型', property: 'objectType', width: '80' },
    { label: '状态', property: 'status', width: '60' },
    { label: '创建人', property: 'createdName', width: '80' },
    { label: '创建时间', property: 'createdTime', sortable: true, width: '130' }
  ],
  tableButtons: [
    {
      label: '查看',
      icon: '',
      role: '',
      operation: 'edit',
      component: 'MsCommentsInfo',
      way: 'dialog',
      type: 'primary',
      title: '查看详情',
      width: '45%'
    },
    {
      label: '',
      icon: '',
      role: '',
      operation: 'editStatus',
      way: 'confirm',
      field: 'status',
      rule: {
        1: { label: '去审', type: '', operation: 'toreview' },
        0: { label: '审核', type: 'success', operation: 'approval' }
      },
    },
    {
      label: '删除',
      icon: '',
      role: '',
      type: 'danger',
      way: 'confirm',
      operation: 'delete',
    }
  ],
  soltButtons: [
    {
      label: '批量审核',
      type: 'primary',
      operation: 'approval',
      way: 'batch-confirm',
    },
    {
      label: '批量去审',
      type: 'primary',
      operation: 'toreview',
      way: 'batch-confirm',
    },
    {
      label: '批量删除',
      type: 'info',
      icon: 'el-icon-close',
      operation: 'delete',
      way: 'batch-confirm',
    },
  ]
}

export default domConfig;
