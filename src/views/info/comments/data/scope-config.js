const scopeConfig = {
  show: {
    isReport: () => {
      return {
        type: 'status',
        rule: {
          0: {label: '否'},
          1: {label: '是'}
        }
      }
    },
    status: () => {
      return {
        type: 'status',
        rule: {
          0: { label: '待审核', background: '#A7ADBD' },
          1: { label: '审核通过', background: '#40A23F' },
        }
      }
    },
    content: () => {
      return {
        // type: 'other',
        // config: {
        //   label: '查看',
        //   operation: 'edit',
        //   component: 'MsCommentsInfo',
        //   way: 'dialog',
        //   type: 'primary',
        //   title: '查看详情',
        //   width: '45%'
        // }
        type: 'other',
        config: {
          way: 'link',
          pathKey: 'objectUrl' 
        }
      }
    },
    createdName: () => {
      return {
        type: 'webLink',
        config: {
          role: '',
          operation: 'edit',
          way: 'page',
          path: 'user-detail',
          params: [{
            keyName: 'id',
            valName: 'createdByCipher'
          }],
        }
      }
    }
  },
  headerShow: {
    objectType: () => {
      return {
        type: 'dropdown',
        icon: 'icon-funnel',
        options: [
          { label: "全部", value: null },
          { label: "资讯评论", value: "article" },
          { label: "视频评论", value: "video" },
          { label: "期刊评论", value: "tool_impact_factor" },
          { label: "话题评论", value: "topic" },
          { label: "课程评论", value: "course" },
          { label: "会议评论", value: "meeting" },
          { label: "基金评论", value: "nsfc" },
          { label: "直播评论", value: "live_info" },
          { label: "指南评论", value: "guider" },
          { label: "知识点评论", value: "point" }
        ],
        operation: 'query'
      }
    },
    status: () => {
      return {
        type: 'dropdown',
        icon: 'icon-funnel',
        options: [
          { label: '全部', value: null },
          { label: '待审核', value: 0 },
          { label: '审核通过', value: 1 }
        ],
        operation: 'query'
      }
    },
    isReport: () => {
      return {
        type: 'dropdown',
        icon: 'icon-funnel',
        options: [
          { label: '全部', value: null },
          { label: '否', value: 0 },
          { label: '是', value: 1 }
        ],
        operation: 'query'
      }
    }
  }
}

export default scopeConfig;
