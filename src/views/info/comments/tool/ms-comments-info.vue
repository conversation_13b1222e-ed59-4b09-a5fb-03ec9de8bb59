<template>
      <el-form
        ref="submitRef"
        class="rule-form comment-form"
        label-width="80px"
        v-loading="getLoading"
      >
        <el-row>
          <el-col :span="12">
            <el-form-item label="创建人:">
              <!-- <span class="comment-info" v-text="submitData.createdName"></span> -->
              <el-link @click="goUserPage()">{{submitData.createdName}}</el-link>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="创建时间:">
              <span class="comment-info" v-text="submitData.createdTime"></span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="来源:">
              <span class="comment-info" v-text="submitData.projectName"></span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="创建渠道:">
              <span class="comment-info" v-text="submitData.channel"></span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="对象类型:">
              <span class="comment-info" v-text="submitData.objectType"></span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="对象URL:">
              <span class="comment-info" v-text="submitData.objectUrl"></span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="评论对象:">
              <span class="comment-info" v-text="submitData.objectTitle"></span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="评论内容:">
              <div class="comment-info" v-html="submitData.content"></div>
              <div>
                <el-image v-for="(item, index) in submitData.attachment" :key="index" :src="item.url"
                  style="width: 100px; height: 90px; margin-right: 8px;margin-top: 8px;" fit="cover"
                  :preview-src-list="[item.url]">
                </el-image>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="审核:">
              <span class="comment-info" v-text="submitData.status===1 ? '审核通过' : '审核未通过'"></span>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
</template>

<script>
export default {
  name: "ms-comments-info",
  data() {
    return {
      getLoading: false,
      submitData: {},
    };
  },
  props: {
    model: Object,
    operation: String
  },
  created() {
    this.getDetail();
  },
  methods: {
    getDetail() {
      this.getLoading = true;
      this.api.getComments({id: this.model.id}).then(response => {
        this.getLoading = false;
        if (response.status === 200 && response.data) {
          this.submitData = {...response.data, attachment: JSON.parse(response.data.attachment)}
        }
      })
    },
    goUserPage() {
      this.$router.push({
        path: 'user-detail',
        query: {
          operation: 'edit',
          id: this.submitData.createdByCipher
        }
      });
    },
  }
};
</script>

<style>
.comment-form .el-form-item__label {
  color: #8b949d;
}
.comment-form .comment-info {
  color: #606266;
}
</style>
