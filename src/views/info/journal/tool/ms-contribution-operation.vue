<template>
  <ms-operation-dialog :title="title">
    <template slot="content">
      <el-tag v-for="(item, index) in tagArr" 
              :key="index" 
              type="info" 
              style="margin: 0 5px 5px 0">{{item}}</el-tag>
    </template>
    <template slot="footer">
      <el-button @click="submitForm"
                 :loading="loading"
                 size="mini"
                 type="primary">确 定</el-button>
      <el-button @click="$emit('close')"
                 size="mini">取 消</el-button>
    </template>
  </ms-operation-dialog>
</template>

<script>
export default {
	name: "ms-contribution-operation",
	data () {
		return {
      userInfo: {},
      dealType: null,
      tagArr: [],
      ids: [],
      journalIds: [],
      title: '',
      loading: false
		}
	},
	props: [
		"model",
		"operation"
  ],
  created() {
    this.userInfo = this.$store.getters.info || {}
    this.init()
  },
	methods: {
    init() {
      let arr = []
      let ids = []
      let jIds = []
      let way = this.operation || this.$route.query.operation
      this.dealType = way === 'approval' ? 2 : way === 'toreview' ? 0 : way === 'delete' ? 3 : null
      this.title = `是否${this.dealType === 2 ? '审核' : this.dealType === 0 ? '去审' : this.dealType === 3 ? '删除' : ''}网友贡献信息`
      if (this.PUBLIC_Methods.isArrayFn(this.model)) {
        this.model.forEach(item => {
          arr.push(item.fullname)
          ids.push(item.id)
          jIds.push(item.journalId)
        });
      } else {
        arr.push(this.model.fullname)
        ids.push(this.model.id)
        jIds.push(this.model.journalId)
      }
      this.tagArr = arr
      this.ids = ids
      this.journalIds = jIds
    },
		submitForm () {
      let params = {
        status: this.dealType,
        ids: this.ids
      }
      if ((this.dealType === 2 || this.dealType === 0) && new Set(this.journalIds).size !== this.journalIds.length) {
        return this.PUBLIC_Methods.apiNotify('存在同一期刊被指定了不同链接，请重新审核', 'warning')
      }
      if(this.dealType === 3) {
        this.$confirm('此操作将永久删除信息，是否继续', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.apiOperationDelete(params)
        })
      } else {
        this.apiOperationAudit(params)
      }
      
    },
    apiOperationDelete(params) {
      this.loading = true;
      this.api.deleteContribution({ids: params.ids}).then(response => {
        if(response.status === 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
        this.$emit('up-date')
        this.loading = false
      }).catch( () => {
        this.loading = false;
        this.$emit('close')
      })
    },
    apiOperationAudit(params) {
      this.loading = true;
      this.api.auditContribution(params).then(response => {
        if(response.status === 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
        this.$emit('up-date')
        this.loading = false
      }).catch( () => {
        this.loading = false;
        this.$emit('close')
      })
    }
	}
}
</script>
