<template>
	<ms-table
		:currentPage="searchParams.pageIndex"
		:loading="loading"
		:operationButtons="domConfig.tableButtons"
		:pageSize="searchParams.pageSize"
		:scopeConfig="scopeConfig.show"
    :scopeHeader="scopeConfig.headerShow"
		:tableData="list"
		:tableHeader="domConfig.tableHeader"
		:total="total"
    :showSelection="true"
    :expandList="expandList"
		@current-change="current_change"
		@operation-change="operation_change"
		@size-change="size_change"
    @header-operation="header_operation"
    @handleSelectionChange="handleSelectionChange"
	>
    <!-- 列表搜索去区域插槽 -->
		<template slot="ms-table-header">
			<div class="slot-search">
				<template v-for="(searchItem, key) in domConfig.listSearch">
					<component
						:index="searchItem.index || ''"
						:is="searchItem.component"
						:key="key"
            :width="searchItem.width || '150px'"
            :model.sync="searchParams[searchItem.model]"
            :label="searchItem.label"
						:operation="searchItem.operation || ''"
						:options="searchItem.options || []"
						:placeholder="searchItem.placeholder || ''"
						:type="searchItem.type || ''"
            :multiple="searchItem.multiple"
            :disabled="searchItem.disabled" 
					></component>
				</template>
        <el-button @click="handleClick('query')" type="primary" plain icon="el-icon-search">查询</el-button>
        <el-button @click="handleClick('reset')">重置</el-button>
			</div>
			<div class="slot-button">
				<template v-for="(item, index) in domConfig.soltButtons">
					<el-button :key="index" 
                     :type="item.type" 
                     size="mini"
                     :icon="item.icon"
                     @click="operation_change({operation: item})"
                     plain>{{ item.label }}</el-button>
				</template>
			</div>
			<el-dialog :visible.sync="dialog" 
                 closeable 
                 show-close
                 :close-on-click-modal="false"
                 :width="dialogWidth"
                 :title="dialogTitle">
				<component :is="dialogComponent" 
                   :model="scopeInfo" 
                   :operation="dialogOperation" 
                   @close="dialog = !dialog" 
                   @up-date="init" 
                   v-if="dialog"></component>
			</el-dialog>
		</template>
	</ms-table>
</template>

<script>
import domConfig from "./data/dom-config";
import scopeConfig from "./data/scope-config";
import tableMixins  from "../../common/mixins/table"
import msContributionOperation from "./tool/ms-contribution-operation"
export default {
  name: "contribution-manage",
  mixins: [tableMixins],
  data () {
    return {
      domConfig: domConfig.contribution,
      scopeConfig: scopeConfig,
      searchParams: { // => 列表查询传参
        fullName: '',
        status: 0
      },
      expandList: [
        { label: '期刊主页', property: 'website', type: 'link' },
        { label: '作者须知', property: 'guideForAuthor', type: 'link' },
        { label: '投稿链接', property: 'submitWebsite', type: 'link' }
      ],
      isFirst: true
    }
  },
  components: {
    msContributionOperation
  },
  methods: {
    init () {
      this.loading = true;
      this.dialog = false;
      let searchParams = {}
      if (this.isFirst) {
        this.isFirst = false
        this.searchParams.pageIndex = 1
        searchParams = {pageIndex: 1, pageSize: this.searchParams.pageSize}
      } else {
        searchParams = {...this.searchParams}
      }
      this.api.getContributionPage(searchParams).then(response => {
        this.loading = false
        this.total = response.totalSize || 0;
        this.list = response.data || []
      }).catch(() => this.loading = false)
    }
  }
};
</script>
