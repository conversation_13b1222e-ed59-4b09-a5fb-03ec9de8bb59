<template>
  <section class="form-tab">
    <el-tabs v-model="activeName">
      <el-tab-pane label="期刊管理" name="journal">
        <journal-manage></journal-manage>
        <div class="journal-link">
          <el-link class="link" href="http://mjl.clarivate.com" target="_blank">相关链接：Clarivate  Analytics</el-link>
          <span class="cut-line">|</span>
          <el-link class="link" href="http://mjl.clarivate.com/cgi-bin/jrnlst/jlcovchanges.cgi?PC=D" target="_blank">最新一年变化期刊</el-link>
          <span class="cut-line">|</span>
          <el-link class="link" href="http://ipscience-help.thomsonreuters.com/incitesLiveJCR/JCRGroup/titleSuppressions.html" target="_blank">镇压期刊</el-link>
        </div>
      </el-tab-pane>
      <el-tab-pane label="网友贡献链接" name="contribution">
        <contribution-manage></contribution-manage>
      </el-tab-pane>
      <el-tab-pane label="批量导入期刊信息" name="import">
        <journal-import></journal-import>
      </el-tab-pane>
    </el-tabs>
  </section>
</template>

<script>
import journalManage from './journal-manage'
import contributionManage from './contribution-manage'
import journalImport from '../journalImport/journal-import'
export default {
  name: "ms-journal-manage",
	data () {
		return {
      activeName: 'journal'
		}
  },
  components: {
    journalManage,
    contributionManage,
    journalImport
  },
}
</script>

<style lang="scss" scoped>
  .journal-link {
    padding: 30px 0 0px;
    text-align: center;
    .cut-line {
      font-size: 14px;
      color: #333;
    }
    .link {
      margin: 0 40px;
    }
  }
</style>
