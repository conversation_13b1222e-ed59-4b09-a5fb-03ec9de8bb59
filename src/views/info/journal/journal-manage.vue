<template>
	<ms-table
		:currentPage="searchParams.pageIndex"
		:loading="loading"
		:operationButtons="domConfig.tableButtons"
		:pageSize="searchParams.pageSize"
    :scopeHeader="scopeConfig.headerShow"
		:tableData="list"
		:tableHeader="domConfig.tableHeader"
		:total="total"
    :showSelection="true"
		@current-change="current_change"
		@operation-change="operation_change"
		@size-change="size_change"
    @header-operation="header_operation"
    @handleSelectionChange="handleSelectionChange"
	>
    <!-- 列表搜索去区域插槽 -->
		<template slot="ms-table-header">
			<div class="slot-search">
				<template v-for="(searchItem, key) in domConfig.listSearch">
					<component
						:index="searchItem.index || ''"
						:is="searchItem.component"
						:key="key"
            :width="searchItem.width || '150px'"
            :model.sync="searchParams[searchItem.model]"
            :label="searchItem.label"
						:operation="searchItem.operation || ''"
						:options="searchItem.options || []"
						:placeholder="searchItem.placeholder || ''"
						:type="searchItem.type || ''"
            :multiple="searchItem.multiple"
            :disabled="searchItem.disabled" 
					></component>
				</template>
        <el-button @click="handleClick('query')" type="primary" plain icon="el-icon-search">查询</el-button>
        <el-button @click="handleClick('reset')">重置</el-button>
			</div>
      <div class="slot-button">
        <template v-for="(item, index) in domConfig.soltButtons">
          <el-button :key="index" :type="item.type" :icon="item.icon" @click="operation_change({operation: item})" plain>{{ item.label }}</el-button>
        </template>
      </div>
			<el-dialog :visible.sync="dialog" 
                 closeable 
                 show-close
                 :close-on-click-modal="false"
                 :width="dialogWidth"
                 :title="dialogTitle">
				<component :is="dialogComponent" 
                   :model="scopeInfo" 
                   :operation="dialogOperation" 
                   @close="dialog = !dialog" 
                   @up-date="init" 
                   v-if="dialog"></component>
			</el-dialog>
		</template>
	</ms-table>
</template>

<script>
import domConfig from "./data/dom-config";
import scopeConfig from "./data/scope-config";
import tableMixins  from "../../common/mixins/table"
import msArticleCategory from "./tool/ms-article-category"
export default {
  name: "journal-manage",
  mixins: [tableMixins],
  data () {
    return {
      domConfig: domConfig.journal,
      scopeConfig: scopeConfig,
      selectList: []
      // searchParams: { // => 列表查询传参
      //   name: '',
      //   ifStart: '',
      //   ifEnd: ''
      // },
      // isFirst: true,
    }
  },
  components: {
    msArticleCategory
  },
  methods: {
    apiInit (params) {
      // if (params.isGetData === undefined) {
      //   this.loading = false;
      //   this.searchParams.isGetData = true
      //   return;
      // }
      this.$store.dispatch('SetSearchParams', {...params, isGetData: true})
      let searchParams = {...params}
      this.api.getImpactFactorPage(searchParams).then(response => {
        this.loading = false
        this.total = response.totalSize || 0;
        this.list = response.data || []
      }).catch(() => this.loading = false)
    },
    operation_change (val) {
      switch (val.operation.way) {
        case "batch":
          if (this.selectList.length > 0) {
            this.dialog = true;
            this.scopeInfo = this.selectList;
            this.dialogOperation = val.operation.operation;
            this.dialogComponent = val.operation.component;
            this.dialogTitle = val.operation.title;
            this.dialogWidth = val.operation.width || '40%';
          } else {
            this.$message.warning('请选择至少一条数据')
          }
          break;
        case "page":
          var paramsObjs = {}
          if (val.operation.params) {
            val.operation.params.forEach(item => {
              let keyName = item.keyName || item
              let valName = item.valName || item
              paramsObjs[keyName] = val.model ? val.model[valName] : ""
            });
          }
          this.$router.push({
            path: val.operation.path,
            query: {
              operation: val.operation.operation,
              component: val.operation.component,
              ...paramsObjs
            }
          });
          break;
        case "delete":
          this.$confirm('此操作将永久删除期刊信息，是否继续', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.api.deleteJournal(val.model.id).then(response => {
              if(response.status === 200) {
                this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
              } else {
                this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
              }
              this.init()
            })
          })
          break;
        default: break;
      }
    },
  }
};
</script>
