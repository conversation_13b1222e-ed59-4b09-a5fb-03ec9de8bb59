<template>
  <section class="form-tab" v-loading="getLoading">
    <!-- 表单内容 -->
    <el-tabs v-model="activeName">
      <el-tab-pane label="期刊内容" name="content">
        <el-form ref="submitRef"
          class="rule-form"
          :model="submitData"
          :rules="rules"
          label-width="90px">
          <el-row :gutter="20">
            <el-col :span="18">
              <el-row>
                <template v-for="(item, index) in formConfig.formField_L">
                  <el-col :key="index"
                          :span="item.colSpan">
                    <el-form-item :prop="item.prop"
                                  :label="item.label">
                      <component :is="item.component"
                                :model.sync="submitData[item.prop]"
                                v-model="submitData[item.prop]"
                                :width="item.width || '100%'"
                                :disabled="item.disabled"
                                :placeholder="item.placeholder"
                                :type="item.type"
                                :code="item.code"
                                :active="item.active"
                                :inactive="item.inactive">
                      </component>
                    </el-form-item>
                  </el-col>
                </template>
              </el-row>
            </el-col>
            <el-col :span="6">
              <el-row>
                <template v-for="(item, index) in formConfig.formField_R">
                  <el-col :key="index"
                          :span="item.colSpan">
                    <el-form-item :prop="item.prop"
                                  :label="item.label">
                      <component :is="item.component"
                                :model.sync="submitData[item.prop]"
                                v-model="submitData[item.prop]"
                                :width="item.width || '100%'"
                                :disabled="item.disabled"
                                :type="item.type"
                                :code="item.code">
                      </component>
                    </el-form-item>
                  </el-col>
                </template>
              </el-row>
            </el-col>
          </el-row>
        </el-form>
      </el-tab-pane>
      <el-tab-pane label="期刊解读" name="reader" :disabled="!$route.query.id">
        <el-form 
          class="rule-form"
          :model="submitData"
          :rules="rules"
          label-width="90px">
          <el-row :gutter="20">
            <el-col :span="18">
              <el-row>
                <template v-for="(item, index) in formConfig.formField_reader">
                  <el-col :key="index"
                          :span="item.colSpan">
                    <el-form-item :prop="item.prop"
                                  :label="item.label">
                      <component :is="item.component"
                                :model.sync="submitData[item.prop]"
                                v-model="submitData[item.prop]"
                                :width="item.width || '100%'"
                                :disabled="item.disabled"
                                :placeholder="item.placeholder"
                                :type="item.type"
                                :code="item.code"
                                :active="item.active"
                                :audioShow="item.audioShow && !!dataId"
                                :inactive="item.inactive">
                      </component>
                    </el-form-item>
                  </el-col>
                </template>
              </el-row>
            </el-col>
          </el-row>
        </el-form>
      </el-tab-pane>
      <el-tab-pane label="显示栏目" name="category">
        <ms-info-setting :categoryModel.sync="submitData.categoryList" :categoryConfigChild="{moduleName: 'tool_impact_factor'}" headerShow></ms-info-setting>
      </el-tab-pane>
    </el-tabs>

    
    <!-- 提交按钮 -->
    <footer-tool-bar v-loading="buttonLoading">
      <template slot="tool-content">
        <el-button type="primary" @click="info_operation('save')">保存</el-button>
        <el-button type="info" @click="info_operation('back')">返回</el-button>
      </template>
    </footer-tool-bar>
  </section>
</template>

<script>
import msInfoSetting from '@/components/MsCommon/ms-info-setting'
import FooterToolBar from '@/components/ToolBar/footer-tool-bar'
import formConfig from './data/form-config' 
import MsEditor from '@/components/MsEditor'
import { mapGetters } from "vuex";
export default {
  name: "journal-operation",
	data () {
    const validatePass = (rule, value, callback) => {
      if (value == '') {
        callback();
      } else if(value < 0 || value == 0){
        callback(new Error('请输入大于0的值'));
      } else {
        callback();
      }
    }
    const validateS = (rule, value, callback) => {
      if (value < 0) {
        callback(new Error(''));
      } else {
        callback();
      }
    }
		return {
      activeName: 'content',
      buttonLoading: false,
      getLoading: false,
      formConfig: formConfig,
      dataId: this.$route.query.id ? this.$route.query.id : 0,
      rules: {
        listViewSort: [
          { validator: validatePass, trigger: 'blur' }
        ],
        isComment: [
          { validator: validateS, trigger: 'change'}
        ]
      },
      submitData: {
        isComment: 1
      }
		}
  },
  components: {
    msInfoSetting,
    FooterToolBar,
    MsEditor
  },
  created() {
    this.init()
  },
  computed: {
    ...mapGetters(["info"]),
  },
  methods: {
    init() {
      this.dialog = false
      let id = this.dataId
      if(id !== 0) {
        this.getLoading = true;
        this.submitData.id = id;
        this.api.showJournal({id: id}).then( response => {
          this.getLoading = false;
          if(response.status === 200) {
            let res = response.data
            this.submitData = {
              ...this.submitData,
              ...res,
              content: this.PUBLIC_Methods.unexcapeHtml(res.content),
              unscrambleContent: this.PUBLIC_Methods.unexcapeHtml(res.unscrambleContent)
            }
          } else {
            this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
          }
        }).catch(() => {
          this.getLoading = false;
        })
      } 
    },
    info_operation(val) {
      switch (val) {
        case 'save': 
          this.dataId ? this.updateRecruitment() : this.createRecruitment()
          break;
        case 'back':
          this.$router.back();
          break;
        default: break;
      }
    },
    createRecruitment() {
      this.$refs['submitRef'].validate((valid) => {
        if (valid) {
          this.buttonLoading = true;
          let params = {
            ...this.submitData,
            userId: this.info.userId,
            username: this.info.userName,
            content: this.PUBLIC_Methods.excapeHtml(this.submitData.content),
            unscrambleContent: this.PUBLIC_Methods.excapeHtml(this.submitData.unscrambleContent)
          }
          this.api.createJournal(params).then(response => {
            this.buttonLoading = false
            if(response.status === 200) {
              this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
              this.$router.back()
            } else {
              this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
            }
          }).catch(() => this.buttonLoading = false)
        } else {
          console.log('error submit!!');
          return false;
        }
      })
    },
    updateRecruitment() {
      this.$refs['submitRef'].validate((valid) => {
        if (valid) {
          this.buttonLoading = true;
          let params = {
            ...this.submitData,
            userId: this.info.userId,
            username: this.info.userName,
            content: this.PUBLIC_Methods.excapeHtml(this.submitData.content),
            unscrambleContent: this.PUBLIC_Methods.excapeHtml(this.submitData.unscrambleContent)
          }
          // console.log(this.submitData,'??');
          this.api.updateJournal(params).then(response => {
            this.buttonLoading = false
            if(response.status === 200) {
              this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
              this.$router.back()
            } else {
              this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
            }
          }).catch(() => this.buttonLoading = false)
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    }
  }
}
</script>
