import serveUrl from '@/store/data/serveUrl.js'

const scopeConfig = {
  show: {
    status: () => {
      return {
        type: 'status',
        rule: {
          0: { label: '待审核', background: '#A7ADBD' },
          2: { label: '审核通过', background: '#40A23F' }
        }
      }
    },
    fullname: () => {
      return {
        type: 'preview',
        config: {
          noCheck: true,
          pageUrl: `${serveUrl['base']}/sci/journal-discuss?id=`
        }
      }
    }
  },
  headerShow: {
    status: () => {
      return {
        type: 'dropdown',
        icon: 'icon-funnel',
        options: [
          { label: '全部', value: null },
          { label: '待审核', value: 0 },
          { label: '审核通过', value: 2 }
        ],
        operation: 'query'
      }
    }
  }
}

export default scopeConfig;
