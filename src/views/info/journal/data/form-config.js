const formConfig = {
  formField_L: [
    {
      label: '期刊全称',
      prop: 'fullname',
      colSpan: 12,
      component: 'ms-input'
    },
    {
      label: '缩写',
      prop: 'abbr',
      colSpan: 12,
      component: 'ms-input'
    },
    {
      label: '中文',
      prop: 'cnname',
      colSpan: 12,
      component: 'ms-input'
    },
    {
      label: 'ISSN',
      prop: 'issn',
      colSpan: 6,
      component: 'ms-input'
    },
    {
      label: 'EISSN',
      prop: 'eissn',
      colSpan: 6,
      component: 'ms-input'
    },
    {
      label: '出版社',
      prop: 'orgnization',
      colSpan: 12,
      component: 'ms-input'
    },
    {
      label: '国别中文名',
      prop: 'countryCn',
      colSpan: 6,
      component: 'ms-input'
    },
    {
      label: '国别英文名',
      prop: 'country',
      colSpan: 6,
      component: 'ms-input'
    },
    {
      label: '出版社地址',
      prop: 'address',
      colSpan: 12,
      component: 'ms-input'
    },
    {
      label: '出版周期',
      prop: 'frequence',
      colSpan: 6,
      component: 'ms-input'
    },
    {
      label: '出版年份',
      prop: 'pyear',
      colSpan: 6,
      component: 'ms-input'
    },
    {
      label: '出版社URL',
      prop: 'orgnizationUrl',
      colSpan: 12,
      component: 'ms-input'
    },
    {
      label: '杂志官网', // 期刊主页
      prop: 'website',
      colSpan: 12,
      component: 'ms-input'
    },
    {
      label: '投稿地址',
      prop: 'submitWebsite',
      colSpan: 12,
      component: 'ms-input'
    },
    {
      label: '作者需知',
      prop: 'guideForAuthor',
      colSpan: 12,
      component: 'ms-input'
    },
    {
      label: '是否OA',
      prop: 'isOa',
      colSpan: 12,
      component: 'ms-select-local',
      code: 'isOa'
    },
    {
      label: '期刊预警',
      prop: 'earlyWarning',
      colSpan: 6,
      component: 'ms-input',
      placeholder: '高 / 中 / 低'
    },
    {
      label: '展示评语',
      prop: 'isComment',
      colSpan: 6,
      component: 'ms-switch',
      active: 1,
      inactive: 0
    },
    {
      label: '状态',
      prop: 'status',
      colSpan: 12,
      component: 'ms-select-local',
      code: 'journalStatus'
    },
    {
      label: '排序值',
      prop: 'listViewSort',
      colSpan: 6,
      component: 'ms-input',
      type: 'number'
    },
    //xz
    {
      label: '期刊质量指数',
      prop: 'qualityIndex',
      colSpan: 12,
      component: 'ms-input-number',
      type: 'number'
    },
    {
      label: '水刊指数',
      prop: 'predatoryIndex',
      colSpan: 6,
      component: 'ms-input-number',
      type: 'number'
    },
    //xz
    {
      label: '数据录入情况',
      prop: 'sciScie',
      colSpan: 24,
      component: 'ms-input',
      type: 'textarea'
    },
    {
      label: '期刊简介',
      prop: 'content',
      colSpan: 24,
      component: 'ms-editor',
    },
    {
      label: '编辑者',
      prop: 'updatedName',
      colSpan: 12,
      component: 'ms-input',
      disabled: true,
      placeholder: ' '
    },
    {
      label: '更新时间',
      prop: 'updatedTime',
      colSpan: 12,
      component: 'ms-input',
      disabled: true,
      placeholder: ' '
    }
  ],
  formField_R: [
    {
      label: 'IF',
      prop: 'impactFactor',
      colSpan: 24,
      component: 'ms-input-number'
    },
    {
      label: '5年IF',
      prop: 'impactFactorNoself',
      colSpan: 24,
      component: 'ms-input-number'
    },
    {
      label: '中文小分类名',
      prop: 'smallclass',
      colSpan: 24,
      component: 'ms-input'
    },
    {
      label: '英文小分类名',
      prop: 'smallclassEn',
      colSpan: 24,
      component: 'ms-input'
    },
    {
      label: 'JCR级别',
      prop: 'smallclassGrade',
      colSpan: 24,
      component: 'ms-input-number'
    },
    {
      label: '封面图片',
      prop: 'cover',
      colSpan: 24,
      component: 'ms-single-image'
    },
    {
      label: '外向链接',
      prop: 'linkOutUrl',
      colSpan: 24,
      component: 'ms-input'
    }
    // 新版期刊显示注释
    // {
    //   label: '大分类',
    //   prop: 'bigclass',
    //   colSpan: 8,
    //   component: 'ms-input'
    // },
    // {
    //   label: '大分类级别',
    //   prop: 'bigclassGrade',
    //   colSpan: 8,
    //   component: 'ms-input-number'
    // },
    // {
    //   label: '中科院大分类',
    //   prop: 'bigclassCas',
    //   colSpan: 8,
    //   component: 'ms-input'
    // },
    // {
    //   label: '中科院小分类',
    //   prop: 'casGrade',
    //   colSpan: 8,
    //   component: 'ms-input-number'
    // },
  ],
  formField_reader: [
    {
      label: '解读内容',
      prop: 'unscrambleContent',
      colSpan: 24,
      component: 'ms-editor',
      audioShow: true
    },
  ]
}

export default formConfig;
