const domConfig = {
  journal: {
    listSearch: [
      {
        label: '期刊名称',
        placeholder: '缩写/全称/ISSN',
        model: 'name',
        component: 'ms-input'
      },
      {
        label: '出版社',
        placeholder: '请输入',
        model: 'organization',
        component: 'ms-input'
      },
      {
        label: 'IF',
        placeholder: '',
        model: 'ifStart',
        component: 'ms-input-number'
      },
      {
        label: '~',
        placeholder: '',
        model: 'ifEnd',
        component: 'ms-input-number'
      },
      {
        label: '专题',
        placeholder: '请选择',
        model: 'categoryId',
        type: 'journal',
        component: 'ms-category-cascader'
      },
    ],
    tableHeader: [
      { label: 'ID', property: 'id', width: 80 },
      { label: 'ISSN', property: 'issn', sortable: true },
      { label: '期刊名称', property: 'fullname', width: 230 },
      { label: '排序值', property: 'listViewSort', sortable: true },
      { label: '缩写', property: 'abbr', width: 200 },
      { label: '专题分类', property: 'impactFactorClasses', width: 200 },
      { label: 'IF', property: 'impactFactor', sortable: true }
    ],
    tableButtons: [
      {
        label: '编辑',
        icon: '',
        role: '',
        operation: 'edit',
        component: 'journal-operation',
        way: 'page',
        type: 'primary',
        path: 'journal-operation',
        params: ['id']
      },
      {
        label: '删除',
        icon: '',
        role: '',
        way: 'delete',
        type: 'danger',
        // roleDisabled: true
      }
    ],
    soltButtons: [
      {
        title: '批量添加专题',
        label: '批量添加专题',
        type: 'success',
        operation: 'add',
        component: 'msArticleCategory',
        way: 'batch',
        identify: 'batch_add_category',
        width: '70%'
      },
      {
        title: '批量移除专题',
        label: '批量移除专题',
        type: 'success',
        operation: 'delete',
        component: 'msArticleCategory',
        way: 'batch',
        identify: 'batch_delete_category',
        width: '70%'
      },
      {
        label: '添加期刊',
        type: 'primary',
        icon: 'el-icon-plus',
        operation: 'created',
        component: 'journal-operation',
        way: 'page',
        path: 'journal-operation',
        params: ['id']
      }
    ]
  },
  contribution: {
    listSearch: [
      {
        label: '期刊名称',
        placeholder: '请输入标题内容',
        model: 'fullname',
        component: 'ms-input'
      }
    ],
    tableHeader: [
      { label: 'ID', property: 'id', sortable: true, width: '40px' },
      { label: '期刊名称', property: 'fullname' },
      { label: '贡献人', property: 'createdName' },
      { label: '状态', property: 'status' },
      { label: '留言时间', property: 'createdTime', sortable: true }
    ],
    tableButtons: [
      {
        label: '',
        icon: '',
        role: '',
        operation: 'editStatus',
        component: 'msContributionOperation',
        way: 'dialog',
        field: 'status',
        rule: {
          2: { label: '去审', type: '', operation: 'toreview', disabled: true },
          0: { label: '审核', type: 'success', operation: 'approval' }
        }
      },
      {
        label: '删除',
        icon: '',
        role: '',
        operation: 'delete',
        component: 'msContributionOperation',
        way: 'dialog',
        type: 'danger'
      }
    ],
    soltButtons: [
      {
        label: '批量审核',
        type: 'primary',
        operation: 'approval',
        component: 'msContributionOperation',
        way: 'batch'
      },
      // { 
      //   label: '批量去审', 
      //   type: 'primary',
      //   operation: 'toreview',
      //   component: 'msContributionOperation',
      //   way: 'batch'
      // },
      {
        label: '批量删除',
        type: 'info',
        icon: 'el-icon-close',
        operation: 'delete',
        component: 'msContributionOperation',
        way: 'batch'
      }
    ]
  }
}

export default domConfig;
