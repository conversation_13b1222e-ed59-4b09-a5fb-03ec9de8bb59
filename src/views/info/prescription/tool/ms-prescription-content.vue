<template>
  <section>
    <el-form :model="submitData"
             ref="submitRef"
             class="rule-form info-form"
             :rules="rules"
             label-width="70px">
      <el-row :gutter="20"> 
        <el-col :span="18">
          <el-row>
            <el-col :span="24">
              <el-form-item label="标题" prop="title">
                <el-input v-model="submitData.title" style="width: 100%" placeholder="简明处方" maxlength="4" show-word-limit></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="产品名称" prop="productName">
                <el-input v-model="submitData.productName" style="width: 100%" placeholder="请输入药品名称" maxlength="15" show-word-limit></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="科室" prop="categoryList">
                <ms-category-cascader
                  :model.sync="submitData.categoryList" :config="{moduleName:'chufang'}" :multiple="true" :placeholder="'请输入并选择产品所属的科室'"
                ></ms-category-cascader>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="详情" prop="content">
                <ms-editor v-model="submitData.content" :height="280"></ms-editor>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="编辑者" prop="editorId">
                <ms-editor-search :model.sync="submitData.editorId" :modelName.sync="submitData.editor"></ms-editor-search>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="微网站" prop="thinWebUrl">
                <el-input v-model="submitData.thinWebUrl" style="width: 100%" placeholder="请输入跳转URL地址"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="签名" prop="sign">
                <el-input v-model="submitData.sign" type="textarea" :rows="4" placeholder="50字以内的文本介绍当前主页" maxlength="50" show-word-limit></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-col>
      </el-row>
    </el-form>
  </section>
</template>

<script>
import MsEditor from '@/components/MsEditor'
import msCategoryCascader from "@/components/MsCommon/ms-category-cascader"
import msEditorSearch from "@/components/MsCommon/ms-editor-search"
import {ms_rule_url_http} from "@/utils/form-rule.js";
import { mapGetters } from "vuex";
export default {
	name: "ms-prescription-content",
	data () {
		return {
      rules: {
        title: [
          { required: true, message: "请填写标题", trigger: 'blur' }
        ],
        categoryList: [
          { required: true, message: "请选择科室", trigger: 'change' }
        ],
        content: [
          { required: true, message: "请填写详情", trigger: 'blur' }
        ],
        editorId: [
          { required: true, message: "请选择编辑者", trigger: 'change' }
        ],
        thinWebUrl:[
         { validator: ms_rule_url_http, trigger: 'blur' }
        ]
      },
		}
  },
  components: {
    MsEditor,
    msCategoryCascader,
    msEditorSearch
  },
  computed: {
    ...mapGetters(["submitData", "info"])
  },
  created() {
  },
	methods: {
    // 数据校验
    validateData(callback) {
      // if (this.submitData.categoryList && this.submitData.categoryList.length > 0) {
      //   this.submitData.categoryList = this.submitData.categoryList.map(v => {
      //     return {
      //       categoryId: v.id,
      //       categoryName: v.name
      //     }
      //   })
      // }
      let params = {
        ...this.submitData,
        userId: this.info.userId,
        username: this.info.userName,
        content: this.PUBLIC_Methods.excapeHtml(this.submitData.content)
      }
      this.$refs["submitRef"].validate( valid => {
        if (valid) {
          callback(params)
        }
      })
    },
	}
}
</script>

<style scope lang="scss">
.add-mail {
  cursor: pointer;
  width: 100%;
  height: 30px;
  line-height: 28px;
  border: 1px dashed #DCDFE6;
  border-radius: 4px;
  text-align: center;
  font-size: 12px;
  color: #333;
  outline: none;
  margin-top: 10px;
  background-color: rgba(0,0,0, 0);
}
.link-pos {
  position: relative;
  top: -2px;
}
.info-form{
  /deep/ .el-checkbox {
    margin-right: 0px;
  }
  &-right {
    /deep/ .el-form-item {
      margin-bottom: 16px;
    }
  }
}
</style>
