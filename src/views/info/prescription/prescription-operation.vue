<template>
  <section class="form-tab" v-loading="getLoading">
    <ms-prescription-content ref="contentTemp"></ms-prescription-content>
    <!-- 提交按钮 -->
    <footer-tool-bar v-loading="buttonLoading">
      <template slot="tool-content">
        <el-button type="primary" @click="info_operation('save')">保存</el-button>
        <el-button type="info" @click="info_operation('back')">返回</el-button>
      </template>
    </footer-tool-bar>
  </section>
</template>

<script>
import msPrescriptionContent from './tool/ms-prescription-content'
import prescriptionMixin from "./prescription-mixin"
import FooterToolBar from '@/components/ToolBar/footer-tool-bar'
import { mapGetters } from "vuex";
export default {
  name: "prescription-operation",
  mixins: [prescriptionMixin],
	data () {
		return {
      dataId: this.$route.query.id ? this.$route.query.id : 0,
      buttonLoading: false,
      getLoading: false,
      submitData: {},
		}
  },
  created() {
    this.init()
  },
  components: {
    msPrescriptionContent,
    FooterToolBar
  },
  computed: {
    ...mapGetters(["info"])
  },
  methods: {
    init() {
      let id = this.dataId
      this.dialog = false;
      this.$store.dispatch('SetSubmitData', this.submitData)
      if(id !== 0) {
        this.getLoading = true;
        this.api.getPrescriptionById({id: id}).then( response => {
          this.getLoading = false;
          if(response.status === 200) {
            let res = response.data
            let categoryList = []
            if (res.categoryList  && res.categoryList .length > 0) {
              categoryList = res.categoryList.map(v => {
                return {
                  categoryId: v.categoryId,
                  categoryName: v.categoryName
                }
              })
            }
            this.submitData = {
              ...res,
              categoryList: categoryList,
              content: this.PUBLIC_Methods.unexcapeHtml(res.content)
            }
          } else {
            this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
          }
          this.$store.dispatch('SetSubmitData', this.submitData)
        }).catch(() => {
          this.getLoading = false;
          this.$store.dispatch('ClearSubmitData')
        })
      } else {
        // this.$router.back()
      }
    },
    info_operation(val) {
      switch (val) {
        case 'save': 
          this.$refs['contentTemp'].validateData(this.saveData)
          break;
        case 'back':
          this.$router.back();
          break;
        default: break;
      }
    },
    saveData(params) {
      this.buttonLoading = true;
      let url = this.dataId ? 'updatePrescription':'savePrescription'
      this.api[url](params).then(response => {
        this.buttonLoading = false
        if(response.status === 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
          this.$router.back()
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
      }).catch(() => this.buttonLoading = false)
    },
  }
}
</script>

<style scoped>
  .live-address, .live-img .label{
    font-size: 12px;
  }
  .live-img .label {
    text-align: center;
  }
  .live-img .img {
    padding: 10px;
    text-align: center;
    height: 100px
    
  }
  .live-img .img img{
    width: auto;
    height: 100%;
  }
</style>
