const scopeConfig = {
  show: {
    status: () => {
      return {
        type: 'status',
        rule: {
          0: { label: '禁用', background: '#A7ADBD' },
          1: { label: '启用', background: '#40A23F' },
        }
      }
    },
    createdTime: () => {
      return {
        type: 'formatTime',
        cFormat: '{y}-{m}-{d} {h}:{i}'
      }
    }
  },
  headerShow: {
    status: () => {
      return {
        type: 'dropdown',
        icon: 'icon-funnel',
        options: [
          { label: '全部', value: null },
          { label: '禁用', value: 0 },
          { label: '启用', value: 1 }
        ],
        operation: 'query'
      }
    },
  }
}

export default scopeConfig;
