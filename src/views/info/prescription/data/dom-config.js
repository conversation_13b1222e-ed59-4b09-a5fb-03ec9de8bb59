const domConfig = {
  listSearch: [
    {
      label: '标题',
      placeholder: '请输入',
      model: 'title',
      component: 'ms-input'
    },
  ],
  tableHeader: [
    { label: '处方ID', property: 'id', sortable: true, width: 80 },
    { label: '标题', property: 'title'},
    { label: '产品名称', property: 'productName'},
    { label: '创建时间', property: 'createdTime', sortable: true, width: 130 },
    { label: '创建人', property: 'createdName'},
    { label: '状态', property: 'status'},
  ],
  tableButtons: [
    {
      label: '编辑',
      icon: '',
      role: '',
      operation: 'edit',
      component: 'prescription-operation',
      way: 'page',
      type: 'primary',
      path: 'prescription-operation',
      params: ['id']
    },
    {
      label: '',
      icon: '',
      role: '',
      operation: 'editStatus',
      component: 'msPrescriptionOperation',
      way: 'dialog',
      field: 'status',
      rule: {
        1: { label: '禁用', type: '', operation: 'toreview' },
        0: { label: '启用', type: 'success', operation: 'approval' }
      }
    },
    // {
    //   label: '删除',
    //   icon: '',
    //   role: '',
    //   operation: 'delete',
    //   way: 'delete',
    //   type: 'danger'
    // },
  ],
  soltButtons: [
    { 
      label: '创建', 
      type: 'primary', 
      icon: 'el-icon-plus',
      title: '创建',
      operation: 'created',
      component: 'prescription-operation',
      path: 'prescription-operation',
      way: 'page',
    }
  ]
}

export default domConfig;
