<template>
  <section v-loading="getLoading" class="form-tab">
    <!-- 表单内容 -->
    <el-row :gutter="10">
      <el-col :span="12">
        <el-form ref="submitRef"
                 class="rule-form"
                 :model="submitData"
                 :rules="rule"
                 label-width="80px">
          <el-form-item prop="chinaTitle" label="标题中文">
            <el-input v-model="submitData.chinaTitle" style="width: 100%"></el-input>
          </el-form-item>
          <el-form-item label="html文件" prop="htmlUrl">
            <ms-file-upload v-model="htmlUrlArr" buttonLabel="文件上传" :isPrivate="false" fileSuffix="scale/calculator" :limit=1></ms-file-upload>
          </el-form-item>
          <el-form-item label="js文件" prop="jsUrl">
            <ms-file-upload v-model="jsUrlArr" buttonLabel="文件上传" :isPrivate="false" fileSuffix="scale/calc" :limit=1></ms-file-upload>
          </el-form-item>
          <el-form-item label="显示选项">
            <el-checkbox v-model="submitData.appVisible" :true-label="1" :false-label="0">APP显示</el-checkbox>
            <el-checkbox v-model="submitData.pcVisible" :true-label="1" :false-label="0">PC显示</el-checkbox>
          </el-form-item>
          <el-form-item prop="keywords" label="关键词">
            <MsTagSearch v-model="submitData.tagList" :notMul="false"></MsTagSearch>
          </el-form-item>
          <el-form-item label="权限" prop="opened">
            <el-select v-model="submitData.opened" style="width: 100%">
              <el-option :value="1" label="开放填写"></el-option>
              <el-option :value="0" label="登录填写"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="开发时间" prop="originDevTime">
            <el-date-picker
              style="width: 100%"
              v-model="submitData.originDevTime"
              type="datetime"
              format="yyyy-MM-dd HH:mm:ss"
              value-format="yyyy-MM-dd HH:mm:ss"
              placeholder="原版开发时间">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="" prop="cnDevTime">
            <el-date-picker
              style="width: 100%"
              format="yyyy-MM-dd HH:mm:ss"
              value-format="yyyy-MM-dd HH:mm:ss"
              v-model="submitData.cnDevTime"
              type="datetime"
              placeholder="中文版开发时间">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="原作者" prop="author">
            <el-input v-model="submitData.author" style="width: 100%" placeholder="请填写原作者"></el-input>
          </el-form-item>
          <el-form-item label="参考文献" prop="document">
            <ms-editor v-model="submitData.document"></ms-editor>
          </el-form-item>
          <el-form-item label="版权" prop="copyright">
            <el-input v-model="submitData.copyright" style="width: 100%" placeholder="请填写版权"></el-input>
          </el-form-item>
        </el-form>
      </el-col>
      <el-col :span="12">
        <ms-info-setting :categoryModel.sync="submitData.categoryList" :headerShow="false" :categoryConfigChild="{moduleName: 'scale'}"></ms-info-setting>
      </el-col>
    </el-row>
    <!-- 弹出层 -->
    <el-dialog :visible.sync="dialog" 
               closeable 
               :show-close="false"
               :close-on-click-modal="false"
               width="50%"
               :title="dialogTitle">
      <component :is="dialogComponent" 
                   :model="dialogInfo" 
                   :operation="dialogOperation" 
                   @close="dialog = !dialog" 
                   @up-date="init"
                   v-if="dialog"></component>
    </el-dialog>
    <!-- 提交按钮 -->
    <footer-tool-bar v-loading="buttonLoading">
      <template slot="tool-content">
        <el-button plain v-show="dataId" @click="info_operation('approval')">{{submitData.status === 1 ? '去审' : '审核'}}</el-button>
        <el-button type="primary" @click="info_operation('save')">保存</el-button>
        <el-button type="info" @click="info_operation('back')">返回</el-button>
      </template>
    </footer-tool-bar>
  </section>
</template>

<script>
import MsEditor from '@/components/MsEditor'
import FooterToolBar from '@/components/ToolBar/footer-tool-bar'
import MsTagSearch from '@/components/MsCommon/ms-tag-search'
import msInfoSetting from '@/components/MsCommon/ms-info-setting'
import scaleMixin from "./scale-mixin"
import { mapGetters } from "vuex";
export default {
  name: "scale-operation",
  mixins: [scaleMixin],
	data () {
		return {
      buttonLoading: false,
      getLoading: false,
      dataId: this.$route.query.id ? this.$route.query.id : 0,

      submitData: { 
        chinaTitle: "",
        htmlUrl: '',
        jsUrl: '',
        contentUrl: "",
        pcVisible: 1,
        appVisible: 1,
        status: 0,
        categoryList: [],
        tagList: [],
        opened: '',
        originDevTime: '',
        cnDevTime: '',
        author: '',
        document: '',
        copyright: ''
      },
      htmlUrlArr: [],
      jsUrlArr: [],
      rule: {
        chinaTitle: [
          { required: true, message: "请输入标题", trigger: 'blur' }
        ]
      },
      dialogComponent: '',
      dialog: false,
      dialogTitle: '',
      dialogOperation: '',
      dialogInfo: {},

      categoryConfig: {
        moduleName: ['medsci-transverse', 'Internal Medicine', 'Surgery', 'Critical Care', 'Other Departments', 'Others', 'hot'],
      }
		}
  },
  components: {
    MsTagSearch,
    msInfoSetting,
    FooterToolBar,
    MsEditor
  },
  computed: {
    ...mapGetters(["info"])
  },
  created() {
    this.init()
  },
  methods: {
    init() {
      this.dialog = false
      let id = this.dataId
      if(id !== 0) {
        this.getLoading = true;
        this.submitData.id = id;
        this.api.getTools({id: id}).then( response => {
          this.getLoading = false;
          if(response.status === 200) {
            let res = response.data
            this.submitData = {
              ...this.submitData,
              ...res
            }
            this.htmlUrlArr = res.htmlUrl ? [{url: res.htmlUrl,name: res.htmlUrl}] : []
            this.jsUrlArr = res.jsUrl ? [{url: res.jsUrl,name: res.jsUrl}] : []
          } else {
            this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
          }
        }).catch(() => {
          this.getLoading = false;
        })
      } 
    },
    info_operation(val) {
      switch (val) {
        case 'save': 
          this.$refs["submitRef"].validate( valid => {
            if (valid) {
              this.dataId ? this.updateScale() : this.createScale()
            }
          })
          break;
        case 'back':
          this.$router.back();
          break;
        case 'approval':
          var params = {
            way: 'dialog',
            operation: this.submitData.status === 1 ? 'toreview' : 'approval',
            component: 'msScaleOperation',
            data: this.submitData
          }
          this.dialog = true;
          this.dialogInfo = params.data
          this.dialogOperation = params.operation;
          this.dialogComponent = params.component;
          this.dialogTitle = params.title;
          break;
        default: break;
      }
    },
    createScale() {
      this.buttonLoading = true;
      let params = {
        userId: this.info.userId,
        username: this.info.userName,
        ...this.submitData,
        htmlUrl: this.htmlUrlArr.length > 0 ? this.htmlUrlArr[0].url : '',
        jsUrl: this.jsUrlArr.length > 0 ? this.jsUrlArr[0].url : ''
      }
      this.api.insertTools(params).then(response => {
        this.buttonLoading = false
        if(response.status === 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
          this.$router.back()
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
      }).catch(() => this.buttonLoading = false)
    },
    updateScale() {
      this.buttonLoading = true;
      let params = {
        userId: this.info.userId,
        username: this.info.userName,
        ...this.submitData,
        htmlUrl: this.htmlUrlArr.length > 0 ? this.htmlUrlArr[0].url : '',
        jsUrl: this.jsUrlArr.length > 0 ? this.jsUrlArr[0].url : ''
      }
      this.api.updateTools(params).then(response => {
        this.buttonLoading = false
        if(response.status === 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
          this.$router.back()
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
      }).catch(() => this.buttonLoading = false)
    }
  }
}
</script>
