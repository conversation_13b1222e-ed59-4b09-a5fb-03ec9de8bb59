const domConfig = {
  listSearch: [
    {
      label: '标题',
      placeholder: '请输入',
      model: 'chinaTitle',
      component: 'ms-input'
    },
    {
      label: '分类',
      placeholder: '请选择',
      model: 'classify',
      component: 'ms-category-cascader'
    },
    {
      label: '创建时间',
      placeholder: '请选择时间段',
      model: 'createTime',
      component: 'ms-picker',
      type: 'daterange',
      width: '240px'
    }
  ],
  tableHeader: [
    { label: 'ID', property: 'id', sortable: true, width: 80 },
    { label: '标题', property: 'chinaTitle', width: 250 },
    { label: '点击数', property: 'allClick', width: '80' },
    { label: '状态', property: 'status'},
    { label: '创建人', property: 'createdName' },
    { label: '创建时间', property: 'createdTime', sortable: true, width: 130  }
  ],
  tableButtons: [
    {
      label: '编辑',
      icon: '',
      role: '',
      operation: 'edit',
      component: 'scale-operation',
      way: 'page',
      type: 'primary',
      path: 'scale-operation',
      params: ['id']
    },
    {
      label: '',
      icon: '',
      role: '',
      operation: 'editStatus',
      component: 'msScaleOperation',
      way: 'dialog',
      field: 'status',
      rule: {
        1: { label: '去审', type: '', operation: 'toreview' },
        0: { label: '审核', type: 'success', operation: 'approval' }
      }
    },
    {
      label: '删除',
      operation: 'delete',
      type: 'danger',
      way: 'dialog',
      component: 'msScaleOperation',
    }
  ],
  soltButtons: [
    { 
      label: '手工添加', 
      type: 'primary', 
      icon: 'el-icon-plus',
      operation: 'created',
      component: 'scale-operation',
      way: 'page',
      path: 'scale-operation',
      params: ['id']
    },
    { 
      label: '批量审核', 
      type: 'primary',
      operation: 'approval',
      component: 'msScaleOperation',
      way: 'batch'
    },
    { 
      label: '批量去审', 
      type: 'primary',
      operation: 'toreview',
      component: 'msScaleOperation',
      way: 'batch'
    },
    { 
      label: '批量删除',
      type: 'info',
      icon: 'el-icon-close',
      operation: 'delete',
      component: 'msScaleOperation',
      way: 'batch'
    }
  ]
}

export default domConfig;
