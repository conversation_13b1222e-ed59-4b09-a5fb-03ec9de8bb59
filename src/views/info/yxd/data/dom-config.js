// import store from '@/store'

const domConfig = {
  listSearch: [
    {
      label: '标题',
      placeholder: '请输入',
      model: 'title',
      component: 'ms-input'
    },
  ],
  tableHeader: [
    { label: 'ID', property: 'id', sortable: true, width: '60' },
    { label: '标题', property: 'title', width: '250'},
    { label: 'PV/UV/分享/点赞/收藏', property: 'fields', width: '160' },
    { label: '创建时间', property: 'createdTime', sortable: true,  width: '130'  },
    { label: '创建人', property: 'createdName' },
    { label: '发布时间', property: 'publishedTime', sortable: true,  width: '130'  },
    { label: '状态', property: 'status'},
  ],
  tableButtons: [
    {
      label: '编辑',
      icon: '',
      role: '',
      operation: 'edit',
      component: 'yxd-operation',
      way: 'page',
      type: 'primary',
      path: 'yxd-operation',
      params: ['id']
    },
    {
      label: '',
      icon: '',
      role: '',
      operation: 'editStatus',
      component: 'msYxdOperation',
      way: 'dialog',
      field: 'status',
      rule: {
        1: { label: '去审', type: '', operation: 'toreview' },
        0: { label: '审核', type: 'success', operation: 'approval' }
      }
    },
    {
      label: '删除',
      way: 'dialog',
      type: 'danger',
      operation: 'delete',
      component: 'msYxdOperation'
    },
  ],
  soltButtons: [
    { 
      label: '手工添加', 
      type: 'primary', 
      icon: 'el-icon-plus',
      operation: 'created',
      component: 'yxd-operation',
      way: 'page',
      path: 'yxd-operation',
      params: ['id']
    },
    { 
      label: '批量审核', 
      type: 'primary',
      operation: 'approval',
      component: 'msYxdOperation',
      way: 'batch'
    },
    { 
      label: '批量去审', 
      type: 'primary',
      operation: 'toreview',
      component: 'msYxdOperation',
      way: 'batch'
    },
    { 
      label: '批量删除',
      type: 'info',
      icon: 'el-icon-close',
      operation: 'delete',
      component: 'msYxdOperation',
      way: 'batch'
    },
    {
      label: '回收站',
      type: 'info',
      operation: '',
      component: 'msYxdRecycle',
      way: 'dialog',
      title: '医讯达回收站',
      width: '90%'
    }
  ]
}

export default domConfig;
