// import store from '@/store'

const domConfig = {
  listSearch: [
    {
      label: '用户名',
      placeholder: '请输入',
      model: 'userName',
      component: 'ms-input'
    },
    {
      label: '手机号码',
      placeholder: '请输入',
      model: 'userMobile',
      component: 'ms-input'
    },
    {
      label: '医院',
      placeholder: '请输入',
      model: 'hospitalName',
      component: 'ms-input'
    },
  ],
  tableHeader: [
    { label: 'ID', property: 'id', sortable: true, width: '60' },
    { label: '用户名', property: 'userName'},
    { label: '手机号', property: 'userMobile'},
    { label: '城市级别', property: 'cityLevel'},
    { label: '医院级别', property: 'hospitalLevel'},
    { label: '科室（部门）', property: 'departmentName'},
    { label: '身份', property: 'professionalCatName'},
    { label: '医讯达标题', property: 'edaTitle'},
    { label: '查看时间', property: 'browseTime', sortable: true,  width: '130'  },
    { label: '查看渠道', property: 'channel' },
    { label: '是否参与答题', property: 'isExam'},
    { label: '答题结果', property: 'examResult'},
  ],
}

export default domConfig;
