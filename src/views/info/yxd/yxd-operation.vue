<template>
  <section class="form-tab" v-loading="getLoading">
    <el-tabs v-model="activeName">
      <el-tab-pane label="内容详情" name="content">
        <ms-yxd-content ref="content" @changeTab="changeTab" :submitData.sync="submitData"></ms-yxd-content>
      </el-tab-pane>
      <el-tab-pane label="设置" name="setting">
        <ms-yxd-setting ref="setting" @changeTab="changeTab" :submitData.sync="submitData"></ms-yxd-setting>
      </el-tab-pane>
      <el-tab-pane label="显示栏目" name="category">
        <ms-info-setting :categoryModel.sync="submitData.categoryList" :categoryConfigChild="{moduleName: 'eda'}" headerShow></ms-info-setting>
      </el-tab-pane>
    </el-tabs>
    <!-- 表单内容 -->
    
    <!-- 提交按钮 -->
    <footer-tool-bar v-loading="buttonLoading">
      <template slot="tool-content">
        <el-button type="primary" @click="info_operation('save')">保存</el-button>
        <el-button type="info" @click="info_operation('back')">返回</el-button>
      </template>
    </footer-tool-bar>
  </section>
</template>

<script>
import FooterToolBar from '@/components/ToolBar/footer-tool-bar'
import msYxdContent from './tool/ms-yxd-content.vue'
import msYxdSetting from './tool/ms-yxd-setting.vue'
import msInfoSetting from '@/components/MsCommon/ms-info-setting'
import { mapGetters } from "vuex";
export default {
  name: "yxd-operation",
	data () {
		return {
      buttonLoading: false,
      getLoading: false,
      dataId: this.$route.query.id ? this.$route.query.id : 0,
      submitData: {
        categoryList:[],
        content:'',
        cover: '',
        editor: '',
        editorId: '',
        examId: '',
        examIntegralUme: '',
        examTitle: '',
        isShowDynamic: 1,
        isShowPart: 1,
        readIntegralUme: '',
        summary: '',
        tagList: [],
        title: '',
        videoIntegralUme: '',
        wordReactText: '',
        wordReactUrl: '',
        targetedGroupSendStatus: 0,
        orientationParamRequest: [],
        medsciShow: 1,
        needLogin: 0,
        linkOutUrl:''
      },
      activeName: 'content',
		}
  },
  computed: {
    ...mapGetters(["info"]),
  },
  components: {
    FooterToolBar,
    msYxdContent,
    msInfoSetting,
    msYxdSetting,
  },
  created() {
    this.init()
  },
  methods: {
    init() {
      this.dialog = false
      let id = this.dataId
      if(id !== 0) {
        this.getLoading = true;
        this.submitData.id = id;
        this.api.getMedsciEdaArticleById({id: id}).then( response => {
          this.getLoading = false;
          if(response.status === 200) {
            let res = response.data
            this.submitData = {
              ...this.submitData,
              ...res,
              content: this.PUBLIC_Methods.unexcapeHtml(res.content)
            }
          } else {
            this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
          }
        }).catch(() => {
          this.getLoading = false;
        })
      } 
    },
    info_operation(val) {
      switch (val) {
        case 'save': 
          try {
             Promise.all([
              this.$refs['setting'].validateData(),
              this.$refs['content'].validateData()
            ]).then(() => {
              this.dataId ? this.updateForm() : this.createForm();
            });
          } catch (error) {
            return;
          }
          break;
        case 'back':
          this.$router.back();
          break;
        default: break;
      }
    },
    changeTab(val) {
      this.activeName = val
    },
    createForm() {
      this.buttonLoading = true;
      this.submitData.content = this.PUBLIC_Methods.excapeHtml(this.submitData.content)
      let params = {
        userId: this.info.userId,
        username: this.info.userName,
        ...this.submitData
      }
      this.api.saveMedsciEdaArticle(params).then(response => {
        this.buttonLoading = false
        if(response.status === 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
          this.$router.back()
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
      }).catch(() => this.buttonLoading = false)
    },
    updateForm() {
      this.buttonLoading = true;
      this.submitData.content = this.PUBLIC_Methods.excapeHtml(this.submitData.content)
      console.log(this.submitData,'1111111');
      let params = {
        userId: this.info.userId,
        username: this.info.userName,
        ...this.submitData
      }
      this.api.updateMedsciEdaArticle(params).then(response => {
        this.buttonLoading = false
        if(response.status === 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
          this.$router.back()
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
      }).catch(() => this.buttonLoading = false)
    },
  }
}
</script>
