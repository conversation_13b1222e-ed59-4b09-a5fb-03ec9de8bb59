<template>
  <section>
    <el-form :model="submitData"
             ref="submitRef"
             class="rule-form info-form"
             :rules="rules"
             label-width="80px">
      <el-row :gutter="20"> 
        <el-col :span="18">
          <el-row>
            <el-col :span="12">
              <el-form-item label="关键词" prop="tagList">
                <MsTagSearch v-model="submitData.tagList" :notMul="false" :placeholder="'请输入关键词并选择'"></MsTagSearch>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="外向链接" prop="linkOutUrl">
                <el-input v-model="submitData.linkOutUrl" style="width: 100%" placeholder="请输入跳转URL"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="梅花总量" prop="maxIntegralUme">
                <el-input-number v-model="submitData.maxIntegralUme" :step="1" step-strictly :min="0"></el-input-number>个梅花
                <span class="light-hint">即当前内容可发放的梅花总量，当达到设定数值后，产生的用户行为将不再获得奖励。</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <!-- 之前是阅读奖励 -->
              <el-form-item label="查看奖励" prop="readIntegralUme">
                <el-input-number v-model="submitData.readIntegralUme" :step="1" step-strictly :min="0"></el-input-number>个梅花
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="视频奖励" prop="videoIntegralUme">
                <el-input-number v-model="submitData.videoIntegralUme" :step="1" step-strictly :min="0"></el-input-number>个梅花
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="选择试题" prop="examId" class="flex-item">
                <el-select style="width:100%" v-model="submitData.examId" filterable placeholder="请输入并选择试题集" clearable @change="changeGather">
                  <el-option
                    v-for="item in gatherList"
                    :key="item.id"
                    :label="item.title"
                    :value="item.id">
                  </el-option>
                </el-select>
                <el-button style="flex:1;margin-left:10px" v-if="submitData.examId" type="primary" @click="goGatherPage">查看</el-button>
              </el-form-item>
            </el-col>
            <el-col :span="11">
              <template v-if="submitData.examId">
                <el-form-item label="做题奖励" prop="examIntegralUme">
                  <el-input-number v-model="submitData.examIntegralUme" :step="1" step-strictly :min="0"></el-input-number>个梅花
                </el-form-item>
              </template>
              <template v-else>
                <el-form-item label="做题奖励">
                  <el-input-number disabled></el-input-number>个梅花
                </el-form-item>
              </template>
            </el-col>
          </el-row>
          <el-row v-if="isOpenResultOpen===0||isOpenResultOpen===1">
            <el-col :span="12">
              <el-form-item label="选择调研" prop="surveyId" class="flex-item">
                <el-select style="width:100%" v-model="submitData.surveyId" filterable placeholder="请输入并选择调研" clearable @change="changeMaterial('0',$event)" @clear="isOpenResultOpen = 0">
                  <el-option
                    v-for="item in materialList0"
                    :key="item.id"
                    :label="item.templateName"
                    :value="item.id">
                  </el-option>
                </el-select>
                <el-button style="flex:1;margin-left:10px" type="primary" v-if="submitData.surveyId" @click="goMaterialPage">查看</el-button>
              </el-form-item>
            </el-col>
            <el-col :span="11">
              <template v-if="submitData.surveyId">
                <el-form-item label="参与奖励" prop="examIntegralUme">
                  <el-input-number v-model="submitData.examIntegralUme" :step="1" step-strictly :min="0"></el-input-number>个梅花
                </el-form-item>
              </template>
              <template v-else>
                <el-form-item label="参与奖励">
                  <el-input-number disabled></el-input-number>个梅花
                </el-form-item>
              </template>
            </el-col>
          </el-row>
          <el-row v-if="isOpenResultOpen===0||isOpenResultOpen===2">
            <el-col :span="12">
              <el-form-item label="选择投票" prop="surveyId" class="flex-item">
                <el-select style="width:100%" v-model="submitData.surveyId" filterable placeholder="请输入并选择投票" clearable @change="changeMaterial('1',$event)" @clear="isOpenResultOpen = 0">
                  <el-option
                    v-for="item in materialList1"
                    :key="item.id"
                    :label="item.templateName"
                    :value="item.id">
                  </el-option>
                </el-select>
                <el-button style="flex:1;margin-left:10px" type="primary" v-if="submitData.surveyId" @click="goMaterialPage">查看</el-button>
              </el-form-item>
            </el-col>
            <el-col :span="11">
              <template v-if="submitData.surveyId">
                <el-form-item label="参与奖励" prop="examIntegralUme">
                  <el-input-number v-model="submitData.examIntegralUme" :step="1" step-strictly :min="0"></el-input-number>个梅花
                </el-form-item>
              </template>
              <template v-else>
                <el-form-item label="参与奖励">
                  <el-input-number disabled></el-input-number>个梅花
                </el-form-item>
              </template>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="文字跳转" prop="wordReactText">
                <el-input v-model="submitData.wordReactText" style="width: 100%" placeholder="请输入显示文本" maxlength="50" show-word-limit></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="wordReactUrl" label-width="10px">
                <el-input v-model="submitData.wordReactUrl" style="width: 100%" placeholder="请输入跳转URL"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
             <el-col :span="24">
              <el-form-item label="上/下一篇" prop="isShowPart">
                <el-switch v-model="submitData.isShowPart" :active-value="1" :inactive-value="0"></el-switch>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="相关动态" prop="isShowDynamic">
                <el-switch v-model="submitData.isShowDynamic" :active-value="1" :inactive-value="0"></el-switch>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="在主站展示" prop="medsciShow">
                <el-switch v-model="submitData.medsciShow" :active-value="1" :inactive-value="0"></el-switch>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="权限" prop="needLogin">
                <el-select style="width:44%" v-model="submitData.needLogin" placeholder="请输入并选择调研" @change="changeNeedLoginMethod">
                  <el-option
                    v-for="item in options"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <template v-if="submitData.needLogin == 1">
            <el-row>
              <el-col :span="24">
                <el-form-item label="定向群发" prop="targetedGroupSendStatus">
                  <el-switch v-model="submitData.targetedGroupSendStatus" :active-value="1" :inactive-value="0"></el-switch>
                </el-form-item>
              </el-col>
            </el-row>
          </template>
          <template v-if="submitData.targetedGroupSendStatus == 1">
            <el-row>
              <el-col :span="12">
                <el-form-item label="" prop="content" class="flex-item">
                  <ms-hospital-search v-model="hospital" :model.sync="hospital" @bindData="bindHospital" :placeholder="'请输入医院名称搜索，并选择'" style="width:100%"></ms-hospital-search>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label-width="10px" prop="content" class="flex-item">
                  <ms-category-cascader
                    v-model="department" :model.sync="department" :config="{moduleName:'chufang'}" :type="'all'" :multiple="true" @bindData="bindDepartment" :placeholder="'请选择科室，默认全选'" style="width:100%"
                  ></ms-category-cascader>
                  <!-- <ms-department-search v-model="department" :model.sync="department" :type="'all'" :level="2"  @bindData="bindDepartment" :placeholder="'请选择科室，默认全选'"  :multiple="true" style="width:100%"></ms-department-search> -->
                  <el-button :disabled="!hospital" style="flex:1;margin-left:10px" type="primary" @click="addMessList">添加</el-button>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <el-form-item label="" prop="">
                  <el-table
                    row-key="additionId" 
                    :data="submitData.orientationParamRequest" 
                    :header-cell-style="headerCellStyle" 
                    :header-row-style="headerRowStyle" 
                    style="width: 100%;border-left: 1px solid #EBEEF5;border-right: 1px solid #EBEEF5">
                    <el-table-column align="center" label="序号" type="index" min-width="50px"></el-table-column>
                    <el-table-column align="center" label="医院" min-width="200px">
                      <template slot-scope="scope">
                        <span>{{ scope.row.hospitalName }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column align="center" label="科室" min-width="200px">
                      <template slot-scope="scope">
                        <span>{{ scope.row.departmentList.map(i=>{return i.categoryName}).join('、') }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column align="center" label="操作" min-width="80px">
                      <template slot-scope="scope">
                        <el-tooltip effect="dark" content="删除" placement="bottom">
                          <span @click="removeMessList(scope.$index)">
                            <svg-icon class-name="drag-handler" icon-class="icon-shanchu" />
                          </span>
                        </el-tooltip>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-form-item>
              </el-col>
            </el-row>
          </template>
        </el-col>
      </el-row>
    </el-form>
  </section>
</template>

<script>
import MsTagSearch from '@/components/MsCommon/ms-tag-search'
import MsHospitalSearch from '@/components/MsCommon/ms-hospital-search'
import msCategoryCascader from '@/components/MsCommon/ms-category-cascader'
export default {
	name: "ms-yxd-setting",
	data () {
		return {
      gatherList: [],
      materialList: [],
      materialList0: [],
      materialList1: [],
      isOpenResultOpen:0,
      options: [
        {id: 0, name: '开放阅读'}, {id: 1, name: '登录阅读'}
      ],
      rules: {
        wordReactUrl:[
          { validator: (rule, value, callback) => {
            console.log(value);
            // eslint-disable-next-line no-useless-escape
            if (this.submitData.wordReactText && !(/(http|https):\/\/([\w.]+(?:\.[\w\.-]+))+[\w\-\._~:/?#[\]@!\$&'\*\+,;=.]+$/.test(value))) {
              callback("请输入带有协议前缀的正确网站域名")
            } else {
              callback()
            }
          }, trigger: 'blur' }
        ],
        linkOutUrl:[
          { validator: (rule, value, callback) => {
            // eslint-disable-next-line no-useless-escape
            if (this.submitData.linkOutUrl&&!(/(http|https):\/\/([\w.]+(?:\.[\w\.-]+))+[\w\-\._~:/?#[\]@!\$&'\*\+,;=.]+$/.test(value))) {
              callback("请输入带有协议前缀的正确网站域名")
            } else {
              callback()
            }
          }, trigger: 'blur' }
        ]
      },
      headerCellStyle: {
        "background-color": "#EBEEF5"
      },
      headerRowStyle: {
        "background-color": "#EBEEF5"
      },
      addData: {
        hospitalId: '',
        hospitalName: '',
        departmentList: []
      },
      hospital: '',
      department: [],
		}
  },
  watch: {
    'submitData.examId'() {
      this.submitData.examId = this.submitData.examId ? this.submitData.examId : '';
    },
    'submitData.surveyId'() {
      this.submitData.surveyId = this.submitData.surveyId ? this.submitData.surveyId : '';
    }
  },
  components: {
    MsTagSearch,
    MsHospitalSearch,
    msCategoryCascader
  },
  props:["submitData"],
  created() {
    this.getGatherList()
    this.getFormMaterialList()
  },
	methods: {
    bindHospital(val) {
      this.addData.hospitalId = val.model.id
      this.addData.hospitalName = val.model.name
    },
    bindDepartment(val) {
      console.log(val.model)
      this.addData.departmentList = val.model
    },
    addMessList() {
      if(this.submitData.orientationParamRequest.length > 99) {
        this.PUBLIC_Methods.apiNotify("最多添加100个医院", "warning")
        return
      }
      if(this.addData.departmentList.length < 1) {
        this.addData.departmentList.push({
          categoryId: -1,
          categoryName: '全部'
        })
      }
      this.submitData.orientationParamRequest.push(this.addData)
      this.addData = {
        hospitalId: '',
        hospitalName: '',
        departmentList: []
      }
      this.hospital = ""
      this.department = []
    },
    removeMessList(index) {
      this.submitData.orientationParamRequest.splice(index, 1)
    },
    getGatherList() {
      let data = {
        pageindex: 1,
        pageSize: 999999,
        auditStatus: 1
      }
      this.api.getQuestionGatherPage(data).then(response => {
        this.gatherList = response.data || []
        if (response.status !== 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
      }).catch(() => {})
    },
    getFormMaterialList() {
      let data = {
        pageindex: 1,
        pageSize: 999999,
        auditStatus: 1
      }
      this.api.getFormMaterialList(data).then(response => {
        this.materialList = response.data || []
        this.materialList0= this.materialList.filter(item=>item.isOpenResult===0)
        this.materialList1= this.materialList.filter(item=>item.isOpenResult===1)
        if (response.status !== 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
      }).catch(() => {})
    },
    goGatherPage() {
      let routeData = this.$router.resolve({
        path: "/questionGather-operation",
        query: {
          operation: 'edit',
          component: 'video-manage-series',
          id: this.submitData.examId
        }
      });
      window.open(routeData.href, '_blank');
    },
    
    goMaterialPage() {
      let url = '';
      this.materialList.some(v => {
        if (v.id === this.submitData.surveyId) {
          url = v.url
          return 
        }
      })
      window.open(url, "_blank")
    },
    // 选择试题和选择调研互斥，则选择某一个时，另一个初始化
    // 选择试题变化
    changeGather(val) {
      if(val) {
        this.submitData.surveyName = '';
        this.submitData.surveyId = '';
        this.submitData.examIntegralUme = 0;
        this.submitData.needLogin = 1
      } else if(!this.submitData.surveyId){
        this.submitData.needLogin = 0
      }
      this.gatherList.some(v => {
        if (v.id === val) {
          this.submitData.examTitle = v.title
          return 
        }
      })
    },
    // 选择调研变化
    changeMaterial(e,val) {
      if(e==='0'){
        this.isOpenResultOpen = 1
      }else if(e==='1'){
        this.isOpenResultOpen = 2
      }
      if(val) {
        this.submitData.examTitle = '';
        this.submitData.examId = '';
        this.submitData.examIntegralUme = 0;
        this.submitData.needLogin = 1
      } else if(!this.submitData.examId) {
        this.submitData.needLogin = 0
      }
      this.materialList.some(v => {
        if (v.id === val) {
          this.submitData.surveyName = v.templateName
          return 
        }
      })
    },
    // 选择权限变化
    changeNeedLoginMethod(val) {
      if(val == 0) {
        this.submitData.targetedGroupSendStatus = 0
        this.submitData.examId = ''
        this.submitData.surveyId = ''
      }
    },
    // 数据校验
    validateData() {
      return new Promise((resolve) => {
        this.$refs["submitRef"].validate( valid => {
          if (valid) {
            resolve()
          }else {
            this.$emit('changeTab','setting')
          }
        })
      })
    }
	}
}
</script>
<style scoped>
.ms-radio-con {
  margin-right: 5px !important;
}
.flex-item /deep/ .el-form-item__content {
  display: flex;
  flex-direction: row;
}
.light-hint{
  padding-left: 10px;
  opacity: 0.6;
}
</style>
<style scoped lang="scss">
.info-form{
  /deep/ .el-checkbox {
    margin-right: 0px;
  }
  &-right {
    /deep/ .el-form-item {
      margin-bottom: 16px;
    }
  }
  .expand {
    .label {
      color: #409EFF; 
      font-size: 12px;
      width: 40px;
      display: inline-block;
      text-align: right;
    }
    /deep/ .el-form-item {
      padding: 4px 0;
      margin-bottom: 14px;
      margin-top: -4px;
      background-color: #E5E6E9;
      border-radius: 4px;
    }
  }
}
</style>
