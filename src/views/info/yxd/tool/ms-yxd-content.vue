<template>
  <section>
    <el-form :model="submitData"
      ref="submitRef"
      class="rule-form info-form"
      :rules="rules"
      label-width="70px">
        <el-row :gutter="10"> 
          <el-col :span="18">
            <el-row>
              <el-col :span="24">
                <el-form-item label="标题" prop="title">
                  <el-input v-model="submitData.title" style="width: 100%" maxlength="50" show-word-limit></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="正文" prop="content">
                  <ms-editor v-model="submitData.content"></ms-editor>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="编辑者" prop="editorId">
                  <ms-editor-search :model.sync="submitData.editorId" :modelName.sync="submitData.editor"></ms-editor-search>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="上线时间" prop="publishedTime">
                  <ms-picker :model.sync="submitData.publishedTime" type="datetime" :config="pickerConfig"></ms-picker>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="封面图片" prop="cover">
                  <ms-single-image v-model="submitData.cover" :upFileSize="0.5"></ms-single-image>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                  <el-form-item label="摘要" prop="summary">
                  <el-input v-model="submitData.summary" type="textarea" :rows="5" @keyup.native="summaryKeyUp = true" maxlength="120" show-word-limit></el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </el-col>
        </el-row>
      </el-form>
  </section>
</template>

<script>
import MsEditor from '@/components/MsEditor'
import msEditorSearch from "@/components/MsCommon/ms-editor-search"
import { getEditContent } from '@/utils/index'
export default {
	name: "ms-article-content",
	data () {
		return {
      rules: {
        title: [
          { required: true, message: "请填写标题", trigger: 'blur' }
        ],
        content: [
          { required: true, message: "请填写正文", trigger: 'blur' },
          { validator: (rule, value, callback) => {
            // eslint-disable-next-line no-useless-escape
            if (this.getStrCount(value,'</video>') > 1) {
              callback("仅限插入一个视频")
            } else {
              callback()
            }
          }, trigger: 'blur' }
        ],
        editorId: [
          { required: true, message: "请选择编辑者", trigger: 'change' }
        ],
        cover: [
          { required: true, message: "请上传封面图片", trigger: 'change' }
        ]
      },
      pickerConfig: {
        pickerOptions: {
          disabledDate: (time) => {
            return time.getTime() < new Date().getTime() - 8.64e7;
          }
        }
      },
      summaryKeyUp: false
		}
  },
  components: {
    MsEditor,
    msEditorSearch
  },
  props:["submitData"],
  watch: {
    'submitData.content': function (val) {
      if (val) {
        if (!this.submitData.id && !this.summaryKeyUp) {
          this.submitData.summary = this.PUBLIC_Methods.unexcapeHtml(getEditContent(val))
        }
      }
    },
  },
	methods: {
    getStrCount(scrstr,armstr) { //scrstr 源字符串 armstr 特殊字符
        var count=0;
        while(scrstr.indexOf(armstr) != -1 ) {
            scrstr = scrstr.replace(armstr,"")
            count++;    
        }
        return count;
    },
    // 数据校验
    validateData() {
      return new Promise((resolve) => {
        this.$refs["submitRef"].validate( valid => {
          if (valid) {
            resolve()
          }else {
            this.$emit('changeTab','content')
          }
        })
      })
    }
	}
}
</script>