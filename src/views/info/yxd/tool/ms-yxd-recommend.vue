<template>
  <ms-operation-dialog>
    <template slot="content">
      <el-form ref="submitRef"
               class="rule-form"
               style="padding: 20px 0 15px;"
               :model="submitData"
               :rules="rules"
               label-width="80px">
        <el-row>
          <el-col :span="22">
            <el-form-item prop="endTime"
                      label="结束时间">
              <el-date-picker v-model="submitData.endTime"
                              type="date"
                              placeholder="请选择时间点"
                              style="width: 100%;"
                              value-format="yyyy-MM-dd HH:mm:ss"
                              :picker-options="pickerOptions">
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </template>
    <template slot="footer">
      <el-button @click="submitForm"
                 :loading="loading"
                 size="mini"
                 type="primary">确 定</el-button>
      <el-button @click="close"
                 :loading="loading"
                 size="mini">取消{{ dealType === 0 ? '推荐' : '固顶' }}</el-button>
    </template>
  </ms-operation-dialog>
</template>

<script>
import {ms_rule_date} from "@/utils/form-rule.js";
import {parseTime} from "@/utils"
export default {
	name: "ms-article-recommend",
	data () {
		return {
      loading: false,
      userInfo: {},
      dealType: null,
      submitData: {
        id: '',
        endTime: '',
      },
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now();
        },
        shortcuts: [{
          text: '3天后',
          onClick(picker) {
            const date = new Date();
            date.setTime(date.getTime() + 3 * 3600 * 1000 * 24);
            picker.$emit('pick', date);
          }
        }, {
          text: '5天后',
          onClick(picker) {
            const date = new Date();
            date.setTime(date.getTime() + 5 * 3600 * 1000 * 24);
            picker.$emit('pick', date);
          }
        }, {
          text: '一周后',
          onClick(picker) {
            const date = new Date();
            date.setTime(date.getTime() + 7 * 3600 * 1000 * 24);
            picker.$emit('pick', date);
          }
        }, {
          text: '一个月后',
          onClick(picker) {
            const date = new Date();
            date.setTime(date.getTime() + 30 * 3600 * 1000 * 24);
            picker.$emit('pick', date);
          }
        }]
      },
      rules: {
        endTime: [
          { required: true, message: "请选择结束时间", trigger: 'blur' },
          { validator: ms_rule_date, trigger: 'blur' }
        ],
        categoryId: [
          { validator: (rule, value, callback) => {
            if ((this.submitData.siteType == 2 || this.submitData.siteType == 3) && !value) {
              callback(new Error('请选择分类'));
            } else {
              callback()
            }
          }, trigger: 'change' }
        ]
      }
		}
	},
	props: {
		model: Object,
    operation: String,
  },
  created() {
    this.userInfo = this.$store.getters.info || {}
    let operationLocal = this.operation || this.$route.query.operation
    let temp = parseTime(new Date().getTime() + (7 * 24 * 60 * 60 * 1000))
    if (operationLocal === 'recommend') {
      this.dealType = 0
      this.submitData.endTime = this.model.recommendEndTime || temp
    } else if (operationLocal === 'sticky') {
      this.dealType = 1
      this.submitData.endTime = this.model.stickyEndTime || temp
    } 
    this.submitData.id = this.model.id || 0
  },
	methods: {
    submitForm () {
      let params = {
        userId: this.userInfo.userId,
        username: this.userInfo.userName,
        type: this.dealType,
        ...this.submitData
      }
      this.$refs["submitRef"].validate(valid => {
        if(valid) {
          if ( (this.dealType === 0 && this.model.sticky === 1) || (this.dealType === 1 && this.model.recommend === 1) ) {
            this.$confirm(`本篇协议已设置${this.dealType === 0 ? '固顶' : '推荐'}, 点击确认将会取消${this.dealType === 0 ? '固顶' : '推荐'}并设置${this.dealType === 0 ? '推荐' : '固顶'}`, {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            }).then(() => {
              this.operaAjax(params)
            }).catch(() => {})
          } else {
            this.operaAjax(params)
          }
        }
      })
    },
    close() {
      let params = {
        userId: this.userInfo.userId,
        username: this.userInfo.userName,
        type: this.dealType === 0 ? 2 : 3,
        ...this.submitData
      }
      this.operaAjax(params)
    },
    // 推荐、固顶\取消推荐、取消固顶
    operaAjax(params) {
      this.loading = true
      this.api.recommendOrSticky(params).then( response => {
        if(response.status === 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
        this.$emit('up-date')
        this.loading = false
      }).catch( () => {
        this.loading = false;
        this.$emit('close')
      })
    }
	}
}
</script>
