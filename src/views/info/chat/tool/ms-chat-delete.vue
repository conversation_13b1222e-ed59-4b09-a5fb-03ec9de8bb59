<template>
    <ms-operation-dialog title="是否要删除以下私信消息">
        <template slot="content">
        <el-tag v-for="(id, index) in strArr" 
                :key="index" 
                type="info" 
                style="margin: 0 5px 5px 0">{{id}}</el-tag>
        </template>
        <template slot="footer">
        <el-button @click="submitForm"
                    :loading="loading"
                    size="mini"
                    type="primary">确 定</el-button>
        <el-button @click="$emit('close')"
                    size="mini">取 消</el-button>
        </template>
    </ms-operation-dialog>
</template>

<script>
export default {
	name: "ms-system-operation",
	data () {
		return {
      loading: false,
      userInfo: {},
      dealType: null,
      ids: [],
      strArr: []
		}
	},
	props: [
		"model",
		"operation"
  ],
  created() {
    this.userInfo = this.$store.getters.info || {}
    let arr = []
    let showArr = []
    if (this.PUBLIC_Methods.isArrayFn(this.model)) {
      this.model.forEach(item => {
        arr.push(item.id)
        showArr.push(item.id)

        // arr.push(item.msgId)
        // showArr.push(item.msgBody?item.msgBody:item.projectName)
      });
    } else {
      arr.push(this.model.id)
      showArr.push(this.model.id)

      // arr.push(this.model.msgId)
      // showArr.push(this.model.msgBody?this.model.msgBody:this.model.projectName)
    }
    this.ids = arr
    this.strArr = showArr
  },
	methods: {
		submitForm () {
            this.loading = true;
            let params = {
                ids: this.ids

            // let params,apiName
            // if (this.PUBLIC_Methods.isArrayFn(this.model)) {
            //   params = {
            //     msgIds: this.ids
            //   }
            //   apiName = 'medsciChatRemoveMsgBatch'
            // }else {
            //   params = {
            //     msgId: this.ids[0]
            //   }
            //   apiName = 'medsciChatRemoveMsg'
            }
            this.api.deleteChat(params).then(response => {
            // this.api[apiName](params).then(response => {
                if(response.status === 200) {
                this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
                } else {
                this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
                }
                this.$emit('up-date')
                this.loading = false
            }).catch( () => {
                this.loading = false;
                this.$emit('close')
            })
		}
	}
}
</script>
