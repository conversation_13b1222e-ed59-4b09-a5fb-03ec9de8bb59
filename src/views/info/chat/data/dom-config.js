const domConfig = {
    // 搜索配置
    listSearch: [
      {
        label: '消息内容',
        placeholder: '请输入',
        model: 'content',
        // label: '用户名',
        // placeholder: '请输入用户名',
        // model: 'userName',
        component: 'ms-input'
      },
      // {
      //   label: '项目名称',
      //   placeholder: '请选择',
      //   model: 'projectName',
      //   component: 'ms-chatProject-search'
      // },
      // {
      //   label: '业务线',
      //   placeholder: '请选择',
      //   model: 'serviceLineName',
      //   component: 'ms-chatServiceLine-search'
      // },
    ],
  
    // 表头配置
    tableHeader: [
      { label: '消息内容', property: 'content', width: '250' },
      { label: '消息发送方', property: 'sendUserName' },
      { label: '消息接收方', property: 'receiverUserName' },
      { label: '举报', property: 'isReport' },
      { label: '创建时间', property: 'createTime' }

      // { label: '消息发送方', property: 'originalFromAccountName' },
      // { label: '消息接收方', property: 'originalToAccountName' },
      // { label: '项目名称', property: 'projectName' },
      // { label: '业务线', property: 'serviceLineName' },
      // { label: '最新消息', property: 'lastMsg',width:'200' },
      // // { label: '消息类型', property: 'chatType' },
      // { label: '最近一次发送时间', property: 'lastMsgTime', width: '130' }
    ],
  
    // 行内列表按钮配置
    tableButtons: [
      {
        label: '删除',
        way: 'dialog',
        type: 'danger',
        operation: 'delete',
        component: 'msChatDelete'
      }
    ],
  
    // 新建项目按钮
    soltButtons: [
      { 
        label: '批量删除', 
        type: 'info', 
        icon: 'el-icon-close',
        operation: 'delete',
        component: 'msChatDelete',
        way: 'batch'
      }
    ]
  }
  export default domConfig