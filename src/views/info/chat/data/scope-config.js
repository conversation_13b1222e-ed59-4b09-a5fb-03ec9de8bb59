const scopeConfig = {
    show: {
      sendUserName: () => {

      // originalFromAccountName: (row) => {
      //     if(!row.originalFromAccountName) {
      //       row.originalFromAccountName = '--'
      //       return false
      //     }
      //     return {
      //         type: 'query',
      //         config: {
      //             way: 'query',
      //             params: [{
      //               keyName: 'userName',
      //               valName: 'originalFromAccountName'
      //             }],
      //             jumpObj: {
      //                 way: 'page',
      //                 path: 'user-detail',
      //                 params: [{
      //                     keyName: 'id',
      //                     valName: 'originalFromAccount'
      //                 }]
      //             }
      //         }
      //     }
      //   },
      //   originalToAccountName: (row) => {
      //       if(!row.originalToAccountName) {
      //         row.originalToAccountName = '--'
      //         return false
      //       }
            return {
                type: 'query',
                config: {
                    way: 'query',
                    params: 'sendUid',

                    // params: [{
                    //   keyName: 'userName',
                    //   valName: 'originalToAccountName'
                    // }],
                    jumpObj: {
                        way: 'page',
                        path: 'user-detail',
                        params: [{
                            keyName: 'id',
                            valName: 'sendUid'
                            // valName: 'originalToAccount'
                        }]
                    }
                }
            }
        },
        receiverUserName: () => {
          return {
              type: 'query',
              config: {
                  way: 'query',
                  params: 'receiverUid',
                  jumpObj: {
                      way: 'page',
                      path: 'user-detail',
                      params: [{
                          keyName: 'id',
                          valName: 'receiverUid'
                      }]
                  }
              }

        // lastMsg: () => {
        //   return {
        //     type: 'webLink',
        //     config: {
        //       role: '',
        //       operation: 'edit',
        //       way: 'page',
        //       path: 'chat-operation',
        //       params: ['appId','chatType','originalFromAccount','originalFromAccountName','originalToAccount','originalToAccountName','groupId'],
        //     }
          }
        },
        isReport: () => {

        // chatType: () => {
          return {
            type: 'status',

            // type: 'code',
            rule: {
              0: {label: '否'},
              1: {label: '是'}

              // 'C2C': { label: '单聊消息' },
              // 'GROUP': { label: '群聊消息' },
            }
          }
        },
    },
    headerShow: {
      isReport: () => {
        return {
          type: 'dropdown',
          icon: 'icon-funnel',
          options: [
            { label: '否', value: 0 },
            { label: '是', value: 1 }
          ],
          operation: 'query'
        }
      }

    }
  }
  
  export default scopeConfig;
  