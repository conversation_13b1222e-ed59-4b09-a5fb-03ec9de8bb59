<template>
  <div class="comment_manage">
    <ms-table
      :currentPage="searchParams.pageIndex"
      :loading="loading"
      :operationButtons="detailsConfig.tableButtons"
      :pageSize="searchParams.pageSize"
      :scopeConfig="detailsScopeConfig.show"
      :tableData="list"
      :tableHeader="detailsConfig.tableHeader"
      :total="total"
      @current-change="current_change"
      @operation-change="operation_change"
      @size-change="size_change"
      @header-operation="header_operation"
      :showSelection="true"
      @handleSelectionChange="handleSelectionChange"
    >
      <template slot="ms-table-header">
        <div class="slot-button" style="float:left">
          <template v-for="(item, index) in detailsConfig.soltButtons">
            <el-button
              :key="index"
              :type="item.type"
              :icon="item.icon"
              @click="operation_change({operation: item})"
              plain
            >{{ item.label }}</el-button>
          </template>
        </div>
        <el-dialog
          :visible.sync="dialog"
          closeable
          show-close
          :close-on-click-modal="false"
          :width="dialogWidth"
          :title="dialogTitle"
        >
          <component
            :is="dialogComponent"
            :model="scopeInfo"
            :operation="dialogOperation"
            @close="dialog = !dialog"
            @up-date="init"
            v-if="dialog"
          ></component>
        </el-dialog>
      </template>
    </ms-table>
  </div>
</template>

<script>
import tableMixins from "../../common/mixins/table";
import dataMixin from "./data-mixin"
export default {
    name: "ms-chat-message",
    mixins: [dataMixin,tableMixins],
    data() {
        return {
            loading: false,
            searchParams: {},
        };
    },
    created() {
      this.handleClick('query')
      this.searchParams = this.$route.query
    },
    methods: {
      apiInit() {
        let searchParams = { ...this.searchParams };
        let apiName
        if(this.searchParams.chatType === 'GROUP') {
          apiName = 'medsciChatGetMsg'
        }else {
          apiName = 'medsciChatGetHistoryMessages'
        }
        this.api[apiName](searchParams).then(response => {
            this.loading = false;
            this.total = response.totalSize || 0;
            this.list = []
            if (response.status === 200) {
                this.list = response.data || [];
            } else {
                // this.PUBLIC_Methods.apiNotify(response.message || "请求出错","warning");
            }
        })
        .catch(() => (this.loading = false));
      },
    }
};
</script>
