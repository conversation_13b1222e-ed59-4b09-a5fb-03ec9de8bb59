<template>
	<ms-table
		:currentPage="searchParams.pageIndex"
		:loading="loading"
		:pageSize="searchParams.pageSize"
		:scopeConfig="scopeConfig.show"
    :scopeHeader="scopeConfig.headerShow"
    :rowStyle="{height: '43px'}"
		:tableData="list"
		:tableHeader="domConfig.recordTableHeader"
		:total="total"
		@current-change="current_change"
		@operation-change="operation_change"
		@size-change="size_change"
    @header-operation="header_operation"
    @handleSelectionChange="handleSelectionChange"
    class="table-svg"
	>
    <!-- 列表搜索去区域插槽 -->
		<template slot="ms-table-header">
			<div class="slot-search">
				<template v-for="(searchItem, key) in domConfig.recordSearch">
					<component
						:index="searchItem.index || ''"
						:is="searchItem.component"
						:key="key"
            :width="searchItem.width || '150px'"
            :model.sync="searchParams[searchItem.model]"
            :label="searchItem.label"
						:operation="searchItem.operation || ''"
						:options="searchItem.options || []"
						:placeholder="searchItem.placeholder || ''"
						:type="searchItem.type || ''"
            :multiple="searchItem.multiple"
            :disabled="searchItem.disabled"
            :code="searchItem.code"
					></component>
				</template>
        <div class="inlineBlock">
          <el-button @click="handleClick('query')" type="primary" plain icon="el-icon-search">查询</el-button>
          <el-button @click="handleClick('reset')">重置</el-button>
        </div>
			</div>
		</template>
	</ms-table>
</template>

<script>
import msMixin from "./ms-mixin"
import tableMixins  from "../../common/mixins/table"
export default {
  name: "contentTagRecord",
  mixins: [tableMixins,msMixin],
  data () {
    return {
    }
  },
  methods: {
    apiInit (params) {
      let searchParams = {...params}
      if (searchParams.createTime) {
        searchParams.startTime = searchParams.createTime[0] || ''
        searchParams.endTime = searchParams.createTime[1] || ''
      }
      this.api.labelLogList(searchParams).then(response => {
        this.loading = false
        this.total = response.totalSize || 0;
        this.list = response.data || []
        if (response.status !== 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
      }).catch(() => this.loading = false)
    },
  }
};
</script>
