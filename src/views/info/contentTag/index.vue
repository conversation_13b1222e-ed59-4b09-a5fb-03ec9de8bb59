<template>
	<ms-table
    :key="symbolKey"
		:currentPage="searchParams.pageIndex"
		:loading="loading"
		:operationButtons="domConfig.tableButtons"
		:pageSize="searchParams.pageSize"
		:scopeConfig="scopeConfig.show"
    :scopeHeader="scopeConfig.headerShow"
		:tableData="list"
    rowKey='ids'
		:tableHeader="domConfig.tableHeader"
		:total="total"
    :showSelection="true"
    @loadChild="loadChild"
    :lazy="true"
    :treeProps="{children: 'children', hasChildren: 'hasChildren'}"
		@current-change="current_change"
		@operation-change="operation_change"
		@size-change="size_change"
    @header-operation="header_operation"
    @handleSelectionChange="handleSelectionChange"
    class="table-svg"
	>
    <!-- 列表搜索去区域插槽 -->
		<template slot="ms-table-header">
			<div class="slot-search">
				<template v-for="(searchItem, key) in domConfig.listSearch">
					<component
						:index="searchItem.index || ''"
						:is="searchItem.component"
						:key="key"
            :width="searchItem.width || '150px'"
            :model.sync="searchParams[searchItem.model]"
            :label="searchItem.label"
						:operation="searchItem.operation || ''"
						:options="searchItem.options || []"
						:placeholder="searchItem.placeholder || ''"
						:type="searchItem.type || ''"
            :multiple="searchItem.multiple"
            :disabled="searchItem.disabled"
            :code="searchItem.code"
            :config="searchItem.config"
            clearable="clearable"
					></component>
				</template>
        <div class="inlineBlock">
          <el-button @click="handleClick('query')" type="primary" plain icon="el-icon-search">查询</el-button>
          <el-button @click="handleClick('reset')">重置</el-button>
        </div>
        
			</div>
			<div class="slot-button-article clearfix">
				<template v-for="(item, index) in domConfig.soltButtons">
					<el-button :key="index" 
                     :type="item.type" 
                     size="mini"
                     :icon="item.icon"
                     @click="operation_change({operation: item})"
                     :disabled="item.roleDisabled"
                     plain>{{ item.label }}</el-button>
				</template>
        <el-alert class="article-total" :title="`共搜索到${total}个表单`" type="info" show-icon></el-alert>
			</div>
			<el-dialog :visible.sync="dialog" 
                 closeable 
                 show-close
                 :close-on-click-modal="false"
                 :width="dialogWidth"
                 :title="dialogTitle">
				<component :is="dialogComponent" 
                   :model="scopeInfo" 
                   :operation="dialogOperation" 
                   @close="dialog = !dialog" 
                   @up-date="init" 
                   v-if="dialog"></component>
			</el-dialog>
      <ms-right-dialog
        :visible.sync="r_dialog"
        :width="dialogWidth"
        :title="dialogTitle"
      >
        <component
          :is="dialogComponent"
          :model="scopeInfo"
          :operation="dialogOperation"
          @close="r_dialog = !r_dialog"
          @up-date="init"
          v-if="r_dialog"
        ></component>
      </ms-right-dialog>
		</template>
	</ms-table>
</template>

<script>
import msMixin from "./ms-mixin"
import tableMixins  from "../../common/mixins/table"
export default {
  name: "contentTag",
  mixins: [tableMixins,msMixin],
  data () {
    return {
      symbolKey: '',
      searchParams: {},
      departmentList: [],
    }
  },
  methods: {
    apiInit (params) {
      console.log(params, 'params');
      let level = {}
      if(!params.level) {
        level = {
          level: 1
        }
      }
      this.searchParams = {...params, ...level};
      this.getData();
    },
    getData(tree, resolve) {
      // console.log(tree, 'rrrrrr')
      if(tree) {
        // this.searchParams.type = Number(tree.level);
        this.searchParams.id = tree.id;
      } else {
        // this.searchParams.type = 0;
        this.searchParams.id = 0;
      }
      if(this.searchParams.createTimeArr && this.searchParams.createTimeArr.length) {
        this.searchParams.startTime = this.searchParams.createTimeArr[0] || '';
        this.searchParams.endTime = this.searchParams.createTimeArr[1] || '';
      } else {
        this.searchParams.startTime = '';
        this.searchParams.endTime = '';
      }
      if(this.searchParams.categoryId) {
        this.searchParams.categoryId = [this.searchParams.categoryId].flat(Infinity)
      }
      if(!tree) {
        this.api.tagLabelList(this.searchParams).then(response => {
          this.loading = false
          if(!tree) {
            this.total = response.totalSize || 0;
            this.list = response.data || [];
            for (let index = 0; index < this.list.length; index++) {
              const element = this.list[index];
              this.list[index].ids = element.id;
              const categoryInfo = JSON.parse(this.list[index].categoryInfo); 
              this.list[index].categoryName = categoryInfo[0].categoryName;
              element.hasChildren = true;
            }
            this.symbolKey = Symbol(new Date().toString())
          }
          if (response.status !== 200) {
            this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
          }
        }).catch(() => this.loading = false)
      } else {
        this.api.tagLabelGetChild(this.searchParams).then(response => {
          this.loading = false
          if(tree.level == 1) {
            let array = response.data || [];
            for (let i = 0; i < array.length; i++) {
              const element = array[i];
              array[i].ids = tree.id + '_' + element.id;
              const categoryInfo = JSON.parse(element.categoryInfo); 
              array[i].categoryName = categoryInfo[0].categoryName;
              element.hasChildren = true;
            }
            resolve(array)
          }
          if(tree.level == 2) {
            let array = response.data || [];
            for (let i = 0; i < array.length; i++) {
              const element = array[i];
              array[i].ids = tree.ids + '_' + element.id;
              const categoryInfo = JSON.parse(element.categoryInfo); 
              array[i].categoryName = categoryInfo[0].categoryName;
              element.hasChildren = false;
            }
            resolve(array)
          }
          if (response.status !== 200) {
            this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
          }
        }).catch(() => this.loading = false)
      }
    },
    loadChild(obj) {
      if(obj.tree.level < 3) {
        this.getData(obj.tree, obj.resolve);
      }
    },
    operation_change_module(val) {
      switch (val.operation.way) {
        case "recommend":
          var recommendParams = {
            id: val.model.id,
            type: val.model.isRecommend == 0 ? 0 : 2, // 0\2
            userId: this.$store.getters.info.userId,
            username: this.$store.getters.info.userName
          }
          this.api.formMaterialRecommendOrSticky(recommendParams).then(response => {
              if(response.status === 200) {
                this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
              } else {
                this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
              }
              this.init()
          })
          break;
        case "sticky":
          var stickyParams = {
            id: val.model.id,
            type: val.model.isSticky == 0 ? 1 : 3, // 1\3
            userId: this.$store.getters.info.userId,
            username: this.$store.getters.info.userName
          }
          this.api.formMaterialRecommendOrSticky(stickyParams).then(response => {
              if(response.status === 200) {
                this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
              } else {
                this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
              }
              this.init()
          })
          break;
        case "export": 
          this.api.getFormExport(val.model.id).then(res => {
            if (res.status === 200) {
              window.location.href = res.data
            } else {
              this.PUBLIC_Methods.apiNotify(res.message || '请求出错', 'warning')
            }
          })
          break;
        default: break;
      }
    }
  }
};
</script>
