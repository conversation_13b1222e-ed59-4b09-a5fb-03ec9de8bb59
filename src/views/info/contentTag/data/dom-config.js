// import store from '@/store'

const domConfig = {
  listSearch: [
    {
      label: '标签名称',
      placeholder: '请输入',
      model: 'name',
      component: 'ms-input'
    },
    {
      label: '所属分类',
      placeholder: '请选择',
      model: 'categoryId',
      component: 'ms-category-cascader',
      config: {
        moduleName: 'label'
      }
    },
    {
      label: '标签等级',
      placeholder: '请选择',
      model: 'level',
      component: 'ms-select-local',
      options: [
        {
          label: '亚专业',
          value: 1,
        },
        {
          label: '疾病',
          value: 2,
        },
        {
          label: '用药',
          value: 3,
        },
      ]
    },
    {
      label: '标签创建人',
      placeholder: '请选择',
      model: 'createdBy',
      component: 'ms-createby-search'
    },
    {
      label: '创建时间',
      placeholder: '请选择时间段',
      model: 'createTimeArr',
      component: 'ms-picker',
      type: 'daterange',
      width: '240px'
    }
  ],
  tableHeader: [
    { label: 'ID', property: 'id', width: '120' },
    { label: '标签名称', property: 'tagName', width: '200'},
    { label: '所属科室', property: 'categoryName', width: '200'},
    { label: '创建人', property: 'createdName' },
    { label: '创建时间', property: 'createdTime', sortable: true,  width: '130' },
  ],
  tableButtons: [
    {
      label: '编辑',
      icon: '',
      role: '',
      operation: 'edit',
      component: 'ms-add',
      title: '编辑标签',
      way: 'dialog',
      type: 'primary',
      params: ['id'],
      position: 'right',
      width: 'calc(100% - 200px)'
    },
    {
      label: '',
      icon: '',
      role: '',
      operation: 'editStatus',
      component: 'msOperation',
      way: 'dialog',
      field: 'status',
      rule: {
        1: { label: '禁用', type: '', operation: 'toreview' },
        0: { label: '审核', type: 'success', operation: 'approval' }
      }
    },
    // {
    //   label: '删除',
    //   way: 'dialog',
    //   type: 'danger',
    //   operation: 'delete',
    //   component: 'msOperation'
    // },
  ],
  soltButtons: [
    { 
      label: '添加标签',
      icon: '',
      role: '',
      operation: 'add',
      component: 'ms-add',
      title: '添加标签',
      way: 'dialog',
      type: 'primary',
      params: ['id'],
      position: 'right',
      width: 'calc(100% - 200px)'
    },
    {
      label: '操作日志',
      type: 'primary',
      way: 'page',
      path: 'contentTagRecord',
    },
    // { 
    //   label: '批量审核', 
    //   type: 'primary',
    //   operation: 'approval',
    //   component: 'msFormOperation',
    //   way: 'batch',
    // },
    // { 
    //   label: '批量删除',
    //   type: 'info',
    //   icon: 'el-icon-close',
    //   operation: 'delete',
    //   component: 'msFormOperation',
    //   way: 'batch',
    // }
  ],
  recordSearch: [
    {
      label: '修改时间',
      placeholder: '请选择时间段',
      model: 'createTime',
      component: 'ms-picker',
      type: 'daterange',
      width: '240px'
    },
    {
      label: '修改人',
      placeholder: '请选择',
      model: 'createdBy',
      component: 'ms-createby-search'
    },
  ],
  recordTableHeader: [
    { label: 'ID', property: 'id', width: '60' },
    { label: '修改前标签名称', property: 'name'},
    { label: '修改后标签名称', property: 'afterName'},
    { label: '修改人', property: 'createdName',  width: '130' },
    { label: '修改时间', property: 'createdTime', sortable: true,  width: '130' },
  ],
}

export default domConfig;
