<template>
    <div class="add-tag">
        <el-form :model="tagContent" ref="tagContent" label-width="120px">
            <el-form-item
                label="标签等级"
                prop="level"
                :rules="[
                    { required: true, message: '标签等级不能为空'},
                ]"
            >
                <el-select v-model="tagContent.level" placeholder="请选择">
                    <el-option
                    v-for="item in levelList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item
                label="关联二级标签"
                prop="categoryId"
                :rules="[
                    { required: true, message: '关联二级标签不能为空', trigger: 'change'},
                ]"
            >
                <el-cascader
                    v-model="tagContent.categoryId"
                    ref="twoTag"
                    :options="departmentList" :show-all-levels="false" checkStrictly :props="props" @change="categoryIdChange"></el-cascader>
            </el-form-item>
            <el-form-item
                v-if="tagContent.level === 2 || tagContent.level === 3"
                class="select-tag"
                label="关联三级标签"
                prop="threeTag"
                :rules="[
                    { type: 'array', required: true, message: '关联三级标签不能为空', trigger: 'change'},
                ]"
            >
                <el-select
                    ref="threeTag"
                    style="width: 300px;"
                    v-model="tagContent.threeTag"
                    filterable
                    remote
                    reserve-keyword
                    multiple
                    :disabled="tagContent.categoryId.length<1"
                    placeholder="请输入关键词下拉选择标签"
                    :remote-method="tagLabelQuery"
                    @change="changeTagSearchThree"
                    :loading="loading">
                    <el-option
                        v-for="item in threeList"
                        :key="'three'+item.id"
                        :label="item.tagName"
                        :value="item.id">
                    </el-option>
                </el-select>
                <!-- <el-select
                    @visible-change="visibleChange"
                    v-model="tagContent.threeTag" placeholder="请选择" filterable :filter-method="labelSearch" multiple :popper-append-to-body="body">
                    <el-option
                        v-for="item in threeList"
                        :key="item.labelId"
                        :label="item.labelName"
                        :value="item.labelId"
                    >
                    </el-option>
                </el-select> -->
            </el-form-item>
            <el-form-item
                v-if="tagContent.level === 3"
                class="select-tag"
                label="关联四级标签"
                prop="fourTag"
                :rules="[
                    { type: 'array', required: true, message: '关联四级标签不能为空', trigger: 'change'},
                ]"
            >
                <el-select
                    ref="fourTag"
                    style="width: 300px;"
                    v-model="tagContent.fourTag"
                    filterable
                    remote
                    reserve-keyword
                    multiple
                    :disabled="tagContent.threeTag.length<1"
                    placeholder="请输入关键词下拉选择标签"
                    :remote-method="tagLabelQueryFour"
                    @change="changeTagSearchFour"
                    :loading="loading">
                    <el-option
                        v-for="item in fourList"
                        :key="item.id"
                        :label="item.tagName"
                        :value="item.id">
                    </el-option>
                </el-select>
                <!-- <el-select v-model="tagContent.fourTag" placeholder="请选择" filterable :filter-method="labelSearchTwo" multiple :popper-append-to-body="body">
                    <el-option
                        v-for="item in fourList"
                        :key="item.labelIdd"
                        :label="item.labelName"
                        :value="item.labelId"
                    >
                    </el-option>
                </el-select> -->
            </el-form-item>
            
            <el-form-item
                label="标签名称"
                prop="name"
                :rules="[
                    { required: true, message: '标签名称不能为空'},
                ]"
            >
                <!-- <el-input class="tag-name" v-model="tagContent.name" placeholder="请输入"></el-input> -->
                
                <el-select
                style="width: 300px;"
                v-model="tagContent.name"
                filterable
                remote
                reserve-keyword
                placeholder="请输入关键词下拉选择标签"
                :remote-method="getTag"
                @change="changeTag"
                :loading="loading">
                    <el-option
                        v-for="item in tagList"
                        :key="item.id"
                        :label="item.tagName"
                        :value="item.id">
                    </el-option>
                </el-select>
            </el-form-item>
            
            <!-- 提交按钮 -->
            <footer-tool-bar v-loading="buttonLoading" style="width: 100%;">
                <template slot="tool-content">
                    <el-button type="primary" @click="info_operation('save')">保存</el-button>
                    <el-button type="info" @click="info_operation('back')">返回</el-button>
                </template>
            </footer-tool-bar>
        </el-form>
    </div>
</template>

<script>
import FooterToolBar from '@/components/ToolBar/footer-tool-bar'
export default {
	name: "ms-add",
    components: {
        FooterToolBar,
    },
	data () {
		return {
            body: false,
            userInfo: {},
            levelList: [
                {
                    label: '三级',
                    value: 1,
                },
                {
                    label: '四级',
                    value: 2,
                },
                {
                    label: '五级',
                    value: 3,
                },
            ],
            props: {
                label: 'titleCn',
                value: 'categoryId',
            },
            tagContent: {
                level: '',
                categoryId: [],
                threeTag: [],
                fourTag: [],
                name: '',
            },
            categoryInfo: {},
            tagName: {},
            departmentList: [],
            threeList: [],
            fourList: [],
            buttonLoading: false,
            tagList: [],
            loading: false,
            categoryDtosObj: {},
            editStatus: false,
		}
	},
	props: {
		model: Object,
		operation: String
    },
    watch: {
        // 'tagContent.categoryId': (value) => {
        //     console.log(value, 'categoryId')
        // }
    },
    created() {
        this.userInfo = this.$store.getters.info || {}
        this.init()
        if(Object.keys(this.model).length) {
            this.editStatus = true;
        }
    },
	methods: {
        changeTagSearchFour(val) {
            console.log(val, '4444444')
            if(val.length > 1 && this.editStatus) {
                this.tagContent.fourTag.splice(-1);
                this.$message({
                    message: '编辑时只能关联一个标签',
                    type: 'warning'
                });
            }
        },
        tagLabelQueryFour(query) {
            let categoryId = this.tagContent.categoryId.length>1 ? this.tagContent.categoryId[1] : this.tagContent.categoryId[0];
            let params = {
                "categoryId": categoryId,
                "level": 2,
                "name": query,
                "parentIds": this.tagContent.threeTag,
                "projectId": 1
            }
            this.api.tagLabelSearch(params).then(async response => {
                if(response.status == 200) {
                    console.log(response, '11')
                    this.fourList = response.data;
                } else {
                    this.$message({
                        message: response.message,
                        type: 'warning'
                    });
                }
            }).catch(() => {})
        },
        changeTagSearchThree(val) {
            console.log(val, '3333333')
            if(val.length > 1 && this.editStatus) {
                this.tagContent.threeTag.splice(-1);
                this.$message({
                    message: '编辑时只能关联一个标签',
                    type: 'warning'
                });
            }
        },
        tagLabelQuery(query) {
            let categoryId = this.tagContent.categoryId.length>1 ? this.tagContent.categoryId[1] : this.tagContent.categoryId[0];
            let params = {
                "categoryId": categoryId,
                "level": 1,
                "name": query,
                "parentIds": [],
                "projectId": 1
            }
            this.api.tagLabelSearch(params).then(async response => {
                if(response.status == 200) {
                    console.log(response, '11')
                    this.threeList = response.data;
                } else {
                    this.$message({
                        message: response.message,
                        type: 'warning'
                    });
                }
            }).catch(() => {})
        },
        changeTag(val) {
            console.log(val, 'ee')
            this.tagContent.tagId = val.id;
            this.tagContent.tagName = val.tagName;
        },
        getTag(query) {
            if(query != '') {
                let params = {
                    "tagName": query,
                    "tenant": 100
                }
                this.api.tagSearch(params).then(async response => {
                    if(response.status == 200) {
                        console.log(response, '11')
                        this.tagList = response.data;
                    } else {
                        this.tagList = [];
                        this.tagContent.name = query;
                        this.$message({
                            message: response.message,
                            type: 'warning'
                        });
                    }
                }).catch(() => {})
            } else {
                this.tagList = [];
            }
        },
        async init() {
            await this.getDepart();
            if(Object.keys(this.model).length) {
                this.labelDetails();
            } else {
                // await this.labelSearch();
            }
            // this.getThreeTag();
            // this.labelOperate();
        },
        labelDetails() {
            let params = {
                "id": this.model.id,
            }
            this.api.tagLabelDetails(params).then(async response => {
                console.log(response, 'rrr')
                if(response.status == 200) {
                    let result = response.data;
                    this.tagContent.level = result.level;
                    let categoryDtosObj = result.categoryDtos[0];
                    this.tagContent.categoryId = categoryDtosObj.categoryId;
                    this.categoryDtosObj = categoryDtosObj;
                    // this.$refs.twoTag.cachedOptions.push({
                    //     titleCn: categoryDtosObj.categoryName,
                    //     categoryId: categoryDtosObj.categoryId,
                    // })
                    this.tagContent.name = result.name;
                    let twoEl = result.subspecialty || {};
                    // this.$refs.threeTag.cachedOptions.push({
                    //     tagName: twoEl.labelName,
                    //     id: twoEl.labelId,
                    // })
                    this.threeList.push({
                        tagName: twoEl.labelName,
                        id: twoEl.labelId,
                    })
                    this.tagContent.threeTag.push(twoEl.labelId);
                    let diseaseEl = result.disease || {};
                    this.tagContent.fourTag.push(diseaseEl.labelId);
                    // this.$refs.fourTag.cachedOptions.push({
                    //     tagName: diseaseEl.labelName,
                    //     id: diseaseEl.labelId,
                    // })
                    this.fourList.push({
                        tagName: diseaseEl.labelName,
                        id: diseaseEl.labelId,
                    })
                    // await this.labelSearch();
                    // await this.labelSearchTwo();
                } else {
                    this.$message({
                        message: response.message,
                        type: 'warning'
                    });
                }
            }).catch(() => {})
        },
        submit() {
            this.buttonLoading = true
            let parentIds = []
            if(this.tagContent.level == 2) {
                parentIds = this.tagContent.threeTag
            }
            if(this.tagContent.level == 3) {
                parentIds = this.tagContent.fourTag
            }
            if(Object.keys(this.model).length) {
                // 编辑
                // let params = {
                //     "categoryId": typeof this.tagContent.categoryId == 'number' ? this.tagContent.categoryId :  this.tagContent.categoryId[1],
                //     "level": this.tagContent.level,
                //     "name": this.tagContent.name,
                //     "parentIds": parentIds,
                //     "projectId": 1,
                //     "userId": this.userInfo.userId,
                //     "username": this.userInfo.userName,
                //     id: this.model.id,
                // }
                let categoryId = '';
                let categoryName = '';
                let tenant = '';
                if(this.tagContent.categoryId.length == 2) {
                    let categOne = this.departmentList.find(element => element.categoryId == this.tagContent.categoryId[0]).children;
                    let categTwo = categOne.find(j => j.categoryId == this.tagContent.categoryId[1]);
                    categoryName = categTwo.titleCn;
                    tenant = categTwo.tenant;
                    categoryId = this.tagContent.categoryId[1];
                }
                if(this.tagContent.categoryId.length == 1) {
                    let categOne = this.departmentList.find(element => element.categoryId == this.tagContent.categoryId[0])
                    categoryName = categOne.titleCn;
                    tenant = categOne.tenant;
                    categoryId = this.tagContent.categoryId[0];
                }
                if(!Array.isArray(this.tagContent.categoryId)) {
                    categoryName = this.categoryDtosObj.categoryName;
                    tenant = this.categoryDtosObj.tenant;
                    categoryId = this.categoryDtosObj.categoryId;

                }
                let tagObj = {}
                if(this.tagList.length) {
                    tagObj = this.tagList.find(element => element.id == this.tagContent.name)
                } else {
                    tagObj={
                        id: '',
                        tagName: this.tagContent.name,
                    }
                }
                let params = {
                    "id": this.model.id,
                    "categoryInfo": [{
                        "categoryId": categoryId,
                        "categoryName": categoryName,
                        "tenant": tenant
                    }],
                    "level": this.tagContent.level,
                    "parentId": parentIds.length > 0 ? parentIds[0] : '',
                    "projectId": 1,
                    // "tagId": tagObj.id,
                    "tagName": tagObj.tagName,
                    "userId": this.userInfo.userId,
                    "username": this.userInfo.userName
                }
                this.api.tagLabelUpdate(params).then(response => {
                    this.buttonLoading = false;
                    if(response.status == 200) {
                        this.$message({
                            message: response.message,
                            type: 'success'
                        });
                        this.$emit('up-date');
                    } else {
                        this.$message({
                            message: response.message,
                            type: 'warning'
                        });
                    }
                }).catch(() => this.buttonLoading = false)
            } else {
                console.log(this.tagContent, 'this.tagContent')
                let categoryId = '';
                let categoryName = '';
                let tenant = '';
                if(this.tagContent.categoryId.length == 2) {
                    let categOne = this.departmentList.find(element => element.categoryId == this.tagContent.categoryId[0]).children;
                    let categTwo = categOne.find(j => j.categoryId == this.tagContent.categoryId[1]);
                    categoryName = categTwo.titleCn;
                    tenant = categTwo.tenant;
                    categoryId = this.tagContent.categoryId[1];
                }
                if(this.tagContent.categoryId.length == 1) {
                    let categOne = this.departmentList.find(element => element.categoryId == this.tagContent.categoryId[0])
                    categoryName = categOne.titleCn;
                    tenant = categOne.tenant;
                    categoryId = this.tagContent.categoryId[0];
                }
                let tagObj = this.tagList.find(element => element.id == this.tagContent.name)
                let params = {
                    "categoryInfo": [{
                        "categoryId": categoryId,
                        "categoryName": categoryName,
                        "tenant": tenant
                    }],
                    "level": this.tagContent.level,
                    "parentIds": parentIds,
                    "projectId": 1,
                    "tagId": tagObj ? tagObj.id : 0,
                    "tagName": tagObj ? tagObj.tagName : this.tagContent.name,
                    "userId": this.userInfo.userId,
                    "username": this.userInfo.userName
                }
                console.log(params, 'params')
                // return false
                this.api.tagLabelAdd(params).then(response => {
                    this.buttonLoading = false;
                    if(response.status == 200) {
                        this.$message({
                            message: response.message,
                            type: 'success'
                        });
                        this.$emit('up-date');
                    } else {
                        this.$message({
                            message: response.message,
                            type: 'warning'
                        });
                    }
                }).catch(() => this.buttonLoading = false)
            }
        },
        visibleChange(val) {
            if(!val && this.tagContent.threeTag.length) {
                this.labelSearchTwo();
            }
        },
        info_operation(val) {
            switch (val) {
                case 'save':
                    this.$refs["tagContent"].validate( valid => {
                        if (valid) {
                            this.submit();
                        }
                    })
                break;
                case 'back':
                    this.$emit('close');
                break;
                default: break;
            }
        },
        // 获取科室
        getDepart() {
            return new Promise((resolve, reject) => {
                let params = {
                    module: 'label'
                }
                this.api.getProjectModuleAndCategoryList(params).then( response => {
                    if(response.status === 200) {
                        this.departmentList = response.data;
                    }
                    resolve()
                }).catch(() => reject())
            })
        },
        categoryIdChange(val) {
            console.log(val)
            // if(this.tagContent.level == 1) {
            //     return false
            // }
            // if(this.tagContent.categoryId !== val[1]) {
            //     this.tagContent.threeTag = [];
            //     this.tagContent.fourTag = [];
            //     this.labelSearch();
            // }
        },
        // 获取三级标签
        labelSearch(val) {
            console.log(this.tagContent.categoryId, 'this.tagContent.categoryId')
            let params = {
                "categoryId": Array.isArray(this.tagContent.categoryId) ? this.tagContent.categoryId[1] : this.tagContent.categoryId,
                "name": val || '',
                "parentIds": [],
                "projectId": 1,
                "type": 3,
                level: 1,
            }
            this.api.labelSearch(params).then( response => {
                if(response.status === 200) {
                    this.threeList = response.data;
                } else {
                    this.threeList = [];
                }
            })
        },
        labelSearchTwo(val) {
            return new Promise((resolve) => {
                let params = {
                    "categoryId": Array.isArray(this.tagContent.categoryId) ? this.tagContent.categoryId[1] : this.tagContent.categoryId,
                    "name": val,
                    "parentIds": this.tagContent.threeTag,
                    "projectId": 1,
                    "type": 3,
                    level: 2,
                }
                this.api.labelSearch(params).then( response => {
                    if(response.status === 200) {
                        this.fourList = response.data;
                        resolve()
                    } else {
                        this.fourList = [];
                        resolve()
                    }
                })
            })
        },
	}
}
</script>

<style scoped>
.add-tag{
    min-height: 500px;
}
.select-tag >>> .el-select{
    width: 100%;
}
.select-tag >>> .el-select .el-tag{
    height: 25px;
    line-height: 25px;
}
.select-tag >>> .el-select-dropdown{
    max-width: calc(100% - 40px);
    left: 150px !important;
}
.select-tag >>> .el-scrollbar__view{
    display: flex;
    flex-wrap: wrap;
}
.select-tag >>> .el-select-dropdown.is-multiple .el-select-dropdown__item.selected::after{
    right: 4px;
}
.tag-name{
    width: 201px;
}
</style>
