<template>
  <ms-operation-dialog>
    <template slot="content">
      <el-form ref="submitRef"
               :model="submitData"
               :rules="rules"
               label-width="85px"
               class="rule-form">
        <el-row>
          <el-col :span="18" :offset="2">
            <div class="form-label">创建考试</div>
            <el-form-item label="考试名称" prop="title">
              <el-input v-model="submitData.title"></el-input>
            </el-form-item>
            <el-form-item label="考试类型" prop="type">
              <el-radio-group v-model="submitData.type">
                <el-radio :label="0">必过考试</el-radio>
                <!-- <el-radio :label="1">评分考试</el-radio> -->
              </el-radio-group>
            </el-form-item>
            <div class="form-label">考试设置</div>
            <el-form-item label="考试时间" prop="timeRange">
              <el-date-picker
                v-model="submitData.timeRange"
                value-format="yyyy-MM-dd HH:mm:ss"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期">
              </el-date-picker>
            </el-form-item>
            <template v-if="submitData.type == 1">
              <el-form-item label="通过分数" prop="passScore">
                <el-input v-model="submitData.passScore" placeholder="X"></el-input>分即可通过考试
                <span>本试卷满分X分</span>
              </el-form-item>
              <el-form-item label="考试次数" prop="allowExamCount">
                <el-input v-model="submitData.allowExamCount" placeholder="0"></el-input>次
                <span>默认0次，即不限制考试次数</span>
              </el-form-item>
            </template>
            <el-form-item label="答案显示" prop="isShowAnswer">
              <el-checkbox v-model="submitData.isShowAnswer" :true-label='0' :false-label='1'>考试结束不显示答案</el-checkbox>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </template>
    <template slot="footer">
      <el-button @click="submitForm"
                 :loading="loading"
                 size="mini"
                 type="primary">保 存</el-button>
      <!-- <el-button @click="submitRelease"
                 :loading="loading"
                 size="mini"
                 type="primary">发布考试</el-button> -->
      <el-button @click="$emit('close')"
                 size="mini">取 消</el-button>
    </template>
  </ms-operation-dialog>
</template>

<script> 
import { mapGetters } from "vuex";
export default {
	name: "ms-questionGather-create",
	data () {
		return {
      loading: false,
      submitData: {
        title: "",
        type: 0,
        timeRange: [],
        startTime: "",
        endTime: "",
        isShowAnswer: 1,
        allowExamCount: 0,
        passScore: ''
      },
      examId: '',
      rules: {
        title: [
          { required: true, message: "请输入考试名称", trigger: 'blur' },
        ],
        type: [
          { required: true, message: "请选择考试类型", trigger: 'blur' },
        ],
        timeRange: [
          { required: true, message: "请选择考试时间", trigger: 'blur' },
        ],
        passScore: [
          { required: true, message: "请输入通过分数", trigger: 'blur' },
        ]
      },
    }
  },
	props: {
		model: Object,
		operation: String
  },
  computed: {
    ...mapGetters(["info"])
  },
  created() {
    this.init()
  },
	methods: {
    init() {
      this.api.examDetails({paperId:this.model.id}).then(response => {
        if(response.status === 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
        this.submitData = response.data
        this.examId = response.data.id
        this.submitData.title = this.submitData.title || this.model.title
        this.submitData.examPaperId = this.submitData.examPaperId || this.model.id
        this.submitData.projectId = this.submitData.projectId || this.info.projectId
        if(response.data.startTime && response.data.endTime) {
          this.$set(this.submitData, 'timeRange', [response.data.startTime ,response.data.endTime])
        }else {
          this.$set(this.submitData, 'timeRange', [])
        }
      }).catch( () => {})
    },
		submitForm() {
      this.$refs.submitRef.validate( valid => {
        if(valid) {
          this.loading = true;
          this.submitEdit()
        }
      })
    },
    submitEdit() {
      this.submitData.startTime = this.submitData.timeRange[0]
      this.submitData.endTime = this.submitData.timeRange[1]
      let params = {
        userId: this.info.userId,
        username: this.info.userName,
        ...this.submitData
      }
      this.api.examSave(params).then(response => {
        if(response.status === 200) {
          this.$emit('up-date')
          this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
        this.loading = false
      }).catch( () => {
        this.loading = false;
        this.$emit('close')
      })
    },
    submitRelease() {
      this.$confirm('确认发布考试，是否继续', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.examRelease()
        })
    },
    examRelease() {
      this.api.examRelease({examId:this.examId}).then(response => {
        if(response.status === 200) {
          this.$emit('up-date')
          this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
        this.loading = false
      }).catch( () => {
        this.loading = false;
        this.$emit('close')
      })
    }
	}
}
</script>
<style lang="scss" scoped>
.form-label {
  margin-bottom: 10px;
  color: #333;
  font-weight: bold;
}
/deep/ .ms-dialog-footer {
  text-align: right;
}
/deep/ .ms-dialog-line {
  height: 0;
}
</style>
