<template>
  <ms-operation-dialog :title="title">
    <template slot="content">
      <el-tag v-for="(item, index) in tagArr" 
              :key="index" 
              type="info" 
              style="margin: 0 5px 5px 0">{{item}}</el-tag>
    </template>
    <template slot="footer">
      <el-button @click="submitForm"
                 :loading="loading"
                 size="mini"
                 type="primary">确 定</el-button>
      <el-button @click="$emit('close')"
                 size="mini">取 消</el-button>
    </template>
  </ms-operation-dialog>
</template>

<script>
export default {
	name: "ms-question-operation",
	data () {
		return {
      userInfo: {},
      type: null,
      tagArr: [],
      ids: [],
      statusArr: [],
      title: '',
      loading: false
		}
	},
	props: [
		"model",
		"operation"
  ],
  created() {
    this.userInfo = this.$store.getters.info || {}
    this.init()
  },
	methods: {
    init() {
      let arr = []
      let ids = []
      let statusArr = []
      let way = this.operation || this.$route.query.operation
      this.type = way === 'approval' ? 'AUDIT' : way === 'toreview' ? 'UN_AUDIT' : way === 'delete' ? 'DELETE' :  way === 'examClose' ? 'EXAM_CLOSE' : null
      this.title = `是否${this.type === 'AUDIT' ? '审核试卷信息' : this.type === 'UN_AUDIT' ? '去审试卷信息' : this.type === 'DELETE' ? '删除试卷信息' : this.type === 'EXAM_CLOSE' ? '关闭考试' : ''}`
      if (this.PUBLIC_Methods.isArrayFn(this.model)) {
        this.model.forEach(item => {
          arr.push(item.title)
          ids.push(item.id)
          statusArr.push(item.status)
        });
      } else {
        arr.push(this.model.title)
        ids.push(this.model.id)
        statusArr.push(this.model.status)
      }
      this.tagArr = arr
      this.ids = ids
      this.statusArr = statusArr
    },
		submitForm () {
      if(this.type == 'EXAM_CLOSE') {
        this.closeExam()
      }else if(this.type === 'DELETE') {
        if(this.statusArr.includes(1)) {
          this.$alert('无法删除已审核的试卷，请重新选择', '温馨提示', {
            confirmButtonText: '确定',
          });
          return false
        }
        this.$confirm('此操作将永久删除试卷信息，是否继续', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.apiOperationAudit()
        }).catch(()=>{
          this.$message({
            type: 'info',
            message: '已取消删除'
          });
        })
      } else {
        this.apiOperationAudit()
      }
    },
    apiOperationAudit() {
      let params = {
        userId: this.userInfo.userId,
        username: this.userInfo.userName,
        action: this.type,
        paperIdList: this.ids
      }
      this.loading = true;
      this.api.examPaperStatus(params).then(response => {
        if(response.status === 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
        this.$emit('up-date')
        this.loading = false
      }).catch( () => {
        this.loading = false;
        this.$emit('close')
      })
    },
    closeExam() {
      this.loading = true;
      this.api.examClose({paperId:this.ids[0]}).then(response => {
        if(response.status === 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
        this.$emit('up-date')
        this.loading = false
      }).catch( () => {
        this.loading = false;
        this.$emit('close')
      })
    }
	}
}
</script>
