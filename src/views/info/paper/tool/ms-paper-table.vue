<template>
  <div class="ms-table">
    <div style="text-align: left;margin-bottom:10px">
      <el-button icon="el-icon-close" :disabled="disabled" @click="deleteItem" size="small" type="info" plain>批量删除</el-button>
    </div>
    <el-table ref="dragTable" :data="list" style="width:100%;min-width:780px" :header-cell-style="{'background-color':'#EBEEF5'}"
			:header-row-style="{'background-color':'#EBEEF5'}" highlight-current-row @selection-change="handleSelectionChange">
     <el-table-column type="selection" width="55" align="center"></el-table-column>
      <el-table-column prop="sort" label="序号" width="80" align="center"></el-table-column>
      <el-table-column prop="questionId" label="题号" width="80"></el-table-column>
      <el-table-column prop="questionTitle" label="题干" min-width="200"></el-table-column>
      <el-table-column prop="questionType" label="题型">
        <template slot-scope="scope">
          {{scope.row.questionType == 1?'单选题':scope.row.questionType == 2?'多选题':scope.row.questionType == 3?'主观题':''}}
        </template>
      </el-table-column>
      <el-table-column prop="score" label="分值">
        <template slot-scope="scope">
          <el-input :disabled="disabled" v-model="scope.row.score" @input="scope.row.score = Number(scope.row.score.replace(/\D+/, ''))" ></el-input>
        </template>
      </el-table-column>
      <el-table-column prop="missedScore" label="漏选">
        <template slot-scope="scope">
          <el-input :disabled="disabled" v-model="scope.row.missedScore" @input="scope.row.missedScore = Number(scope.row.missedScore.replace(/\D+/, ''))" ></el-input>
        </template>
      </el-table-column>
      <el-table-column label="操作">
        <template slot-scope="scope">
          <el-button @click="deleteItem(scope.row,scope.$index)" :disabled="disabled" size="mini" type="danger" plain>删除</el-button>
        </template>
      </el-table-column>
    </el-table>
  </div> 
</template>

<script>
import Sortable from 'sortablejs'
import peperMixin from "../paper-mixin"
export default {
  name: "ms-paper-table",
  mixins: [peperMixin],
  props: ['list'],
  data () {
    return {
      selectionList:[],
      disabled: false
    }
  },
  created() {
    this.init()
    this.disabled = this.$route.query.status == 1 ? true : false
  },
  methods: {
    init () {
      this.$nextTick(() => {
        this.setSort()
      })
    },
    handleSelectionChange(val) {
      this.selectionList = val.map(n=>n.id)
    },
    deleteItem(row,index) {
      if(row && row.id) {
        this.list.splice(index,1)
      }else {
        this.selectionList.forEach(m=>{
          for(var i=this.list.length-1;i>=0;i--){
            if(this.list[i].id == m) {
              this.list.splice(i,1)
            }
          }
        })
      }
    },
    setSort() {
      if(this.disabled) return false
      const el = this.$refs.dragTable.$el.querySelectorAll('.el-table__body-wrapper > table > tbody')[0]
      this.sortable = Sortable.create(el, {
        ghostClass: 'sortable-ghost',
        setData: function(dataTransfer) {
          dataTransfer.setData('Text', '')
        },
        onEnd: evt => {
          let data = this.list.slice(0);
          let drapObj = data[evt.oldIndex]
          data.splice(evt.oldIndex, 1)
          data.splice(evt.newIndex, 0, drapObj)
          var newArray = data.slice(0);
          data = [];
          this.$nextTick(() => {
            newArray.forEach((item,index)=>{
              data.push({
                id: item.id,
                questionId: item.questionId,
                questionTitle: item.questionTitle,
                score: item.score,
                missedScore: item.missedScore,
                isQuestionRandom: item.isQuestionRandom,
                questionType: item.questionType,
                sort: index + 1
              })
            })
            this.$emit('up-date', data)
          })
        }
      })
    },
  }
};
</script>
<style lang="scss" scoped>
/deep/ .el-table th, .el-table td {
  padding: 12px 0;
}
/deep/ input::-webkit-outer-spin-button,
/deep/ input::-webkit-inner-spin-button {
  -webkit-appearance: none !important;
}
/deep/ input[type='number'] {
  -moz-appearance: textfield !important;
}
</style>

