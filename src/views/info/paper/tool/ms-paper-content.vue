<template>
  <section>
    <el-form :model="submitData"
             ref="submitRef"
             class="rule-form info-form"
             :rules="rules"
             label-width="90px">
      <el-row :gutter="20"> 
        <el-col :span="16">
          <el-row>
            <el-col :span="24">
              <el-form-item label="试卷名称" prop="title">
                <el-input v-model="submitData.title" :disabled="disabled" placeholder="请输入20字以内的试卷名称" style="width: 100%" maxlength="20" show-word-limit></el-input>
              </el-form-item>
              <el-form-item label="试卷类型" prop="type">
                <el-radio-group v-model="submitData.type" :disabled="disabled">
                  <el-radio :label="0">固定试卷</el-radio>
                  <!-- <el-radio :label="1">随机试卷</el-radio> -->
                </el-radio-group>
              </el-form-item>
              <el-form-item label="选项随机" prop="optionRandom">
                <el-switch
                  :disabled="disabled"
                  v-model="submitData.optionRandom"
                  :active-value="1"
                  :inactive-value="0">
                </el-switch>
              </el-form-item>
              <el-form-item label="题目顺序随机" prop="questionRandom">
                <el-switch
                  v-model="submitData.questionRandom"
                  :active-value="1"
                  :inactive-value="0">
                </el-switch>
              </el-form-item>
              <el-form-item label="题目来源" prop="gatherId">
                <el-select v-model="submitData.gatherId" :disabled="disabled || disChangeId" filterable placeholder="输入关键字检索" clearable @change="changeGather">
                  <el-option
                    v-for="item in gatherList"
                    :key="item.id"
                    :label="item.title"
                    :value="item.id">
                  </el-option>
                </el-select>
                <el-button v-if="submitData.gatherId" style="margin-left:20px" :disabled="disabled" type="primary" @click="changeGatherId">修改</el-button>
                <el-button v-if="submitData.gatherId" style="margin-left:20px" type="primary" @click="goGatherPage">查看</el-button>
              </el-form-item>
              <el-form-item label="选择题目" prop="questionShip" v-if="submitData.gatherId">
                <el-button :disabled="disabled" type="primary" @click="addQuestion">添加题目</el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-col>
      </el-row>
      <el-tabs v-model="activeName" type="card" @tab-click="changeTab">
        <el-tab-pane label="单选题" name="1" :disabled='loading'>
          <ms-paper-table :list='list' @up-date='updateList'></ms-paper-table>
        </el-tab-pane>
        <el-tab-pane label="多选题" name="2" :disabled='loading'>
          <ms-paper-table :list='list' @up-date='updateList'></ms-paper-table>
        </el-tab-pane>
        <el-tab-pane label="主观题" name="3" :disabled='loading'>
          <ms-paper-table :list='list' @up-date='updateList'></ms-paper-table>
        </el-tab-pane>
      </el-tabs>
    </el-form>
    <ms-right-dialog :visible.sync="r_dialog" :width="dialogWidth" :title="dialogTitle">
      <component
        :is="dialogComponent"
        :model="scopeInfo"
        :operation="dialogOperation"
        @close="r_dialog = !r_dialog"
        @up-date="init"
        v-if="r_dialog"
      ></component>
    </ms-right-dialog>
  </section>
</template>

<script>
import { mapGetters } from "vuex";
import tableMixins  from "../../../common/mixins/table"
import peperMixin from "../paper-mixin"
import msPaperAdd from "./ms-paper-add"
import msPaperTable from "./ms-paper-table"
export default {
	name: "ms-paper-content",
  mixins: [tableMixins, peperMixin],
	data () {
		return {
      testLoading: false,
      rules: {
        title: [
          { required: true, message: "请输入试卷名称", trigger: 'blur' }
        ],
        type: [
          { required: true, message: "请选择试卷类型", trigger: 'blur' },
        ],
        gatherId: [
          { required: true, message: "请选择题目来源", trigger: 'blur' },
        ],
      },
      gatherList: [],
      addInfo:{
        operation: {
          label: '添加题目',
          icon: '',
          role: '',
          operation: 'edit',
          component: 'msPaperAdd',
          way: 'dialog',
          type: 'primary',
          position: 'right',
          title: '选择题目',
          width: '55%',
        },
        model: {}
      }, 
      r_dialog: false,
      dialogWidth: '55%',
      dialogTitle: '',
      dialogComponent: '',
      scopeInfo: {},
      dialogOperation: '',
      activeName: '1',
      questionType: '1',
      disabled: false,
      disChangeId: true
		}
  },
  computed: {
    ...mapGetters(["submitData", "info", "localCode"])
  },
  components: { msPaperAdd, msPaperTable },
  created() {
    this.getGatherList()
    this.disabled = this.$route.query.status == 1 ? true : false
  },
	methods: {
    apiInit() {
      let params = {
        examPaperId: this.$route.query.id,
        questionType : Number(this.activeName)
      }
      this.api.paperShipDetails(params).then(response => {
        this.loading = false
        this.list = response.data || []
        if (response.status !== 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
      }).catch(() => this.loading = false)
    },
    updateList(data) {
      this.list = []
      this.$nextTick(() => {
        this.list = data
      })
    },
    changeTab() {
      this.submitTab()
      this.apiInit()
    },
    async addQuestion() {
      await this.submitTab()
      this.operation_change(this.addInfo)
    },
    async submitTab() {
      if(this.disabled) return false
      this.loading = true
      let params = {
        userId: this.info.userId,
        username: this.info.userName,
        paperId: this.$route.query.id,
        mixAdd: false,
        questionType: Number(this.questionType),
        questionShipDtos: this.list
      }
      await this.api.paperShipEdit(params).then(response => {
        if(response.status !== 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
        this.questionType = Number(this.activeName)
        this.loading = false
      }).catch( () => {
        this.loading = false
      })
    },
    getGatherList() {
      let data = {
        pageindex: 1,
        pageSize: 999999,
        auditStatus: 1
      }
      this.api.getQuestionGatherPage(data).then(response => {
        this.gatherList = response.data || []
        this.addInfo.model.id = this.submitData.gatherId
        this.$store.dispatch('SetGatherId', this.submitData.gatherId)
        if (response.status !== 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
      }).catch(() => {})
    },
    goGatherPage() {
      let routeData = this.$router.resolve({
        path: "/questionGather-operation",
        query: {
          operation: 'edit',
          component: 'video-manage-series',
          id: this.submitData.gatherId
        }
      });
      window.open(routeData.href, '_blank');
    },
    changeGather() {
      this.addInfo.model.id = this.submitData.gatherId
      this.$store.dispatch('SetGatherId', this.submitData.gatherId)
      let data = {
        userId: this.info.userId,
        username: this.info.userName,
        gatherId: this.submitData.gatherId,
        examPaperId: this.$route.query.id,
      }
        this.api.examSwitchoverGather(data).then(response => {
        if (response.status !== 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }else {
          this.$message({
            type: 'success',
            message: '变更成功!'
          });
          this.apiInit()
        }
      }).catch(() => {})
    },
    changeGatherId() {
         this.$confirm('变更题目来源将清空原先题目绑定关系, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.disChangeId = false;
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消变更'
          });
        });
    },
    // 数据校验
    async validateData(callback) {
      await this.submitTab()
      let params = {
        ...this.submitData,
        userId: this.info.userId,
        username: this.info.userName
      }
      this.$refs["submitRef"].validate( valid => {
        if (valid) {
          callback(params)
        }
      })
    }
	}
}
</script>

<style lang="scss" scoped>
.info-form{
  /deep/ .el-checkbox {
    margin-right: 0px;
  }
  /deep/ .el-checkbox.width80 {
    width: 80%;
    margin-right: 30px;
  }
  /deep/ .el-checkbox.width80 .el-checkbox__label {
    width: 100%;
  }
  /deep/ .el-radio.width80 {
    width: 80%;
  }
  /deep/ .el-radio.width80 .el-radio__label {
    width: 100%;
  }
  &-right {
    /deep/ .el-form-item {
      margin-bottom: 16px;
    }
  }
  .expand {
    .label {
      color: #829FFF; 
      font-size: 12px;
      width: 40px;
      display: inline-block;
      text-align: right;
    }
    /deep/ .el-form-item {
      padding: 4px 0;
      margin-bottom: 14px;
      margin-top: -4px;
      background-color: #E5E6E9;
      border-radius: 4px;
    }
  }
  /deep/ .addBtn {
    margin-bottom: 20px;
  }
}
</style>
