<template>
  <ms-operation-dialog>
    <template slot="content">
      <el-form ref="submitRef"
               :model="submitData"
               :rules="rules"
               label-width="85px"
               class="rule-form">
        <el-row>
          <el-col :span="18" :offset="2">
            <el-form-item label="试卷名称" prop="title">
              <el-input v-model="submitData.title" placeholder="请输入20个字符以内的文本"></el-input>
            </el-form-item>
            <el-form-item label="试卷类型" prop="type">
              <el-radio-group v-model="submitData.type">
                <el-radio :label="0">固定试卷</el-radio>
                <!-- <el-radio :label="1">随机试卷</el-radio> -->
              </el-radio-group>
            </el-form-item>
            <el-form-item label="选项随机" prop="optionRandom">
              <el-switch
                v-model="submitData.optionRandom"
                :active-value="1"
                :inactive-value="0">
              </el-switch>
            </el-form-item>
            <el-form-item label="题目顺序随机" prop="questionRandom">
              <el-switch
                v-model="submitData.questionRandom"
                :active-value="1"
                :inactive-value="0">
              </el-switch>
            </el-form-item>
            <el-form-item label="题目来源" prop="gatherId">
              <el-select v-model="submitData.gatherId" filterable placeholder="输入关键字检索" clearable @change="changeGather">
                <el-option
                  v-for="item in gatherList"
                  :key="item.id"
                  :label="item.title"
                  :value="item.id">
                </el-option>
              </el-select>
              <el-button v-if="submitData.gatherId" style="margin-left:20px" type="primary" @click="goGatherPage">查看</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </template>
    <template slot="footer">
      <el-button @click="submitForm"
                 :loading="loading"
                 size="mini"
                 type="primary">提交</el-button>
      <el-button @click="$emit('close')"
                 size="mini">取 消</el-button>
    </template>
  </ms-operation-dialog>
</template>

<script> 
import { mapGetters } from "vuex";
export default {
	name: "ms-questionGather-create",
	data () {
		return {
      loading: false,
      gatherList: [],
      submitData: {
        title: "",
        type: 0
      },
      rules: {
        title: [
          { required: true, message: "请输入试题集名称", trigger: 'blur' },
          { max: 20, message: '请输入20个字符以内的文本', trigger: 'blur' }
        ],
        type: [
          { required: true, message: "请选择试卷类型", trigger: 'blur' },
        ],
        gatherId: [
          { required: true, message: "请选择题目来源", trigger: 'blur' },
        ],
      },
    }
  },
	props: {
		model: Object,
		operation: String
  },
  computed: {
    ...mapGetters(["info"])
  },
  created() {
    this.getGatherList()
  },
	methods: {
    getGatherList() {
      let data = {
        pageindex: 1,
        pageSize: 999999,
        auditStatus: 1
      }
      this.api.getQuestionGatherPage(data).then(response => {
        this.gatherList = response.data || []
        this.addInfo.model.id = this.submitData.gatherId
        this.$store.dispatch('SetGatherId', this.submitData.gatherId)
        if (response.status !== 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
      }).catch(() => {})
    },
    goGatherPage() {
      let routeData = this.$router.resolve({
        path: "/questionGather-operation",
        query: {
          operation: 'edit',
          component: 'video-manage-series',
          id: this.submitData.gatherId
        }
      });
      window.open(routeData.href, '_blank');
    },
    changeGather() {
      this.$store.dispatch('SetGatherId', this.submitData.gatherId)
    },
		submitForm() {
      this.$refs.submitRef.validate( valid => {
        if(valid) {
          this.loading = true;
          if(this.operationLocal === 'edit') {
            this.submitEdit()
          } else {
            this.submitAdd()
          }
        }
      })
    },
    submitAdd() {
      let params = {
        userId: this.info.userId,
        username: this.info.userName,
        ...this.submitData
      }
      this.api.examPaperSave(params).then(response => {
        if(response.status === 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
          this.$router.push({
            path: '/paper-operation',
            query: {
              operation: 'edit',
              component: 'video-manage-series',
              id: response.data
            }
          })
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
        this.$emit('up-date')
        this.loading = false
      }).catch( () => {
        this.loading = false;
        this.$emit('close')
      })
    },
	}
}
</script>