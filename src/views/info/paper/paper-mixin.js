import msPaperOperation from './tool/ms-paper-operation'
import msPaperCategory from "./tool/ms-paper-category"
import msPaperCreate from "./tool/ms-paper-create"
import msPaperExam from "./tool/ms-paper-exam"
import msPaperStatus from "@/components/MsCommon/ms-paper-status"
import msInfoSetting from '@/components/MsCommon/ms-info-setting'
import domConfig from "./data/dom-config";
import formConfig from "./data/form-config";
import scopeConfig from "./data/scope-config";
import tableConfig from "./data/table-config";

export default {
  data() {
    return {
      domConfig: domConfig, // => dom展示数据
      formConfig: formConfig, // => form展示数据
      scopeConfig: scopeConfig, // => 显示信息配置 
      tableConfig: tableConfig, // => 显示信息配置 
    }
  },
  components: {
    msPaperOperation,
    msPaperCategory,
    msPaperCreate,
    msPaperExam,
    msPaperStatus,
    msInfoSetting
  }
}
