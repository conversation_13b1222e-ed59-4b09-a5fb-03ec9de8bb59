const formConfig = {
  listSearch: [
    {
      label: ' ',
      placeholder: '题型',
      model: 'type',
      component: 'ms-question-type'
    },
    {
      label: ' ',
      placeholder: '难度',
      model: 'difficulty',
      component: 'ms-question-difficulty'
    },
    {
      label: ' ',
      placeholder: '关键词',
      model: 'tagId',
      component: 'ms-exam-tag-search'
    },
  ],
  tableHeader: [
    { label: 'ID', property: 'id', sortable: true, width: 80 },
    { label: '题干', property: 'title', width: 250 },
    { label: '题型', property: 'type', width: 100 },
    { label: '关键词', property: 'tagStr' },
    { label: '难度', property: 'difficulty' }
  ],
  tableButtons: [],
  soltButtons: [],
  shipButtons: []
}

export default formConfig;
