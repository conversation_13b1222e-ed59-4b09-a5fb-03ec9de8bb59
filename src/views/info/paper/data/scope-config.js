const scopeConfig = {
  show: {
    status: () => {
      return {
        type: 'status',
        rule: {
          0: { label: '未审核', background: '#A7ADBD' },
          1: { label: '已审核', background: '#40A23F' }
        }
      }
    },
    releaseStatus: () => {
      return {
        type: 'status',
        rule: {
          0: { label: '未发布', background: '#A7ADBD' },
          1: { label: '已发布', background: '#40A23F' }
        }
      }
    },
    fields: () => {
      return {
        type: 'fidd',
        fields: [
          { name: 'questionNum', way: 'text' },
          { name: 'scoreTotal', way: 'text' }
        ]
      }
    },
    type: () => {
      return {
        type: 'code',
        rule: {
          0: { label: '固定试卷' },
          1: { label: '随机试卷' }
        }
      }
    },
    difficulty: () => {
      return {
        type: 'code',
        rule: {
          1: { label: '简单' },
          2: { label: '中等' },
          3: { label: '困难' }
        }
      }
    },
    auditAt: () => {
      return {
        type: 'formatTime',
        cFormat: '{y}-{m}-{d} {h}:{i}'
      }
    },
    createdAt: () => {
      return {
        type: 'formatTime',
        cFormat: '{y}-{m}-{d} {h}:{i}'
      }
    },
    title: () => {
      return {
        type: 'preview',
        config: {
          label: '编辑',
          icon: '',
          role: '',
          operation: 'edit',
          component: 'video-manage-series',
          way: 'page',
          type: 'primary',
          path: 'paper-operation',
          params: ['id', 'status'],
        }
      }
    },
  },
  headerShow: {
    status: () => {
      return {
        type: 'dropdown',
        icon: 'icon-funnel',
        options: [
          { label: '全部', value: null },
          { label: '未审核', value: 0 },
          { label: '已审核', value: 1 }
        ],
        operation: 'query'
      }
    },
    releaseStatus: () => {
      return {
        type: 'dropdown',
        icon: 'icon-funnel',
        options: [
          { label: '全部', value: null },
          { label: '未发布', value: 0 },
          { label: '已发布', value: 1 }
        ],
        operation: 'query'
      }
    }
  }
}

export default scopeConfig;