const domConfig = {
  listSearch: [
    {
      label: '试卷名称',
      placeholder: '请输入',
      model: 'title',
      component: 'ms-input'
    },
    {
      label: ' ',
      placeholder: '试卷状态',
      model: 'status',
      component: 'ms-paper-status'
    },
  ],
  tableHeader: [
    { label: 'ID', property: 'id', sortable: true, width: 80 },
    { label: '试卷名称', property: 'title', width: 250 },
    { label: '试卷类型', property: 'type', width: 100 },
    { label: '题目数量/总分', property: 'fields', width: 100 },
    { label: '试卷状态', property: 'status' },
    // { label: '试卷状态', property: 'releaseStatus' },
    { label: '创建人', property: 'createdName' },
    { label: '创建时间', property: 'createdAt', width: 130, sortable: true },
    { label: '审核时间', property: 'auditAt', width: 130, sortable: true },
  ],
  tableButtons: [
    {
      label: '编辑',
      icon: '',
      role: '',
      operation: 'edit',
      component: 'video-manage-series',
      way: 'page',
      type: 'primary',
      path: 'paper-operation',
      params: ['id', 'status'],
      showCallback: (val) => {
        if (val.status == 1) {
          return false
        } else {
          return true
        }
      },
    },
    {
      label: '',
      icon: '',
      role: '',
      operation: 'editStatus',
      component: 'msPaperOperation',
      way: 'dialog',
      field: 'status',
      rule: {
        1: { label: '去审', type: '', operation: 'toreview' },
        0: { label: '审核', type: 'success', operation: 'approval' }
      }
    },
    {
      label: '删除',
      icon: '',
      role: '',
      type: 'danger',
      operation: 'delete',
      component: 'msPaperOperation',
      way: 'dialog',
      field: 'status',
      showCallback: (val) => {
        if (val.status == 1) {
          return false
        } else {
          return true
        }
      },
    },
    {
      label: '考试',
      icon: '',
      role: '',
      operation: 'exam',
      component: 'msPaperExam',
      way: 'dialog',
      position: 'right',
      title: '编辑考试',
      type: '',
      width: '45%',
      params: ['id'],
      showCallback: (val) => {
        if (val.status == 0) {
          return false
        } else {
          return true
        }
      },
    },
  ],
  soltButtons: [
    {
      label: '创建试卷',
      type: 'primary',
      icon: 'el-icon-plus',
      title: '创建试卷',
      operation: 'created',
      component: 'msPaperCreate',
      way: 'dialog',
    },
    {
      label: '批量审核',
      type: 'primary',
      operation: 'approval',
      component: 'msPaperOperation',
      way: 'batch'
    },
    {
      label: '批量去审',
      type: 'primary',
      operation: 'toreview',
      component: 'msPaperOperation',
      way: 'batch'
    },
    // {
    //   title: '批量添加分类',
    //   label: '批量添加分类',
    //   type: 'success',
    //   operation: 'add',
    //   component: 'msQuestionGatherCategory',
    //   way: 'batch',
    //   identify: 'batch_add_category',
    //   width: '70%'
    // },
    // {
    //   title: '批量移除分类',
    //   label: '批量移除分类',
    //   type: 'success',
    //   operation: 'delete',
    //   component: 'msQuestionGatherCategory',
    //   way: 'batch',
    //   identify: 'batch_delete_category',
    //   width: '70%'
    // },
    {
      label: '批量删除',
      type: 'info',
      icon: 'el-icon-close',
      operation: 'delete',
      component: 'msPaperOperation',
      way: 'batch'
    }
  ]
}

export default domConfig;
