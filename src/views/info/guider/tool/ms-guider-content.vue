<template>
  <section>
    <el-form :model="submitData"
             ref="submitRef"
             class="rule-form info-form"
             :rules="rules"
             label-width="70px">
      <el-row :gutter="20"> 
        <el-col :span="18">
          <el-row>
            <el-col :span="12">
              <el-form-item label="标题中文" prop="title">
                <el-input v-model="submitData.title" style="width: 100%"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="标题英文" prop="enTitle">
                <el-input v-model="submitData.enTitle" style="width: 100%"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="外向链接" prop="linkOutUrl">
                <el-input v-model="submitData.linkOutUrl" style="width: 100%"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="原始链接" prop="originalUrl">
                <el-input v-model="submitData.originalUrl" style="width: 100%"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="内容" prop="content">
                <ms-editor v-model="submitData.content"></ms-editor>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="摘要" prop="summary">
                <el-input v-model="submitData.summary" type="textarea" :rows="4" @keyup.native="summaryKeyUp = true" maxlength="100" show-word-limit></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              <el-form-item prop="publishedTime" label="定时上线">
                <!-- <ms-picker :model.sync="submitData.publishedTime" type="datetime"></ms-picker> -->
                <el-date-picker
                  @change="change"
                  v-model="submitData.publishedTime"
                  type="datetime"
                  format="yyyy-MM-dd HH:mm"
                  :picker-options="pickerOptions"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期">
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="8" v-show="formatDisabled">
              <el-form-item label="编辑者：" label-width="100px">
                <span class="font-12">{{submitData.editor}}</span>
              </el-form-item>
            </el-col>
            <el-col :span="8" v-show="formatDisabled">
              <el-form-item label="创建时间：" label-width="80px">
                <span class="font-12">{{submitData.createdTime | parseTime}}</span>
              </el-form-item>

              
            </el-col>
          </el-row>
        </el-col>
        <el-col :span="6" class="info-form-right">
          <el-row>
            <el-col :span="24">
              <el-form-item label="附件" :prop="submitData.attachmentList.length ? '' : 'fileUrl'">
                <el-input v-model="submitData.fileUrl" style="width: 100%;" placeholder="输入指南资源地址"></el-input>
              </el-form-item>
              <el-form-item prop="attachmentList">
                <ms-file-upload v-model="submitData.attachmentList" :limit="10" accept=".doc, .docx, .pdf, .pub, .vip" :upFileSize="50" buttonLabel="手动上传资源"></ms-file-upload>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="来源" prop="guiderFrom">
                <el-input v-model="submitData.guiderFrom" style="width: 100%"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="发布机构" prop="associationId">
                <MsAssociationSearch v-model="submitData.associationId" :modelName.sync="submitData.associationName"></MsAssociationSearch>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="指南发布" prop="guiderPublishedTime">
                <el-date-picker v-model="submitData.guiderPublishedTime" type="date" style="width: 100%;" value-format="yyyy-MM-dd HH:mm:ss"></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="类型" >
                <el-row >
                  <el-col :span="12">
                    <el-select v-model="submitData.guiderType" style="width: 100%">
                      <el-option v-for="(item,index) in localCode['guiderType'] || []"
                                :key="index"
                                :label="item.name"
                                :value="+item.value">
                      </el-option>
                    </el-select>
                  </el-col>
                  <el-col :span="12">
                    <el-select v-model="submitData.guiderArea" style="width: 100%">
                      <el-option v-for="(item,index) in localCode['guiderArea'] || []"
                                :key="index"
                                :label="item.name"
                                :value="item.value">
                      </el-option>
                    </el-select>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="12">
                    <el-select v-model="submitData.guiderLanguage" style="width: 100%;">
                      <el-option v-for="(item,index) in localCode['language'] || []"
                                :key="index"
                                :label="item.name"
                                :value="+item.value">
                      </el-option>
                    </el-select>
                  </el-col>
                  <el-col :span="12">
                    <el-select v-model="submitData.guiderRegion" style="width: 100%;">
                      <el-option v-for="(item,index) in localCode['region'] || []"
                                :key="index"
                                :label="item.name"
                                :value="item.value">
                      </el-option>
                    </el-select>
                  </el-col>
                </el-row>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="版权" prop="copyright">
                <el-select v-model="submitData.copyright" style="width: 100%">
                  <el-option v-for="(item,index) in localCode['copyRight'] || []"
                            :key="index"
                            :label="item.name"
                            :value="+item.value">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
             <el-col :span="24">
              <el-form-item label="资讯拓展">
                <el-row>
                  <el-col :span="18"><MsTagSearch v-model="submitData.articleKeyword" style="width: 95%;display: inline-block;" placeholder="资讯标题关键词" :modelId.sync="submitData.articleKeywordId" :notMul="true"></MsTagSearch></el-col>
                  <el-col :span="6"><el-input placeholder="数量" style="width: 100%" v-model="submitData.articleKeywordNum"></el-input></el-col>
                </el-row>
              </el-form-item>
              <el-form-item label="指南拓展">
                <el-row>
                  <el-col :span="18"><MsTagSearch v-model="submitData.guiderKeyword" style="width: 95%;display: inline-block;" placeholder="指南标题关键词" :modelId.sync="submitData.guiderKeywordId" :notMul="true"></MsTagSearch></el-col>
                  <el-col :span="6"><el-input placeholder="数量" style="width: 100%" v-model="submitData.guiderKeywordNum"></el-input></el-col>
                </el-row>
              </el-form-item>
              <el-form-item label="关联课程">
                <el-row>
                  <el-col>
                    <el-select
                      ref="relationVersion"
                      style="width:100%"
                      v-model="submitData.relationCourseIds"
                      filterable
                      multiple
                      remote
                      reserve-keyword
                      placeholder="请输入关联课程关键词"
                      :remote-method="getCoureByTitle"
                      value-key="title"
                      @change="changeCoure"
                      :loading="loading">
                      <el-option
                        v-for="item in payData"
                        :key="item.id"
                        :label="item.title"
                        :value="{id:item.id,title:item.title}">
                      </el-option>
                    </el-select>
                  </el-col>
                </el-row>
              </el-form-item>
              <el-form-item label="其他版本">
                <el-row>
                  <el-col>
                    <el-select
                      style="width:100%"
                      ref="otherVersion"
                      v-model="submitData.otherVersionIds"
                      filterable
                      multiple
                      remote
                      reserve-keyword
                      placeholder="请输入其他版本关键词"
                      :remote-method="getOtherVersion"
                      value-key="title"
                      @change="changeOtherVersion"
                      :loading="otherLoading">
                      <el-option
                        v-for="item in versionList"
                        :key="item.id"
                        :label="item.title"
                        :value="{id:item.id,title: item.title}">
                      </el-option>
                    </el-select>
                  </el-col>
                </el-row>
              </el-form-item>
            </el-col>
            <!-- <el-col :span="24">
              <el-form-item label="显示选项">
                <el-checkbox v-model="submitData.appVisible" style="width: 45%" :true-label="1" :false-label="0">APP显示</el-checkbox>
                <el-checkbox v-model="submitData.pcVisible" style="width: 45%" :true-label="1" :false-label="0">PC显示</el-checkbox>
                <el-row type="flex" style="margin-top: 5px;" v-permission="['/guider','recommend']">
                  <el-checkbox v-model="submitData.recommend" style="width: 45%" :true-label="1" :false-label="0">推荐</el-checkbox>
                  <ms-picker :model.sync="submitData.recommendEndTime" type="datetime"></ms-picker>
                </el-row>
                <el-row type="flex" style="margin-top: 5px;" v-permission="['/guider','sticky']">
                  <el-checkbox v-model="submitData.sticky" style="width: 45%" :true-label="1" :false-label="0">固顶</el-checkbox>
                  <ms-picker :model.sync="submitData.stickyEndTime" type="datetime"></ms-picker>
                </el-row>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="权限" prop="opened">
                <el-select v-model="submitData.opened" style="width: 100%">
                  <el-option :value="1" label="开放下载"></el-option>
                  <el-option :value="0" label="积分下载"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="积分" prop="integral" v-show="+submitData.opened === 0">
                <el-input-number v-model="submitData.paymentAmount" :min="0" style="width: 100%"></el-input-number>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="关键词" prop="tagList">
                <MsTagSearch v-model="submitData.tagList" :notMul="false"></MsTagSearch>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="资讯拓展">
                <el-row>
                  <el-col :span="18"><MsTagSearch v-model="submitData.articleKeyword" style="width: 95%;display: inline-block;" placeholder="资讯标题关键词" :modelId.sync="submitData.articleKeywordId" :notMul="true"></MsTagSearch></el-col>
                  <el-col :span="6"><el-input placeholder="数量" style="width: 100%" v-model="submitData.articleKeywordNum"></el-input></el-col>
                </el-row>
              </el-form-item>
              <el-form-item label="指南拓展">
                <el-row>
                  <el-col :span="18"><MsTagSearch v-model="submitData.guiderKeyword" style="width: 95%;display: inline-block;" placeholder="指南标题关键词" :modelId.sync="submitData.guiderKeywordId" :notMul="true"></MsTagSearch></el-col>
                  <el-col :span="6"><el-input placeholder="数量" style="width: 100%" v-model="submitData.guiderKeywordNum"></el-input></el-col>
                </el-row>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="封面图片" prop="cover">
                <ms-single-image v-model="submitData.cover"></ms-single-image>
              </el-form-item>
            </el-col>
            <el-col :span="24" style="text-align: left">
              <el-form-item label="自动水印" prop="waterMark" style="display: inline-block;">
                  <el-switch v-model="submitData.waterMark" :disabled="formatDisabled" :active-value="1" :inactive-value="0"></el-switch>
              </el-form-item>
            </el-col> -->
          </el-row>
        </el-col>
      </el-row>
    </el-form>
  </section>
</template>

<script>
import MsEditor from '@/components/MsEditor'
import MsAssociationSearch from '@/components/MsCommon/ms-association-search'
import MsTagSearch from '@/components/MsCommon/ms-tag-search'
import { getEditContent } from '@/utils/index'
import { mapGetters } from "vuex";
import {ms_rule_url, ms_rule_upload_url} from "@/utils/form-rule.js";
import { getSession } from "@/utils/auth";
import moment from 'moment'
export default {
	name: "ms-guider-content",
	data () {
		return {
      testLoading: false,
      payData: [],
      loading: false,
      versionList: [],
      otherLoading: false,
      rules: {
        title: [
          { required: true, message: "请填写文章标题", trigger: 'blur' }
        ],
        // enTitle: [
        //   { required: true, message: "请填写文章标题", trigger: 'blur' }
        // ],
        content: [
          { required: true, message: "请填写文章内容", trigger: 'change' }
        ],
        linkOutUrl: [
          { validator: ms_rule_url, trigger: 'blur' }
        ],
        originalUrl: [
          { validator: ms_rule_url, trigger: 'blur' }
        ],
        fileUrl: [
          { required: true,validator: ms_rule_upload_url, trigger: 'blur' }
        ]
        // guiderFrom: [
        //   { required: true, message: "请填写文章来源", trigger: 'change' }
        // ]
      },
      formatDisabled: false,
      summaryKeyUp: false,
      pickerOptions: {
        disabledDate: time => {
          let delay = new Date().getTime() - 86400000
          if(delay){
            // 小于接口返回的值得日期全部禁选
            return time.getTime() < delay
          }
        }
      }
		}
  },
  components: {
    MsEditor,
    MsTagSearch,
    MsAssociationSearch
  },
  props:["submitData"],
  computed: {
    ...mapGetters(["info", "localCode"])
  },
  watch: {
    'submitData.id': function (val) {
      if (val) {
        this.formatDisabled = true
      } 
    },
    'submitData.associationName': function (val) {
      
      if (val && val.indexOf('中华医学会') !== -1) {
        this.submitData.copyright = 1
      }
    },
    'submitData.recommend': function (val) {
      if (val === 1) {
        this.submitData.sticky = 0
      }
    },
    'submitData.sticky': function (val) {
      if (val === 1) {
        this.submitData.recommend = 0
      }
    },
    'submitData.content': function (val) {
      if (val) {
        if (!this.submitData.id && !this.summaryKeyUp) {
          this.submitData.summary = this.PUBLIC_Methods.unexcapeHtml(getEditContent(val))
        }
      }
    }
  },
  created() {
    this.submitData.editor = this.info.userName
  },
	methods: {
    change(val){
      if(!val){
        this.submitData.publishedTime = val
      } else {
        let time = moment(val).format("YYYY-MM-DD HH:mm") + ':59'
        if(new Date(time).getTime()<new Date().getTime()){
          this.$message({
            message: '不能选择当前时间之前的时间',
            type: 'warning'
          })
          this.submitData.publishedTime = getSession('guider-publishedTime')
        }else {
          this.submitData.publishedTime = moment(val).format("YYYY-MM-DD HH:mm") + ':00'
        }
      }
    },
    // 数据校验
    // validateData(callback, isDraft = false) {
    //   // let expandRead = '';
    //   let fileList = []
    //   if (this.submitData.attachmentList.length > 0) {
    //     this.submitData.attachmentList.forEach(v => {
    //       fileList.push({
    //         attachmentKey: v.fileKey,
    //         attachmentUrl: v.url,
    //         fileName: v.name
    //       })
    //     });
    //   }
    //   let params = {
    //     ...this.submitData,
    //     userId: this.info.userId,
    //     username: this.info.userName,
    //     editorId: this.info.userId,
    //     editor: this.info.userName,
    //     content: this.PUBLIC_Methods.excapeHtml(this.submitData.content),
    //     attachmentList: fileList,
    //     approvalStatus: isDraft ? 2 : this.submitData.approvalStatus === 2 ? 0 : this.submitData.approvalStatus
    //   }
    //   this.$refs["submitRef"].validate( valid => {
    //     if (valid) {
    //       callback(params)
    //     }
    //   })
    // },
    validateData(isDraft = false) {
      return new Promise((resolve) => {
        // let fileList = []
        // if (this.submitData.attachmentList.length > 0) {
        //   this.submitData.attachmentList.forEach(v => {
        //     fileList.push({
        //       attachmentKey: v.fileKey,
        //       attachmentUrl: v.url,
        //       fileName: v.name
        //     })
        //   });
        // }
        // this.submitData.attachmentList = fileList
        this.submitData.approvalStatus = isDraft ? 2 : this.submitData.approvalStatus === 2 ? 0 : this.submitData.approvalStatus
        console.log(this.submitData.summary)
        this.$refs["submitRef"].validate( valid => {
          if (valid) {
            resolve()
          }else {
            this.$emit('changeTab','content')
          }
        })
      })
    },
    getCoureByTitle(query) {
      this.loading = true
      if(query !== '') {
        let searchParams = {
          title: query,
          type: 1
        }
        this.api.getCoureByTitle(searchParams).then(response => {
          this.loading = false
          this.payData = response.data || []
          if (response.status !== 200) {
            this.PUBLIC_Methods.apiNotify(
              response.message || "请求出错",
              "warning"
            )
          }
        })
        .catch((err) => (console.log(err)))
      } else {
        this.payData = []
      }
    },
    getOtherVersion(query) {
      this.otherLoading = true
      if(query !== '') {
        let searchParams = {
          value: query,
          pageIndex: 1,
          pageSize: 20,
          approvalStatus: 1
        }
        this.api.getToolGuiderPage(searchParams).then(response => {
          this.otherLoading = false
          this.versionList = response.data || []
          if (response.status !== 200) {
            this.PUBLIC_Methods.apiNotify(
              response.message || "请求出错",
              "warning"
            )
          }
        })
        .catch((err) => (console.log(err)))
      } else {
        this.versionList = []
      }
    },
    changeCoure(val) {
      console.log(val,'1111')
    },
    changeOtherVersion(val) {
      console.log(val, '2222')
    },
	}
}
</script>

<style scope lang="scss">
.info-form{
  /deep/ .el-checkbox {
    margin-right: 0px;
  }
  &-right {
    /deep/ .el-form-item {
      margin-bottom: 16px;
    }
  }
  .expand {
    .label {
      color: #409EFF; 
      font-size: 12px;
      width: 40px;
      display: inline-block;
      text-align: right;
    }
    /deep/ .el-form-item {
      padding: 4px 0;
      margin-bottom: 14px;
      margin-top: -4px;
      background-color: #E5E6E9;
      border-radius: 4px;
    }
  }
}
</style>
