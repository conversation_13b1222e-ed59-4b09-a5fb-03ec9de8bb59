<template>
  <section>
    <el-form ref="submitRef"
          class="rule-form"
          :model="submitData"
          :rules="rules"
          label-width="100px">
      <el-row :gutter="20">
        <el-col :span="18">
          <el-row>
            <el-col :span="24">
              <el-form-item label="关键词" prop="tagList" style="width: 65%">
                <MsTagSearch v-model="submitData.tagList" :notMul="false"></MsTagSearch>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="显示选项">
                <el-checkbox v-model="submitData.appVisible" style="width: 100px" :true-label="1" :false-label="0">APP显示</el-checkbox>
                <el-checkbox v-model="submitData.pcVisible" style="width: 100px" :true-label="1" :false-label="0">官网首页</el-checkbox>
                <el-row type="flex" style="margin-top: 5px;" v-permission="['/guider','recommend']">
                  <el-checkbox v-model="submitData.recommend" style="width: 100px" :true-label="1" :false-label="0">推荐</el-checkbox>
                  <ms-picker :model.sync="submitData.recommendEndTime" type="datetime"></ms-picker>
                </el-row>
                <el-row type="flex" style="margin-top: 5px;" v-permission="['/guider','sticky']">
                  <el-checkbox v-model="submitData.sticky" style="width: 100px" :true-label="1" :false-label="0">固顶</el-checkbox>
                  <ms-picker :model.sync="submitData.stickyEndTime" type="datetime"></ms-picker>
                </el-row>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="权限" prop="opened" style="width: 65%">
                <el-select v-model="submitData.opened" style="width: 100%" @change="changeOpened">
                  <el-option :value="1" label="开放下载"></el-option>
                  <el-option :value="0" label="付费下载"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="24" style="text-align: left" v-if="+submitData.opened === 0">
              <el-col :span="6">
                <el-form-item label="付费会员专属" prop="isPrivilege" style="display: inline-block">
                  <el-switch v-model="submitData.isPrivilege" :active-value="1" :inactive-value="0"></el-switch>
                </el-form-item>
              </el-col>
              <el-col :span="18">
                <el-form-item v-if="submitData.isPrivilege == 1" label=" " label-width="20px" :prop="submitData.isPrivilege == 1 ? 'memberCardIds' : ''" style="display: inline-block; width: 53.3%">
                  <el-select v-model="submitData.memberCardIds" clearable placeholder="请选择会员" multiple style="width: 100%">
                    <el-option  v-for="(item,index) in memberList" :key="index" :label="item.cardName" :value="item.id"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-col>
            <el-col :span="24">
              <p style="text-align:left;padding-left:100px;margin-top:-10px;margin-bottom:10px;color:red">提示：指南付费会员专属，必须勾选全部指南会员，否则会导致部分会员用户下载不成功！</p>
            </el-col>
            <el-col :span="24">
              <el-form-item label="积分" prop="integral" v-show="+submitData.opened === 0" style="width: 300px">
                <el-input-number v-model="submitData.paymentAmount" :min="0" style="width: 100%"></el-input-number>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="指南封面" prop="cover">
                <ms-single-image v-model="submitData.cover" :upFileSize="0.5"></ms-single-image>
              </el-form-item>
            </el-col>
            <el-col :span="24" style="text-align: left">
              <el-form-item label="自动水印" prop="waterMark" style="display: inline-block;">
                  <el-switch v-model="submitData.waterMark" :disabled="formatDisabled" :active-value="1" :inactive-value="0"></el-switch>
              </el-form-item>
            </el-col>
          </el-row>
        </el-col>
      </el-row>
    </el-form>
  </section>
</template>

<script>
import MsTagSearch from '@/components/MsCommon/ms-tag-search'
export default {
	name: "ms-guider-content",
	data () {
    var validateMember = (rule, value, callback) => {
      if (this.submitData.opened === 0 && this.submitData.isPrivilege == 1 && this.submitData.memberCardIds.length < 1) {
        callback(new Error('请选择'));
      } else {
        callback();
      }
    };
		return {
      rules: {
        memberCardIds: [
          { required: true, validator: validateMember, trigger: 'change' }
        ],
      },
      formatDisabled: false,
      memberList: [],
		}
  },
  components: {
    MsTagSearch
  },
  props:["submitData"],
  watch: {
    'submitData.id': function (val) {
      if (val) {
        this.formatDisabled = true
      } 
    },
  },
  created() {
    this.initMemberList()
  },
	methods: {
    initMemberList() {
      let searchParams = {
        pageIndex: 1,
        pageSize: 99,
        status: 1,
        propertyName: "指南会员"
      }
      this.api.memberCardQuery(searchParams).then(response => {
        this.memberList = response.data || []
        if (response.status !== 200) {
          this.PUBLIC_Methods.apiNotify(
            response.message || "请求出错",
            "warning"
          )
        }
      })
      .catch((err) => (console.log(err)))
    },
    changeOpened() {
      this.submitData.paymentAmount = 0
    },
    // 数据校验
    validateData() {
      return new Promise((resolve) => {
        this.$refs["submitRef"].validate( valid => {
          if (valid) {
            resolve()
          }else {
            this.$emit('changeTab','setting')
          }
        })
      })
    }
	}
}
</script>
<style scoped>
.flex-item /deep/ .el-form-item__content {
  display: flex;
  flex-direction: row;
}
.tips {
  font-size: 14px;
  margin-top: 6px;
  color: #999;
  text-align: right;
}
</style>