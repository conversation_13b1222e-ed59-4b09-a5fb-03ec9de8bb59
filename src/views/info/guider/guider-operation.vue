<template>
  <section class="form-tab" v-loading="getLoading">
    <el-tabs v-model="activeName">
      <el-tab-pane label="指南详情" name="content">
        <ms-guider-content ref="contentTemp" @handle-click="handle_click" @changeTab="changeTab" :submitData.sync="submitData"></ms-guider-content>
      </el-tab-pane>
      <el-tab-pane label="设置" name="setting">
        <ms-guider-setting ref="setting" @changeTab="changeTab" :submitData.sync="submitData"></ms-guider-setting>
      </el-tab-pane>
      <el-tab-pane label="显示栏目" name="category">
        <ms-info-setting :categoryModel.sync="submitData.categoryList" :drainageModel.sync="submitData.drainageInfoDtoList" headerShow :categoryConfigChild="{moduleName: 'guider'}"></ms-info-setting>
      </el-tab-pane>
    </el-tabs>
<!-- aa -->
    <!-- 弹出层 -->
    <el-dialog :visible.sync="dialog" 
               closeable 
               :show-close="false"
               :close-on-click-modal="false"
               width="50%"
               :title="dialogTitle">
      <component :is="dialogComponent" 
                   :model="dialogInfo" 
                   :operation="dialogOperation" 
                   :isNeedApi="false"
                   @close="closeDialog" 
                   @up-date="init" 
                   v-if="dialog"></component>
    </el-dialog>
    <!-- 提交按钮 -->
    <footer-tool-bar v-loading="buttonLoading">
      <template slot="tool-content">
        <!-- <el-button plain v-show="isCanEdit" v-permission="['/guider','draft']" @click="info_operation('draft')">
          提交草稿
          <el-tooltip class="item" effect="light" content="草稿只允许重新提交、编辑、删除" placement="top"><i class="el-icon-info"></i></el-tooltip>
        </el-button>
        <el-button plain v-show="guiderId && submitData.approvalStatus !== 2" @click="info_operation('approval')" v-permission="['/guider','status']">{{submitData.approvalStatus === 1 ? '去审' : '审核'}}</el-button>
        <el-button type="primary" v-show="isCanEdit" @click="info_operation('save')" v-permission="['/guider','edit']">保存</el-button>
        <el-button type="info" @click="info_operation('back')">返回</el-button> -->

        <!-- 提交草稿 -->
        <el-button plain v-show="!(guiderId && submitData.approvalStatus === 1) && isCanEdit"  v-permission="['/guider','draft']" @click="info_operation('draft')">
          存为草稿
          <el-tooltip class="item" effect="light" content="草稿只允许存为草稿、保存、在详情页审核、删除" placement="top"><i class="el-icon-info"></i></el-tooltip>
        </el-button>

        <!-- 审核 -->
        <el-button plain v-show="guiderId && isCanEdit" @click="info_operation('approval')" v-permission="['/guider','status']">{{submitData.approvalStatus === 1 ? '去审文章' : '审核文章'}}</el-button>

        <!-- 保存 -->
        <el-button type="primary" v-show="isCanEdit" @click="info_operation('save')" v-permission="['/guider','edit']">保存</el-button>

        <!-- 返回 -->
        <el-button type="info" @click="info_operation('back')">返回</el-button>
      </template>
    </footer-tool-bar>
  </section>
</template>

<script>
import msGuiderContent from './tool/ms-guider-content'
import msInfoSetting from '@/components/MsCommon/ms-info-setting'
import msGuiderSetting from "./tool/ms-guider-setting"
import guiderMixin from "./guider-mixin"
import FooterToolBar from '@/components/ToolBar/footer-tool-bar'
import { mapGetters } from "vuex";
import {parseTime} from "@/utils"
import { setSession } from "@/utils/auth";
export default {
  name: "guider-operation",
  mixins: [guiderMixin],
	data () {
		return {
      activeName: 'content',
      guiderId: this.$route.query.id ? this.$route.query.id : 0,
      buttonLoading: false,
      getLoading: false,
      isCanEdit: true,

      userInfo: this.$store.getters.info,

      submitData: { // => userId、username、integral、waterMark、formatted 、expandRead (保存添加属性)
        userId: '', // => 用户Id
        username: '', // => 用户名称 (string)
        title: '', // => 标题 (string)
        enTitle: '', // => 英文标题 (string)
        authorId: 0, // =>作者id (long)
        author: '', // => 作者 (string)
        opened: 0, // => 开放阅读 (0.不开放 1.开放)
        paymentType: '', // => 支付方式
        paymentAmount: '', // => 支付金额
        originalUrl: '', // => 原始链接 (string)
        linkOutUrl: '', // => 外向链接 (string)
        pcVisible: 1, // => PC可见 (0.不可见,1.可见)
        appVisible: 1, // => APP可见 (0.不可见,1.可见)
        recommend: 0, // => 推荐（0.不推荐 1.推荐）
        recommendEndTime: '', // => 推荐结束时间 
        sticky: 0, // => 置顶（0.不置顶 1.置顶）
        stickyEndTime: '', // => 置顶结束时间 (date-time)
        guiderFrom: '', // => 来源 (string)
        journalId: 0, // => 所属期刊id
        associationId: 0, // => 协会Id
        associationName: '',
        guiderPublishedTime: '', // => 指南发布时间
        guiderType: 0, // => 0: 单篇 1: 合集
        guiderArea: '', // => 指南所属领域
        guiderLanguage: 0, // => 0: 中文 1: 英文
        guiderRegion: '3', // => 区域
        content: '', // => 文章内容 (string)
        copyright: 0, // => 0.无版权限制 1.有版权限制
        summary: '', // => 摘要 (string)
        editorId: 0, // => 编辑人id (long)
        editor: '', // => 编辑人名称 (string)
        publishedTime: parseTime(new Date), // => 发布时间 (date-time)
        waterMark: 0, // 自动水印 (0.无水印,1.加水印)
        formatted: 0, // 去复杂格式 (0.不去格式,1.去格式)
        cover: '', // 封面 (string)
        attachmentList: [], // => 附件
        fileUrl: '',
        expandRead: '', // => 拓展阅读
        articleKeywordId: 0, // => 资讯拓展关键词Id
        articleKeyword: '', // => 资讯拓展关键词
        articleKeywordNum: 6, // => 拓展条数
        guiderKeywordId: 0, // => 指南扩张关键词Id
        guiderKeyword: '', // => 指南拓展关键词
        guiderKeywordNum: 6, // => 指南拓展条数
        approvalStatus: 0, // => 发布状态 (0.待审批 1.审批通过, 2.草稿)
        tagList: [], // => 标签集合
        categoryList: [{categoryId: 85, categoryName: "指南&解读"}], // => 专题分类集合
        isPrivilege: 0, // 付费会员免费
        memberCardIds: [], //付费会员ids
        relationCourseIds: [], // 关联课程
        otherVersionIds: [], // 其他选项
        drainageInfoDtoList:[]  //引流信息列表
      },

      dialogComponent: '',
      dialog: false,
      dialogTitle: '',
      dialogOperation: '',
      dialogInfo: {}
		}
  },
  computed: {
    ...mapGetters(["info"]),
  },
  created() {
    this.init()
  },
  components: {
    msGuiderContent,
    msInfoSetting,
    msGuiderSetting,
    FooterToolBar
  },
  methods: {
    init() {
      let id = this.guiderId
      this.dialog = false;
      if(id !== 0) {
        this.getLoading = true;
        this.submitData.id = id;
        this.api.getToolGuiderById({id: id}).then( response => {
          this.getLoading = false;
          if(response.status === 200) {
            let res = response.data
            let file = []
            if (res.attachmentList && res.attachmentList.length > 0) {
              res.attachmentList.forEach(v => {
                file.push({
                  name: v.fileName || v.attachmentKey,
                  url: v.attachmentUrl,
                  fileKey: v.attachmentKey
                })
              });
            }
            this.submitData = {
              ...this.submitData,
              ...res,
              guiderRegion: res.guiderRegion && res.guiderRegion !== '0' ? res.guiderRegion : null,
              content: this.PUBLIC_Methods.unexcapeHtml(res.content),
              attachmentList: file,
              memberCardIds: res.memberCards && res.memberCards.length ? res.memberCards.map(i=> {return i.id}) : []
            }
            this.remoteSearchDetail(res.courseDetails,this.submitData.relationCourseIds,'relationVersion')
            this.remoteSearchDetail(res.otherVersionGuiders,this.submitData.otherVersionIds,'otherVersion')
            setSession('guider-publishedTime', this.submitData.publishedTime)
            if (this.userInfo.roleLevel === 3 && this.submitData.createdBy !== this.userInfo.userId) { this.isCanEdit = false }
          } else {
            this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
          }
        }).catch(() => {
          this.getLoading = false;
        })
      }
    },
    changeTab(val) {
      this.activeName = val
    },
    // 远程搜索回显
    remoteSearchDetail(array,content,selectName) {
      let str = array && array.length ? array: []
      let courseIds = array && array.length ? array.map(i=> {return i.id}) : []
      let courseTitles = array && array.length ? array.map(i=> {return i.title}) : []
      for (let i=0; i<str.length; i++) {
        content.push({ //data中赋值
          id: courseIds[i],
          title: courseTitles[i]
        })
        this.$refs.contentTemp.$refs[selectName].cachedOptions.push({
          currentLabel: courseTitles[i],
          currentValue: {
            id: courseIds[i],
            title: courseTitles[i]
          },
          label:courseTitles[i],
          value: {
            id: courseIds[i],
            title: courseTitles[i]
          },
        })
      }
    },
    info_operation(val) {
      switch (val) {
        case 'save': 
          try {
             Promise.all([
              this.$refs['setting'].validateData(),
              this.$refs['contentTemp'].validateData()
            ]).then(() => {
              this.guiderId ? this.updateGuider(): this.createGuider();
            });
          } catch (error) {
            return;
          }
          break;
        case 'draft':
          try {
             Promise.all([
              this.$refs['setting'].validateData(),
              this.$refs['contentTemp'].validateData(true)
            ]).then(() => {
              this.guiderId ? this.updateGuider(): this.createGuider();
            });
          } catch (error) {
            return;
          }
          break;
        case 'back':
          this.$router.back();
          break;
        case 'approval':
          var params = {
            way: 'dialog',
            operation: this.submitData.approvalStatus === 1 ? 'toreview' : 'approval',
            component: 'msGuiderOperation',
            data: {
              id: this.guiderId,
              title: this.submitData.title
            }
          }
          this.handle_click(params)
          break;
        default: break;
      }
    },
    createGuider() {
      this.buttonLoading = true;
      if(this.submitData.drainageInfoDtoList && this.submitData.drainageInfoDtoList.length != 0 && this.submitData.drainageInfoDtoList[0].categoryName){
        this.submitData.drainageInfoDtoList = [{
          categoryId:this.submitData.drainageInfoDtoList[0].categoryId,
          switchDrainage:1,
          projectName:this.submitData.drainageInfoDtoList[0].categoryName,
          url:this.submitData.drainageInfoDtoList[0].url+'/guider/',
        }]
        if(this.submitData.drainageInfoDtoList[0].url.indexOf('https://') == -1 && this.submitData.drainageInfoDtoList[0].url.indexOf('http://') == -1){
          this.submitData.drainageInfoDtoList[0].url = 'https://' + this.submitData.drainageInfoDtoList[0].url
        }
      }else{
        this.submitData.drainageInfoDtoList = []
      }
      // console.log(this.submitData,'asdf');
      let paramsData = {
        ...this.submitData,
        memberCardIds: this.submitData.isPrivilege == 1 ? this.submitData.memberCardIds : [],
        content: this.PUBLIC_Methods.excapeHtml(this.submitData.content),
        userId: this.info.userId,
        username: this.info.userName,
        categoryList: this.submitData.categoryList
      }
      this.api.saveToolGuider(paramsData).then(response => {
        this.buttonLoading = false
        if(response.status === 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
          this.$router.back()
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
      }).catch(() => this.buttonLoading = false)
    },
    updateGuider() {
      this.buttonLoading = true;
      if(this.submitData.drainageInfoDtoList && this.submitData.drainageInfoDtoList.length != 0 && this.submitData.drainageInfoDtoList[0].categoryName){
        this.submitData.drainageInfoDtoList = [{
          categoryId:this.submitData.drainageInfoDtoList[0].categoryId,
          switchDrainage:1,
          projectName:this.submitData.drainageInfoDtoList[0].categoryName,
          url:this.submitData.drainageInfoDtoList[0].url+'/guider/'+this.submitData.encryptionId,
        }]
        if(this.submitData.drainageInfoDtoList[0].url.indexOf('https://') == -1 && this.submitData.drainageInfoDtoList[0].url.indexOf('http://') == -1){
          this.submitData.drainageInfoDtoList[0].url = 'https://' + this.submitData.drainageInfoDtoList[0].url
        }
      }else{
        this.submitData.drainageInfoDtoList = []
      }
      // console.log(this.submitData,'QWER');
      let paramsData = {
        ...this.submitData,
        memberCardIds: this.submitData.isPrivilege == 1 ? this.submitData.memberCardIds : [],
        content: this.PUBLIC_Methods.excapeHtml(this.submitData.content),
        userId: this.info.userId,
        username: this.info.userName,
        categoryList: this.submitData.categoryList
      }
      this.api.updateToolGuiderById(paramsData).then(response => {
        this.buttonLoading = false
        if(response.status === 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
          this.$router.back()
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
      }).catch(() => this.buttonLoading = false)
    },
    handle_click(val) {
      switch (val.way) {
        case 'dialog': 
          this.dialog = true;
          this.dialogInfo = val.data
          this.dialogOperation = val.operation;
          this.dialogComponent = val.component;
          this.dialogTitle = val.title;
          break;
        default: break;
      }
    },
    closeDialog(val) {
      this.dialog = !this.dialog
      if (val) {
        if (val.operation === 'recommend') {
          this.submitData.recommend = 0
        } else if (val.operation === 'sticky') {
          this.submitData.sticky = 0
        }
      }
    }
  }
}
</script>
