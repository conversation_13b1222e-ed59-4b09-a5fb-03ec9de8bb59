<template>
	<ms-table
		:currentPage="searchParams.pageIndex"
		:loading="loading"
		:operationButtons="domConfig.tableButtons"
		:pageSize="searchParams.pageSize"
		:scopeConfig="scopeConfig.show"
    :scopeHeader="scopeConfig.headerShow"
		:tableData="list"
		:tableHeader="domConfig.tableHeader"
		:total="total"
    :showSelection="true"
		@current-change="current_change"
		@operation-change="operation_change"
		@size-change="size_change"
    @header-operation="header_operation"
    @handleSelectionChange="handleSelectionChange"
    class="table-svg high-row-none"
    :rowColor="rowColor"
	>
    <!-- 列表搜索去区域插槽 -->
		<template slot="ms-table-header">
			<div class="slot-search">
				<template v-for="(searchItem, key) in domConfig.listSearch">
					<component
						:index="searchItem.index || ''"
						:is="searchItem.component"
						:key="key"
            :width="searchItem.width || '150px'"
            :model.sync="searchParams[searchItem.model]"
            :label="searchItem.label"
						:operation="searchItem.operation || ''"
						:options="searchItem.options || []"
						:placeholder="searchItem.placeholder || ''"
						:type="searchItem.type || ''"
            :multiple="searchItem.multiple"
            :disabled="searchItem.disabled" 
            :config="searchItem.config"
            v-if="key < search_number"
					></component>
				</template>
        <div class="inlineBlock">
          <el-button @click="handleClick('query')" type="primary" plain icon="el-icon-search">查询</el-button>
          <el-button @click="handleClick('reset')">重置</el-button>
          <el-button @click="search_number = search_number === 99 ? 3 : 99">{{search_number === 99 ? '收起' : '展开'}} <i :class="search_number === 99 ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i></el-button>
        </div>
        
			</div>
			<div class="slot-button-article clearfix">
				<template v-for="(item, index) in domConfig.soltButtons">
					<el-button :key="index" 
                     :type="item.type" 
                     size="mini"
                     :icon="item.icon"
                     v-show="!item.roleDisabled"
                     @click="operation_change({operation: item})"
                     plain>{{ item.label }}</el-button>
				</template>
        <el-alert class="article-total" :title="`共搜索到${total}篇指南`" type="info" show-icon></el-alert>
			</div>
			<el-dialog :visible.sync="dialog" 
                 closeable 
                 show-close
                 :close-on-click-modal="false"
                 :width="dialogWidth"
                 :title="dialogTitle">
				<component :is="dialogComponent" 
                   :model="scopeInfo" 
                   :operation="dialogOperation" 
                   @close="dialog = !dialog" 
                   @up-date="init" 
                   v-if="dialog"></component>
			</el-dialog>
		</template>
	</ms-table>
</template>

<script>
import guiderMixin from "./guider-mixin"
import tableMixins  from "../../common/mixins/table"
import msGuiderCategory from "./tool/ms-guider-category"
export default {
  name: "ms-guider-manage",
  mixins: [tableMixins,guiderMixin],
  components: {
    msGuiderCategory
  },
  data () {
    return {
      notifyInst: null,
      tipFlag: true,
      search_number: 3
    }
  },
  created() {
    this.tipFlag = this.$store.getters.infoListTip.guiderTip
    if (this.tipFlag) {
      this.notifyInst = this.$notify({
        position: 'bottom-left',
        dangerouslyUseHTMLString: true,
        message: '<div><p style="color:#F56C6C;">红色栏：首页推荐（至多轮播5条）</p><p style="color: #E6A23C">黄色栏：首页固顶（至多置顶5条）</p><p style="color: #50bfff">蓝色栏：PC端不显示</p></div>',
        duration: 10000,
        customClass: 'notify-info'
      });
      this.$store.dispatch('SetInfoTip','guiderTip')
    }
  },
  beforeDestroy() {
    if (this.tipFlag) this.notifyInst.close()
  },
  methods: {
    rowColor({row}) {
      if (row.recommend === 1) {
        return 'recommend-row';
      } else if (row.sticky === 1) {
        return 'sticky-row';
      } else if (row.recommend !== 1 && row.sticky !== 1 && row.pcVisible === 0) {
        return 'pc-visible'
      }
      return '';
    },
    apiInit (params) {
      let searchParams = {...params}
      if (searchParams.createTime) {
        searchParams.publishedStartTime = searchParams.createTime[0] || ''
        searchParams.publishedEndTime = searchParams.createTime[1] || ''
      }
      this.api.getToolGuiderPage(searchParams).then(response => {
        this.loading = false
        this.total = response.totalSize || 0;
        this.list = response.data || []
        if (response.status !== 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
      }).catch(() => this.loading = false)
    },
    operation_change_module(val) {
      switch (val.operation.way) {
        case "cache":
          this.api.clearCache({}).then(response => {
            if (response.status) {
              this.$message({
                message: '前台指南缓存已清除',
                type: 'success'
              });
            }
          })
          break;
        default: break;
      }
    }
  }
};
</script>
