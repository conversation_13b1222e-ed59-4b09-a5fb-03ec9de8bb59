// import store from '@/store'
const domConfig = {
  listSearch: [
    {
      label: '标题',
      placeholder: '请输入',
      model: 'value',
      component: 'ms-input'
    },
    {
      label: '创建人',
      placeholder: '请选择',
      model: 'createdBy',
      component: 'ms-createby-search'
    },
    {
      label: '创建时间',
      placeholder: '请选择时间段',
      model: 'createTime',
      component: 'ms-picker',
      type: 'daterange',
      width: '240px'
    },
    {
      label: '来源',
      placeholder: '请输入',
      model: 'guiderFrom',
      component: 'ms-input'
    },
    {
      label: '专题',
      placeholder: '请选择',
      model: 'classify',
      component: 'ms-category-cascader',
      config: {
        moduleName: 'guider'
      }
    }
  ],
  tableHeader: [
    // { label: '', property: 'recommend', width: '25' },
    // { label: '', property: 'sticky', width: '25' },
    { label: 'ID', property: 'id', sortable: true, width: '60' },
    { label: '标题', property: 'title', width: '250'},
    { label: '点/APP/PC/赞/享/评', property: 'fields', width: '160' },
    { label: '创建人', property: 'createdName' },
    { label: '发布时间', property: 'publishedTime', sortable: true, width: '130' },
    // { label: '创建时间', property: 'createdTime', sortable: true, width: '130' },
    { label: '状态', property: 'approvalStatus'}
  ],
  tableButtons: [
    {
      label: '编辑',
      icon: '',
      role: '',
      operation: 'edit',
      component: 'guider-operation',
      way: 'page',
      type: 'primary',
      path: 'guider-operation',
      params: ['id'],
      identify: 'edit'
    },
    {
      label: '',
      icon: '',
      role: '',
      operation: 'editStatus',
      component: 'msGuiderOperation',
      way: 'dialog',
      field: 'approvalStatus',
      rule: {
        1: { label: '去审', type: '', operation: 'toreview' },
        0: { label: '审核', type: 'success', operation: 'approval' }
      },
      identify: 'status'
    },
    {
      label: '删除',
      way: 'dialog',
      type: 'danger',
      operation: 'delete',
      component: 'msGuiderOperation',
      // showCallback: (val) => {
      //   if (store.getters.info.roleLevel === 3 && val.createdBy !== store.getters.info.userId) {
      //     return false
      //   } else {
      //     return true
      //   }
      // },
      identify: 'single_delete'
    },
    {
      label: '更多',
      operation: 'more',
      type: 'info',
      showCallback: (val) => {
        if (val.approvalStatus === 0 || val.approvalStatus === 1) {
          return true
        } else {
          return false
        }
      },
      children: [
        {
          label: '推荐',
          way: 'dialog',
          operation: 'recommend',
          component: 'msGuiderRecommend',
          title: '推荐文章',
          identify: 'recommend'
        },
        {
          label: '固顶',
          way: 'dialog',
          operation: 'sticky',
          component: 'msGuiderRecommend',
          title: '固定文章',
          identify: 'sticky'
        }
      ]
    }
  ],
  soltButtons: [
    { 
      label: '手工添加', 
      type: 'primary', 
      icon: 'el-icon-plus',
      operation: 'created',
      component: 'guider-operation',
      way: 'page',
      path: 'guider-operation',
      params: ['id'],
      identify: 'created'
    },
    { 
      label: '批量审核', 
      type: 'primary',
      operation: 'approval',
      component: 'msGuiderOperation',
      way: 'batch',
      identify: 'batch_approval'
    },
    { 
      label: '批量去审', 
      type: 'primary',
      operation: 'toreview',
      component: 'msGuiderOperation',
      way: 'batch',
      identify: 'batch_toreview'
    },
    { 
      title: '批量添加专题',
      label: '批量添加专题', 
      type: 'success',
      operation: 'add',
      component: 'msGuiderCategory',
      way: 'batch',
      identify: 'batch_add_category',
      width: '70%'
    },
    { 
      title: '批量移除专题',
      label: '批量移除专题', 
      type: 'success',
      operation: 'delete',
      component: 'msGuiderCategory',
      way: 'batch',
      identify: 'batch_delete_category',
      width: '70%'
    },
    { 
      label: '批量删除',
      type: 'info',
      icon: 'el-icon-close',
      operation: 'delete',
      component: 'msGuiderOperation',
      way: 'batch',
      identify: 'batch_delete'
    },
    {
      label: '回收站',
      type: 'info',
      operation: '',
      component: 'msGuiderRecycle',
      way: 'dialog',
      title: '指南回收站 (仅展示最近一个月指南信息)',
      width: '90%',
      identify: 'recycle'
    },
    {
      label: '清除缓存',
      type: 'info',
      operation: '',
      component: '',
      way: 'cache',
      icon: 'el-icon-delete',
      identify: 'clear_cache'
    }
  ]
}

export default domConfig;
