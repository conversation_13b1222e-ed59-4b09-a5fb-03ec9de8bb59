// import store from '@/store'
const domConfig = {
    listSearch: [
      {
        label: '标题',
        placeholder: '请输入',
        model: 'value',
        component: 'ms-input'
      },
      {
        label: '创建时间',
        placeholder: '请选择时间段',
        model: 'createTime',
        component: 'ms-picker',
        type: 'daterange',
        width: '240px'
      },
      {
        label: '来源',
        placeholder: '请输入',
        model: 'guiderFrom',
        component: 'ms-input'
      },
      {
        label: '专题',
        placeholder: '请选择',
        model: 'classify',
        component: 'ms-category-cascader',
        config: {
          moduleName: 'guider'
        }
      }
    ],
    tableHeader: [
      // { label: '', property: 'recommend', width: '25' },
      // { label: '', property: 'sticky', width: '25' },
      { label: 'ID', property: 'id', sortable: true, width: '60' },
      { label: '标题', property: 'title', width: '250'},
      { label: '点/APP/PC/赞/享/评', property: 'fields', width: '160' },
      { label: '创建人', property: 'createdName' },
      { label: '发布时间', property: 'publishedTime', sortable: true, width: '130' },
      // { label: '创建时间', property: 'createdTime', sortable: true, width: '130' },
      { label: '状态', property: 'approvalStatus'}
    ],
    tableButtons: [
      {
        label: '编辑',
        icon: '',
        role: '',
        operation: 'edit',
        component: 'workbenchGuider-operation',
        way: 'page',
        type: 'primary',
        path: 'workbenchGuider-operation',
        params: ['id'],
      },
      {
        label: '删除',
        way: 'dialog',
        type: 'danger',
        operation: 'delete',
        component: 'msGuiderOperation',
      }
    ],
    soltButtons: [
      { 
        label: '手工添加', 
        type: 'primary', 
        icon: 'el-icon-plus',
        operation: 'created',
        component: 'workbenchGuider-operation',
        way: 'page',
        path: 'workbenchGuider-operation',
        params: ['id'],
      },
      { 
        label: '批量删除',
        type: 'info',
        icon: 'el-icon-close',
        operation: 'delete',
        component: 'msGuiderOperation',
        way: 'batch',
      },
      {
        label: '回收站',
        type: 'info',
        operation: '',
        component: 'msGuiderRecycle',
        way: 'dialog',
        title: '指南回收站 (仅展示最近一个月指南信息)',
        width: '90%',
      }
    ]
  }
  
  export default domConfig;
  