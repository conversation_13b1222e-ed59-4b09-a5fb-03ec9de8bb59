const domConfig = {
  listSearch: [
    
  ],
  tableHeader: [
    { label: 'ID', property: 'id', sortable: true, width: 80 },
    { label: '文件名', property: 'excelOldName', width: 250 },
    { label: '上传人', property: 'createdName', width: 100},
    { label: '上传时间', property: 'createdTime', sortable: true, width: 120},
    { label: '状态', property: 'status'},
  ],
  tableButtons: [
    {
      label: '查看详情',
      type: 'primary',
      operation: 'view',
      way: 'dialog',
      component: 'journalImportList',
      position: 'right',
      title: '查看导入期刊',
      width: '80%',
      tdWidth: '90'
    },
    {
      label: '执行结果',
      type: 'primary',
      way: 'dialog',
      component: 'journalExec',
      title: '执行结果',
      tdWidth: '90',
      showCallback: (val) => {
        if (val.status === 2) {
          return true
        } else {
          return false
        }
      }
    }
  ]
}

export default domConfig;
