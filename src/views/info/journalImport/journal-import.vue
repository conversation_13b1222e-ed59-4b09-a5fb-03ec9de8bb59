<template>
	<ms-table
		:currentPage="searchParams.pageIndex"
		:loading="loading"
		:operationButtons="domConfig.tableButtons"
		:pageSize="searchParams.pageSize"
        :scopeHeader="scopeConfig.headerShow"
        :scopeConfig="scopeConfig.show"
		:tableData="list"
		:tableHeader="domConfig.tableHeader"
		:total="total"
		@current-change="current_change"
		@operation-change="operation_change"
		@size-change="size_change"
        @header-operation="header_operation"
	>
        <!-- 列表搜索去区域插槽 -->
		<template slot="ms-table-header">
			<div class="slot-search">
				<template v-for="(searchItem, key) in domConfig.listSearch">
					<component
						:index="searchItem.index || ''"
						:is="searchItem.component"
						:key="key"
                        :width="searchItem.width || '150px'"
                        :model.sync="searchParams[searchItem.model]"
                        :label="searchItem.label"
						:operation="searchItem.operation || ''"
						:options="searchItem.options || []"
						:placeholder="searchItem.placeholder || ''"
						:type="searchItem.type || ''"
                        :multiple="searchItem.multiple"
                        :disabled="searchItem.disabled" 
					></component>
				</template>
                <el-button type="success" plain @click="downTmp()">下载模版</el-button>
                <journal-upload @up-date="uploadSuccess" :disabled="uploadDisabled"></journal-upload>
                <el-button type="primary" plain @click="init()">历史记录</el-button>
                <div class="progress" v-show="showProgress">
                    <span>数据导入进度</span>
                    <el-progress :stroke-width="20" :percentage="percentage" :color="colors" ></el-progress>
                </div>
			</div>
			<el-dialog :visible.sync="dialog" 
                closeable 
                show-close
                :close-on-click-modal="false"
                :width="dialogWidth"
                :title="dialogTitle">
				<component :is="dialogComponent" 
                   :model="scopeInfo" 
                   :operation="dialogOperation" 
                   @close="dialog = !dialog" 
                   @up-date="init" 
                   v-if="dialog"></component>
			</el-dialog>
            <ms-right-dialog :visible.sync="r_dialog" :width="dialogWidth" :title="dialogTitle">
                <component
                    :is="dialogComponent"
                    :model="scopeInfo"
                    :operation="dialogOperation"
                    @close="r_dialog = !r_dialog"
                    @up-date="apiInit"
                    v-if="r_dialog"
                ></component>
            </ms-right-dialog>
		</template>
	</ms-table>
</template>

<script>
import domConfig from "./data/dom-config";
import scopeConfig from "./data/scope-config";
import tableMixins  from "../../common/mixins/table"
// import socketMixins  from "../../common/mixins/socket"
import journalImportList from "./tool/journal-import-list"
import journalExec from "./tool/journal-exec"
import journalUpload from "./tool/journal-upload"
export default {
    name: "journal-import",
    mixins: [tableMixins],
    data () {
        return {
            domConfig: domConfig,
            scopeConfig: scopeConfig,
            colors: [
                {color: '#409EFF', percentage: 99},
                {color: '#67C23A', percentage: 100}
            ],
            showProgress: false,
            percentage: 0,
            uploadDisabled: false
        }
    },
    components: {
        journalImportList,
        journalExec,
        journalUpload
    },
    mounted() {
        if (this.$store.getters.journalImportData.excelId) {
            this.viewStatus(this.$store.getters.journalImportData.excelId)
        }
    },
    methods: {
        apiInit () {
            let searchParams = {...this.searchParams}
            this.percentage = 0
            this.showProgress = false
            this.uploadDisabled = false
            this.api.getJournalExcelPage(searchParams).then(response => {
                this.loading = false
                this.total = response.totalSize || 0;
                this.list = response.data || []
            }).catch(() => this.loading = false)
        },
        uploadSuccess(data) {
            this.uploadDisabled = true
            this.$store.dispatch('SetJournalImport', {excelId: data.excelId})
            this.viewStatus(data.excelId)
        },
        operation_change_module (val) {
            switch (val.operation.way) {
                case "download":
                    window.location.href = val.model.excelUrl
                break;
                default: break;
            }
        },
        // 下载模版
        downTmp() {
            window.location.href = 'https://img.medsci.cn/Journal/JournalHome.xlsx'
        },
        // 查看执行状态
        viewStatus(id) {
            this.showProgress = true
            this.api.getProcessState({id: id}).then(res => {
                if (res.status === 200) {
                    if (res.data.status === 0) {
                        this.percentage = Math.floor(res.data.successSize/res.data.totalSize * 100)
                        setTimeout(()=> {
                            this.viewStatus(id)
                        }, 1000)
                    } else if (res.data.status === 1){
                        this.uploadDisabled = false
                        this.percentage = 100
                        this.$store.dispatch('SetJournalImport', {excelId: 0})
                        this.$alert('点击确定查看导入内容', '期刊导入成功', {
                            confirmButtonText: '确定',
                            callback: () => {
                                let params = {
                                    operation: {
                                        way: 'dialog',
                                        component: 'journalImportList',
                                        position: 'right',
                                        title: '查看导入期刊',
                                        width: '80%'
                                    },
                                    model: {
                                        id: id
                                    }
                                }
                                this.apiInit()
                                this.operation_change(params)
                            }
                        });
                    }
                } else {
                    this.showProgress = false
                    this.uploadDisabled = false
                    this.$store.dispatch('SetJournalImport', {excelId: 0})
                    this.$message({
                        message: '未获取到文件导入状态',
                        type: 'success'
                    });
                }
            })
        }
    }
};
</script>
