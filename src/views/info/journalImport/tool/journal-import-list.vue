<template>
    <ms-right-content>
        <el-steps :active="activeStep" finish-status="success" align-center class="ms-step" space="200px">
            <el-step description="excel已导入"></el-step>
            <el-step description="期刊数据匹配"></el-step>
            <el-step description="导入数据库"></el-step>
        </el-steps>
        <ms-table :currentPage="searchParams.pageIndex"
                :loading="loading"
                :pageSize="searchParams.pageSize"
                :tableData="list"
                :tableHeader="tableHeader"
                :total="total"
                :expandList="expandList"
                @current-change="current_change"
                @size-change="size_change" 
                :rowColor="rowColor"
                @rowClick="rowClick"
                :style="{height: 'calc(100vh - 145px)'}">
                <template slot="ms-table-header">
                    <div class="slot-button-article">
                        <ms-input width="150px" :model.sync="searchParams['value']" label="期刊名称" placeholder="缩写/全称/ISSN"></ms-input>
                        <ms-select-local width="150px" :model.sync="searchParams['isNew']" label="是否为新期刊" :disabled="journalStatus !== 0 ? false : true" :options="selectOptions" placeholder="请选择"></ms-select-local>
                        <el-button @click="init" type="primary" plain icon="el-icon-search">查询</el-button>
                        <el-button @click="reset">重置</el-button>
                        <el-alert class="article-total" v-if="journalStatus !== 0" title="黄色栏为旧期刊" type="warning" show-icon style="position: relative;top: -5px;"></el-alert>
                        <template v-if="journalStatus === 0">
                            <el-button type="primary" plain @click="journalMatch()" class="fr" style="margin-right: 16px;">期刊匹配</el-button>
                        </template>
                        <template v-else-if="journalStatus === 1">
                            <el-button type="primary" plain @click="journalImport()" :disabled="btnDisabled" class="fr" style="margin-right: 16px;">执行导入</el-button>
                        </template>
                        
                        <div class="progress" v-show="showProgress">
                            <span>数据导入进度</span>
                            <el-progress :stroke-width="20" :percentage="percentage" :color="colors" ></el-progress>
                        </div>
                    </div>
                    
                </template>
        </ms-table>
    </ms-right-content>
</template>

<script>
import serveUrl from '@/store/data/serveUrl.js'
import MsRightContent from "@/components/MsDialog/ms-right-content";
import { mapGetters } from "vuex";
export default {
	name: "journal-import-list",
	data () {
		return {
            loading: false,
            getLoading: false,
            searchParams: {
                pageSize: 20,
                pageIndex: 1,
                value: '',
                isNew: null
            },
            total: 0,
            list: [],
            tableHeader: [
                { label: 'ISSN', property: 'issn', width: '100' },
                { label: '期刊名称', property: 'fullname', width: '150' },
                { label: '简称', property: 'abbr' },
                { label: '即时影响因子', property: 'immediacyIndex' },
                { label: '影响因子级别', property: 'impactFactorGrade' }
            ],
            expandList: [
                { label: '影响因子', property: 'impactFactor', colSpan: 6 },
                { label: '5年影响因子', property: 'fiveYear', colSpan: 6 },
                { label: '即时影响因子', property: 'immediacyIndex', colSpan: 6 },
                { label: '影响因子级别', property: 'impactFactorGrade', colSpan: 6 },
                { label: 'totalCites', property: 'totalCites', colSpan: 6 },
                { label: '当年文章总数', property: 'articleNumbers', colSpan: 6 },
                { label: '被引半衰期', property: 'citedHl', colSpan: 6 },
                { label: '引用半衰期', property: 'citingHl', colSpan: 6 },
                { label: 'eigenfactorScore', property: 'eigenfactorScore', colSpan: 6 },
                { label: 'influenceScore', property: 'influenceScore', colSpan: 6 },
                { label: 'impactFactorNoself', property: 'impactFactorNoself', colSpan: 6 }
            ],
            journalStatus: 0,
            showProgress: false,
            percentage: 0,
            colors: [
                {color: '#409EFF', percentage: 99},
                {color: '#67C23A', percentage: 100}
            ],
            btnDisabled: false,
            selectOptions: [
                {label: '新期刊',value: 1},
                {label: '旧期刊',value: 0}
            ],
            activeStep: 1
		}
	},
	props: {
		model: {
            type: Object,
            default: () => {
                return {}
            }
        },
        operation: String
    },
    computed: {
        ...mapGetters(["info"])
    },
    components: {
        MsRightContent
    },
    created() {
        this.init()
        this.getJournalStataus()
    },
	methods: {
        init() {
            this.getJournalPage()
        },
        reset() {
            this.searchParams =  {
                pageSize: 20,
                pageIndex: 1,
                value: ''
            }
            this.getJournalPage()
        },
        getJournalPage() {
            this.loading = true;
            let params = {
                ...this.searchParams,
                excelId: this.model.id
            }
            this.api.getJournalPageByQuery(params).then(response => {
                this.loading = false
                this.total = response.totalSize || 0;
                this.list = response.data || []
                if (response.status !== 200) {
                    this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
                }
            }).catch(() => this.loading = false)
        },
        getJournalStataus() {
            this.api.getJournalExcelById({id: this.model.id}).then(response => {
                if (response.status === 200) {
                    this.journalStatus = response.data.status
                    this.activeStep = Number(response.data.status) + 1
                }
            })
        },
        journalMatch() {
            this.loading = true;
            this.api.matchJournal({excelId: this.model.id}).then(res => {
                if (res.status === 200) {
                    this.searchParams.pageIndex = 1
                    this.getJournalPage()
                    this.getJournalStataus()
                    this.$emit('up-date')
                } else {
                    this.loading = false
                }
            })
        },
        journalImport() {
            this.btnDisabled = true
            this.loading = true
            this.api.executeJournal({excelId: this.model.id}).then(res => {
                if (res.status === 200) {
                    this.$message({
                        message: res.message,
                        type: 'success'
                    });
                    this.btnDisabled = false
                    this.loading = false
                    this.getJournalStataus()
                    this.$emit('up-date')
                }
                
            })
        },
        // 查看执行状态
        // viewStatus(id) {
        //     this.showProgress = true
        //     this.api.getProcessState({id: id}).then(res => {
        //         if (res.status) {
        //             if (res.data.status === 4) {
        //                 this.percentage = Math.floor(res.data.successSize/res.data.totalSize * 100)
        //                 setTimeout(()=> {
        //                     this.viewStatus(id)
        //                 }, 1000)
        //             } else if (res.data.status === 5){
        //                 this.btnDisabled = false
        //                 this.percentage = 100
        //                 this.getJournalStataus()
        //                 this.$emit('up-date')
        //                 this.$message({
        //                     message: `已成功导入${res.data.successSize}条数据，可点击列表数据查看`,
        //                     type: 'success'
        //                 });
        //             }
        //         } else {
        //             this.showProgress = false
        //             this.btnDisabled = false
        //             this.$message({
        //                 message: '未获取到文件导入状态',
        //                 type: 'warning'
        //             });
        //         }
        //     })
        // },
        rowClick(row) {
            if (row.encodeId && this.journalStatus === 2) {
                window.open(`${serveUrl['base']}/sci/submit.do?id=${row.encodeId}`,'_blank')
            }
            
        },
        rowColor({row}) {
            if (row.isNew === 0) {
                return 'sticky-row';
            } 
            return '';
        },
        size_change (val) {
            this.searchParams.pageSize = val;
            this.$store.dispatch('SetPagesize', val)
            this.init();
        },
        current_change (val) {
            this.searchParams.pageIndex = val;
            this.init();
        }
	}
}
</script>

<style lang="scss" scoped>
  .ms-step{
    justify-content: center;
    /deep/ .el-step__description {
      margin: 5px 0 10px;
    }
  }
</style>
