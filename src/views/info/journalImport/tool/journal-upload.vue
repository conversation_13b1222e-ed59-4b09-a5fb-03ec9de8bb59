<template>
    <el-popover
        placement="bottom"
        width="320"
        v-model="visibleUpload">
        <el-upload
            class="upload-journal"
            drag
            action=""
            :http-request="upLoad"
            :before-upload="beforeUpload"
            v-loading="buttonLoading"
            element-loading-text="文件上传中">
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        </el-upload>
        <el-button type="primary" slot="reference" plain :disabled="disabled">上传文件</el-button>
    </el-popover>
</template>

<script>
import { mapGetters } from "vuex";
import upload from "@/components/UpFile/upload"
export default {
    name: 'jouranl-upload',
    mixins: [upload],
    data() {
        return {
            visibleUpload: false,
            buttonLoading: false,
            fileSuffix: 'journalExcel'
        }
    },
    computed: {
        ...mapGetters(["info"])
    },
    props: {
        disabled: {
            type: <PERSON><PERSON><PERSON>,
            default: false
        }
    },
    methods: {
        uploadSuccess(res) {
            this.buttonLoading = true
            let params = {
                userId: this.info.userId,
                username: this.info.userName,
                fileUrl: `https://${res.name}`
            }
            this.api.uploadJournal(params).then(res => {
                if (res.status === 200) {
                    this.visibleUpload = false;
                    this.PUBLIC_Methods.apiNotify('上传成功', 'success')
                    this.$emit('up-date', res.data)
                } 
                this.buttonLoading = false
            })
        },
        uploadFiled(err) {
            this.$message.error(err.message || '上传失败');
        }
    }
}
</script>

<style lang="scss">
.upload-journal {
    .el-upload {
        width: 100%;
    }
    .el-upload-dragger {
        width: 100%;
    }
}
</style>
