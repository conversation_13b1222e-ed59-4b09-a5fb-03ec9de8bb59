<template>
    <ms-operation-dialog>
        <template slot="content">
            <el-form>
                <el-row >
                    <el-col :span="24">
                        <el-form-item label="总计：">共{{execData.totalSize || 0}}条记录</el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="已执行：">{{execData.totalSize - execData.notSize}}条</el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="未执行：">{{execData.notSize >= 0 ? execData.notSize : 0}}条</el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="未执行条目：">
                            <template v-for="(item,index) in execData.errorStr" >
                                <span :key="index">{{item.issn}}:{{item.message}}; </span>
                            </template>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
        </template>
        <template slot="footer">
            <el-button type="info" @click="$emit('close')" plain>关闭</el-button>
        </template>
    </ms-operation-dialog>
</template>

<script>
export default {
	name: "journal-exec",
	data () {
		return {
            loading: false,
            execData: {
                totalSize: 0,
                notSize: 0,
                errorStr: []
            }
		}
	},
	props: [
		"model",
		"operation"
    ],
    created() {
        this.init()
    },
	methods: {
        init() {
            this.api.getJournalExcelById({id: this.model.id}).then(response => {
                if (response.status === 200) {
                    this.execData = {
                        totalSize: response.data.totalSize || 0,
                        notSize: response.data.notSize || 0,
                        errorStr: JSON.parse(response.data.erroStr) || []
                    }
                }
            })
        }	
	}
}
</script>
