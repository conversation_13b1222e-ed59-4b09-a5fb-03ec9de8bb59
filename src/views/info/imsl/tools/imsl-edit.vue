<template>
  <ms-operation-dialog>
    <template slot="content">
      <el-form
        ref="submitRef"
        :model="submitData"
        :rules="rules"
        label-width="85px"
        class="rule-form"
      >
        <el-row>
          <el-col :span="18" :offset="3">
            <el-form-item label="提示语" prop="chatTemplate">
              <el-input
                type="textarea"
                :rows="5"
                v-model="submitData.chatTemplate"
                placeholder="请输入完整的指令内容，仅限文本"
              ></el-input>
            </el-form-item>
            <el-form-item label="变量" prop="chatVariable">
              <div class="varBox">
                <el-input
                  v-model="submitData.chatVariable"
                  placeholder="请输入上述文本中用户可自定义修改的内容"
                ></el-input>
                <el-button @click="addVariable" size="mini">
                  添加变量
                </el-button>
              </div>
              <el-form-item
                v-for="(item, index) in variableList"
                :key="item"
                label=""
              >
                <div class="variable_row">
                  <div class="left">
                    {{ item }}
                  </div>
                  <div class="right" @click="delVariableList(index)">
                    <i class="el-icon-delete"></i>
                  </div>
                </div>
              </el-form-item>
            </el-form-item>
            <el-form-item label="关键词" prop="chatTagList" label-width="85px">
              <MsTagSearch
                v-model="submitData.chatTagList"
                :notMul="false"
              ></MsTagSearch>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </template>
    <template slot="footer">
      <el-button @click="$emit('close')" size="mini">取 消</el-button>
      <el-button @click="submitForm" size="mini" type="primary"
        >确 定</el-button
      >
    </template>
  </ms-operation-dialog>
</template>

<script>
import { mapGetters } from "vuex";
import MsTagSearch from "../ms-search-tag.vue";
export default {
  name: "imsl-create",
  data() {
    return {
      loading: false,
      getLoading: false,
      variableList: [],
      userInfo: {},
      id: "",
      name: "",
      submitData: {
        chatTemplate: "",
        chatVariable: "",
        chatTemplateAlias: "",
        createdTime: "",
        chatTags: "",
        tagName: "",
        updatedTime: "",
        approvalStatus: "",
        id: "",
        sort: "",
        chatTagList: [],
        variableList: [],
      },
      rules: {
        chatTemplate: [
          { required: true, message: "请输入指令内容", trigger: "blur" },
        ],
        chatVariable: [
          {
            required: true,
            validator: this.validatePass,
            message: "请输入变量",
            trigger: "blur",
          },
        ],
        keywords: [{ required: false, trigger: "blur" }],
      },
    };
  },
  props: {
    model: Object,
    operation: String,
  },
  created() {
    this.userInfo = this.$store.getters.info || {};
    this.id = this.userInfo.id;
    this.name = this.userInfo.roleName;
    this.init();
  },
  computed: {
    ...mapGetters(["info"]),
  },
  components: {
    MsTagSearch,
  },
  methods: {
    init() {
      if (this.model.id) {
        this.api.getAiTemplateById(this.model.id).then((res) => {
          res.data.chatTagList = JSON.parse(res.data.chatTags);
          res.data.variableList = res.data.chatVariable.split(",");
          this.variableList = res.data.variableList;
          res.data.chatTemplate = res.data.chatTemplateAlias;
          this.model = Object.assign(this.model, res.data);
          this.submitData = Object.assign(this.submitData, this.model);
          this.submitData.chatVariable = "";
        });
      }
    },
    validatePass(rule, value, callback) {
      if (this.variableList !== []) {
        callback();
      } else {
        callback(new Error("请输入上述文本中用户可自定义修改的内容"));
      }
    },
    delVariableList(index) {
      this.variableList.splice(index, 1);
    },
    addVariable() {
      if (this.submitData.chatVariable == "") {
        return;
      }
      if (this.variableList.length >= 3) {
        this.PUBLIC_Methods.apiNotify("变量不能超出三组", "warning");
        return;
      }
      if (
        this.submitData.chatTemplate.indexOf(this.submitData.chatVariable) == -1
      ) {
        this.PUBLIC_Methods.apiNotify("指令文本中不包含此变量", "warning");
        return;
      } else {
        if (this.variableList.indexOf(this.submitData.chatVariable) == -1) {
          //将当前chatVariable加入variableList
          this.variableList.push(this.submitData.chatVariable);
          this.submitData.chatVariable = "";
        } else {
          this.PUBLIC_Methods.apiNotify("指令文本中已包含此变量", "warning");
          return;
        }
      }
    },
    submitForm() {
      this.$refs["submitRef"].validate((valid) => {
        if (valid) {
          this.loading = true;
          if (this.model.id) {
            this.submitEdit();
          } else {
            this.createForm();
          }
        }
      });
    },

    submitEdit() {
      let chatVariable = this.variableList.join(",");
      this.submitData.chatVariable = chatVariable;
      let params = {
        id: this.model.id,
        updatedBy: this.info.userId,
        updatedName: this.info.userName,
        chatTemplate: this.submitData.chatTemplate,
        ...this.submitData,
      };
      this.api
        .updateAiTemplate(params)
        .then((response) => {
          this.loading = false;
          if (response.status === 200) {
            this.PUBLIC_Methods.apiNotify(
              response.message || "请求成功",
              "success"
            );
            this.$emit("up-date");
          } else {
            this.PUBLIC_Methods.apiNotify(
              response.message || "请求出错",
              "warning"
            );
          }
        })
        .catch(() => {
          this.loading = false;
        });
    },
    createForm() {
      this.loading = true;
      this.buttonLoading = true;
      let chatVariable = this.variableList.join(",");
      this.submitData.chatVariable = chatVariable;
      let params = {
        createdBy: this.id,
        createdName: this.name,
        chatTemplate: this.submitData.chatTemplate,
        ...this.submitData,
        sort: this.model.id,
      };
      this.api
        .addAiTemplate(params)
        .then((response) => {
          this.loading = false;
          if (response.status === 200) {
            this.PUBLIC_Methods.apiNotify(
              response.message || "请求成功",
              "success"
            );
            this.$emit("up-date");
          } else {
            this.PUBLIC_Methods.apiNotify(
              response.message || "请求出错",
              "warning"
            );
          }
          this.loading = false;
        })
        .catch(() => (this.buttonLoading = false));
    },
  },
};
</script>

<style lang="scss" scoped>
.varBox {
  display: flex;
  .el-button {
    margin-left: 8px;
  }
}
.el-form-item .el-form-item {
  margin-bottom: 0;
}
.variable_row {
  display: flex;
  justify-content: space-between;
  margin-top: 5px;
  .left {
    margin-left: 14px;
    font-size: 13px;
  }
  .right {
    cursor: pointer;
    margin-right: 32px;
    .el-icon-delete:hover {
      color: #409eff;
    }
  }
}
</style>
