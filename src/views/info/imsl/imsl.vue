<template>
  <div>
    <el-tabs v-model="activeName" @tab-click="handleClick" >
      <el-tab-pane label="iMSL列表" name="imsl"></el-tab-pane>
      <el-tab-pane label="提示语管理" name="instru"></el-tab-pane>
      <keep-alive>
        <component :is="comp"></component>
      </keep-alive>
    </el-tabs>
  </div>
</template>

<script>
import imslManage from "../imsl/imslList/imsl-manage.vue";
import liveMixin from "./imsl-mixin"
import instruManage from "../imsl/instruManage/instru-manage.vue";
export default {
  data() {
    return {
      comp: "imslManage",
      activeName: 'imsl',
    };
  },
  mixins:[liveMixin],
  components: { imslManage, instruManage },
  methods: {
    handleClick() {
      if (this.activeName === 'imsl') {
        this.comp = 'imslManage'
      } else {
        this.comp = 'instruManage'
      }
    },
  },
};
</script>

<style>

</style>
