<template>
  <ms-table
    :currentPage="searchParams.pageIndex"
    :loading="loading"
    :pageSize="searchParams.pageSize"
    :tableData="list"
    :tableHeader="domConfig.tableHeader"
    :scopeConfig="scopeConfig.show"
    :total="total"
    class="table-svg"
    @current-change="current_change"
    @operation-change="operation_change"
    @size-change="size_change"
    @header-operation="header_operation"
    @handleSelectionChange="handleSelectionChange"
  >
    <!-- 列表搜索去区域插槽 -->
    <template slot="ms-table-header">
      <div class="slot-search">
        <div class="search-input">
          <template v-for="(searchItem, key) in domConfig.listSearch">
            <component
              :index="searchItem.index || ''"
              :is="searchItem.component"
              :key="key"
              :width="searchItem.width || '150px'"
              :model.sync="searchParams[searchItem.model]"
              :label="searchItem.label"
              :operation="searchItem.operation || ''"
              :options="searchItem.options || []"
              :placeholder="searchItem.placeholder || ''"
              :type="searchItem.type || ''"
              :multiple="searchItem.multiple"
              :disabled="searchItem.disabled"
            ></component>
          </template>
        </div>
        <div class="inlineBlock">
          <el-button @click="handleClick('query')" type="primary"
            >查询</el-button
          >
          <el-button @click="handleClick('reset')">重置</el-button>
        </div>
      </div>
    </template>
  </ms-table>
</template>

<script>
import domConfig from "../data/dom-config";
import tableMixins from "../../../mixins/table";
import scopeConfig from "../data/scope-config";
export default {
  name: "imsl-manage",
  mixins: [tableMixins],
  data() {
    return {
      domConfig,
      scopeConfig,
      searchParams: {
        // => 用户列表查询传参
        chatName: "",
        message: "",
        conversationId: "",
      },
    };
  },
  methods: {
    apiInit(params) {
      this.dialog = false;
      let searchParams = { ...params };
      this.api
        .getQueryAiTopicLog(searchParams)
        .then((response) => {
          response.data.forEach((item) => {
            item.url = `#/aicon?conversationId=${item.conversationId}&chatName=${item.chatName}`;
            item.chatTopic = decodeURIComponent(item.chatTopic.replace(/<[^>]*>/g, ""));
          });
          this.loading = false;
          this.total = response.totalSize || 0;
          if (response.status === 200) {
            this.list = response.data.map((v) => {
              return {
                ...v,
              };
            });
          } else {
            this.list = [];
            this.PUBLIC_Methods.apiNotify(
              response.message || "请求出错",
              "warning"
            );
          }
        })
        .catch(() => (this.loading = false));
    },
  },
};
</script>

<style lang="scss" scoped>
.slot-search {
  width: 100%;
  display: flex;
  justify-content: space-between;
}
</style>
