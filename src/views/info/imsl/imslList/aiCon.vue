<template>
  <div class="content_box w">
    <div class="dialogueBox" v-for="(item, index) in dialogueList" :key="index">
      <div class="inner_box">
        <div class="img_box">
          <img src="../images/avatar.png" alt="" />
        </div>
        <div class="txt">
          <p>
            <span>{{ decodeURIComponent(item.dialogues[0].message) }}</span>
          </p>
        </div>
      </div>
      <div class="answer_box">
        <div class="img_answer_box">
          <img src="../images/response.png" alt="" />
        </div>
        <div class="answerTxt">
          <p
            class="parah"
            v-html="decodeURIComponent(item.dialogues[1].message)"
          ></p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "aiCon",
  data() {
    return {
      url: "",
      logParams: "",
      pageIndex: 1,
      pageSize: 10,
      dialogueList: [],
      logArray: [],
      userId: "",
    };
  },
  props: {
    value: {
      type: Array,
    },
  },
  created() {},
  mounted() {
    this.show();
    this.urlTool();
    this.getAiLog();
  },
  methods: {
    show() {
      this.url = location.href;
    },
    urlTool() {
      let dataParams = {};
      let list = this.url
        .split("?")
        .pop()
        .split("&");
      list.forEach((el) => {
        let dataArray = el.split("=");
        dataParams[dataArray[0]] = dataArray[1];
        this.logParams = dataParams;
      });
      return dataParams;
    },
    getAiLog() {
      this.api
        .getMedsciAiLog({
          pageIndex: this.pageIndex,
          pageSize: this.pageSize,
          conversationId: this.logParams.conversationId,
          chatName: this.logParams.chatName,
        })
        .then((res) => {
          this.userId = res.data[0].dialogues[0].userId;
          console.log(res.data, "aaaaaa");
          res.data.forEach((item) => {
            // item.dialogues[1].message = JSON.stringify(item.dialogues[1].message)
            // console.log(JSON.stringify(item.dialogues[1].message));
            console.log(111, item.dialogues[1].message);
            item.dialogues[1].message = item.dialogues[1].message.replace(
              /\\n/g,
              "<br>"
            );
            console.log(222, item.dialogues[1].message);
          });
          console.log(res.data, "bbbbbbb");

          this.dialogueList = res.data;
          console.log(this.dialogueList, "0000000");

          this.logArray = this.dialogueList[0].dialogues;
          this.logArray.forEach((item) => {
            item.chatType === 1
              ? this.logArray[0].message
              : item.chatType === 2
              ? this.logArray[1].message
              : "";
          });
        });
    },
  },
};
</script>

<style lang="scss" scoped>
.w {
  width: 1025px;
  margin: 56px auto 0 auto;
}

.inner_box {
  width: 100%;
  display: flex;

  .img_box {
    width: 203px;
    height: 50px;
    position: relative;

    img {
      width: 40px;
      height: 40px;
      position: absolute;
      top: 4px;
      right: 15px;
    }
  }

  .txt {
    width: calc(100% - 203px);
    text-align: left;
    line-height: 50px;
    border-left: 2px solid #fff;
    span {
      font-size: 14px;
    }
  }
}

.answer_box {
  width: 100%;
  display: flex;
  background-color: rgb(242, 242, 242);

  .img_answer_box {
    width: 203px;
    height: 50px;
    position: relative;

    img {
      width: 40px;
      height: 40px;
      position: absolute;
      top: 7px;
      right: 15px;
    }
  }

  .answerTxt {
    width: calc(100% - 203px);
    line-height: 50px;
    border-left: 2px solid #fff;
    letter-spacing: normal;
    color: #333333;
    text-align: left;
    line-height: normal;
    white-space: break-spaces;

    .parah {
      width: 657px;
      font-size: 14px;
    }
  }
}
</style>
