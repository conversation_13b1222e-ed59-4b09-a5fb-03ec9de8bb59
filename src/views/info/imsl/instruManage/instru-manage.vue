<template>
  <ms-table
    :currentPage="searchParams.pageIndex"
    :loading="loading"
    :operationButtons="domConfig.tableButtons"
    :pageSize="searchParams.pageSize"
    :scopeConfig="scopeConfig.show"
    :scopeHeader="scopeConfig.headerShow"
    :tableData="list"
    :tableHeader="domConfig.tableHeader"
    :total="total"
    :showSelection="true"
    class="table-svg"
    @current-change="current_change"
    @operation-change="operation_change"
    @size-change="size_change"
    @header-operation="header_operation"
    @handleSelectionChange="handleSelectionChange"
  >
    <template slot="ms-table-header">
      <div class="slot-button">
        <template v-for="(item, index) in domConfig.soltButtons">
          <el-button
            :key="index"
            :type="item.type"
            :icon="item.icon"
            @click="operation_change({ operation: item })"
            plain
            >{{ item.label }}</el-button
          >
        </template>
      </div>
      <el-dialog
        :visible.sync="dialog"
        closeable
        show-close
        :close-on-click-modal="false"
        :width="dialogWidth"
        :title="dialogTitle"
      >
        <component
          :is="dialogComponent"
          :model="scopeInfo"
          :operation="dialogOperation"
          @close="dialog = !dialog"
          @up-date="init"
          v-if="dialog"
        ></component>
      </el-dialog>
    </template>
  </ms-table>
</template>

<script>
import liveMixin from "../imsl-mixin";
import domConfig from "../data2/dom-config";
import tableMixins from "../../../mixins/table";
import scopeConfig from "../data2/scope-config";

export default {
  name: "imsl-manage",
  mixins: [tableMixins, liveMixin],
  data() {
    return {
      domConfig,
      scopeConfig,
      userInfo: {},
      id: "",
      sort: "",
      searchParams: {},
    };
  },
  methods: {
    apiInit(params) {
      this.dialog = false;
      this.userInfo = this.$store.getters.info || {};
      let searchParams = { ...params };
      this.api
        .getAiTemplate(searchParams)
        .then((response) => {
          let responseData = response.data;
          this.loading = false;
          if (response.status === 200) {
            this.total = response.totalSize || 0;
            responseData.forEach((item) => {
              let chatTag = JSON.parse(item.chatTags);
              let tagNames = "";
              chatTag.forEach((e) => {
                tagNames += e.tagName + "，";
              });
              // item.tagName = tagNames.substring(0,tagNames.length - 1)
              item.tagName =
                tagNames.length > 5
                  ? tagNames.substring(0, 5) + "..."
                  : tagNames.substring(0, tagNames.length - 1);
            });
            this.list = responseData;
          } else {
            this.list = [];
            this.PUBLIC_Methods.apiNotify(
              response.message || "请求出错",
              "warning"
            );
          }
        })
        .catch(() => (this.loading = false));
    },
  },
};
</script>

<style lang="scss" scoped>
.inlineBlock {
  width: 100%;
  display: flex;
}
.slot-button {
  margin-bottom: 5px;
}
</style>
