const domConfig = {
  tableHeader: [
    { label: "序号", property: "id", sortable: true, width: 80 },
    { label: "关键词", property: "tagName", width: 120 },
    { label: "提示语文本", property: "chatTemplateAlias", width: 400 },
    { label: "变量", property: "chatVariable", width: 120 },
    { label: "创建时间", property: "createdTime", sortable: true, width: 130 },
  ],
  tableButtons: [
    {
      label: "编辑",
      icon: "",
      role: "",
      operation: "edit",
      component: "imslEdit",
      way: "dialog",
      type: "primary",
      title: "编辑指令",
      width: "70%",
    },

    {
      label: "",
      icon: "",
      role: "",
      operation: "editStatus",
      component: "imslOperation",
      way: "dialog",
      field: "approvalStatus",
      rule: {
        1: { label: "去审", type: "", operation: "toreview" },
        0: { label: "审核", type: "success", operation: "approval" },
      },
      identify: "status",
    },
    {
      label: "删除",
      component: "imslOperation",
      operation: "delete",
      way: "dialog",
      type: "danger",
      identify: "single_delete",
    },
  ],
  soltButtons: [
    { 
      label: '批量审核', 
      type: 'primary',
      operation: 'approval',
      component: 'imslOperation',
      way: 'batch',
     
    },
    { 
      label: '批量去审', 
      type: 'primary',
      operation: 'toreview',
      component: 'imslOperation',
      way: 'batch',
    },
    { 
      label: '批量删除',
      type: 'info',
      icon: 'el-icon-close',
      operation: 'delete',
      component: 'imslOperation',
      way: 'batch',
    },
    {
      label: "添加",
      type: "primary",
      title: "添加指令",
      operation: "created",
      component: "imslEdit",
      way: "dialog",
      width: "45%",
    },
   
  ],
};

export default domConfig;
