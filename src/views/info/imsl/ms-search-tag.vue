<template>
    <div :class="{'input-label': label}">
      <label v-show="label">{{label}}</label>
      <el-select size="mini"
                 v-model="result" 
                 clearable 
                 filterable 
                 :multiple="multiple"
                 :style="{width: width || '100%'}"
                 remote
                 :remote-method="remoteMethod"
                 :loading="loading"
                 value-key="tagId"
                 :placeholder="placeholder ? placeholder : '请搜索选择关键词'"
                 @change="change">
          <el-option v-for="(item,index) in optionSearch"
                     :key="index"
                     :label="item.tagName"
                     :value="{tagId: item.tagId, tagName: item.tagName}">
          </el-option>
      </el-select>
    </div>
  </template>
  
  <script>
    export default {
      name: "ms-tag-search",
      props: [
        "value",
        "label",
        "width",
        "placeholder",
        "notMul",
        "modelId"
      ],
      data() {
        return {
          loading: false,
          result: [],
          optionSearch: [],
          filterVal: '',
          isSearch: false,
          multiple: true,
          progTagId: 0
        }
      },
      watch: {
        value: function(val) {
          this.initValue(val)
        }
      },
      created() {
        if (this.notMul) {
          this.multiple = false
        }
        this.initValue(this.value)
      },
      methods: {
        initValue (val) {
          if (val && val.length > 0) {
            if (this.multiple) {
              this.result = val
            } else {
              this.result = {
                tagId: this.modelId ? this.modelId : 0,
                tagName: val
              }
            }
            if (!this.isSearch) {
              if (this.multiple) {
                this.optionSearch = val
              } else {
                this.optionSearch = [{
                  tagId: this.modelId ? this.modelId : 0,
                  tagName: val
                }]
              }
            }
          }
        },
        remoteMethod(val) {
          if(val) {
            this.getOptionData(val)
          }
        },
        change(val) {
          if (this.multiple) {
            this.$emit('input', val)
          } else {
            this.$emit('input', val.tagName)
            this.$emit('update:modelId', val.tagId)
          }
        },
        getOptionData(val) {
          this.loading = true;
          // 准备数据源
          let selectVal = this.multiple ? [...this.result] : this.result.tagId ? [this.result] : []
          let selectName = []
          for (let n = 0; n < selectVal.length; n++) {
            selectName.push(selectVal[n].tagName)
          }
          
          // 接口获取数据
          let params = {
            tagName : val ? val : ''
          }
          let addNewTag = true
          this.api.searchTag(params).then(response => {
            this.optionSearch = selectVal
            this.loading = false
            this.isSearch = true
            if(response.status === 200) {
              let arr = response.data || []
              for (let i = 0; i< arr.length; i++) {
                if (selectName.indexOf(arr[i].tagName) === -1) {
                  this.optionSearch.push({tagId: arr[i].id, tagName: arr[i].tagName})
                }
                if (arr[i].tagName.toUpperCase() === val.toUpperCase()) {
                  addNewTag = false
                }
              }
            }
            if (addNewTag && this.multiple) { this.optionSearch.push({tagId: --this.progTagId, tagName: val}) }
          })
        },
      }
    }
  </script>
  