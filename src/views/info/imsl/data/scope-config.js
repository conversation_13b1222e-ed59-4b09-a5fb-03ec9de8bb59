const scopeConfig = {
    submissionTime: () => {
        return {
          type: 'formatTime',
          cFormat: '{y}-{m}-{d} {h}:{i}'
        }
      },
    show: {
      chatName: () => {
        return {
          type: 'webLink',
          config: {
            role: '',
            operation: 'edit',
            way: 'page',
            path: 'user-detail',
            params: [{
              keyName: 'id',
              valName: 'chatUserId',
            },{
              keyName: 'flag',
              valName: 'id',
            }],
          }
        }
      },
      chatTopic: () => {
        return {
          type: 'webLink',
        config: {
          way: 'link',
          pathKey: 'url'
        }
        }
      }
    },
  }
  

  

  export default scopeConfig
  