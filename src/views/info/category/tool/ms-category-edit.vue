<template>
    <ms-operation-dialog>
        <template slot="content">
            <el-form ref="submitRef"
                    class="rule-form"
                    :model="submitData"
                    label-width="90px"
                    v-loading="getLoading">
                <el-row>
                <template v-for="(item, index) in formConfig.formField">
                    <el-col :key="index"
                            :span="item.colSpan" >
                    <el-form-item :prop="item.prop"
                                    :label="item.label">
                        <component :is="item.component"
                                :model.sync="submitData[item.prop]"
                                v-model="submitData[item.prop]"
                                :placeholder="item.placeholder"
                                :width="item.width || '100%'"
                                :disabled="item.disabled"
                                :options="item.options"
                                :type="item.type || 'text'"
                                :active="item.active"
                                :inactive="item.inactive"
                                :operation="item.operation"
                                :config="item.config">
                        </component>
                    </el-form-item>
                    </el-col>
                </template>
                </el-row>
            </el-form>
        </template>
        <template slot="footer">
            <el-button @click="submitForm"
                        :loading="loading"
                        size="mini"
                        type="primary">确 定</el-button>
            <el-button @click="$emit('close')"
                        size="mini">取 消</el-button>
        </template>
    </ms-operation-dialog>
</template>

<script>
import { mapGetters } from "vuex";
import formConfig from '../data/form-config' 
import MsCategoryCheck from '@/components/MsCommon/ms-category-check'
import MsModuleCheck from '@/components/MsCommon/ms-module-check'
import MsCategoryTran from '@/components/MsCommon/ms-category-tran'
import MsTagSearch from '@/components/MsCommon/ms-tag-search'
export default {
	name: "ms-category-edit",
	data () {
		return {
            loading: false,
            getLoading: false,
            formConfig: formConfig,
            submitData: {
                pid: 0,
                code: "",
                titleCn: "",
                titleEn: "",
                module: "",
                cover: "",
                transverseCategory: [],
                status: 0,
                sort: 0,
                moduleList: [],
                tagList: [],
                relationUserId: '',
            },

            //common
            operationLocal: ""
        }
    },
    props: {
        model: Object,
        operation: String
    },
    computed: {
        ...mapGetters(["info"])
    },
    components: {
        MsCategoryCheck,
        MsModuleCheck,
        MsCategoryTran,
        MsTagSearch
    },
    created() {
        this.init()
    },
    methods: {
        init() {
            this.operationLocal = this.operation || this.$route.query.operation
            if(this.operationLocal === 'edit' && this.model.categoryId) {
                this.getCategoryDetail(this.model.categoryId)
            }
        },
        getCategoryDetail(id) {
            this.getLoading = true;
            this.api.getProjectCategoryDetail({categoryId: id}).then( response => {
                this.getLoading = false;
                if(response.status === 200) {
                    let tran = response.data.transverseCategory && response.data.transverseCategory.map(v => {
                        return {
                            categoryId: v.categoryId,
                            categoryName: v.titleCn
                        }
                    })
                    this.submitData = {...this.submitData,...response.data, transverseCategory: tran}
                } else {
                    this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
                }
            }).catch(() => this.getLoading = false)
        },
        submitForm () {
            let params = {
                userId: this.info.userId,
                username: this.info.userName,
                moduleList: this.submitData.moduleList,
                categoryId: this.submitData.categoryId,
                tagList: this.submitData.tagList,
                relationUserId: this.submitData.relationUserId,
            }
            this.api.UpdateProjectCategory(params).then(response => {
                if(response.status === 200) {
                    this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
                } else {
                    this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
                }
                this.$emit('up-date')
                this.loading = false
            }).catch( () => {
                this.loading = false;
                this.$emit('close')
            })
        }
	}
}
</script>
