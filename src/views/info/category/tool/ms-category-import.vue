<template>
  <ms-right-content>
    <el-form :model="submitData" ref="submitRef" :rules="rule" label-width="50px">
      <el-form-item label="模块" prop="moduleId">
        <ms-module-search :model.sync="submitData.moduleId" ></ms-module-search>
      </el-form-item>
      <el-tree ref="tree"
              :data="dataTree"
              show-checkbox
              node-key="id"
              :expand-on-click-node="false"
              :check-on-click-node="true"
              :check-strictly="isCheckStrictly"
              :default-expand-all="false"
              :default-checked-keys="checkedTree"
              :props="{ label: 'titleCn', children: 'children' }"
              v-loading="getLoading">
      </el-tree>
    </el-form>
    <template slot="footer">
      <el-button @click="submitForm"
                 :loading="loading"
                 size="mini"
                 type="primary">确 定</el-button>
      <el-button @click="$emit('close')"
                 size="mini">取 消</el-button>
    </template>
  </ms-right-content>
</template>

<script>
import { mapGetters } from "vuex";
import MsRightContent from "@/components/MsDialog/ms-right-content";
import MsModuleSearch from '@/components/MsCommon/ms-module-search'
export default {
	name: "ms-category-important",
	data () {
		return {
      loading: false,
      getLoading: false,
      dataTree: [],
      checkedTree: [],
      // moduleId: null,
      isCheckStrictly: false,
      submitData: {
        moduleId: null
      },
      rule: {
        moduleId: [
          { required: true, message: "请选择模块", trigger: 'change' }
        ]
      }
		}
	},
	props: {
		model: Object,
		operation: String
  },
  computed: {
    ...mapGetters(["info"])
  },
  watch: {
    "submitData.moduleId": function(val) {
      this.getModuleCategory(val)
    }
  },
  components: {
    MsModuleSearch,
    MsRightContent
  },
  created() {
    this.init()
  },
	methods: {
    init() {
      this.getLoading = true
      this.api.getCategoryTreeByModule({status:1}).then( response => {
        if (response.status === 200) {
          this.dataTree = response.data
        }
        this.getLoading = false
      }).catch(() => this.getLoading = false)
    },
    async getModuleCategory(model) {
      this.getLoading = true;
      this.isCheckStrictly = true;
      let params = {status: 1}
      if (model === 'article') {
        params.type = 2
      } 
      if (model === 'guider') {
        params.type = 4
      }
      if (model === 'exam_paper') {
        params.type = 5
      }
      const newDataTree = await this.api.getCategoryTreeByModule(params)
      const checkData = await this.api.getImportProjectCategotyTreeList({module: model})

      this.checkedTree = checkData.data.categorys
      this.dataTree = newDataTree.data

      this.getLoading = false

      console.log(this.checkedTree)
      this.$nextTick(()=> {
        this.isCheckStrictly = false;
      })
    },
    submitForm() {
      this.$refs["submitRef"].validate( valid => {
        if (valid) {
          this.loading = true
          let checkedNode = this.$refs.tree.getCheckedNodes() || []
          let params = {
            categoryIds: [],
            userId: this.info.userId,
            username: this.info.userName,
            module: this.submitData.moduleId,
          }
          checkedNode.forEach(item => {
            params.categoryIds.push(item.id)
          });
          this.api.saveProjectCategoryList(params).then(response => {
            this.loading = false
            if(response.status === 200) {
              this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
              this.$emit('up-date')
            } else {
              this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
            }
          }).catch(()=> { this.loading = false })
        }
      })
      
    }
	}
}
</script>
