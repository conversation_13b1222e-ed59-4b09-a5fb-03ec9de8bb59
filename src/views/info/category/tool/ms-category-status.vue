<template>
  <ms-operation-dialog :title="`是否要${+model.status === 1 ? '禁用' : '启用'}模块分类`">
    <template slot="footer">
      <el-button @click="submitForm"
                 :loading="loading"
                 size="mini"
                 type="primary">确 定</el-button>
      <el-button @click="$emit('close')"
                 size="mini">取 消</el-button>
    </template>
  </ms-operation-dialog>
</template>

<script>
import { mapGetters } from "vuex";
export default {
	name: "ms-category-status",
	data () {
		return {
			loading: false
		}
	},
	props: {
		model: Object,
		operation: String
  },
  computed: {
      ...mapGetters(["info"])
  },
	methods: {
		submitForm () {
      this.loading = true;
      let params = {
          userId: this.info.userId,
          username: this.info.userName,
          status: +this.model.status === 1 ? 0 : 1,
          id: this.model.categoryId
      }
      this.api.updateProjectCategoryStatus(params).then(response => {
        if(response.status === 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
        this.$emit('up-date')
        this.loading = false
      }).catch( () => {
        this.loading = false;
        this.$emit('close')
      })
		}
	}
}
</script>
