<template>
  <ms-operation-dialog title="是否要删除模块分类">
    <template slot="footer">
      <el-button @click="submitForm"
                 :loading="loading"
                 size="mini"
                 type="primary">确 定</el-button>
      <el-button @click="$emit('close')"
                 size="mini">取 消</el-button>
    </template>
  </ms-operation-dialog>
</template>

<script>
export default {
	name: "ms-category-delete",
	data () {
		return {
			loading: false
		}
	},
	props: {
		model: Object,
		operation: String
    },
	methods: {
		submitForm () {
        this.loading = true;
        let params = {
            id: this.model.projectCategoryId
        }
        this.api.deleteProjectCategory(params).then(response => {
            if(response.status === 200) {
                this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
            } else {
                this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
            }
            this.$emit('up-date')
            this.loading = false
            }).catch( () => {
                this.loading = false;
                this.$emit('close')
            })
		}
	}
}
</script>
