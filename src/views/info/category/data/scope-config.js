const scopeConfig = {
  show: {
    // titleCn: () => {
    //   return {
    //     type: 'link',
    //     way: 'page',
    //     url: 'https://www.medsci.cn/article/list.do',
    //     params: [],
    //     target: '_blank'
    //   }
    // },
    status: () => {
      return {
        type: 'status',
        rule: {
          0: { label: '禁用', background: '#A7ADBD' },
          1: { label: '启用', background: '#40A23F' }
        }
      }
    },
    fields: () => {
      return {
        type: 'fidd',
        fields: [
          {name: 'articleNumber', way: 'page', path: 'article', params:[{keyName: "classify", valName: "id"},{keyName: "pid", valName: "pid"}], setVuex: true },
          {name: 'guiderNumber', way: 'page', path: 'guider', params:[{keyName: "classify", valName: "id"},{keyName: "pid", valName: "pid"}], setVuex: true },
          // {name: 'videoNumber', way: 'page', path: 'article', params:[{keyName: "categoryId", valName: "id"}] }
        ]
      }
    }
  },
  headerShow: {
    status: () => {
      return {
        type: 'dropdown',
        icon: 'icon-funnel',
        options: [
          { label: '全部', value: null },
          { label: '禁用', value: 0 },
          { label: '启用', value: 1 }
        ],
        operation: 'query'
      }
    }
  }
}

export default scopeConfig;
