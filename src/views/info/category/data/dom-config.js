const domConfig = {
  listSearch: [
    {
      label: '专题中文名',
      placeholder: '请输入',
      model: 'titleCn',
      component: 'ms-input'
    },
    {
      label: '模块选择',
      placeholder: '请选择',
      model: 'module',
      component: 'ms-module-search',
      type: 'module'
    }
  ],
  tableHeader: [
    { label: 'CODE', property: 'code', sortable: true, width: 80 },
    { label: '专题中文名', property: 'titleCn'},
    { label: '专题英文名', property: 'titleEn'},
    // { label: '资讯/指南', property: 'fields' },
    // { label: '归类标识', property: 'module'},
    { label: '状态', property: 'status' }
  ],
  tableButtons: [
    {
      label: '编辑',
      icon: '',
      role: '',
      operation: 'edit',
      component: 'msCategoryedit',
      way: 'dialog',
      title: '专题分类编辑',
      type: 'primary',
      width: '60%'
    },
    {
      icon: '',
      role: '',
      operation: 'editStatus',
      component: 'msCategoryStatus',
      way: 'dialog',
      field: 'status',
      rule: {
        1: { label: '禁用', type: 'info' },
        0: { label: '启用', type: 'success' }
      }
    },
    {
      label: '删除',
      operation: 'delete',
      type: 'danger',
      way: 'dialog',
      component: 'msCategoryDelete',
    }
  ],
  soltButtons: [
    { 
      label: '模块导入', 
      icon: 'el-icon-plus',
      type: 'primary', 
      operation: 'add',
      component: 'msCategoryImport',
      way: 'dialog',
      position: 'right',
      title: '模块导入',
      width: '30%'
    }
  ]
}

export default domConfig;
