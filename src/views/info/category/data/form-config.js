const formConfig = {
  formField: [
    {
      label: '上级分类',
      prop: 'pid',
      colSpan: 12,
      component: 'ms-input',
      disabled: true
    },
    {
      label: 'ID',
      prop: 'categoryId',
      colSpan: 12,
      component: 'ms-input',
      disabled: true
    },
    {
      label: '专题中文名',
      prop: 'titleCn',
      colSpan: 12,
      component: 'ms-input',
      disabled: true
    },
    {
      label: '专题英文名',
      prop: 'titleEn',
      colSpan: 12,
      component: 'ms-input',
      disabled: true
    },
    {
      label: '排序',
      prop: 'sort',
      colSpan: 12,
      component: 'ms-input',
      disabled: true
    },
    {
      label: '图片URL',
      prop: 'cover',
      colSpan: 12,
      component: 'ms-input',
      disabled: true
    },
    {
      label: '横向分类',
      prop: 'transverseCategory',
      colSpan: 24,
      component: 'ms-category-tran',
      disabled: true,                                                                          
    },
    {
      label: '所属模块',
      prop: 'moduleList',
      colSpan: 24,
      component: 'ms-module-check'                                                                          
    },
    {
      label: '亚专业',
      prop: 'tagList',
      colSpan: 24,
      placeholder: '请输入',
      component: 'ms-tag-search'
    },
    {
      label: '编辑者',
      prop: 'relationUserId',
      colSpan: 24,
      placeholder: '请输入',
      component: 'ms-createby-search'
    }

  ]
}

export default formConfig;
