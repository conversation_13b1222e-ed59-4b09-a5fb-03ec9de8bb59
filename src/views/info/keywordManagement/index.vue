<template>
  <div class="container">
    <MsSearch
      @reset="reset"
      @queryKeyWordsData="queryKeyWordsData"
      ref="form"
    />
    <el-tabs v-model="activeName" @tab-click="tabsClick">
      <el-tab-pane label="热搜推荐" name="hot">
        <el-table
          :header-cell-style="{ background: 'rgb(235, 238, 245)' }"
          :data="hotList"
          border
          v-loading="hotLoading"
          @selection-change="handleHotList"
          ref="hotMultiple"
          @sort-change="sortChange"
        >
          <el-table-column type="selection"> </el-table-column>
          <el-table-column label="关键词名称" prop="content"> </el-table-column>
          <el-table-column label="添加时间" sortable prop="createdTime">
          </el-table-column>
          <el-table-column prop="module" label="产品类别">
            <template slot-scope="scope">
              <span v-if="scope.row.module === 'nsfc'">基金</span>
              <span v-if="scope.row.module === 'tool_impact_factor'">期刊</span>
              <span v-if="scope.row.module === 'guider'">指南</span>
            </template>
          </el-table-column>
          <el-table-column prop="recommend">
            <template slot="header">
              是否推荐
              <el-dropdown
                trigger="click"
                size="medium"
                @command="commandChange"
              >
                <span>
                  <svg-icon
                    icon-class="icon-funnel"
                    style="font-size: 11px"
                  ></svg-icon>
                </span>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item v-for="(item,index) in reDropdown" :key="index"  :command="item.command"  :style="{color: item.command === selectVal ? '#409EFF': ''}">{{item.label}}</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </template>
            <template slot-scope="scope">
              <span v-if="scope.row.recommend == 1">是</span>
              <span v-else>否</span>
            </template>
          </el-table-column>
          <el-table-column prop="recommendWeight">
            <template slot="header" align="center">
              推荐权重
              <el-dropdown trigger="click">
                <span>
                  <i
                    class="el-icon-warning-outline"
                    style="font-size: 15px"
                  ></i>
                </span>
                <el-dropdown-menu slot="dropdown" class="menu">
                  展示在产品搜索框的热门搜索中,默认展示10条。
                  权重为热门搜索的排序规则，数值越大排序越靠前
                </el-dropdown-menu>
              </el-dropdown>
            </template>
            <template slot-scope="scope">
              {{ scope.row.recommendWeight}}
            </template>
          </el-table-column>
          <el-table-column label="添加人员" prop="username"> </el-table-column>
          <el-table-column label="操作">
            <template slot-scope="scope">
              <el-button
                type="text"
                @click="handleOperate('cancel', scope.row)"
                v-if="scope.row.recommend == 1"
                >取消推荐</el-button
              >
              <el-button
                type="text"
                @click="handleOperate('set', scope.row)"
                v-else
                >设为推荐</el-button
              >
              <el-button type="text" @click="handleOperate('edit', scope.row)"
                >编辑</el-button
              >
              <el-button type="text" @click="handleOperate('delete', scope.row)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          :page-sizes="[5, 10, 20, 30, 40, 50, 100]"
          :page-size="params.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="params.totalSize"
          @current-change="current_change"
          @size-change="handleSizeChange"
        >
        </el-pagination>
      </el-tab-pane>
      <el-tab-pane label="关键词管理" name="keyWords">
        <el-table
          :header-cell-style="{ background: 'rgb(235, 238, 245)' }"
          border
          :data="keywordsList"
          v-loading="keywordsLoading"
          ref="keyWordsMultiple"
        >
          <el-table-column type="selection"> </el-table-column>
          <el-table-column label="ID" prop="id"> </el-table-column>
          <el-table-column label="关键词名称" prop="content"> </el-table-column>
          <el-table-column prop="searchNum">
            <template slot="header">
              {{ headerValue }}
              <el-dropdown
                trigger="click"
                size="medium"
                @command="changeDropdown"
              >
                <span>
                  <svg-icon
                    icon-class="icon-funnel"
                    style="font-size: 11px"
                  ></svg-icon>
                </span>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item :command="conItem.command" v-for="(conItem ,conIndex) in conDropdown" :key="conIndex" :style="{color: conItem.command === keywordsVal ? '#409EFF': ''}">{{conItem.label}}</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </template>
          </el-table-column>
          <el-table-column prop="module" label="产品类型">
            <template slot-scope="scope">
              <span v-if="scope.row.module === 'nsfc'">基金</span>
              <span v-if="scope.row.module === 'tool_impact_factor'">期刊</span>
              <span v-if="scope.row.module === 'guider'">指南</span>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          :page-sizes="[5, 10, 20, 30, 40, 50, 100]"
          :page-size="params.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="params.totalSize"
          @current-change="current_change"
          @size-change="handleSizeChange"
        >
        </el-pagination>
      </el-tab-pane>
    </el-tabs>
    <div class="but" v-if="activeName === 'hot'">
      <el-button type="primary" plain @click="handleOperate('batch')"
        >批量删除</el-button
      >
      <el-button type="primary" plain @click="handleOperate('add')"
        >添加关键词</el-button
      >
    </div>
    <Popup
      ref="pop"
      :title="title"
      :popupValue="popupValue"
      :deleteData="deleteData"
      :updateVal="updateVal"
      @refreshData="refreshData"
      @cancel='cancel'
      @resetEditData='resetEditData'
    ></Popup>
  </div>
</template>
<script>
import MsSearch from "./tool/ms-search";
import Popup from "./tool/ms-pop-up";
export default {
  components: {
    Popup,
    MsSearch,
  },
  data() {
    return {
      params: {
        pageIndex: 1,
        pageSize: 20,
        totalSize: 0,
        sortType: 2,
      },
      activeName: "hot",
      hotList: [],
      hotLoading: false,
      keywordsList: [],
      keywordsLoading: false,
      popupValue: "",
      title: "",
      deleteData: [],
      headerValue: "近一个月搜索量",
      updateVal: {},
      dateType: 1,
      reDropdown: [
         {
      command: '2',
          label: '全部'
        },
        {
          command: '0',
          label: '否'
        },
        {
          command: '1',
          label: '是'
        }
      ],
      conDropdown :[
        {
          command: '1',
          label: '近一个月搜索量'
        },
        {
          command: '2',
          label: '近三个月搜索量'
        },
        {
          command: '3',
          label: '近半年搜索量'
        },
        {
          command: '4',
          label: '近一年搜索量'
        },

      ],
      selectVal: '2',
      keywordsVal: '1'
    };
  },
  created() {
    this.getHotList();
  },
  methods: {
    getHotList() {
      this.hotLoading = true;
      this.api
        .getHotSearchRecommendPage(this.params)
        .then((res) => {
          this.hotLoading = false;
          this.hotList = res.data || [];
          this.params.totalSize = res.totalSize;
        })
        .catch(() => {
          this.hotLoading = false;
        });
    },
    getKeywordsList() {
      this.keywordsLoading = true;
      this.api
        .getSearchDailyStatisticsPage(this.params)
        .then((res) => {
          this.keywordsLoading = false;
          this.keywordsList = res.data;
          this.params.totalSize = res.totalSize;
        })
        .catch(() => {
          this.keywordsLoading = false;
        });
    },
    queryKeyWordsData(form) {
      this.headerValue = "搜索量";
      this.params = {
        ...this.params,
        ...form,
      };
      this.activeName === "hot" ? this.getHotList() : this.getKeywordsList();
    },
    reset(form) {
      this.headerValue = "近一个月搜索量";
      this.params = {
        pageIndex: 1,
        pageSize: 20,
        totalSize: 0,
        sortType: 2,
        ...form,
      };
       this.$refs.hotMultiple.clearSort();
       this.activeName === "hot" ? this.getHotList() : this.handleDate(this.dateType);
    },
    tabsClick() {
      this.selectVa = null
      this.keywordsVal = '1'
      this.deleteData = [];
      this.headerValue = "近一个月搜索量";
      this.$refs.form.toggleTabs();
      this.$refs.hotMultiple.clearSelection();
      this.$refs.keyWordsMultiple.clearSelection();
      this.params = {
        content: "",
        module: "",
        startTime: "",
        endTime: "",
        pageIndex: 1,
        pageSize: 20,
        totalSize: 0,
        sortType: 2,
      };
      this.activeName === "hot" ? this.getHotList() : this.handleDate(this.dateType);
    },
    sortChange(column) {
      this.params.pageIndex =1;
      if(column.order === 'ascending') {
        this.params.sortType = 1
      } else {
        this.params.sortType = 2
      }
      this.getHotList()
    },
    handleOperate(val, row) {
      switch (val) {
        case "cancel":
          this.updateVal = row;
          this.popupValue = val;
          this.title = "取消推荐";
          this.$refs.pop.showPopup();
          break;
        case "set":
          this.updateVal = row;
          this.popupValue = val;
          this.title = "设为推荐";
          this.$refs.pop.showPopup();
          break;
        case "edit":
          this.updateVal = row;
          this.popupValue = val;
          this.title = "编辑热门搜索";
          this.$refs.pop.showPopup();
          break;
        case "delete":
          this.popupValue = val;
          this.deleteData = [row];
          this.queryIsRecommend();
          break;
        case "add":
          this.popupValue = val;
          this.title = "添加热门搜索";
          this.$refs.pop.showPopup();
          break;
        case "batch":
          if (this.deleteData && this.deleteData.length) {
            this.popupValue = "delete";
            this.queryIsRecommend();
          } else {
            this.$message.warning("请选择至少一条数据");
          }
          break;
      }
    },
    queryIsRecommend() {
      let isRecommend = this.deleteData.some((item) => {
        return item.recommend === 1;
      });
      if (isRecommend) {
        this.PUBLIC_Methods.apiNotify("推荐中的关键词不可删除");
      } else {
        this.title = "删除";
        this.$refs.pop.showPopup();
      }
    },
    handleHotList(val) {
      this.deleteData = val;
    },
    commandChange(val) {
      val === '2' ?  this.params.recommend = '' : this.params.recommend = val;
      this.selectVal = val
      this.getHotList();
    },
    changeDropdown(val) {
      this.keywordsVal = val
      this.dateType = val;
      switch (val) {
        case "1":
          this.headerValue = "近一个月搜索量";
          this.handleDate(1);
          break;
        case "2":
          this.headerValue = "近三个月搜索量";
          this.handleDate(3);
          break;
        case "3":
          this.headerValue = "近半年搜索量";
          this.handleDate(6);
          break;
        case "4":
          this.headerValue = "近一年搜索量";
          this.handleDate(12);
          break;
      }
    },
    handleDate(val) {
      let date = new Date();
      let dateHours = date.getHours()
      let dateMinutes = date.getMinutes();
      let dateSeconds = date.getSeconds();
      if (dateHours < 10) {
        dateHours = "0" + dateHours;
      }
      if (dateMinutes < 10) {
        dateMinutes = "0" + dateMinutes;
      }
      if (dateSeconds < 10) {
        dateSeconds = "0" + dateSeconds;
      }
      let endTime = date.toLocaleDateString().split("/").join("-");
      this.params.endTime = endTime + " " + dateHours + ":" + dateMinutes + ":" + dateSeconds
      date.setMonth(date.getMonth() - val);
      let startTime = date.toLocaleDateString().split("/").join("-");
      this.params.startTime = startTime + " " + dateHours + ":" + dateMinutes + ":" + dateSeconds
      this.getKeywordsList();
    },
    current_change(val) {
      this.params.pageIndex = val;
      this.activeName === "hot" ? this.getHotList() : this.getKeywordsList();
    },
    handleSizeChange(val) {
      this.params.pageSize = val;
      this.activeName === "hot" ? this.getHotList() : this.getKeywordsList();
    },
    refreshData() {
      this.getHotList();
    },
    cancel() {
      this.deleteData = []
      this.updateVal = {}
      this.$refs.hotMultiple.clearSelection();
    },
    resetEditData() {
      this.updateVal = {}
    }
  },
};
</script>
<style scoped lang="scss">
.container {
  position: relative;
}
/deep/ .el-tabs__nav-wrap::after {
  height: 0;
}
.el-pagination {
  margin-top: 20px;
  float: right;
}
.menu {
  width: 200px;
  padding: 10px;
}
.but {
  position: absolute;
  top: 78px;
  right: 40px;
}
</style>
