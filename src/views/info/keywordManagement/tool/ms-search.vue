<template>
  <div class="container">
    <el-form
      :inline="true"
      :model="searchForm"
      class="demo-form-inline rule-form"
    >
      <el-form-item label="关键词">
        <el-input
          v-model="searchForm.content"
          placeholder="输入关键词"
        ></el-input>
      </el-form-item>
      <el-form-item label="产品类别">
        <el-select v-model="searchForm.module" placeholder="请选择">
          <el-option label="基金" value="nsfc"></el-option>
          <el-option label="期刊" value="tool_impact_factor"></el-option>
          <el-option label="指南" value="guider"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="时间筛选">
        <el-date-picker
          format="yyyy-MM-dd HH:mm:ss"
          value-format="yyyy-MM-dd HH:mm:ss"
          v-model="date"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="pickerChange"
        >
        </el-date-picker>
      </el-form-item>
      <el-button @click="reset">重置</el-button>
      <el-button type="primary" plain @click="queryKeyWordsData"
        >查询</el-button
      >
    </el-form>
  </div>
</template>
<script>
export default {
  data() {
    return {
      searchForm: {
        content: "",
        module: "",
        startTime:'',
        endTime: '',
      },
       date: "",
    };
  },
  methods: {
    reset() {
      this.searchForm = {
        content: "",
        module: "",
        startTime:'',
        endTime: '',
      };
      this.date = ''
      this.$emit("reset",this.searchForm)
    },
    queryKeyWordsData() {
      if(this.date && this.date.length) {
        this.searchForm.startTime = this.date[0]
         this.searchForm.endTime = this.date[1]
      }
        this.$emit("queryKeyWordsData",this.searchForm)
    },
    toggleTabs() {
        this.searchForm = {
        content: "",
        module: "",
        startTime:'',
        endTime: '',
      };
       this.date = ''
    },
    pickerChange() {
      if(!this.date) {
        this.searchForm.startTime = ''
         this.searchForm.endTime = ''
      }
    }
  },
};
</script>
<style scoped lang="scss">
.el-form {
  text-align: left;
  margin-bottom: 30px;
}
</style>