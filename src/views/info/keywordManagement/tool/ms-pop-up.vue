<template>
  <div class="container">
    <el-dialog :title="title" :visible.sync="dialogVisible" width="30%">
      <el-form :inline="true" class="demo-form-inline rule-form"  :rules="rules" :model="addForm" ref="submitRef">
        <template v-if="popupValue === 'add' || popupValue === 'edit'">
          <el-form-item label="关键词" prop="content">
            <el-input
              placeholder="单行输入"
              v-model="addForm.content"
              class="ms mt"
            ></el-input>
          </el-form-item>
          <el-form-item label="产品类型" prop="module">
            <el-select placeholder="全部" v-model="addForm.module" class="mt">
              <el-option label="基金" value="nsfc"></el-option>
              <el-option label="期刊" value="tool_impact_factor"></el-option>
              <el-option label="指南" value="guider"></el-option>
            </el-select>
          </el-form-item>
        </template>
        <template v-if="popupValue === 'cancel'">
          <p>取消推荐后,该关键词不再热门搜索排行中。</p>
        </template>
        <template v-if="popupValue === 'set'">
          <el-form-item label="推荐权重" prop="recommendWeight">
            <el-input
              placeholder="单行输入"
              v-model="addForm.recommendWeight"
              class="mt"
            ></el-input>
          </el-form-item>
          <p class="mc">权重为热门搜索的排序规则,数值越大排序越靠前。</p>
        </template>
        <template v-if="popupValue === 'delete'">
          <p>确定将该关键字从列表中删除?</p>
          <el-tag
            type="info"
            v-for="(item, index) in deleteData"
            :key="index"
            class="md"
            >{{ item.content }}</el-tag
          >
        </template>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" @click="confirm">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
export default {
  props: {
    popupValue: {
      type: String,
      default: "",
    },
    title: {
      type: String,
      default: "",
    },
    deleteData: {
      type: Array,
      default: () => [],
    },
    updateVal: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      dialogVisible: false,
      addForm: {
        content: "",
        module: "",
        recommendWeight: "",
        username:''
      },
      params: {},
       rules: {
        content: [
          { required: true, message: "请输入关键词", trigger: "blur" },
        ],
        module: [
          { required: true, message: "请选择产品类型", trigger: "blur" },
        ],
        recommendWeight: [
          { required: true, message: "请输入推荐权重", trigger: "blur" },
        ],
      },
    };
  },
  watch: {
    updateVal: function(val) {
      this.addForm = {
        content: val.content,
        module: val.module,
      };
    },
  },
  methods: {
    showPopup() {
      this.addForm = {
        content: "",
        module: "",
        recommendWeight: "",
      },
      this.dialogVisible = true;
    },
    confirm() {
      this.$refs.submitRef.validate(valid => {
        if(valid) {
      this.dialogVisible = false;
      switch (this.popupValue) {
        case "delete":
          this.handleDelete();
          this.$emit("cancel")
          break;
        case "add":
          this.handleAdd();
          break;
        case "cancel":
          this.params = {
            id: this.updateVal.id,
            recommend: 0,
          };
          this.handleUpdate(this.params);
          break;
        case "set":
          this.params = {
            recommendWeight: this.addForm.recommendWeight,
            id: this.updateVal.id,
            recommend: 1,
          };
          this.handleUpdate(this.params);
          break;
        case "edit":
          this.params = {
            id: this.updateVal.id,
            content: this.addForm.content,
            module: this.addForm.module,
            username: JSON.parse(window.sessionStorage.getItem('user_info')).userName
          };
          this.handleUpdate(this.params);
          break;
      }
        }
      })
    },
    cancel() {
       this.dialogVisible = false;
       this.$emit("cancel")
    },
    handleDelete() {
      let params = {
        ids: this.deleteData.map((item) => item.id),
      };
      this.api.removeHotSearchRecommend(params).then((res) => {
        if (res.status === 200) {
          this.$emit("refreshData");
          this.PUBLIC_Methods.apiNotify(res.message || '请求成功', 'success')
        } else {
          this.PUBLIC_Methods.apiNotify(res.message || '请求出错', 'warning')
        }
      });
    },
    handleAdd() {
      this.addForm.username = JSON.parse(window.sessionStorage.getItem('user_info')).userName
      this.api.saveHotSearchRecommend(this.addForm).then((res) => {
        if (res.status === 200) {
          this.$emit("refreshData");
          this.PUBLIC_Methods.apiNotify(res.message || '请求成功', 'success')
        } else {
          this.PUBLIC_Methods.apiNotify(res.message || '请求出错', 'warning')
        }
      });
    },
    handleUpdate(params) {
      this.api.updateHotSearchRecommend(params).then((res) => {
        if (res.status === 200) {
          this.$emit("refreshData");
           this.PUBLIC_Methods.apiNotify(res.message || '请求成功', 'success')
        } else {
          this.$emit("resetEditData")
           this.PUBLIC_Methods.apiNotify(res.message || '请求出错', 'warning')
        }
      });
    },
  },
};
</script>
<style scoped lang="scss">
.dialog-footer {
  display: flex;
  justify-content: center;
}
.el-form {
  text-align: left;
}
.ms {
  margin-left: 12px;
}
.mt {
  width: 230px;
}
.md {
  margin: 5px;
}
.mc {
  color: #909399;
}
</style>
