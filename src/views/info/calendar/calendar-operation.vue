<template>
  <section class="form-tab" v-loading="getLoading">
    <!-- 表单内容 -->
    <el-form ref="submitRef"
          class="rule-form"
          :model="submitData"
          :rules="rules"
          label-width="80px">
      <el-row>
        <template v-for="(item, index) in formConfig.formField">
          <el-col :key="index"
                  :span="item.colSpan">
            <el-form-item :prop="item.prop"
                          :label="item.label">
              <component :is="item.component"
                        :model.sync="submitData[item.prop]"
                        v-model="submitData[item.prop]"
                        :width="item.width || '100%'"
                        :disabled="item.disabled || isReadOnly"
                        :type="item.type"
                        :active="item.active"
                        :inactive="item.inactive">
              </component>
            </el-form-item>
          </el-col>
        </template>
      </el-row>
    </el-form>
    <!-- 提交按钮 -->
    <footer-tool-bar v-loading="buttonLoading">
      <template slot="tool-content">
        <el-button type="primary" v-if="!isReadOnly" @click="info_operation('save')">保存</el-button>
        <el-button type="info" @click="info_operation('back')">返回</el-button>
      </template>
    </footer-tool-bar>
  </section>
</template>

<script>
import FooterToolBar from '@/components/ToolBar/footer-tool-bar'
import formConfig from './data/form-config' 
import MsEditor from '@/components/MsEditor'
import { mapGetters } from "vuex";
export default {
  name: "calendar-operation",
	data () {
		return {
      buttonLoading: false,
      getLoading: false,
      formConfig: formConfig,
      dataId: this.$route.query.id ? this.$route.query.id : 0,
      isReadOnly: this.$route.query.operation && this.$route.query.operation === 'readOnly' ? true : false,
      submitData: { 
        calendarTitle: "",
        calendarTag: 0,
        startAt: "",
        endAt: "",
        content: "",
        calendarTime: []
      },
      rules: {
        calendarTitle: [
          { required: true, message: "请输入标题", trigger: 'blur' }
        ],
      }
		}
  },
  components: {
    FooterToolBar,
    MsEditor
  },
  computed: {
    ...mapGetters(["info"])
  },
  created() {
    this.init()
  },
  methods: {
    init() {
      let id = this.dataId
      if(id !== 0) {
        this.getLoading = true;
        this.submitData.id = id;
        this.api.getOperaCalen({id: id}).then( response => {
          this.getLoading = false;
          if(response.status === 200) {
            let res = response.data
            this.submitData = {
              ...this.submitData,
              ...res,
              content: this.PUBLIC_Methods.unexcapeHtml(res.content),
              calendarTime: res.startAt && res.endAt ? [res.startAt, res.endAt] : null
            }
          } else {
            this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
          }
        }).catch(() => {
          this.getLoading = false;
        })
      } 
    },
    info_operation(val) {
      switch (val) {
        case 'save': 
          if (!this.isReadOnly) {
            this.$refs["submitRef"].validate( valid => {
              if (valid) {
                if (this.submitData.calendarTime) {
                  this.submitData.startAt = this.submitData.calendarTime[0] || ''
                  this.submitData.endAt = this.submitData.calendarTime[1] || ''
                }
                this.submitData.content = this.PUBLIC_Methods.excapeHtml(this.submitData.content)
                this.dataId ? this.updateCalendar() : this.createCalendar()
              }
            })
          }
          break;
        case 'back':
          this.$router.back();
          break;
        default: break;
      }
    },
    createCalendar() {
      this.buttonLoading = true;
      let params = {
        userId: this.info.userId,
        username: this.info.userName,
        ...this.submitData
      }
      this.api.insertOperaCalen(params).then(response => {
        this.buttonLoading = false
        if(response.status === 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
          this.$router.back()
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
      }).catch(() => this.buttonLoading = false)
    },
    updateCalendar() {
      this.buttonLoading = true;
      let params = {
        userId: this.info.userId,
        username: this.info.userName,
        ...this.submitData
      }
      this.api.updateOperaCalen(params).then(response => {
        this.buttonLoading = false
        if(response.status === 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
          this.$router.back()
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
      }).catch(() => this.buttonLoading = false)
    }
  }
}
</script>
