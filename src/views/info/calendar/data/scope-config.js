const scopeConfig = {
  show: {
    calendarTag: () => {
      return {
        type: 'icon',
        icon: 'icon-xingxing',
        showVal: 1,
        color: '#FFCE92'
      }
    },
    calendarTitle: () => {
      return {
        type: 'preview',
        config: {
          component: 'msCalendarPreview',
          way: 'dialog',
          type: 'info',
          width: '60%',
          title: '内容查看'
        }
      }
    },
    startAt: () => {
      return {
        type: 'formatTime',
        cFormat: '{m}-{d}'
      }
    },
    endAt: () => {
      return {
        type: 'formatTime',
        cFormat: '{m}-{d}'
      }
    }
  }
}

export default scopeConfig;
