const domConfig = {
  listSearch: [
    {
      label: '事件',
      placeholder: '请输入事件名称',
      model: 'calendarTitle',
      component: 'ms-input'
    },
    {
      label: '开始时间',
      placeholder: '',
      model: 'startBeginAt',
      component: 'ms-month-search'
    },
    {
      label: '~',
      placeholder: '',
      model: 'startStopAt',
      component: 'ms-month-search'
    }
  ],
  tableHeader: [
    { label: '', property: 'calendarTag', width: '30' },
    { label: '事件名称', property: 'calendarTitle', width: '300' },
    { label: '开始时间', property: 'startAt', sortable: true },
    { label: '结束时间', property: 'endAt', sortable: true },
    { label: '创建人', property: 'createdName', width: '100' }
  ],
  tableButtons: [
    {
      label: '查看',
      icon: '',
      role: '',
      operation: 'readOnly',
      component: 'msCalendarPreview',
      way: 'dialog',
      type: 'info',
      width: '60%',
      title: '内容查看',
      identify: 'view'
    },
    {
      label: '编辑',
      icon: '',
      role: '',
      operation: 'edit',
      component: 'calendar-operation',
      way: 'page',
      type: 'primary',
      path: 'calendar-operation',
      params: ['id'],
      identify: 'edit'
    },
    {
      label: '删除',
      icon: '',
      role: '',
      type: 'danger',
      way: 'delete',
      identify: 'delete'
    }
  ],
  soltButtons: [
      { 
        label: '添加', 
        icon: 'el-icon-plus',
        type: 'primary', 
        operation: 'created',
        component: 'calendar-operation',
        way: 'page',
        path: 'calendar-operation',
        params: ['id'],
        identify: 'created'
      }
    ]
}

export default domConfig;
