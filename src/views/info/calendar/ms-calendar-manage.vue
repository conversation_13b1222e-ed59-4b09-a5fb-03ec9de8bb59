<template>
	<ms-table
		:currentPage="searchParams.pageIndex"
		:loading="loading"
		:operationButtons="domConfig.tableButtons"
		:pageSize="searchParams.pageSize"
		:scopeConfig="scopeConfig.show"
		:tableData="list"
		:tableHeader="domConfig.tableHeader"
		:total="total"
		@current-change="current_change"
		@operation-change="operation_change"
		@size-change="size_change"
    @header-operation="header_operation"
    class="table-svg"
	>
    <!-- 列表搜索去区域插槽 -->
		<template slot="ms-table-header">
			<div class="slot-search">
				<template v-for="(searchItem, key) in domConfig.listSearch">
					<component
						:index="searchItem.index || ''"
						:is="searchItem.component"
						:key="key"
            :width="searchItem.width || '150px'"
            :model.sync="searchParams[searchItem.model]"
            :label="searchItem.label"
						:operation="searchItem.operation || ''"
						:options="searchItem.options || []"
						:placeholder="searchItem.placeholder || ''"
						:type="searchItem.type || ''"
            :multiple="searchItem.multiple"
            :disabled="searchItem.disabled" 
					></component>
				</template>
        <div class="inlineBlock">
          <el-button @click="handleClick('query')" type="primary" plain icon="el-icon-search">查询</el-button>
          <el-button @click="handleClick('reset')">重置</el-button>
        </div>
			</div>
      <div class="slot-button">
        <template v-for="(item, index) in domConfig.soltButtons">
          <el-button :key="index" v-show="!item.roleDisabled" :type="item.type" :icon="item.icon" @click="operation_change({operation: item})" plain>{{ item.label }}</el-button>
        </template>
      </div>
      <el-dialog :visible.sync="dialog" 
                 closeable 
                 show-close
                 :close-on-click-modal="true"
                 :width="dialogWidth"
                 :title="dialogTitle">
				<component :is="dialogComponent" 
                   :model="scopeInfo" 
                   :operation="dialogOperation" 
                   @close="dialog = !dialog" 
                   @up-date="init" 
                   v-if="dialog"></component>
			</el-dialog>
		</template>
	</ms-table>
</template>

<script>
import tableMixins  from "../../common/mixins/table"
import domConfig from "./data/dom-config"
import scopeConfig from "./data/scope-config"
import msMonthSearch from '@/components/MsCommon/ms-month-search'
import msCalendarPreview from '@/components/MsCommon/ms-calendar-preview'
import {parseTime} from "@/utils"
export default {
  name: "ms-calendar-manage",
  mixins: [tableMixins],
  data () {
    return {
      domConfig: domConfig,
      scopeConfig: scopeConfig, 
      //searchParams: { // => 列表查询传参
        // calendarTitle: "",
        // startBeginAt: "",
        // startStopAt: "",
        // endBeginAt: "",
        // endStopAt: "",
        // startTime: [],
        // endTime: []
      //} 
    }
  },
  components: {
    msCalendarPreview,
    msMonthSearch
  },
  methods: {
    apiInit (params) {
      let searchParams = {...params}
      this.api.getOperaCalenPage(searchParams).then(response => {
        this.loading = false
        this.total = response.totalSize || 0;
        this.list = response.data.map( v => {
          return {
            ...v,
            startAt: v.startAt ? parseTime(v.startAt,'{m}-{d}') : null,
            endAt: v.endAt ? parseTime(v.endAt,'{m}-{d}') : null
          }
        })
        if (response.status !== 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
      }).catch(() => this.loading = false)
    },
    operation_change_module (val) {
      switch (val.operation.way) {
        case "delete":
          this.$confirm('此操作将永久删除事件信息，是否继续', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.api.delOperaCalen({id: val.model.id}).then(response => {
              if(response.status === 200) {
                this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
              } else {
                this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
              }
              this.init()
            })
          })
          break;
        default: break;
      }
    },
  }
};
</script>
