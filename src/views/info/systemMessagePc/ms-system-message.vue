<template>
  <div class="comment_manage">
    <ms-table
      :currentPage="searchParams.pageIndex"
      :loading="loading"
      :operationButtons="domConfig.tableButtons"
      :pageSize="searchParams.pageSize"
      :scopeConfig="scopeConfig.show"
      :scopeHeader="scopeConfig.headerShow"
      :tableData="list"
      :tableHeader="domConfig.tableHeader"
      :total="total"
      @current-change="current_change"
      @operation-change="operation_change"
      @size-change="size_change"
      @header-operation="header_operation"
      :showSelection="true"
      @handleSelectionChange="handleSelectionChange"
    >
      <template slot="ms-table-header">
        <div class="slot-search">
          <template v-for="(searchItem, key) in domConfig.listSearch">
            <component
              :index="searchItem.index || ''"
              :is="searchItem.component"
              :key="key"
              :width="searchItem.width || '150px'"
              :model.sync="searchParams[searchItem.model]"
              :label="searchItem.label"
              :operation="searchItem.operation || ''"
              :options="searchItem.options || []"
              :placeholder="searchItem.placeholder || ''"
              :type="searchItem.type || ''"
              :multiple="searchItem.multiple"
              :disabled="searchItem.disabled"
            ></component>
          </template>
          <div class="inlineBlock">
            <el-button @click="handleClick('query')" type="primary" plain icon="el-icon-search">查询</el-button>
            <el-button @click="handleClick('reset')">重置</el-button>
          </div>
        </div>
        <div class="slot-button">
          <template v-for="(item, index) in domConfig.soltButtons">
            <el-button
              :key="index"
              :type="item.type"
              :icon="item.icon"
              @click="operation_change({operation: item})"
              plain
            >{{ item.label }}</el-button>
          </template>
        </div>
        <el-dialog
          :visible.sync="dialog"
          closeable
          show-close
          :close-on-click-modal="false"
          :width="dialogWidth"
          :title="dialogTitle"
        >
          <component
            :is="dialogComponent"
            :model="scopeInfo"
            :operation="dialogOperation"
            @close="dialog = !dialog"
            @up-date="init"
            v-if="dialog"
          ></component>
        </el-dialog>
      </template>
    </ms-table>
  </div>
</template>

<script>
import tableMixins from "../../common/mixins/table";
import dataMixin from "./data-mixin"
export default {
    name: "ms-system-message",
    mixins: [dataMixin,tableMixins],
    data() {
        return {
            loading: false,
            searchParams: {},
        };
    },
    methods: {
        apiInit() {
          // console.log(this.searchParams,'123');
            let params = { 
              createdName:this.searchParams.createdName,
              messageTitle:this.searchParams.messageTitle,
              pageIndex:this.searchParams.pageIndex,
              pageSize:this.searchParams.pageSize,
            };
            if (this.searchParams.createTime) {
              params.createStartTime = this.searchParams.createTime[0] || ''
              params.createEndTime = this.searchParams.createTime[1] || ''

            }
            this.api.queryMessagePush(params).then(response => {
                this.loading = false;
                this.total = response.totalSize || 0;
                this.list = []
                if (response.status === 200) {
                    this.list = response.data || [];
                    this.list.map(item => {
                      return item.contentId = item.moduleInfo ? JSON.parse(item.moduleInfo).contentId:''
                    })
                    this.list.forEach(item => {
                        item.exNum = Number(item.appSendNum)  + Number(item.exposureNum) + '/' +  item.exposureNum + '/' + item.appSendNum
                        item.clickNum = Number(item. appReadNum) + Number(item.uniqueVisitor) + '/' + item.uniqueVisitor + '/' + item. appReadNum  
                    })
                } else {
                    this.PUBLIC_Methods.apiNotify(response.message || "请求出错","warning");
                }
            })
            .catch(() => (this.loading = false));
        },
        operation_change_module(val) {
          if (val.operation.way === 'systemTypeLink') {
            let query = {}
            let page = ''
            if (val.model.objType === 'article') {
              page = 'article-operation'
              query = {
                operation: 'edit',
                component: 'article-operation',
                id: val.model.objId
              }
            } else if (val.model.objType === 'guider'){
              page = 'guider-operation'
              query = {
                operation: 'edit',
                component: 'guider-operation',
                id: val.model.objId
              }
            } else if (val.model.objType === 'tool_impact_factor') {
              page = 'journal-operation'
              query = {
                operation: 'edit',
                component: 'journal-operation',
                id: val.model.objId
              }
            } else if (val.model.objType === 'video') {
              page = 'video-manage-single'
              query = {
                operation: 'edit',
                component: 'video-manage-single',
                id: val.model.objId
              }
            }
            this.$router.push({
              path: page,
              query: query
            })
          }
        } 
    }
};
</script>
