<template>
    <ms-operation-dialog :title="`是否要${dealType === 1 ? '审核' : dealType === 3 ? '启用' : dealType === 4 ? '禁用' : dealType === 5 ? '删除' : ''}以下系统通知`">
        <template slot="content">
        <el-tag v-for="(id, index) in strArr" 
                :key="index" 
                type="info" 
                style="margin: 0 5px 5px 0">{{id}}</el-tag>
        </template>
        <template slot="footer">
        <el-button @click="submitForm"
                    :loading="loading"
                    size="mini"
                    type="primary">确 定</el-button>
        <el-button @click="$emit('close')"
                    size="mini">取 消</el-button>
        </template>
    </ms-operation-dialog>
</template>

<script>
export default {
	name: "ms-system-operation",
	data () {
		return {
      loading: false,
      userInfo: {},
      dealType: null,
      ids: [],
      strArr: []
		}
	},
	props: [
		"model",
		"operation"
  ],
  created() {
    this.userInfo = this.$store.getters.info || {}
    let arr = []
    let showArr = []
    let operationLocal = this.operation || this.$route.query.operation
    // console.log(operationLocal,'x11');
    this.dealType = operationLocal === 'approval' ? 1 : operationLocal === 'forbidden' ? 3 : operationLocal === 'startUsing' ? 4 : operationLocal === 'delete' ? 5: null;
    // console.log(this.model,'x');
    // console.log(this.id,'x');
    if (this.PUBLIC_Methods.isArrayFn(this.model)) {
      this.model.forEach(item => {
        arr.push(item.id)
        showArr.push(item.id)
      });
    } else {
      arr.push(this.model.id)
      showArr.push(this.model.id)
    }
    this.ids = arr
    this.strArr = showArr
  },
	methods: {
		submitForm () {
      if(this.dealType == '5'){
        this.loading = true;
        let params = {
          // userId: this.userInfo.userId,
          // username: this.userInfo.userName,
          dealType: this.dealType,
          ids: this.ids
        }
        this.api.dealMessageDelete(params).then(response => {
          if(response.status === 200) {
            this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
          } else {
            this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
          }
          this.$emit('up-date')
          this.loading = false
        }).catch( () => {
          this.loading = false;
          this.$emit('close')
        })
      }else{
        this.loading = true;
        let params = {
          // userId: this.userInfo.userId,
          // username: this.userInfo.userName,
          dealType: this.dealType,
          id: this.model.id
        }
        this.api.dealMessagePush(params).then(response => {
          if(response.status === 200) {
            this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
          } else {
            this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
          }
          this.$emit('up-date')
          this.loading = false
        }).catch( () => {
          this.loading = false;
          this.$emit('close')
        })
      }
		}
	}
}
</script>
