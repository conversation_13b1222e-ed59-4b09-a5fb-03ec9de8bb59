<template>
  <section>
    <el-form :model="submitData"
             ref="submitRef"
             class="rule-form info-form"
             :rules="rules"
             label-width="70px">
      <el-row :gutter="20"> 
        <el-col :span="20">
          <el-row>
            <el-col :span="10">
              <el-form-item label="通知标题">
                <el-input v-model="submitData.messageTitle" style="width: 100%"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="10">
              <el-row :gutter="24">
                <el-col :span="12">
                  <el-form-item label="模块信息">
                    <ms-dictionary-search :model.sync='submitData.messageModule' :modelName.sync='search.contentType' :clearable="true" type="user_message_push" @messageSearch="messageSearch"></ms-dictionary-search>
                    <!-- <ms-dictionary-search v-model="keywords" :model.sync="keywords" :modelName.sync='search.contentType' type="special_topic" :clearable="true" style="width: 100%;"></ms-dictionary-search> -->
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label-width="0px">
                    <!-- <el-input placeholder="请搜索选择模块内容" v-model="submitData.moduleInfo"></el-input> -->
                    <el-select style="width:100%" v-model="search.contentTitle" value-key="contentId" :loading="getLoading" @change="messageChange" filterable remote :remote-method="remoteContent" placeholder="输入标题关键字搜索" clearable>
                    <el-option
                      v-for="item in moduleInfoList"
                      :key="item.contentId"
                      :label="item.contentTitle"
                      :value="item">
                    </el-option>
                  </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-col>
            <el-col :span="20">
              <el-form-item label="通知正文">
                <el-input v-model="submitData.messageContent" type="textarea" :rows="4" maxlength="1024" show-word-limit></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="20">
              <el-form-item label="发送类型" prop="sendType">
                <el-radio-group style="width: 400px" v-model="submitData.sendType" @change="resetSendObject"  placeholder="请选择类型">
                  <el-radio :label="0">全部</el-radio>
                  <el-tooltip effect="dark" content="仅针对浏览器用户生效，原生会全量发送" placement="top">
                    <el-radio :label="1">按需选择</el-radio>
                  </el-tooltip>
                  <el-tooltip effect="dark" content="仅针对浏览器用户生效，原生会全量发送" placement="top">
                  <el-radio :label="2">白名单发送</el-radio>
                </el-tooltip>
                </el-radio-group>
              </el-form-item>
            </el-col>
            
            <el-col :span="20" v-if="submitData.sendType == 1">
              <el-form-item class="send1" label="发送对象">
                <!-- <el-select v-model="product" clearable placeholder="选择产品">
                  <el-option
                    v-for="item in productList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                  </el-option>
                </el-select> -->
                <el-cascader
                  placeholder="所在科室"
                  @change="changeCategoryId"
                  ref="CategoryId"
                  :options="sendObject.departmentModelList"
                  :props="{
                    multiple: true,
                    value:'departmentId',
                    label:'departmentName',
                    children:'departmentChildList',
                    emitPath: false
                  }"
                  v-model="departments"
                  collapse-tags
                  clearable>
                </el-cascader>
                <el-cascader
                  placeholder="用户身份"
                  @change="changeIdentity"
                  ref="Identity"
                  :options="sendObject.identityModelList"
                  :props="{
                    multiple: true,
                    value:'identityId',
                    label:'identityIdName',
                    children:'identityChildList',
                    emitPath: false
                  }"
                  v-model="identity"
                  collapse-tags
                  clearable>
                </el-cascader>
                <el-button @click="getUser" :loading="loading">确定</el-button>
                <span>共{{submitData.countNum}}个用户</span>
              </el-form-item>
            </el-col>

            <el-col :span="5" v-if="submitData.sendType == 2">
              <el-form-item class="upload" label="发送对象" prop="attachmentList">
                <div class="upload-top">
                  <!-- <ms-file-upload v-model="submitData.attachmentList" buttonLabel="上传文件" style="display: inline-block;"></ms-file-upload> -->
                  <el-upload 
                             action=""
                             class="upload-conten"
                             ref="upload"
                            :on-change="onChange"
                            :http-request="upLoad"
                            :file-list="fileList"
                            :before-upload="beforeUpload"
                            :disabled="buttonLoading"
                            :limit="2">
                    <el-button plain :loading="buttonLoading" icon="el-icon-upload">上传文件</el-button>
                  </el-upload>
                </div>
                <div class="upload-bottom">
                  
                  <a href="https://static.medsci.cn/doc/%E7%99%BD%E5%90%8D%E5%8D%95%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BF.csv" style="color: #aae1f8;">下载导入模板</a>
                  <span v-if="uploadError.length != 0" class="upload-warning" @click="centerDialogVisible = true">{{uploadError.length}}条不可发送</span>
                
                </div>
              </el-form-item>
            </el-col>
          </el-row> 
          <el-row>
            <el-col :span="10">
              <el-form-item label="发送时间" prop="sendTimeStart">
                <el-date-picker
                  @change="change"
                  v-model="submitData.sendTimeStart"
                  type="datetime"
                  format="yyyy-MM-dd HH:mm"
                  :picker-options="pickerOptions"
                  placeholder="消息发送时间">
                </el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>
        </el-col>
      </el-row>
    </el-form>
    <el-dialog
      title="以下手机号码异常，无法发送"
      :visible="centerDialogVisible"
      width="30%"
      center>
      <div style="margin-left: 40px; line-height: 30px;" v-for="(item, index) in uploadError" :key="index">{{item.mobile}}</div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="centerDialogVisible = false">好 的</el-button>
      </span>
    </el-dialog>
  </section>
</template>

<script>
import msDictionarySearch from '@/components/MsCommon/ms-dictionary-search'
import { mapGetters } from "vuex";
import { getSession } from "@/utils/auth";
import moment from 'moment'
import axios from "axios";
export default {
	name: "ms-message-content",
	data () {
		return {
      centerDialogVisible: false,
      // publishedTime:'',//时间集合
      search: {
        contentTitle: "",
        contentType: "",
        contentId:'',
        type:''
      },
      sendObject:{
          productModelList: [],
          departmentModelList:[],
          identityModelList:[]
      }, 
      identityList:[],
      moduleInfoList:[], //模块信息列表
      buttonLoading:false, //上传Loading
      getLoading:false, //关键词搜索Loading
      // product:'',
      departments:'',
      identity:'',
      fileList:[], //上传的文件
      uploadError:[], // 错误信息
      rules: {
        sendTimeStart: [
          { required: true, message: "请填写发送时间", trigger: 'change' }
        ],
        sendType: [
          { required: true, message: "请选择发送类型", trigger: 'change' }
        ],
      },
      payData: [],
      loading: false,
      versionList: [],
      otherLoading: false,
      formatDisabled: false,
      summaryKeyUp: false,
      pickerOptions: {
        disabledDate: time => {
          let delay = new Date().getTime() - 86400000
          if(delay){
            // 小于接口返回的值得日期全部禁选
            return time.getTime() < delay
          }
        }
      }
		}
  },
  components: {
    msDictionarySearch
  },
  props:["submitData",'outDepartmentsList','outIdentityList'],
  computed: {
    ...mapGetters(["info", "localCode"])
  },
  watch: {
    // 'submitData.id': function (val) {
    //   if (val) {
    //     this.formatDisabled = true
    //   } 
    // },
    // 'submitData.associationName': function (val) {
      
    //   if (val && val.indexOf('中华医学会') !== -1) {
    //     this.submitData.copyright = 1
    //   }
    // },
    // 'submitData.recommend': function (val) {
    //   if (val === 1) {
    //     this.submitData.sticky = 0
    //   }
    // },
    // 'submitData.sticky': function (val) {
    //   if (val === 1) {
    //     this.submitData.recommend = 0
    //   }
    // },
  },
  async created() {
    const [res1,res2] = await Promise.all([
      this.api.getProjectModuleAndCategoryList({module: 'message_push'}),
      this.api.getTitleList()
    ])
    this.sendObject.departmentModelList = this.getDepartmentList(res1.data)
    this.sendObject.identityModelList = this.getIdentityList(res2.data)
    this.search = this.submitData.moduleInfo
  },
  mounted() {
    setTimeout(()=>{
      this.departments = this.getFields(this.outDepartmentsList,'departmentId')
      this.identity = this.getFields(this.outIdentityList,'identityId')
    },500)
  },
	methods: {
    change(val){
      if(!val){
        this.sendTimeStart = val
      } else {
        let time = moment(val).format("YYYY-MM-DD HH:mm") + ':59'
        if(new Date(time).getTime()<new Date().getTime()){
          this.$message({
            message: '不能选择当前时间之前的时间',
            type: 'warning'
          })
          this.submitData.sendTimeStart = getSession('message-sendTimeStart') 
        }else {
          // console.log(val,'nn');
          this.submitData.sendTimeStart = moment(val).format("YYYY-MM-DD HH:mm") + ':00'
        }
      }
    },
    validateData() {
      return new Promise((resolve) => {
        this.$refs["submitRef"].validate( valid => {
          if (valid) {
            resolve()
          }else {
            this.$emit('changeTab','content')
          }
        })
      })
    },
    getCoureByTitle(query) {
      this.loading = true
      if(query !== '') {
        let searchParams = {
          title: query,
          type: 1
        }
        this.api.getCoureByTitle(searchParams).then(response => {
          this.loading = false
          this.payData = response.data || []
          if (response.status !== 200) {
            this.PUBLIC_Methods.apiNotify(
              response.message || "请求出错",
              "warning"
            )
          }
        })
        .catch((err) => (console.log(err)))
      } else {
        this.payData = []
      }
    },
    getOtherVersion(query) {
      this.otherLoading = true
      if(query !== '') {
        let searchParams = {
          value: query,
          pageIndex: 1,
          pageSize: 20,
          approvalStatus: 1
        }
        this.api.getToolGuiderPage(searchParams).then(response => {
          this.otherLoading = false
          this.versionList = response.data || []
          if (response.status !== 200) {
            this.PUBLIC_Methods.apiNotify(
              response.message || "请求出错",
              "warning"
            )
          }
        })
        .catch((err) => (console.log(err)))
      } else {
        this.versionList = []
      }
    },
    // changeCoure(val) {
    //   console.log(val,'1111')
    // },
    // changeOtherVersion(val) {
    //   console.log(val, '2222')
    // },
    // 按需选择，获取用户
    async getUser(){
      this.loading = true
      //  console.log(this.submitData.sendObject);
       if(this.submitData.sendObject.departmentModelList.length == 0 && this.submitData.sendObject.identityModelList.length == 0){
        this.loading = false
         return  this.PUBLIC_Methods.apiNotify('筛选用户为空，请重新筛选！', 'warning')
       }else{
        const res = await this.api.getSendObjectNum({...this.submitData.sendObject})
        // console.log(res.data);
        this.submitData.countNum = res.data
        this.loading = false
       }
    },
    // handleRemove(file, fileList) {
    //   console.log(file, fileList,'移除');
    // },
    // 上传
    async upLoad(file){
      // console.log(file);
      let messageFile = new FormData()
      messageFile.append('file', file.file)
      messageFile.append('whiteUnique', this.submitData.whiteUnique||'')
      // const res = await this.api.userWhiteMessageRecord(messageFile)
      const res = await axios({
         method:"post",
         url:window.location.host.includes('medsci.cn')?'https://apigateway.medsci.cn/api/mg/paas-mgr/medsciUserWhiteMessageRecord/leadingIn':'https://apigateway.medon.com.cn/api/mg/paas-mgr/medsciUserWhiteMessageRecord/leadingIn',
         data:messageFile,
         headers: {
           'Content-Type': 'multipart/form-data'
         }
      })
      this.buttonLoading = false
      this.submitData.whiteUnique = res.data.data.whiteUnique
      this.uploadError = res.data.data.leadingInResults
    },
    beforeUpload(file){
      let fileSuffix = file.name.substr(file.name.lastIndexOf('.')).toLowerCase()
      // 危险文件类型校验
      if (fileSuffix === '.bat' || fileSuffix === '.sh') {
          this.$message({
              message: '警告！您上传的文件存在风险',
              type: 'warning'
          })
          this.fileList = []
          return false;
      }
      // 校验文件格式
      if (!/\.(csv)$/.test(fileSuffix)) {
          this.$message({
              message: '请上传csv类型文件',
              type: 'warning'
          })
          this.fileList = []
          return false;
      }
      this.buttonLoading = true
    },
    // 重新上传
    onChange(file, fileList){
      if (fileList.length > 0) {
        this.fileList = [fileList[fileList.length - 1]]
      }
    },
    // 获取模块关键词列表
    remoteContent(keyWord) {
      this.moduleInfoList = []
      if(!(keyWord && keyWord.length > 1)) {
        return
      }
      this.getLoading = true;
      this.search.contentTitle = keyWord
      let params = {
        userId: this.info.userId,
        username: this.info.userName,
        contentType:this.search.contentType,
        contentTitle:this.search.contentTitle,
      }

      this.api.searchContentTitle(params).then( response => {
        this.getLoading = false
        if(response.status === 200) {
          this.moduleInfoList = response.data
          // console.log(this.moduleInfoList,'11');
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
      }).catch(() => {
        this.getLoading = false
      })
    },
    // 更改模块信息
    messageSearch(){
      this.submitData.moduleInfo =''
      this.search = {
        contentTitle: "",
        contentType: "",
        contentId:'',
        type:''
      }
    },
    // 级联选择器科室数据处理
    departmenData(inputData) {
      let resultMap = {};
      let resultList = [];
      // 根据父级值将数据分类
      for (let i = 0; i < inputData.length; i++) {
        if(inputData[i].parent){
          let obj = inputData[i];
          let parentValue = obj.parent.value;
          if (!resultMap[parentValue]) {
            resultMap[parentValue] = [];
          }
          resultMap[parentValue].push(obj);
        }
      }
      // 根据分类后的数据生成结果
      for (let parentValue in resultMap) {
        let childList = [];
        let parentObj = resultMap[parentValue][0].parent;
        for (let i = 0; i < resultMap[parentValue].length; i++) {
          let obj = resultMap[parentValue][i];
          let childObj = {
            departmentName: obj.label,
            departmentId: obj.value
          };
          childList.push(childObj);
        }
        let resultObj = {
          departmentChildList: childList,
          departmentName: parentObj.label,
          departmentId: parentObj.value
        };
        resultList.push(resultObj);
      }
      return resultList;
    },
    // 级联选择器身份数据处理
    identityData(inputData) {
      let resultMap = {};
      let resultList = [];
      // 根据父级值将数据分类
      for (let i = 0; i < inputData.length; i++) {
        if(inputData[i].parent){
          let obj = inputData[i];
          let parentValue = obj.parent.value;
          if (!resultMap[parentValue]) {
            resultMap[parentValue] = [];
          }
          resultMap[parentValue].push(obj);
        }
      }
      // 根据分类后的数据生成结果
      for (let parentValue in resultMap) {
        let childList = [];
        let parentObj = resultMap[parentValue][0].parent;
        for (let i = 0; i < resultMap[parentValue].length; i++) {
          let obj = resultMap[parentValue][i];
          let childObj = {
            identityIdName: obj.label,
            identityId: obj.value
          };
          childList.push(childObj);
        }
        let resultObj = {
          identityChildList: childList,
          identityIdName: parentObj.label,
          identityId: parentObj.value
        };
        resultList.push(resultObj);
      }
      return resultList;
    },
    // 科室
    changeCategoryId(){
      let nodesObj = this.$refs['CategoryId'].getCheckedNodes()
      // console.log(nodesObj);
      let a = this.departmenData(nodesObj)
      this.submitData.sendObject.departmentModelList = a 
      this.submitData.countNum = 0
      // console.log(this.submitData.sendObject.departmentModelList,'1')
    },
    // 身份
    changeIdentity(){
      let nodesObj = this.$refs['Identity'].getCheckedNodes()
      // 特殊数据处理
      nodesObj = nodesObj.map(item => {
        if(item.label == "学生" || item.label == "企业"){
           return {
            label:item.label,
            value:item.value,
            parent:{
              label:item.label,
              value:item.value,
            }
          }
        }else{
          return item
        }
      })
      let a = this.identityData(nodesObj)
      this.submitData.sendObject.identityModelList = a 
      this.submitData.countNum = 0
    },
    //  科室数据总列表处理
    getDepartmentList(data){
      return  data.map(item=>{
          if(item.children){
            return {
              departmentChildList:this.getDepartmentList(item.children),
              departmentId:item.categoryId,
              departmentName:item.titleCn
            }
          }else{
            return {
              departmentId:item.categoryId,
              departmentName:item.titleCn
            }
          }
      })
    },
    // 用户身份数据总列表处理
    getIdentityList(data){
      return  data.map(item=>{
          if(item.childMenu){
            return {
              identityChildList:this.getIdentityList(item.childMenu),
              identityId:item.id,
              identityIdName:item.name
            }
          }else{
            return {
              identityId:item.id,
              identityIdName:item.name
            }
          }
      })
    },
    // 回显数据处理
    getFields(arr,field){
      let output = [];
      for (var i=0; i < arr.length ; ++i)
          output.push(arr[i][field]);
      return output;
    },
    resetSendObject(){
      // console.log('ss');
      // this.submitData.sendObject ={
      //     productModelList: [],
      //     departmentModelList:[],
      //     identityModelList:[]
      // },
      // this.departments = ''
      // this.identity = ''
      // this.submitData.countNum = 0
      // this.submitData.whiteUnique = ''
    },
    messageChange(val){
      // console.log(val);
      if(val){
        this.submitData.moduleInfo={
          contentTitle:val.contentTitle,
          contentType: val.contentType,
          contentId:val.contentId,
          type:val.type,
        }
        this.search.contentTitle = val.contentTitle
      }
    }
	}
}
</script>

<style scope lang="scss">
.info-form{
  /deep/ .el-checkbox {
    margin-right: 0px;
  }
  &-right {
    /deep/ .el-form-item {
      margin-bottom: 16px;
    }
  }
  .expand {
    .label {
      color: #409EFF; 
      font-size: 12px;
      width: 40px;
      display: inline-block;
      text-align: right;
    }
    /deep/ .el-form-item {
      padding: 4px 0;
      margin-bottom: 14px;
      margin-top: -4px;
      background-color: #E5E6E9;
      border-radius: 4px;
    }
  }
}
 .upload{
   .el-form-item__content{
    display: flex;
    flex-direction: column;
    .upload-top{
      display: flex;
      position: relative;
      // flex-direction: column;
      .upload-conten{
        display: flex;
        ul>li{
          margin-top: 3px;
        }
      }
      .upload-warning{
        position: absolute;
        top: 28px;
        right: -21px;
        color: #de0f35;
      }
    }
    .upload-bottom{
      width:200px;
      // margin-left: 10px;
      margin-left: 0px;
      display: flex;
      // flex-direction: column;
      .upload-warning{
        color: #de0f35;
        margin-left:18px
      }
    }
  }
}
.send1{
  .el-form-item__label::before {
    content: '*';
    color: #F56C6C;
    margin-right: 4px;
  }
   .el-cascader--mini{
    margin-right: 3px;
    .el-cascader__tags i{
       display: none;
    }
  }
  .el-select--mini{
    margin-right: 3px;
  }
  button{
    margin-right: 10px;
  }

}
</style>
