<template>
  <section class="form-tab" v-loading="getLoading">
    <el-tabs v-model="activeName">
      <el-tab-pane label="消息内容" name="content">
        <ms-message-content ref="contentTemp" @changeTab="changeTab" :submitData.sync="submitData" :outDepartmentsList="outDepartmentsList" :outIdentityList="outIdentityList"></ms-message-content>
      </el-tab-pane>
      <!-- <el-tab-pane label="显示栏目" name="category">
        <ms-info-setting :categoryModel.sync="submitData.messageCategory" headerShow :categoryConfigChild="{moduleName: 'message_push_pc'}"></ms-info-setting>
      </el-tab-pane> -->
    </el-tabs>
<!-- aa -->
    <!-- 弹出层 -->
    <!-- <el-dialog :visible.sync="dialog" 
               closeable 
               :show-close="false"
               :close-on-click-modal="false"
               width="50%"
               :title="dialogTitle">
      <component :is="dialogComponent" 
                   :model="dialogInfo" 
                   :operation="dialogOperation" 
                   :isNeedApi="false"
                   @close="closeDialog" 
                   @up-date="init" 
                   v-if="dialog"></component>
    </el-dialog> -->
    <!-- 提交按钮 -->
    <footer-tool-bar v-loading="buttonLoading">
      <template slot="tool-content">
        <!-- 返回 -->
        <el-button type="info" @click="info_operation('back')">取消</el-button>

        <!-- 保存 -->
        <el-button type="primary" @click="info_operation('save')" v-permission="['/guider','edit']">提交</el-button>
      </template>
    </footer-tool-bar>
  </section>
</template>

<script>
import msMessageContent from './tool/ms-message-content'
// import msInfoSetting from '@/components/MsCommon/ms-info-setting'
// import guiderMixin from "./guider-mixin"
import FooterToolBar from '@/components/ToolBar/footer-tool-bar'
import { mapGetters } from "vuex";
import { setSession } from "@/utils/auth";
export default {
  name: "pcSystemMessage-operation",
  // mixins: [guiderMixin],
	data () {
		return {
      activeName: 'content',
      guiderId: this.$route.query.id ? this.$route.query.id : 0,
      buttonLoading: false,
      getLoading: false,
      userInfo: this.$store.getters.info,
      submitData:{    //列表
        messageTitle:'',   //通知标题
        messageModule:'',   //模块
        moduleInfo:{
          contentTitle: "",
          contentType: "",
          contentId:'',
          type:'',
        },     //	模块信息
        messageContent:'', //通知正文
        sendType:0,        //	发送类型
        sendTimeStart:'', //发送时间
        sendTimeEnd:'', //发送截止时间
        sendObject:{
          productModelList: [],
          departmentModelList:[],
          identityModelList:[]
        },    //发送对象
        whiteUnique:'',    //白名单导入标识
        messageCategory:[],  //消息专题分类
        countNum:0
      },
      dialogComponent: '',
      dialog: false,
      dialogTitle: '',
      dialogOperation: '',
      dialogInfo: {},
      outDepartmentsList:[], //科室回显数据
      outIdentityList:[] //身份回显数据

		}
  },
  computed: {
    ...mapGetters(["info"]),
  },
  async created() {
    // this.submitData.editor = this.info.userName
    this.init()
  },
  components: {
    msMessageContent,
    // msInfoSetting,
    FooterToolBar
  },
  methods: {
    init() {
      let id = this.guiderId
      this.dialog = false;
      if(id !== 0) {
        this.getLoading = true;
        this.submitData.id = id;
        // 回显
        this.api.getMessageId(id).then(response => {
          this.getLoading = false;
          if(response.status === 200) {
            let res = response.data
            this.submitData = {
              ...this.submitData,
              messageTitle:res.messageTitle,
              messageModule:res.messageModule,        
              messageContent:res.messageContent,
              sendType:res.sendType,
              sendTimeStart:res.sendTimeStart,
              whiteUnique:res.whiteUnique,
              messageCategory:JSON.parse(res.messageCategory),
              countNum:res.countNum
            }
            if(res.moduleInfo){
              this.submitData.moduleInfo = JSON.parse(res.moduleInfo)
            }
            if(res.sendType == 1){
              this.submitData.sendObject = JSON.parse(res.sendObject)
              this.parseDepartmentArr(JSON.parse(res.sendObject).departmentModelList,this.outDepartmentsList)
              this.parseIdentityArr(JSON.parse(res.sendObject).identityModelList,this.outIdentityList)
            }
            console.log(this.submitData,'hx');
            // console.log(this.outDepartmentsList,1);
            // console.log(this.outIdentityList,1);
            

            setSession('message-sendTimeStart', this.submitData.sendTimeStart)
          } else {
            this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
          }
        }).catch(() => {
          this.getLoading = false;
        })
      }
    },
    changeTab(val) {
      this.activeName = val
    },
    info_operation(val) {
      // console.log(val,'s');
      switch (val) {
        case 'save': 
          try {
             Promise.all([
              this.$refs['contentTemp'].validateData()
            ]).then(() => {
              this.guiderId ? this.updateMessag(): this.createMessag();
            });
          } catch (error) {
            return;
          }
          break;
        case 'back':
          this.$router.back();
          break;
        default: break;
      }
    },
    createMessag() {
      // console.log(this.submitData.messageModule,'1');
      // console.log(!this.submitData.moduleInfo,'2');
      if(this.submitData.messageModule && !this.submitData.moduleInfo){
       return this.PUBLIC_Methods.apiNotify('请完善模块信息', 'warning')
      }
      if(this.submitData.sendType == 1 && this.submitData.sendObject.departmentModelList.length == 0 && this.submitData.sendObject.identityModelList.length == 0){
       return this.PUBLIC_Methods.apiNotify('发送对象不能为空', 'warning')
      }
      // if(this.submitData.sendType == 1 && this.submitData.countNum == 0 ){
      //  return this.PUBLIC_Methods.apiNotify('发送对象不能为空', 'warning')
      // }
      this.buttonLoading = true;
      let paramsData = {
        ...this.submitData,
        createdBy: this.info.userId,
        createdName: this.info.userName,
      }
      paramsData.messageCategory = JSON.stringify(this.submitData.messageCategory)
      if(this.submitData.sendType == 1){
        paramsData.sendObject = JSON.stringify(this.submitData.sendObject)
      }else{
        paramsData.sendObject = '[]'
      }
      if(this.submitData.messageModule){
        paramsData.moduleInfo = JSON.stringify({
          contentId:this.submitData.moduleInfo.contentId,
          contentTitle:this.submitData.moduleInfo.contentTitle,
          contentType:this.submitData.moduleInfo.contentType,
          type:this.submitData.moduleInfo.type
        })
      }else{
        paramsData.moduleInfo = ''
      }
      this.api.saveMessagePush(paramsData).then(response => {
        this.buttonLoading = false
        if(response.status === 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
          this.$router.back()
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
      }).catch(() => this.buttonLoading = false)
    },
    updateMessag() {
      // console.log(this.submitData.messageModule,'1');
      if(this.submitData.messageModule && !this.submitData.moduleInfo.contentTitle){
       return this.PUBLIC_Methods.apiNotify('请完善模块信息', 'warning')
      }
      if(this.submitData.sendType == 1 && this.submitData.sendObject.departmentModelList.length == 0 && this.submitData.sendObject.identityModelList.length == 0){
       return this.PUBLIC_Methods.apiNotify('发送对象不能为空', 'warning')
      }
      // if(this.submitData.sendType == 1 && this.submitData.countNum == 0 ){
      //  return this.PUBLIC_Methods.apiNotify('发送对象不能为空', 'warning')
      // }
      this.buttonLoading = true;
      // console.log(this.submitData,'QWER');
      let paramsData = {
        ...this.submitData,
        createdBy: this.info.userId,
        createdName: this.info.userName,
      }
      paramsData.messageCategory = JSON.stringify(this.submitData.messageCategory)
      if(this.submitData.sendType == 1){
        paramsData.sendObject = JSON.stringify(this.submitData.sendObject)
      }else{
        paramsData.sendObject = '[]'
      }
      if(this.submitData.messageModule){
        paramsData.moduleInfo = JSON.stringify({
          contentId:this.submitData.moduleInfo.contentId,
          contentTitle:this.submitData.moduleInfo.contentTitle,
          contentType:this.submitData.moduleInfo.contentType,
          type:this.submitData.moduleInfo.type
        })
      }else{
        paramsData.moduleInfo = ''
      }
      console.log(this.submitData,'asdf');
      this.api.updateMessagePush(paramsData).then(response => {
        this.buttonLoading = false
        if(response.status === 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
          this.$router.back()
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
      }).catch(() => this.buttonLoading = false)
    },
    // 数据回显扁平化处理
    parseDepartmentArr(arr,res){
      // console.log(arr,res,'555');
      let i = 0
      for(i=0;i<arr.length;i++){
        if(arr[i].departmentChildList){
          this.parseDepartmentArr(arr[i].departmentChildList,res)
        }else{
          res.push(arr[i])
        }
      }
      // console.log(res,'k');
    },
    parseIdentityArr(arr,res){
      // console.log(arr,res,'k');
      let i = 0
      for(i=0;i<arr.length;i++){
        if(arr[i].identityChildList){
          this.parseIdentityArr(arr[i].identityChildList,res)
        }else{
          res.push(arr[i])
        }
      }
    }
    // handle_click(val) {
    //   switch (val.way) {
    //     case 'dialog': 
    //       this.dialog = true;
    //       this.dialogInfo = val.data
    //       this.dialogOperation = val.operation;
    //       this.dialogComponent = val.component;
    //       this.dialogTitle = val.title;
    //       break;
    //     default: break;
    //   }
    // },
    // closeDialog(val) {
    //   this.dialog = !this.dialog
    //   if (val) {
    //     if (val.operation === 'recommend') {
    //       this.submitData.recommend = 0
    //     } else if (val.operation === 'sticky') {
    //       this.submitData.sticky = 0
    //     }
    //   }
    // }
  }
}
</script>

