const domConfig = {
  // 搜索配置
  listSearch: [
    {
      label: '通知标题',
      placeholder: '请输入通知标题',
      model: 'messageTitle',
      component: 'ms-input'
    },
    {
      label: '创建人',
      placeholder: '请输入创建人',
      model: 'createdName',
      component: 'ms-input'
    },
    {
      label: '创建时间',
      placeholder: '请选择时间段',
      model: 'createTime',
      component: 'ms-picker',
      type: 'daterange',
      width: '240px'
    }
  ],

  // 表头配置
  tableHeader: [
    { label: 'ID', property: 'id', width: '80' },
    { label: '通知标题', property: 'messageTitle'},
    { label: '模块', property: 'messageModule', width: '80' },
    { label: '模块ID', property: 'contentId' },
    { label: '创建人', property: 'createdName' },
    { label: '创建时间', property: 'createdTime',width: '110' },
    { label: '审核状态', property: 'approvalStatus' },
    { label: '发送状态', property: 'messageSendStatus' },
    { label: '发送时间', property: 'sendTimeStart' ,width: '120'  },
    { label: '应发量', property: 'countNum', },
    { label: '发送量', property: 'sendCount', },
    // { label: '点击量', property: 'uniqueVisitor', },
    { label: '曝光量', property: 'exNum', },
    { label: '点击量', property: 'clickNum', },
  ],

  // 行内列表按钮配置
  tableButtons: [
    {
      label: '编辑',
      operation: 'edit',
      // component: 'guider-operation',
      way: 'page',
      type: 'primary',
      path: 'pcSystemMessage-operation',
      disabled: (val) => {
        return val.approvalStatus == 0 ? false : true
      },
      params: ['id'],
      identify: 'edit'
    },
    {
      label: '',
      icon: '',
      role: '',
      operation: 'editStatus',
      component: 'msSystemOperation',
      way: 'dialog',
      field: 'approvalStatus',
      rule: {
        0: { label: '审核', type: 'success', operation: 'approval' },
      },
    },
    {
      label: '',
      icon: '',
      role: '',
      operation: 'editStatus',
      component: 'msSystemOperation',
      way: 'dialog',
      field: 'approvalSendStatus',
      showCallback: (val) => {
        if (val.approvalStatus == 1 || val.approvalStatus == 2) {
          return true
        } else {
          return false
        }
      },
      rule: {
        0: { label: '禁用', type: 'success', operation: 'startUsing' },
        1: { label: '启用', type: 'success', operation: 'forbidden' },
      },
    },
    {
      label: '删除',
      way: 'dialog',
      type: 'danger',
      operation: 'delete',
      component: 'msSystemOperation'
    }
  ],

  // 新建项目按钮
  soltButtons: [
    { 
      label: '添加系统消息', 
      icon: 'el-icon-plus',
      type: 'primary', 
      operation: 'created',
      component: 'pcSystemMessage-operation',
      way: 'page',
      path: 'pcSystemMessage-operation',
      params: ['id'],
    },
    { 
      label: '批量删除', 
      type: 'info', 
      icon: 'el-icon-close',
      operation: 'delete',
      component: 'msSystemOperation',
      way: 'batch'
    }
  ]
}
export default domConfig