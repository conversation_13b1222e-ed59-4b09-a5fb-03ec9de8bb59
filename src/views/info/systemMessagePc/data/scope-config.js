const scopeConfig = {
  show: {
    messageSendStatus: () => {
      return {
        type: 'status',
        rule: {
          0: { label: '未发送', background: '#A7ADBD' },
          1: { label: '已发送', background: '#40A23F' }
        }
      }
    },
    approvalStatus: () => {
      return {
        type: 'status',
        rule: {
          2: { label: '已禁用', background: '#F56C6C' },
          1: { label: '审核通过', background: '#40A23F' },
          0: { label: '待审核', background: '#A7ADBD' }
        }
      }
    },

    messageModule: () => {
      return {
        type: 'status-char',
        rule: {
          "article": { label: '资讯'},
          "live_info": { label: '直播'},
          "medsci_survey": { label: '调研'},
          "course": { label: '课程'},
          "eda": { label: '医迅达'},
          "guider": { label: '指南'},
          "tool_impact_factor": { label: '期刊'},
          "topic": { label: '话题'},
          "nsfc": { label: '国自然基金'},
        }
      }
    },
    createdTime: () => {
      return {
        type: 'formatTime',
        cFormat: '{y}-{m}-{d} {h}:{i}'
      }
    },
    objId: () => {
      return {
        type: 'other',
        config: {
          way: 'systemTypeLink',
        }
      }
    },
  },
  headerShow: {
    status: () => {
      return {
        type: 'dropdown',
        icon: 'icon-funnel',
        options: [
          { label: '全部', value: null },
          { label: '待审核', value: 0 },
          { label: '审核通过', value: 1 }
        ],
        operation: 'query'
      }
    }
  }
}

export default scopeConfig;
