const scopeConfig = {
  show: {
    mgStatus: () => {
      return {
        type: 'status',
        rule: {
          0: { label: '未回复', background: '#A7ADBD' },
          1: { label: '已回复', background: '#40A23F' }
        }
      }
    },
    moduleType: () => {
      return {
        type: 'code',
        rule: {
          'article': { label: '资讯留言' },
          'video': { label: '视频留言' },
          'tool_impact_factor': { label: '期刊留言' },
          'topic': { label: '话题留言' },
          'meeting': { label: '会议留言' },
          'nsfc': { label: '基金留言' },
          'live_info': { label: '直播留言' },
        }
      }
    },
  },
  headerShow: {
    moduleType: () => {
      return {
        type: 'dropdown',
        icon: 'icon-funnel',
        options: [
          { label: "全部", value: null },
          { label: "资讯留言", value: "article" },
          { label: "视频留言", value: "video" },
          { label: "期刊留言", value: "tool_impact_factor" },
          { label: "话题留言", value: "topic" },
          { label: "会议留言", value: "meeting" },
          { label: "基金留言", value: "nsfc" },
          { label: "直播留言", value: "live_info" }
        ],
        operation: 'query'
      }
    },
    mgStatus: () => {
      return {
        type: 'dropdown',
        icon: 'icon-funnel',
        options: [
          { label: '全部', value: null },
          { label: '未回复', value: 0 },
          { label: '已回复', value: 1 }
        ],
        operation: 'query'
      }
    },
    mgSource: () => {
      return {
        type: 'dropdown',
        icon: 'icon-funnel',
        options: [
          { label: '全部', value: null },
          { label: 'APP', value: 'APP' },
          { label: 'PC', value: 'PC' }
        ],
        operation: 'query'
      }
    }
  }
}

export default scopeConfig;
