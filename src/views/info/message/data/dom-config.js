const domConfig = {
  listSearch: [
    {
      label: '标题',
      placeholder: '请输入',
      model: 'mgTitle',
      component: 'ms-input'
    },
    {
      label: '留言人',
      placeholder: '请输入',
      model: 'mgUsername',
      component: 'ms-input'
    }
  ],
  tableHeader: [
    { label: 'ID', property: 'id', sortable: true, width: 80 },
    { label: '项目名称', property: 'projectName', width: 150 },
    { label: '标题', property: 'mgTitle', width: 250 },
    { label: '对象类型', property: 'moduleType', width: '80' },
    { label: '状态', property: 'mgStatus' },
    { label: '来源', property: 'mgSource' },
    { label: '留言人', property: 'mgUsername' },
    { label: '留言时间', property: 'mgCreateTime', width: 130, sortable: true },
    { label: '回复人', property: 'replyUsername' },
    { label: '回复时间', property: 'replyCreateTime', width: 130, sortable: true }
  ],
  tableButtons: [
    // {
    //   label: '查看',
    //   icon: '',
    //   role: '',
    //   operation: 'readOnly',
    //   component: 'message-operation',
    //   way: 'page',
    //   type: 'info',
    //   path: 'message-operation',
    //   params: ['id']
    // },
    {
      label: '回复',
      icon: '',
      role: '',
      operation: 'edit',
      component: 'message-operation',
      way: 'page',
      type: 'primary',
      path: 'message-operation',
      params: ['id']
    },
    {
      label: '删除',
      icon: '',
      role: '',
      type: 'danger',
      way: 'dialog',
      operation: 'delete',
      component: 'msMessageDelete',
    }
  ],
  soltButtons: [
    {
      label: '批量删除',
      type: 'info',
      icon: 'el-icon-close',
      operation: 'delete',
      component: 'msMessageDelete',
      way: 'batch'
    }
  ]
}

export default domConfig;
