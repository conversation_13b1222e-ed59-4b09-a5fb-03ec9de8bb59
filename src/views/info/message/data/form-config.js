const formConfig = {
  formField: [
    {
      label: '标题',
      prop: 'mgTitle',
      colSpan: 12,
      component: 'ms-input',
      disabled: true
    },
    {
      label: '留言人姓名',
      prop: 'mgRealName',
      colSpan: 6,
      component: 'ms-input',
      disabled: true
    },
    {
      label: '留言人',
      prop: 'mgUsername',
      colSpan: 6,
      component: 'ms-input',
      disabled: true
    },
    
    {
      label: '单位',
      prop: 'mgUnit',
      colSpan: 12,
      component: 'ms-input',
      disabled: true
    },
    {
      label: '电话',
      prop: 'mgTell',
      colSpan: 6,
      component: 'ms-input',
      disabled: true
    },
    {
      label: '邮箱',
      prop: 'mgEmail',
      colSpan: 6,
      component: 'ms-input',
      disabled: true
    },
    {
      label: '留言时间',
      prop: 'mgCreateTime',
      colSpan: 6,
      component: 'ms-picker',
      type: 'date',
      disabled: true
    },
    {
      label: '留言内容',
      prop: 'mgContent',
      colSpan: 24,
      component: 'ms-input',
      type: 'textarea',
      disabled: true
    },
    {
      label: '附件',
      prop: 'mgImageList',
      colSpan: 24,
      component: 'ms-image-list'
    },
    {
      label: '内容',
      prop: 'replyContent',
      colSpan: 24,
      component: 'ms-editor'
    }
  ],
  rule: {
    replyContent: [
      { required: true, message: "请输入回复内容", trigger: 'blur' }
    ]
  }
}

export default formConfig;
