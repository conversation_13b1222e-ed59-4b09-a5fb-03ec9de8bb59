<template>
  <section class="form-tab" v-loading="getLoading">
    <!-- 表单内容 -->
    <el-form ref="submitRef"
             class="rule-form"
             :model="submitData"
             label-width="80px"
             :rules="formConfig.rule">
      <el-row>
        <template v-for="(item, index) in formConfig.formField">
          <el-col :key="index"
                  :span="item.colSpan"
                  v-if="item.prop !== 'mgAccessory' || (submitData[item.prop] && submitData[item.prop] !== '无')">
            <el-form-item :prop="item.prop"
                          :label="item.label">
              <component :is="item.component"
                        :model.sync="submitData[item.prop]"
                        v-model="submitData[item.prop]"
                        :width="item.width || '100%'"
                        :disabled="item.disabled || isReadOnly"
                        :type="item.type"
                        :isBlock="false">
              </component>
            </el-form-item>
          </el-col>
        </template>
      </el-row>
    </el-form>
    <!-- 提交按钮 -->
    <footer-tool-bar v-loading="buttonLoading">
      <template slot="tool-content">
        <el-button type="primary" v-if="!isReadOnly" @click="info_operation('save')">提交回复</el-button>
        <el-button type="info" @click="info_operation('back')">返回</el-button>
      </template>
    </footer-tool-bar>
  </section>
</template>

<script>
import FooterToolBar from '@/components/ToolBar/footer-tool-bar'
import formConfig from './data/form-config' 
import MsEditor from '@/components/MsEditor'
import MsImageList from '@/components/MsCommon/ms-image-list'
import { mapGetters } from "vuex";
export default {
  name: "message-operation",
	data () {
		return {
      buttonLoading: false,
      getLoading: false,
      formConfig: formConfig,
      dataId: this.$route.query.id ? this.$route.query.id : 0,
      isReadOnly: this.$route.query.operation && this.$route.query.operation === 'readOnly' ? true : false,
      submitData: {
        replyContent: ''
      }
		}
  },
  components: {
    FooterToolBar,
    MsEditor,
    MsImageList
  },
  computed: {
    ...mapGetters(["info"])
  },
  created() {
    this.init()
  },
  methods: {
    init() {
      let id = this.dataId
      if(id !== 0) {
        this.getLoading = true;
        this.submitData.id = id;
        this.api.getLeaveMessage({id: id}).then( response => {
          this.getLoading = false;
          
          if(response.status === 200) {
            let res = response.data
            let imageList = res.mgAccessoryList ? res.mgAccessoryList.map(v => {
              return v.url
            }) : []
            this.submitData = {...res,mgImageList: imageList}
          } else {
            this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
          }
        }).catch(() => {
          this.getLoading = false;
        })
      } 
    },
    info_operation(val) {
      switch (val) {
        case 'save': 
          if (!this.isReadOnly) {
            this.dataId ? this.replyMessage() : this.PUBLIC_Methods.apiNotify('请选择留言信息', 'warning')
          }
          break;
        case 'back':
          this.$router.back();
          break;
        default: break;
      }
    },
    replyMessage() {
      let params = {
        id: this.dataId,
        replyUserid: this.info.userId,
        replyUsername: this.info.userName,
        replyContent: this.PUBLIC_Methods.excapeHtml(this.submitData.replyContent)
      }
      this.$refs["submitRef"].validate( valid => {
        if (valid) {
          this.buttonLoading = true;
          this.api.updateReplyLeaveMessage(params).then(response => {
            this.buttonLoading = false
            if(response.status === 200) {
              this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
              this.$router.back();
            } else {
              this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
            }
          }).catch(() => this.buttonLoading = false)
        }
      })
    }
  }
}
</script>
