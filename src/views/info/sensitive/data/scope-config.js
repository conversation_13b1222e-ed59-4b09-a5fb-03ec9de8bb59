const scopeConfig = {
  show: {
    level: () => {
      return {
        type: 'status-button',
        rule: {
          1: { label: '一级', background: '#F56C6C' },
          2: { label: '二级', background: '#E6A23C' }
        }
      }
    },
    createdTime: () => {
      return {
        type: 'formatTime',
        cFormat: '{y}-{m}-{d} {h}:{i}'
      }
    },
  },
  headerShow: {
    badtype: () => {
      return {
        type: 'multi',
        icon: 'icon-funnel',
        options: [
          { label: '色情', value: '色情' },
          { label: '广告', value: '广告' },
          { label: '违禁', value: '违禁' },
          { label: '涉政', value: '涉政' },
          { label: '谩骂', value: '谩骂' },
          { label: '灌水', value: '灌水' }
        ],
        operation: 'query'
      }
    },
    level: () => {
      return {
        type: 'dropdown',
        icon: 'icon-funnel',
        options: [
          { label: '全部', value: null },
          { label: '一级', value: 1 },
          { label: '二级', value: 2 },
        ],
        operation: 'query'
      }
    }
  }
}

export default scopeConfig;
