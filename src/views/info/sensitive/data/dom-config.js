const domConfig = {
  // 搜索配置
  listSearch: [
    {
      label: '敏感词',
      placeholder: '请输入敏感词',
      model: 'badword',
      component: 'ms-input'
    }
  ],

  // 表头配置
  tableHeader: [
    { label: 'ID', property: 'id', sortable: false, width: '80' },
    { label: '敏感词', property: 'badword', sortable: false },
    { label: '分类', property: 'badtype', sortable: false },
    { label: '级别', property: 'level', sortable: false },
    { label: '创建人', property: 'createdName', sortable: false },
    { label: '创建时间', property: 'createdTime', sortable: true },
  ],

  // 行内列表按钮配置
  tableButtons: [
    {
      label: '编辑',
      icon: '',
      role: '',
      operation: 'edit',
      component: 'msSensitiveEdit',
      way: 'dialog',
      type: 'primary',
      title: '敏感词编辑',
      width: '45%'
    },
    {
      label: '删除',
      operation: 'delete',
      type: 'danger',
      way: 'dialog',
      component: 'msSensitiveDelete',
    },
  ],

  // 新建项目按钮
  soltButtons: [
    { 
      label: '添加敏感词', 
      icon: 'el-icon-plus',
      type: 'primary', 
      operation: 'created',
      component: 'msSensitiveEdit',
      way: 'dialog',
      title: '添加敏感词',
      width: '45%'
    },
    { 
      label: '批量删除',
      type: 'info',
      icon: 'el-icon-close',
      operation: 'delete',
      component: 'msSensitiveDelete',
      way: 'batch'
    }
  ]
}
export default domConfig