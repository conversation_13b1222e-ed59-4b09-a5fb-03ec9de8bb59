<template>
    <ms-operation-dialog title="是否删除已下敏感词">
        <template slot="content">
            <el-tag v-for="(item, index) in tagArr" 
                    :key="index" 
                    type="info" 
                    style="margin: 0 5px 5px 0">{{item}}</el-tag>
        </template>
        <template slot="footer">
            <el-button @click="submitForm"
                        :loading="loading"
                        size="mini"
                        type="primary">确 定</el-button>
            <el-button @click="$emit('close')"
                        size="mini">取 消</el-button>
        </template>
    </ms-operation-dialog>
</template>

<script>
import { mapGetters } from "vuex";
export default {
	name: "ms-sensitive-delete",
	data () {
		return {
            tagArr: [],
            ids: [],
            loading: false
		}
    },
    computed: {
        ...mapGetters(["info"])
    },
	props: [
		"model",
		"operation"
    ],
    created() {
        this.init()
    },
	methods: {
        init() {
            let arr = []
            let ids = []
            if (this.PUBLIC_Methods.isArrayFn(this.model)) {
                this.model.forEach(item => {
                arr.push(item.badword)
                ids.push(item.id)
                });
            } else {
                arr.push(this.model.badword)
                ids.push(this.model.id)
            }
            this.tagArr = arr
            this.ids = ids
        },
		submitForm () {
            this.$confirm('此操作将永久删除敏感词信息，是否继续', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.apiOperation()
            }).catch(() => {
                this.$emit('close')
            })
        },
        apiOperation() {
            let params = {
                userId: this.info.userId,
                username: this.info.userName,
                ids: this.ids
            }
            this.loading = true;
            this.api.delBadWordsBatch(params).then(response => {
                if(response.status === 200) {
                    this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
                } else {
                    this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
                }
                this.$emit('up-date')
                this.loading = false
            }).catch( () => {
                this.loading = false;
                this.$emit('close')
            })
        }
	}
}
</script>
