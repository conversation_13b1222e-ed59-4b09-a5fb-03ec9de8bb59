<template>
  <ms-operation-dialog>
    <template slot="content">
      <el-form
        ref="submitRef"
        class="rule-form"
        :model="submitData"
        :rules="formConfig.rule"
        label-width="110px"
        v-loading="getLoading"
      >
        <el-row>
          <template v-for="(item, index) in formConfig.formField">
            <el-col :key="index" :span="item.colSpan">
              <el-form-item :prop="item.prop" :label="item.label">
                <component
                  :is="item.component"
                  :model.sync="submitData[item.prop]"
                  :width="item.width || '100%'"
                  :disabled="item.disabled "
                  :type="item.type || 'text'"
                  :active="item.active"
                  :inactive="item.inactive"
                  :options="item.options"
                  :placeholder="item.placeholder"
                ></component>
              </el-form-item>
            </el-col>
          </template>
        </el-row>
      </el-form>
    </template>
    <template slot="footer">
      <el-button @click="submitForm" :loading="loading" size="mini" type="primary">确 定</el-button>
      <el-button @click="$emit('close')" size="mini">取 消</el-button>
    </template>
  </ms-operation-dialog>
</template>

<script>
import formConfig from "../data/form-config";
import { mapGetters } from "vuex";
export default {
  name: "ms-sensitive-edit",
  data() {
    return {
      loading: false,
      getLoading: false,
      formConfig: formConfig,
      submitData: {
        appZhName: "",
        appEnName: "",
        appVersion: "",
        appDesc: "",
        actived: 0
      },

      //common
      operationLocal: ""
    };
  },
  props: {
    model: Object,
    operation: String
  },
  computed: {
    ...mapGetters(["info"])
  },
  created() {
    this.init();
  },
  methods: {
    even(v) {
      if (v == 0) {
        return "色情";
      } else if (v == 1) {
        return "广告";
      } else if (v == 2) {
        return "违禁";
      } else if (v == 3) {
        return "涉政";
      } else if (v == 4) {
        return "谩骂";
      } else if (v == 5) {
        return "灌水";
      } else {
        return v;
      }
    },
    init() {
      this.operationLocal = this.operation || this.$route.query.operation;
      if (this.operationLocal === "edit" && this.model.id) {
        this.getDetail();
      }
    },
    getDetail() {
      this.submitData = { ...this.model };
    },
    submitForm() {
      this.$refs["submitRef"].validate(valid => {
        if (valid) {
          this.loading = true;
          if (this.operationLocal === "edit") {
            this.submitEdit();
          } else {
            this.submitAdd();
          }
        }
      });
    },
    submitEdit() {
      this.submitData.badtype = this.even(this.submitData.badtype);
      let params = {
        userId: this.info.userId,
        username: this.info.userName,
        ...this.submitData
      };
      this.api
        .updateBadWords(params)
        .then(response => {
          if (response.status === 200) {
            this.PUBLIC_Methods.apiNotify(
              response.message || "请求成功",
              "success"
            );
          } else {
            this.PUBLIC_Methods.apiNotify(
              response.message || "请求出错",
              "warning"
            );
          }
          this.$emit("up-date");
          this.loading = false;
        })
        .catch(() => {
          this.loading = false;
          this.$emit("close");
        });
    },
    submitAdd() {
      this.submitData.badtype = this.even(this.submitData.badtype);
      let params = {
        userId: this.info.userId,
        username: this.info.userName,
        ...this.submitData
      };
      this.api
        .insertBadWords(params)
        .then(response => {
          if (response.status === 200) {
            this.PUBLIC_Methods.apiNotify(
              response.message || "请求成功",
              "success"
            );
          } else {
            this.PUBLIC_Methods.apiNotify(
              response.message || "请求出错",
              "warning"
            );
          }
          this.$emit("up-date");
          this.loading = false;
        })
        .catch(() => {
          this.loading = false;
          this.$emit("close");
        });
    }
  }
};
</script>
