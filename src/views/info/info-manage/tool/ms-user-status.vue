<template>
  <ms-operation-dialog :title="`是否要${+model.status === 1 ? '禁用' : '启用'}该用户`">
    <template slot="footer">
      <el-button @click="submitForm"
                 :loading="loading"
                 size="mini"
                 type="primary">确 定</el-button>
      <el-button @click="$emit('close')"
                 size="mini">取 消</el-button>
    </template>
  </ms-operation-dialog>
</template>

<script>
export default {
	name: "ms-user-status",
	data () {
		return {
			loading: false
		}
	},
	props: {
		model: Object,
		operation: String
  },
	methods: {
		submitForm () {
      this.loading = true;
      let params = {
        userId: this.model.id,
        statusId: +this.model.status === 1 ? 0 : 1,
        opsUserId: this.$store.getters.info.userId,
        opsUserName: this.$store.getters.info.userName,
      }
      this.api.changeUserStatus(params).then(response => {
        if(response.status === 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
        this.$emit('up-date')
        this.loading = false
      }).catch( () => {
        this.loading = false;
        this.$emit('close')
      })
		}
	}
}
</script>
