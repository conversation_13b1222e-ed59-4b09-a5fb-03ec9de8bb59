<template>
    <ms-operation-dialog>
        <template slot="content">
            <el-form class="rule-form" label-width="75px">
                <el-row >
                    <el-col :span="11">
                        <el-form-item label="积分增减">
                            <el-input-number controls-position="right" v-model="submitData.integralBase"></el-input-number>
                        </el-form-item>
                    </el-col>
                    <el-col :span="11" :offset="1">
                        <el-form-item label="原积分">
                            <el-input-number controls-position="right" :min="0" v-model="oldIntegral" disabled></el-input-number>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="备注">
                            <el-input type="textarea" :autosize="{ minRows: 4, maxRows: 4}" placeholder="请输入备注" v-model="submitData.remarks">
                            </el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
        </template>
        <template slot="footer">
            <el-button @click="submitForm"
                        :loading="loading"
                        size="mini"
                        type="primary">确 定</el-button>
            <el-button @click="$emit('close')"
                        size="mini">取 消</el-button>
        </template>
    </ms-operation-dialog>
</template>

<script>
import { mapGetters } from "vuex";
export default {
	name: "ms-user-integral",
	data () {
		return {
            loading: false,
            submitData: {
                integralBase: "",
                remarks: ""
            },
            oldIntegral: 0
        }
	},
	props: {
		model: Object,
		operation: String
    },
    computed: {
        ...mapGetters(["info"])
    },
    created() {
        this.oldIntegral = this.model.integralBase || 0
    },
	methods: {
		submitForm () {
            let params = {
                userId: this.info.userId,
                username: this.info.userName,
                id: this.model.id,
                integralBase: this.submitData.integralBase,
                remarks: this.submitData.remarks
            }
            this.api.updateIntegral(params).then(response => {
                if(response.status === 200) {
                    this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
                } else {
                    this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
                }
                this.$emit('up-date')
                this.loading = false
            }).catch( () => {
                this.loading = false;
                this.$emit('close')
            }) 
        }
	}
}
</script>
