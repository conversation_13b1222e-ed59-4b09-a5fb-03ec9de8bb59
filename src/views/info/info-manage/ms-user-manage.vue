<template>
	<ms-table
		:currentPage="searchParams.pageIndex"
		:loading="loading"
		:operationButtons="$route.path.includes('Bioon')?bioonConfig.tableButtons:domConfig.tableButtons"
		:pageSize="searchParams.pageSize"
		:scopeConfig="scopeConfig.show"
    :scopeHeader="scopeConfig.headerShow"
		:tableData="list"
		:tableHeader="domConfig.tableHeader"
		:total="total"
		@current-change="current_change"
		@operation-change="operation_change"
		@size-change="size_change"
    @header-operation="header_operation"
	>
    <!-- 列表搜索去区域插槽 -->
		<template slot="ms-table-header">
			<div class="slot-search">
				<template v-for="(searchItem, key) in domConfig.listSearch">
					<component
						:index="searchItem.index || ''"
						:is="searchItem.component"
						:key="key"
            :width="searchItem.width || '150px'"
            :model.sync="searchParams[searchItem.model]"
            :label="searchItem.label"
						:operation="searchItem.operation || ''"
						:options="searchItem.options || []"
						:placeholder="searchItem.placeholder || ''"
						:type="searchItem.type || ''"
					></component>
				</template>
        <div class="inlineBlock">
          <el-button @click="handleClick('query')" type="primary" plain icon="el-icon-search">查询</el-button>
          <el-button @click="handleClick('reset')">重置</el-button>
        </div>
        <p>
          <el-link type="danger"
                   :underline="false">{{statisticTip}}
          </el-link>
        </p>
			</div>
			<el-dialog :visible.sync="dialog"
                 closeable
                 show-close
                 :close-on-click-modal="false"
                 :width="dialogWidth"
                 :title="dialogTitle">
				<component :is="dialogComponent"
                   :model="scopeInfo"
                   :operation="dialogOperation"
                   @close="dialog = !dialog"
                   @up-date="init"
                   v-if="dialog"></component>
			</el-dialog>
		</template>
	</ms-table>
</template>

<script>
import userMixin from "./user-mixin"
// import tableMixins  from "../../common/mixins/table"
import store from '@/store/index'
import { mapGetters } from "vuex";
export default {
  name: "ms-user-manage",
  mixins: [userMixin],
  data () {
    return {
      statisticTip: '',
      searchParams: { // => 用户列表查询传参
        realName: "",
        status: null,
        pageIndex: 1,
        pageSize: 20
      },
      loading: false, // => 列表加载动画控制
      list: [], // => 列表数据
      total: 0, // => 列表总数
      scopeInfo: {},
      r_dialog: false,
      dialog: false,
      dialogOperation: "",
      dialogComponent: "",
      dialogWidth: "",
      dialogTitle: "",

      searchCacheFlag: true,

      selectList: [] // => 列表多选选中数组
    };
  },
  computed: {
    ...mapGetters(["permissions", "info", "pageSize", "tableDropId", "listSearchParams", "permissionMenuId", "project"])
  },
  watch: {
    listSearchParams: function (val) {
      if (val && JSON.stringify(val) !== '{}' && this.searchCacheFlag) {
        this.searchParams = { ...this.searchParams, ...this.listSearchParams }
      }
    },
    permissionMenuId: function (val) {
      if (val) {
        this.getListPermission()
      }

    }
  },
  created() {
    this.searchParams.pageSize = this.pageSize || 20
    this.$store.dispatch('SetDropId', null)
    if (this.listSearchParams && this.searchCacheFlag) {
      this.searchParams = { ...this.searchParams, ...this.listSearchParams }
    }
    if (Object.keys(this.permissionMenuId).length > 0) {
      this.getListPermission()
    }
    this.searchParams = { ...this.searchParams, ...this.$route.query }
    // this.init();
  },
  mounted () {
  },
  methods: {
        /**
     * 获取菜单按钮权限
     */
     getListPermission() {
      let btnParams = {
        "projectId": this.info.projectId,
        "roleId": this.info.roleId,
        "menuId": this.permissionMenuId[this.$route.fullPath]
      }
      let permissionStr = ''
      let permissionRole = {}
      permissionRole[this.$route.fullPath] = []
      if (this.$route.fullPath === '/article' || this.$route.fullPath === '/guider' || this.$route.fullPath === '/calendar' || this.$route.fullPath === '/company' || this.$route.fullPath.includes('Bioon')) {
        this.api.getRoleMenuPermissionsBtnList(btnParams).then(response => {
          if (response.status === 200 && response.data && response.data.length > 0) {
            response.data.forEach(v => {
              permissionStr += v.menuPers + ','
              permissionRole[this.$route.fullPath].push(v.menuPers)
            });
            if(this.$route.fullPath.includes('Bioon')) {
              // 搜索框下按钮权限
              this.bioonConfig.soltButtons = this.bioonConfig.soltButtons.map(v => {
                return {
                  ...v,
                  roleDisabled: permissionStr.indexOf(v.identify) === -1 ? true : false
                }
              })
              // 列表按钮权限
              this.bioonConfig.tableButtons = this.bioonConfig.tableButtons.map(v => {
                if (v.children && v.children.length > 0) {
                  let childrenBtn = v.children.map(childV => {
                    return {
                      ...childV,
                      roleDisabled: permissionStr.indexOf(childV.identify) === -1 ? true : false
                    }
                  })
                  return {
                    ...v,
                    children: childrenBtn
                  }
                } else {
                  return {
                    ...v,
                    roleDisabled: permissionStr.indexOf(v.identify) === -1 ? true : false
                  }
                }
              })
            } else {
              // 搜索框下按钮权限
              this.domConfig.soltButtons = this.domConfig.soltButtons.map(v => {
                return {
                  ...v,
                  roleDisabled: permissionStr.indexOf(v.identify) === -1 ? true : false
                }
              })
              // 列表按钮权限
              this.domConfig.tableButtons = this.domConfig.tableButtons.map(v => {
                if (v.children && v.children.length > 0) {
                  let childrenBtn = v.children.map(childV => {
                    return {
                      ...childV,
                      roleDisabled: permissionStr.indexOf(childV.identify) === -1 ? true : false
                    }
                  })
                  return {
                    ...v,
                    children: childrenBtn
                  }
                } else {
                  return {
                    ...v,
                    roleDisabled: permissionStr.indexOf(v.identify) === -1 ? true : false
                  }
                }
              })
            }
          }
          // 储存按钮权限
          this.$store.dispatch('SetPermissionBtnName', permissionRole)
        }).catch(() => { })
      }
    },
    init() {
      this.loading = true;
      this.dialog = false;
      this.r_dialog = false;
      let searchParams = { ...this.searchParams }
      this.$store.dispatch('SetSearchParams', searchParams)
      this.apiInit(searchParams)
    },
    apiInit () {
      this.dialog = false;
      this.loading = true;
      let params = {
        operatorName:store.getters.info.userName,
        projectId:store.getters.info.projectId,
        tenant:store.getters.info.projectId===1?100:110,
        ...this.searchParams
      }
      // console.log(store.getters.info);
      console.log(params,'sss');
      if(!params.hospitalName&&!params.realName){
        this.loading = false;
        return this.$message.warning('请选择至少一条数据')
      } 
      this.api.getListExpertUsers(params).then(response => {
        this.loading = false
        this.total = response.totalSize || 0;
        this.list = response.data || []
        if (response.status !== 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
      }).catch(() => this.loading = false)
    },
    handleClick(val) {
      switch (val) {
        case "reset":
          this.searchParams = this.PUBLIC_Methods.resetParams(this.searchParams, ["pid"])
          this.searchParams.pageIndex = 1;
          this.searchParams.pageSize = this.pageSize || 20;
          if (this.tableDropId === -1) {
            this.$store.dispatch('SetDropId', null)
          } else {
            this.$store.dispatch('SetDropId', -1)
          }
          this.searchParams.isShare = 0;
          // eslint-disable-next-line no-case-declarations
          let searchParams = { ...this.searchParams }
          this.$store.dispatch('SetSearchParams', searchParams)
          this.total = 0;
          this.list = []
          break;
        case "query":
          this.searchParams.pageIndex = 1;
          this.searchParams.pageSize = this.pageSize || 20;
          this.init()
          break;
        default: break;
      }
    },
    header_operation(val) {
      switch (val.operation) {
        case "query":
          var fieldName = val.config && val.config.params || val.index
          this.searchParams[fieldName] = val.value
          this.init()
          break;
        default: break;
      }
    },
        /**
     * @name 分页方法
     */
     size_change(val) {
      this.searchParams.pageSize = val;
      this.$store.dispatch('SetPagesize', val)
      this.init();
    },
    current_change(val) {
      this.searchParams.pageIndex = val;
      this.init();
    },
    /**
     * @name 列表操作点击方法
     * @param {*} val 
     */
    operation_change(val) {
      console.log(val, 'val')
      switch (val.operation.way) {
        case "batch":
          if (this.selectList.length > 0) {
            this.dialog = true;
            this.scopeInfo = this.selectList;
            this.dialogOperation = val.operation.operation;
            this.dialogComponent = val.operation.component;
            this.dialogTitle = val.operation.title;
            this.dialogWidth = val.operation.width || '40%';
          } else {
            this.$message.warning('请选择至少一条数据')
          }
          break;
        case "dialog":
          if (val.operation.position === 'right') {
            this.r_dialog = true;
          } else {
            this.dialog = true;
          }
          this.scopeInfo = val.model ? val.model : {};
          this.dialogOperation = val.operation.operation;
          this.dialogComponent = val.operation.component;
          this.dialogTitle = val.operation.title;
          this.dialogWidth = val.operation.width || '40%';
          break;
        case "page":
          var paramsObjs = {}
          if (val.operation.params) {
            val.operation.params.forEach(item => {
              let keyName = item.keyName || item
              let valName = item.valName || item
              paramsObjs[keyName] = val.model ? val.model[valName] : ""
            });
          }
          this.$store.dispatch("SetActive", val.operation.path);
          if (val.operation.setVuex) {
            this.$store.dispatch('SetPageParams', { ...paramsObjs })
          }
          this.$router.push({
            path: val.operation.path,
            query: {
              operation: val.operation.operation,
              component: val.operation.component,
              ...paramsObjs
            }
          });
          break;
        case "link":
          window.open(`${val.model[val.operation.pathKey]}`, "_blank")
          break;
        case "query":
          // if (val.operation.params.length) {
          //   val.operation.params.forEach(item => {
          //     let keyName = item.keyName || item
          //     let valName = item.valName || item
          //     this.searchParams[keyName] = val.model[valName]
          //   });
          // }else {
            this.searchParams[val.operation.params] = val.model[val.operation.params]
          // }
          this.searchParams.pageIndex = 1;
          this.searchParams.pageSize = 20;
          this.init()
          break;
        case "preview":
          var statusVal = val.operation.field && val.model[val.operation.field]
          if (statusVal === 1 || val.operation.noCheck) {  // 已审核

            if (val.operation.paramsVal) {
              if(val.operation.query) {
                window.open(`${val.operation.pageUrl}${val.model[val.operation.paramsVal]}${val.operation.query}`, "_blank")
              }else {
                window.open(`${val.operation.pageUrl}${val.model[val.operation.paramsVal]}`, "_blank")
              }
            } else {
              if(window.location.href.includes('Bioon')) {
                window.open(`${val.operation.pageUrl}${val.model.encodeId}.html`, "_blank")
              }else {
                window.open(`${val.operation.pageUrl}${val.model.encodeId}`, "_blank")
              }
            }
          }
          else if (statusVal === 0 || statusVal === 2) {  // 未审核
            if (!val.operation.previewName) {
              return this.$message({
                showClose: true,
                message: '该数据未审核',
                type: 'warning'
              });
            }
            this.dialog = true;
            this.scopeInfo = val.model ? val.model : {};
            this.dialogComponent = val.operation.previewName || 'MsPreviewDialog';
            this.dialogTitle = val.operation.title || "文章预览";
            this.dialogWidth = '60%';
          }
          break;
        case "activePreview":
          if (val.operation.paramsVal) {
            if(val.operation.query) {
              window.open(`${val.operation.pageUrl}${val.model[val.operation.paramsVal]}${val.operation.query}`, "_blank")
            }else {
              window.open(`${val.operation.pageUrl}${val.model[val.operation.paramsVal]}`, "_blank")
            }
          } else {
            if(window.location.href.includes('Bioon')) {
              window.open(`${val.operation.pageUrl}${val.model.encodeId}.html`, "_blank")
            }else {
              window.open(`${val.operation.pageUrl}${val.model.encodeId}`, "_blank")
            }
          }
          break;
        default:
          this.operation_change_module ? this.operation_change_module(val) : ''
          break;
      }
    },
     /**
     * @name 列表多选选中值
     * @param {*} val 
     */
     handleSelectionChange(val) {
      this.selectList = [...val]
    }
  }
}
</script>
