const scopeConfig = {
  show: {
    status: () => {
      return {
        type: 'status',
        rule: {
          0: { label: '禁用', background: '#A7ADBD' },
          1: { label: '启用', background: '#40A23F' },
          2: { label: '未激活', background: '#A7ADBD' },
          3: { label: '已注销', background: '#A7ADBD' }
        }
      }
    }
  },
  headerShow: {
    status: () => {
      return {
        type: 'dropdown',
        icon: 'icon-funnel',
        options: [
          { label: '全部', value: null },
          { label: '禁用', value: 0 },
          { label: '启用', value: 1 },
          { label: '未激活', value: 2 },
          { label: '已注销', value: 3 }
        ],
        operation: 'query'
      }
    }
  },
}

export default scopeConfig
