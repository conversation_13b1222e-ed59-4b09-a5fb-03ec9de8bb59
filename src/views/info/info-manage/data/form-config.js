// import {ms_rule_phone, ms_rule_mail} from "@/utils/form-rule.js";
const formConfig = {
  formField: [
    {
      label: '用户名',
      prop: 'userName',
      colSpan: 6,
      component: 'ms-input',
      disabled: true
    },
    {
      label: '真实姓名',
      prop: 'realName',
      colSpan: 6,
      component: 'ms-input',
      disabled: true
    },
    {
      label: 'ID',
      prop: 'id',
      colSpan: 6,
      component: 'ms-input',
      disabled: true
    },
    {
      label: '注册时间',
      prop: 'createdTime',
      colSpan: 6,
      component: 'ms-picker',
      type: 'date',
      disabled: true
    },
    {
      label: '手机号',
      prop: 'mobile',
      colSpan: 12,
      component: 'ms-input',
      disabled: true
    },
    {
      label: '邮箱',
      prop: 'email',
      colSpan: 12,
      component: 'ms-input',
      disabled: true
    },
    {
      label: '微信号',
      prop: 'wechatAccount',
      colSpan: 12,
      component: 'ms-input',
      disabled: true
    },
    {
      label: 'QQ',
      prop: 'qqAccount',
      colSpan: 12,
      component: 'ms-input',
      disabled: true
    },
    {
      label: '状态',
      prop: 'status',
      colSpan: 12,
      component: 'ms-radio',
      options: [
        { label: '启用', value: 1 },
        { label: '禁用', value: 0 }
      ],
      disabled: true
    },
    {
      label: '微信unionid',
      prop: 'wechatUnionid',
      component: 'ms-input',
      disabled: true
    }
  ],
  formUserExtField: [
    {
      label: '积分',
      prop: 'integralBase',
      colSpan: 6,
      component: 'ms-input-number',
      disabled: true
    },
    {
      label: '梅花',
      prop: 'integralUme',
      colSpan: 6,
      component: 'ms-input-number',
      disabled: true
    },
    {
      label: '城市',
      prop: 'cityId',
      colSpan: 6,
      component: 'ms-area-cascader',
      disabled: true
    },
    {
      label: '认证状态',
      prop: 'authenticateStatus',
      colSpan: 6,
      component: 'ms-select-local',
      options: [
        { label: '未认证', value: 0 },
        { label: '已认证', value: 1 },
        { label: '认证失败', value: 2 }
      ],
      disabled: true
    },
    {
      label: '单位',
      prop: 'companyName',
      colSpan: 12,
      component: 'ms-hospital-search',
      disabled: true
    },
    {
      label: '科室',
      prop: 'departmentId',
      colSpan: 6,
      component: 'ms-department-search',
      disabled: true
    },
    {
      label: '职称',
      prop: 'professionalId',
      colSpan: 6,
      component: 'ms-professional-cascader',
      disabled: true
    },
    {
      label: '临床擅长标签',
      prop: 'clinicalTags',
      colSpan: 24,
      component: 'ms-input',
      disabled: true
    },
    {
      label: '研究擅长标签',
      prop: 'researchTags',
      colSpan: 24,
      component: 'ms-input',
      disabled: true
    },
    {
      label: '个人介绍',
      prop: 'briefIntroduction',
      colSpan: 24,
      component: 'ms-input',
      type: "textarea",
      disabled: true
    },
    {
      label: '头像',
      prop: 'avatar',
      colSpan: 12,
      component: 'ms-single-image',
      disabled: true
    }
  ],
  rule: {
    userName: [
      { required: true, message: "请输入用户名", trigger: 'blur' }
    ],
    // realName: [
    //   { required: true, message: "请输入姓名", trigger: 'blur' }
    // ],
    email: [
      // { validator: ms_rule_mail, trigger: 'blur' }
    ],
    mobile: [
      // { validator: ms_rule_phone, trigger: 'blur' }
    ]
  }
}

export default formConfig;
