<template>
	<ms-table
		:currentPage="searchParams.pageIndex"
		:loading="loading"
		:operationButtons="domConfig.tableButtons"
		:pageSize="searchParams.pageSize"
		:scopeConfig="scopeConfig.show"
    :scopeHeader="scopeConfig.headerShow"
		:tableData="list"
		:tableHeader="domConfig.tableHeader"
		:total="total"
    :showSelection="true"
		@current-change="current_change"
		@operation-change="operation_change"
		@size-change="size_change"
    @header-operation="header_operation"
    @handleSelectionChange="handleSelectionChange"
	>
    <!-- 列表搜索去区域插槽 -->
		<template slot="ms-table-header">
			<div class="slot-search">
				<template v-for="(searchItem, key) in domConfig.listSearch">
					<component
						:index="searchItem.index || ''"
						:is="searchItem.component"
						:key="key"
            :width="searchItem.width || '150px'"
            :model.sync="searchParams[searchItem.model]"
            :label="searchItem.label"
						:operation="searchItem.operation || ''"
						:options="searchItem.options || []"
						:placeholder="searchItem.placeholder || ''"
						:type="searchItem.type || ''"
            :multiple="searchItem.multiple"
            :disabled="searchItem.disabled" 
					></component>
				</template>
        <div class="inlineBlock">
          <el-button @click="handleClick('query')" type="primary" plain icon="el-icon-search">查询</el-button>
          <el-button @click="handleClick('reset')">重置</el-button>
        </div>
			</div>
      <div class="slot-button">
        <template v-for="(item, index) in domConfig.soltButtons">
          <el-button :key="index" :type="item.type" :icon="item.icon" @click="operation_change({operation: item})" plain>{{ item.label }}</el-button>
        </template>
      </div>
      <el-dialog :visible.sync="dialog" 
                 closeable 
                 show-close
                 :close-on-click-modal="false"
                 :width="dialogWidth"
                 :title="dialogTitle">
				<component :is="dialogComponent" 
                   :model="scopeInfo" 
                   :operation="dialogOperation" 
                   @close="dialog = !dialog" 
                   @up-date="init" 
                   v-if="dialog"></component>
			</el-dialog>
		</template>
	</ms-table>
</template>

<script>
import tableMixins  from "../../common/mixins/table"
import domConfig from "./data/dom-config"
import scopeConfig from "./data/scope-config"
import msPagekeyStatus from './tool/ms-pagekey-status'
import msPagekeyDelete from './tool/ms-pagekey-delete'
import msPagekeyEdit from './tool/ms-pagekey-edit'
export default {
  name: "ms-pagekey-manage",
  mixins: [tableMixins],
  data () {
    return {
      domConfig: domConfig,
      scopeConfig: scopeConfig, 
      searchParams: { // => 列表查询传参
        keyword: "",
        status: null
      }
    }
  },
  components: {
    msPagekeyDelete,
    msPagekeyEdit,
    msPagekeyStatus
  },
  methods: {
    init () {
      this.loading = true;
      this.dialog = false;
      let searchParams = {...this.searchParams}
      this.api.getPageKeyWordsPage(searchParams).then(response => {
        this.loading = false
        this.total = response.totalSize || 0;
        this.list = response.data || []
        if (response.status !== 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
      }).catch(() => this.loading = false)
    },
  }
};
</script>
