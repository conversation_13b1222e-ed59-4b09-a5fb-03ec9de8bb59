const domConfig = {
  listSearch: [
    {
      label: '关键词',
      placeholder: '请输入',
      model: 'keyword',
      component: 'ms-input'
    }
  ],
  tableHeader: [
    { label: 'ID', property: 'id', sortable: true },
    { label: '关键词', property: 'keyword' },
    { label: 'URL', property: 'url' },
    { label: '替换次数', property: 'replaceCount' },
    { label: '优先级', property: 'priority', sortable: true },
    { label: '状态', property: 'status' }
  ],
  tableButtons: [
    {
      label: '编辑',
      icon: '',
      role: '',
      operation: 'edit',
      component: 'msPagekeyEdit',
      way: 'dialog',
      type: 'primary',
      title: '关键词编辑',
      width: '50%'
    },
    {
      icon: '',
      role: '',
      operation: 'editStatus',
      component: 'msPagekeyStatus',
      way: 'dialog',
      field: 'status',
      rule: {
        1: { label: '禁用', type: 'info' },
        0: { label: '启用', type: 'success' }
      }
    },
    {
      label: '删除',
      icon: '',
      role: '',
      type: 'danger',
      way: 'dialog',
      operation: 'delete',
      component: 'msPagekeyDelete',
    }
  ],
  soltButtons: [
    { 
      label: '添加关键词', 
      icon: 'el-icon-plus',
      type: 'primary', 
      operation: 'created',
      component: 'msPagekeyEdit',
      way: 'dialog',
      title: '关键词新建',
      width: '50%'
    },
    { 
      label: '批量删除', 
      type: 'info', 
      icon: 'el-icon-close',
      operation: 'delete',
      component: 'msPagekeyDelete',
      way: 'batch'
    }
  ]
}

export default domConfig;
