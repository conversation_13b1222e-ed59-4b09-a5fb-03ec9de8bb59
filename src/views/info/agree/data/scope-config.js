// import serveUrl from '@/store/data/serveUrl.js'

const scopeConfig = {
  show: {
    approvalStatus: () => {
      return {
        type: 'status',
        rule: {
          0: { label: '待审核', background: '#A7ADBD' },
          1: { label: '审核通过', background: '#40A23F' },
          2: { label: '草稿', background: '#E6A23C' }
        }
      }
    },
    // title: () => {
    //   return {
    //     type: 'preview',
    //     config: {
    //       field: 'approvalStatus',
    //       pageUrl: `${serveUrl['article']}`,
    //       previewName: 'msArticlePreview'
    //     }
    //   }
    // },
    fields: () => {
      return {
        type: 'fidd',
        fields: [
          {name: 'allHits', way: 'text'},
          {name: 'appHits', way: 'text'},
          {name: 'pcHits', way: 'text'}
        ]
      }
    },
    recommend: () => {
      return {
        type: 'icon',
        icon: 'icon-tuijiansel',
        showVal: 1,
        color: '#EA6E72'
      }
    },
    sticky: () => {
      return {
        type: 'icon',
        icon: 'icon-tuijian2',
        showVal: 1,
        color: '#FFD500'
      }
    },
    publishedTime: () => {
      return {
        type: 'formatTime',
        cFormat: '{y}-{m}-{d} {h}:{i}'
      }
    },
    createdTime: () => {
      return {
        type: 'formatTime',
        cFormat: '{y}-{m}-{d} {h}:{i}'
      }
    }
  },
  headerShow: {
    fields: () => {
      return {
        type: 'dropdown',
        icon: '',
        options: [
          { label: '默认排序', value: null },
          { label: '按总点击量', value: 1 },
          { label: '按APP点击量', value: 2 },
          { label: '按PC点击量', value: 3 },
          { label: '按点赞量', value: 4 },
          { label: '按分享量', value: 5 },
          { label: '按评论量', value: 6 },
        ],
        operation: 'query',
        params: 'sortType'
      }
    },
    approvalStatus: () => {
      return {
        type: 'dropdown',
        icon: 'icon-funnel',
        options: [
          { label: '全部', value: 2 },
          { label: '待审核', value: 0 },
          { label: '审核通过', value: 1 }
        ],
        operation: 'query'
      }
    }
  }
}

export default scopeConfig;
