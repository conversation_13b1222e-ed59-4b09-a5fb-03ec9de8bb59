// import store from '@/store'

const domConfig = {
  listSearch: [
    {
      label: '标题',
      placeholder: '请输入',
      model: 'value',
      component: 'ms-input'
    },
    {
      label: ' ',
      model: 'isTitle',
      component: 'ms-checkbox',
      options: [
        { label: '搜题目' }
      ]
    },
    {
      label: '创建人',
      placeholder: '请选择',
      model: 'createdBy',
      component: 'ms-createby-search'
    },
    {
      label: '发布时间',
      placeholder: '请选择时间段',
      model: 'createTime',
      component: 'ms-picker',
      type: 'daterange',
      width: '240px'
    }
  ],
  tableHeader: [
    // { label: '', property: 'recommend', width: '25' },
    // { label: '', property: 'sticky', width: '25' },
    { label: 'ID', property: 'id', sortable: true, width: '60' },
    { label: '标题', property: 'title', width: '250'},
    { label: '点/APP/PC', property: 'fields', width: '160' },
    { label: '创建人', property: 'createdName' },
    { label: '发布时间', property: 'publishedTime', sortable: true,  width: '130'  },
    { label: '状态', property: 'approvalStatus'},
  ],
  tableButtons: [
    {
      label: '编辑',
      icon: '',
      role: '',
      operation: 'edit',
      component: 'agree-operation',
      way: 'page',
      type: 'primary',
      path: 'agree-operation',
      params: ['id']
    },
    {
      label: '',
      icon: '',
      role: '',
      operation: 'editStatus',
      component: 'msAgreeOperation',
      way: 'dialog',
      field: 'approvalStatus',
      rule: {
        1: { label: '去审', type: '', operation: 'toreview' },
        0: { label: '审核', type: 'success', operation: 'approval' }
      }
    },
    {
      label: '删除',
      way: 'dialog',
      type: 'danger',
      operation: 'delete',
      component: 'msAgreeOperation'
    },
    // {
    //   label: '更多',
    //   operation: 'more',
    //   type: 'info',
    //   showCallback: (val) => {
    //     if (val.approvalStatus === 0 || val.approvalStatus === 1) {
    //       return true
    //     } else {
    //       return false
    //     }
    //   },
    //   children: [
    //     {
    //       label: '推荐',
    //       way: 'dialog',
    //       operation: 'recommend',
    //       component: 'msArticleRecommend',
    //       title: '推荐文章',
    //       identify: 'recommend'
    //     },
    //     {
    //       label: '固顶',
    //       way: 'dialog',
    //       operation: 'sticky',
    //       component: 'msArticleRecommend',
    //       title: '固顶文章',
    //       identify: 'sticky'
    //     }
    //   ]
    // }
  ],
  soltButtons: [
    { 
      label: '手工添加', 
      type: 'primary', 
      icon: 'el-icon-plus',
      operation: 'created',
      component: 'agree-operation',
      way: 'page',
      path: 'agree-operation',
      params: ['id']
    },
    { 
      label: '批量审核', 
      type: 'primary',
      operation: 'approval',
      component: 'msAgreeOperation',
      way: 'batch'
    },
    { 
      label: '批量去审', 
      type: 'primary',
      operation: 'toreview',
      component: 'msAgreeOperation',
      way: 'batch'
    },
    { 
      label: '批量删除',
      type: 'info',
      icon: 'el-icon-close',
      operation: 'delete',
      component: 'msAgreeOperation',
      way: 'batch'
    },
    {
      label: '回收站',
      type: 'info',
      operation: '',
      component: 'msAgreeRecycle',
      way: 'dialog',
      title: '资讯回收站 (仅展示最近一个月资讯信息)',
      width: '90%'
    }
  ]
}

export default domConfig;
