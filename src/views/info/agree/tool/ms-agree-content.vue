<template>
  <section>
    <el-form :model="submitData"
             ref="submitRef"
             class="rule-form info-form"
             :rules="rules"
             label-width="70px">
      <el-row :gutter="20"> 
        <el-col :span="18">
          <el-row>
            <el-col :span="24">
              <el-form-item label="标题中文" prop="title">
                <el-input v-model="submitData.title" style="width: 100%"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="外向链接" prop="linkOutUrl">
                <el-input v-model="submitData.linkOutUrl" style="width: 100%"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="原始链接" prop="originalUrl">
                <el-input v-model="submitData.originalUrl" style="width: 100%"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="内容" prop="content">
                <ms-editor v-model="submitData.content"></ms-editor>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="摘要" prop="summary">
                <el-input v-model="submitData.summary" type="textarea" :rows="4" @keyup.native="summaryKeyUp = true" maxlength="100" show-word-limit></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              <el-form-item prop="publishedTime" label="发布时间">
                <ms-picker :model.sync="submitData.publishedTime" type="datetime"></ms-picker>
              </el-form-item>
            </el-col>
            <el-col :span="8" v-show="formatDisabled">
              <el-form-item label="编辑者：" label-width="100px">
                <span class="font-12">{{submitData.editor}}</span>
              </el-form-item>
            </el-col>
            <el-col :span="8" v-show="formatDisabled">
              <el-form-item label="创建时间：" label-width="80px">
                <span class="font-12">{{submitData.createdTime | parseTime}}</span>
              </el-form-item>
            </el-col>
          </el-row>
        </el-col>
        <el-col :span="6" class="info-form-right">
          <el-row>
            <el-col :span="24">
              <el-form-item label="创作选项">
                <el-checkbox-group v-model="submitData.creationTypeList">
                  <el-checkbox :label=1 >MedSci原创</el-checkbox>
                  <el-checkbox :label=2 style="margin-left: 5px;">推广软文</el-checkbox>
                </el-checkbox-group>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="来源" prop="articleFrom">
                <el-input v-model="submitData.articleFrom" style="width: 100%" placeholder="非原创请直接填写来源"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="作者" prop="author">
                <el-input v-model="submitData.author" style="width: 100%"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="显示选项">
                <el-checkbox v-model="submitData.appVisible" style="width: 45%" :true-label="1" :false-label="0">APP显示</el-checkbox>
                <el-checkbox v-model="submitData.pcVisible" style="width: 45%" :true-label="1" :false-label="0">PC显示</el-checkbox>
                <el-row type="flex" style="margin-top: 5px;" v-permission="['/article','recommend']">
                  <el-checkbox v-model="submitData.recommend" style="width: 45%" :true-label="1" :false-label="0">推荐</el-checkbox>
                  <ms-picker :model.sync="submitData.recommendEndTime" type="datetime"></ms-picker>
                </el-row>
                <el-row type="flex" style="margin-top: 5px;" v-permission="['/article','sticky']">
                  <el-checkbox v-model="submitData.sticky" style="width: 45%" :true-label="1" :false-label="0">固顶</el-checkbox>
                  <ms-picker :model.sync="submitData.stickyEndTime" type="datetime"></ms-picker>
                </el-row>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="权限" prop="opened">
                <el-select v-model="submitData.opened" style="width: 100%">
                  <el-option :value="1" label="开放阅读"></el-option>
                  <el-option :value="0" label="登录阅读"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="积分" prop="integral" v-show="+submitData.opened === 0">
                <el-input-number v-model="submitData.paymentAmount" :min="0" style="width: 100%"></el-input-number>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="关键词" prop="tagList">
                <MsTagSearch v-model="submitData.tagList" :notMul="false"></MsTagSearch>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="所属期刊" prop="journalId">
                <MsJournalSearch v-model="submitData.journalId"></MsJournalSearch>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="资讯拓展">
                <el-row>
                  <el-col :span="18"><MsTagSearch v-model="submitData.articleKeyword" style="width: 95%;display: inline-block;" placeholder="资讯标题关键词" :modelId.sync="submitData.articleKeywordId" :notMul="true"></MsTagSearch></el-col>
                  <el-col :span="6"><el-input placeholder="数量" style="width: 100%" v-model="submitData.articleKeywordNum"></el-input></el-col>
                </el-row>
              </el-form-item>
              <el-form-item label="指南拓展">
                <el-row>
                  <el-col :span="18"><MsTagSearch v-model="submitData.guiderKeyword" style="width: 95%;display: inline-block;" placeholder="指南标题关键词" :modelId.sync="submitData.guiderKeywordId" :notMul="true"></MsTagSearch></el-col>
                  <el-col :span="6"><el-input placeholder="数量" style="width: 100%" v-model="submitData.guiderKeywordNum"></el-input></el-col>
                </el-row>
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item label="封面图片" prop="cover">
                <ms-single-image v-model="submitData.cover" :upFileSize="0.5"></ms-single-image>
              </el-form-item>
            </el-col>
            <el-col :span="24" style="text-align: left">
              <el-form-item label="自动水印" prop="waterMark" style="display: inline-block;">
                  <el-switch v-model="submitData.waterMark" :disabled="formatDisabled" :active-value="1" :inactive-value="0"></el-switch>
              </el-form-item>
            </el-col>
          </el-row>
        </el-col>
      </el-row>
    </el-form>
  </section>
</template>

<script>
import MsEditor from '@/components/MsEditor'
import MsTagSearch from '@/components/MsCommon/ms-tag-search'
import MsJournalSearch from '@/components/MsCommon/ms-journal-search'
import { getEditContent } from '@/utils/index'
import { mapGetters } from "vuex";
import {ms_rule_url} from "@/utils/form-rule.js";
export default {
	name: "ms-article-content",
	data () {
		return {
      rules: {
        title: [
          { required: true, message: "请填写文章标题", trigger: 'blur' }
        ],
        content: [
          { required: true, message: "请填写文章内容", trigger: 'change' }
        ],
        articleFrom: [
          { required: true, message: "请填写文章来源", trigger: 'change' }
        ],
        linkOutUrl: [
          { validator: ms_rule_url, trigger: 'blur' }
        ],
        originalUrl: [
          { validator: ms_rule_url, trigger: 'blur' }
        ],
        summary: [
          { required: true, message: "请填写摘要", trigger: 'blur' }
        ]
      },
      formatDisabled: false,
      summaryKeyUp: false
		}
  },
  components: {
    MsEditor,
    MsTagSearch,
    MsJournalSearch
  },
  computed: {
    ...mapGetters(["submitData", "info", "editorImgArr"]),
  },
  watch: {
    'submitData.creationTypeList': function(val,oldVal) {
      if (val.includes(1)) {
        this.submitData.articleFrom = 'MedSci原创'
        this.submitData.copyright = '原创'
      } else if (oldVal.includes(1)){
        this.submitData.articleFrom = ''
        this.submitData.copyright = '转发'
      }
    },
    'submitData.id': function (val) {
      if (val) {
        this.formatDisabled = true
      } 
    },
    'submitData.recommend': function (val) {
      if (val === 1) {
        this.submitData.sticky = 0
      }
    },
    'submitData.sticky': function (val) {
      if (val === 1) {
        this.submitData.recommend = 0
      }
    },
    'submitData.content': function (val) {
      if (val) {
        if (!this.submitData.id && !this.summaryKeyUp) {
          this.submitData.summary = this.PUBLIC_Methods.unexcapeHtml(getEditContent(val))
        }
      }
    },
    editorImgArr: function(val) {
      if (val && val.length > 0 && !this.submitData.id) {
        this.submitData.cover = val[0]
      }
    }
  },
  created() {
    this.submitData.editor = this.info.userName
    console.log(this.submitData,'77777');

  },
	methods: {
    // 数据校验
    validateData(callback, isDraft = false) {
      let multiCover = []
      if (this.submitData.cover) {
        multiCover[0] = this.submitData.cover
      } 
      this.submitData.content.replace(/<img [^>]*src=['"]([^'"]+)[^>]*>/g, function (match, capture) {
          multiCover.push(capture)
      });

      let params = {
        ...this.submitData,
        userId: this.info.userId,
        username: this.info.userName,
        editorId: this.info.userId,
        editor: this.info.userName,
        content: this.PUBLIC_Methods.excapeHtml(this.submitData.content),
        creationType: this.submitData.articleFrom === 'MedSci原创' ? 1 : this.submitData.creationType === 2 ? 2 : 0,
        approvalStatus: isDraft ? 2 : this.submitData.approvalStatus === 2 ? 0 : this.submitData.approvalStatus,
        multiCover: multiCover
      }
      
      this.$refs["submitRef"].validate( valid => {
        if (valid) {
          callback(params)
        }
      })
    }
	}
}
</script>
<style scope>
.ms-radio-con {
  margin-right: 5px !important;
}
</style>
<style scope lang="scss">
.info-form{
  /deep/ .el-checkbox {
    margin-right: 0px;
  }
  &-right {
    /deep/ .el-form-item {
      margin-bottom: 16px;
    }
  }
  .expand {
    .label {
      color: #409EFF; 
      font-size: 12px;
      width: 40px;
      display: inline-block;
      text-align: right;
    }
    /deep/ .el-form-item {
      padding: 4px 0;
      margin-bottom: 14px;
      margin-top: -4px;
      background-color: #E5E6E9;
      border-radius: 4px;
    }
  }
}
</style>
