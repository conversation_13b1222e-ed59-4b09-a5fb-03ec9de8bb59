<template>
  <section class="form-tab" v-loading="getLoading">

    <el-tabs v-model="activeName">
      <el-tab-pane label="协议内容" name="content">
        <el-form :model="submitData"
             ref="submitRef"
             class="rule-form info-form"
             :rules="rules"
             label-width="70px">
          <el-row :gutter="10"> 
            <el-col :span="18">
              <el-row>
                <el-col :span="12">
                  <el-form-item label="协议名称" prop="title">
                    <el-input v-model="submitData.title" style="width: 100%"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="协议内容" prop="content">
                    <ms-editor v-model="submitData.content"></ms-editor>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="8">
                  <el-form-item prop="publishedTime" label="发布时间">
                    <ms-picker :model.sync="submitData.publishedTime" type="datetime"></ms-picker>
                  </el-form-item>
                </el-col>
                <el-col :span="8" v-show="dataId">
                  <el-form-item label="编辑者：" label-width="100px">
                    <span class="font-12">{{submitData.editor}}</span>
                  </el-form-item>
                </el-col>
                <el-col :span="8" v-show="dataId">
                  <el-form-item label="创建时间：" label-width="80px">
                    <span class="font-12">{{submitData.createdTime | parseTime}}</span>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-col>
            <el-col :span="6" class="info-form-right">
              <el-row>
                <el-col :span="24">
                  <el-form-item label="显示选项">
                    <el-checkbox v-model="submitData.appVisible" style="width: 40%" :true-label="1" :false-label="0">APP显示</el-checkbox>
                    <el-checkbox v-model="submitData.pcVisible" style="width: 40%" :true-label="1" :false-label="0">PC显示</el-checkbox>
                    <el-row type="flex" style="margin-top: 5px;">
                      <el-checkbox v-model="submitData.recommend" style="width: 45%" :true-label="1" :false-label="0">推荐</el-checkbox>
                      <ms-picker :model.sync="submitData.recommendEndTime" type="datetime"></ms-picker>
                    </el-row>
                    <el-row type="flex" style="margin-top: 5px;">
                      <el-checkbox v-model="submitData.sticky" style="width: 45%" :true-label="1" :false-label="0">固顶</el-checkbox>
                      <ms-picker :model.sync="submitData.stickyEndTime" type="datetime"></ms-picker>
                    </el-row>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="权限" prop="opened">
                    <el-select v-model="submitData.opened" style="width: 100%">
                      <el-option :value="1" label="开放阅读"></el-option>
                      <el-option :value="0" label="登录阅读"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item label="封面图片" prop="cover">
                    <ms-single-image v-model="submitData.cover" :upFileSize="0.5"></ms-single-image>
                  </el-form-item>
                </el-col>
                <el-col :span="24" style="text-align: left">
                  <el-form-item label="自动水印" prop="waterMark" style="display: inline-block;">
                      <el-switch v-model="submitData.waterMark" :disabled="!!dataId" :active-value="1" :inactive-value="0"></el-switch>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-col>
          </el-row>
        </el-form>
      </el-tab-pane>
      <el-tab-pane label="显示栏目" name="category">
        <ms-info-setting :categoryModel.sync="submitData.categoryList" headerShow></ms-info-setting>
      </el-tab-pane>
    </el-tabs>
    <!-- 表单内容 -->
    
    <!-- 提交按钮 -->
    <footer-tool-bar v-loading="buttonLoading">
      <template slot="tool-content">
        <el-button type="primary" @click="info_operation('save')">保存</el-button>
        <el-button type="info" @click="info_operation('back')">返回</el-button>
      </template>
    </footer-tool-bar>
  </section>
</template>

<script>
import FooterToolBar from '@/components/ToolBar/footer-tool-bar'
import MsEditor from '@/components/MsEditor'
import msInfoSetting from '@/components/MsCommon/ms-info-setting'
export default {
  name: "agree-operation",
	data () {
		return {
      buttonLoading: false,
      getLoading: false,
      dataId: this.$route.query.id ? this.$route.query.id : 0,
      rules: {
        title: [
          { required: true, message: "请填写协议名称", trigger: 'blur' }
        ],
        content: [
          { required: true, message: "请填写协议内容", trigger: 'change' }
        ]
      },
      submitData: { 
        title: '', // => 标题 (string)
        opened: 1, // => 开放阅读 (0.不开放 1.开放)
        pcVisible: 1, // => PC可见 (0.不可见,1.可见)
        appVisible: 1, // => APP可见 (0.不可见,1.可见)
        recommend: 0, // => 推荐（0.不推荐 1.推荐）
        recommendEndTime: '', // => 推荐结束时间 
        sticky: 0, // => 置顶（0.不置顶 1.置顶）
        stickyEndTime: '',// => 置顶结束时间 (date-time)
        content: '', // => 文章内容 (string)
        waterMark: 0, // 自动水印 (0.无水印,1.加水印)
        categoryList: [], // => 专题分类集合
      },
      activeName: 'content'
		}
  },
  components: {
    FooterToolBar,
    MsEditor,
    msInfoSetting
  },
  created() {
    this.init()
  },
  methods: {
    init() {
      this.dialog = false
      let id = this.dataId
      if(id !== 0) {
        this.getLoading = true;
        this.submitData.id = id;
        this.api.getAgreementById({id: id}).then( response => {
          this.getLoading = false;
          if(response.status === 200) {
            let res = response.data
            this.submitData = {
              ...this.submitData,
              ...res,
              content: this.PUBLIC_Methods.unexcapeHtml(res.content)
            }
          } else {
            this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
          }
        }).catch(() => {
          this.getLoading = false;
        })
      } 
    },
    info_operation(val) {
      switch (val) {
        case 'save': 
          this.$refs["submitRef"].validate( valid => {
            if (valid) {
              this.submitData.content = this.PUBLIC_Methods.excapeHtml(this.submitData.content)
              this.dataId ? this.updateForm() : this.createForm()
            }
          })
          break;
        case 'back':
          this.$router.back();
          break;
        default: break;
      }
    },
    createForm() {
      this.buttonLoading = true;
      let params = {
        userId: this.$store.getters.info.userId,
        username: this.$store.getters.info.userName,
        ...this.submitData
      }
      this.api.saveAgreement(params).then(response => {
        this.buttonLoading = false
        if(response.status === 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
          this.$router.back()
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
      }).catch(() => this.buttonLoading = false)
    },
    updateForm() {
      this.buttonLoading = true;
      let params = {
        ...this.submitData
      }
      this.api.updateAgreementById(params).then(response => {
        this.buttonLoading = false
        if(response.status === 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
          this.$router.back()
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
      }).catch(() => this.buttonLoading = false)
    }
  }
}
</script>
