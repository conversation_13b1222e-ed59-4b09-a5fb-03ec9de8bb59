<template>
	<ms-table
		:currentPage="searchParams.pageIndex"
		:loading="loading"
		:operationButtons="domConfig.tableButtons"
		:pageSize="searchParams.pageSize"
		:scopeConfig="scopeConfig.show"
    :scopeHeader="scopeConfig.headerShow"
		:tableData="list"
		:tableHeader="domConfig.tableHeader"
		:total="total"
    :showSelection="true"
		@current-change="current_change"
		@operation-change="operation_change"
		@size-change="size_change"
    @header-operation="header_operation"
    @handleSelectionChange="handleSelectionChange"
    class="table-svg"
	>
    <!-- 列表搜索去区域插槽 -->
		<template slot="ms-table-header">
			<div class="slot-search">
				<template v-for="(searchItem, key) in domConfig.listSearch">
					<component
						:index="searchItem.index || ''"
						:is="searchItem.component"
						:key="key"
            :width="searchItem.width || '150px'"
            :model.sync="searchParams[searchItem.model]"
            :label="searchItem.label"
						:operation="searchItem.operation || ''"
						:options="searchItem.options || []"
						:placeholder="searchItem.placeholder || ''"
						:type="searchItem.type || ''"
            :multiple="searchItem.multiple"
            :disabled="searchItem.disabled"
            :code="searchItem.code"
					></component>
				</template>
        <div class="inlineBlock">
          <el-button @click="handleClick('query')" type="primary" plain icon="el-icon-search">查询</el-button>
          <el-button @click="handleClick('reset')">重置</el-button>
        </div>
        
			</div>
			<div class="slot-button-article clearfix">
				<template v-for="(item, index) in domConfig.soltButtons">
					<el-button :key="index" 
                     :type="item.type" 
                     size="mini"
                     :icon="item.icon"
                     @click="operation_change({operation: item})"
                     :disabled="item.roleDisabled"
                     plain>{{ item.label }}</el-button>
				</template>
        <el-alert class="article-total" :title="`共搜索到${total}篇协议`" type="info" show-icon></el-alert>
			</div>
			<el-dialog :visible.sync="dialog" 
                 closeable 
                 show-close
                 :close-on-click-modal="false"
                 :width="dialogWidth"
                 :title="dialogTitle">
				<component :is="dialogComponent" 
                   :model="scopeInfo" 
                   :operation="dialogOperation" 
                   @close="dialog = !dialog" 
                   @up-date="init" 
                   v-if="dialog"></component>
			</el-dialog>
		</template>
	</ms-table>
</template>

<script>
import agreeMixin from "./agree-mixin"
import tableMixins  from "../../common/mixins/table"
export default {
  name: "ms-agree-manage",
  mixins: [tableMixins,agreeMixin],
  data () {
    return {
      notifyInst: null,
      tipFlag: true
    }
  },
  created() {
    // this.tipFlag = this.$store.getters.infoListTip.articleTip
    // if (this.tipFlag) {
    //   this.notifyInst = this.$notify({
    //     position: 'bottom-left',
    //     dangerouslyUseHTMLString: true,
    //     message: '<div><p style="color:#F56C6C;">红色栏：首页推荐（至多轮播5条）</p><p style="color: #E6A23C">黄色栏：首页固顶（至多置顶5条）</p><p style="color: #50bfff">蓝色栏：PC端不显示</p></div>',
    //     duration: 10000,
    //     customClass: 'notify-info'
    //   });
    //   this.$store.dispatch('SetInfoTip','articleTip')
    // }
    
  },
  beforeDestroy() {
    // if (this.tipFlag) this.notifyInst.close()
  },
  methods: {
    // rowColor({row}) {
    //   if (row.recommend === 1) {
    //     return 'recommend-row';
    //   } else if (row.sticky === 1) {
    //     return 'sticky-row';
    //   } else if (row.recommend !== 1 && row.sticky !== 1 && row.pcVisible === 0) {
    //     return 'pc-visible'
    //   }
    //   return '';
    // },
    apiInit (params) {
      let searchParams = {...params}
      if (searchParams.createTime) {
        searchParams.publishedStartTime = searchParams.createTime[0] || ''
        searchParams.publishedEndTime = searchParams.createTime[1] || ''
      }
      searchParams.isTitle = searchParams.isTitle ? '2' : '1'
      this.api.getAgreementPage(searchParams).then(response => {
        this.loading = false
        this.total = response.totalSize || 0;
        this.list = response.data || []
        if (response.status !== 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
      }).catch(() => this.loading = false)
    }
  }
};
</script>
