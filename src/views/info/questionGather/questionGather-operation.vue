<template>
  <section class="form-tab" v-loading="getLoading">
    <el-tabs v-model="activeName">
      <el-tab-pane label="试题集操作信息" name="content">
        <ms-question-Gather-content ref="contentTemp" @handle-click="handle_click"></ms-question-Gather-content>
      </el-tab-pane>
    </el-tabs>

    <!-- 弹出层 -->
    <el-dialog :visible.sync="dialog" 
               closeable 
               :show-close="false"
               :close-on-click-modal="false"
               width="50%"
               :title="dialogTitle">
      <component :is="dialogComponent" 
                   :model="dialogInfo" 
                   :operation="dialogOperation" 
                   :isNeedApi="false"
                   @close="dialog = !dialog"
                   @up-date="init" 
                   v-if="dialog"></component>
    </el-dialog>
    <!-- 提交按钮 -->
    <footer-tool-bar v-loading="buttonLoading">
      <template slot="tool-content">
        <!-- <el-button plain>提交</el-button> -->
        <!-- <el-button plain v-show="videoId" @click="info_operation('approval')">{{submitData.auditStatus === 1 ? '去审' : '审核'}}</el-button> -->
        <el-button type="primary" @click="info_operation('save')">提交</el-button>
        <el-button type="info" @click="info_operation('back')">取消</el-button>
      </template>
    </footer-tool-bar>
  </section>
</template>

<script>
import msQuestionGatherContent from './tool/ms-questionGather-content'
import questionGatherMixin from "./questionGather-mixin"
import FooterToolBar from '@/components/ToolBar/footer-tool-bar'
import { mapGetters } from "vuex";
export default {
  name: "video-special-operation",
  mixins: [questionGatherMixin],
	data () {
		return {
      activeName: 'content',
      videoId: this.$route.query.id ? this.$route.query.id : 0,
      buttonLoading: false,
      getLoading: false,

      submitData: { // => userId、username、integral、waterMark、formatted 、expandRead (保存添加属性)
        userId: '', // => 用户Id
        username: '', // => 用户名称 (string)
        title: '', // => 标题 (string)
      },

      dialogComponent: '',
      dialog: false,
      dialogTitle: '',
      dialogOperation: '',
      dialogInfo: {},
		}
  },
  computed: {
    ...mapGetters(["info"])
  },
  created() {
    this.init()
  },
  components: {
    msQuestionGatherContent,
    FooterToolBar
  },
  methods: {
    init() {
      let id = this.videoId
      this.dialog = false;
      this.$store.dispatch('SetSubmitData', this.submitData)
      if(id !== 0) {
        this.getLoading = true;
        this.submitData.id = id;
        let params = {
          gatherId: id
        }
        this.api.getQuestionGatherDetail(params).then( response => {
          this.getLoading = false;
          if(response.status === 200) {
            let res = response.data
            this.submitData = {
              ...this.submitData,
              ...res
            }
          } else {
            this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
          }
          this.$store.dispatch('SetSubmitData', this.submitData)
        }).catch(() => {
          this.getLoading = false;
          this.$store.dispatch('ClearSubmitData')
        })
      }
    },
    info_operation(val) {
      switch (val) {
        case 'save': 
          this.$refs['contentTemp'].validateData(this.videoId ? this.updateData: this.createData)
          break;
        case 'back':
          this.$router.back();
          break;
        case 'approval':
          var params = {
            way: 'dialog',
            operation: this.submitData.auditStatus === 1 ? 'toreview' : 'approval',
            component: 'msQuestionGatherContent',
            data: this.submitData
          }
          this.handle_click(params)
          break;
        default: break;
      }
    },
    createData(params) {
      this.buttonLoading = true;
      let paramsData = {
        ...params
      }
      this.api.addGather(paramsData).then(response => {
        this.buttonLoading = false
        if(response.status === 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
          this.$router.back()
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
      }).catch(() => this.buttonLoading = false)
    },
    updateData(params) {
      this.buttonLoading = true;
      let paramsData = {
        ...params
      }
      this.api.updateGather(paramsData).then(response => {
        this.buttonLoading = false
        if(response.status === 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
          this.$router.back()
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
      }).catch(() => this.buttonLoading = false)
    },
    handle_click(val) {
      switch (val.way) {
        case 'dialog': 
          this.dialog = true;
          this.dialogInfo = val.data
          this.dialogOperation = val.operation;
          this.dialogComponent = val.component;
          this.dialogTitle = val.title;
          break;
        default: break;
      }
    }
  }
}
</script>
