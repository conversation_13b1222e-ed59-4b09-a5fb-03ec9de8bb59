const formConfig = {
  listSearch: [
    {
      label: ' ',
      placeholder: '题型',
      model: 'type',
      component: 'ms-question-type'
    },
    {
      label: ' ',
      placeholder: '难度',
      model: 'difficulty',
      component: 'ms-question-difficulty'
    },
    {
      label: ' ',
      placeholder: '关键词',
      model: 'tagId',
      component: 'ms-question-tag-search'
    },
  ],
  tableHeader: [
    { label: 'ID', property: 'id', sortable: true, width: 80 },
    { label: '题干', property: 'title', width: 250 },
    { label: '题型', property: 'type', width: 100 },
    { label: '关键词', property: 'tagStr' },
    { label: '难度', property: 'difficulty' }
  ],
  tableButtons: [
    {
      label: '删除',
      icon: '',
      role: '',
      operation: 'deleteShip',
      component: 'msQuestionGatherOperation',
      way: 'dialog',
      type: 'danger'
    }
  ],
  soltButtons: [
    {
      label: '从题库选择',
      icon: '',
      role: '',
      operation: 'edit',
      component: 'msQuestionGatherAdd',
      way: 'dialog',
      type: 'primary',
      position: 'right',
      title: '从题库选择（若题干/解析有图片），则需要先在【题库管理】中创建题目',
      width: '55%'
    },
    {
      label: '批量导入',
      icon: '',
      role: '',
      operation: 'edit',
      component: 'msQuestionGatherImport',
      way: 'dialog',
      type: 'primary',
      position: 'right',
      title: '导入题目',
      width: '55%'
    },
  ],
  shipButtons: [
    {
      label: '批量删除',
      type: 'info',
      icon: 'el-icon-close',
      operation: 'deleteShip',
      component: 'msQuestionGatherOperation',
      way: 'batch'
    }
  ]
}

export default formConfig;
