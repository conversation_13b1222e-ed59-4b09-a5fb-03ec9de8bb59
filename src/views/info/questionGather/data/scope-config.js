const scopeConfig = {
  show: {
    auditStatus: () => {
      return {
        type: 'status',
        rule: {
          0: { label: '未审核', background: '#A7ADBD' },
          1: { label: '已审核', background: '#40A23F' }
        }
      }
    },
    type: () => {
      return {
        type: 'code',
        rule: {
          1: { label: '单选题' },
          2: { label: '多选题' },
          3: { label: '主观题' }
        }
      }
    },
    difficulty: () => {
      return {
        type: 'code',
        rule: {
          1: { label: '简单' },
          2: { label: '中等' },
          3: { label: '困难' }
        }
      }
    },
    createdAt: () => {
      return {
        type: 'formatTime',
        cFormat: '{y}-{m}-{d} {h}:{i}'
      }
    },
    updatedAt: () => {
      return {
        type: 'formatTime',
        cFormat: '{y}-{m}-{d} {h}:{i}'
      }
    }
  },
  headerShow: {
    auditStatus: () => {
      return {
        type: 'dropdown',
        icon: 'icon-funnel',
        options: [
          { label: '全部', value: null },
          { label: '未审核', value: 0 },
          { label: '已审核', value: 1 }
        ],
        operation: 'query'
      }
    }
  }
}

export default scopeConfig;
