const domConfig = {
  listSearch: [
    {
      label: '试题集',
      placeholder: '请输入',
      model: 'title',
      component: 'ms-input'
    },
    {
      label: '创建人',
      placeholder: '请选择',
      model: 'createdBy',
      component: 'ms-createby-search'
    },
  ],
  tableHeader: [
    { label: 'ID', property: 'id', sortable: true, width: 80 },
    { label: '试题集名称', property: 'title', width: 250 },
    { label: '题目数量', property: 'number', width: 100 },
    { label: '创建人', property: 'createdName' },
    { label: '创建时间', property: 'createdAt', width: 130, sortable: true },
    { label: '更新时间', property: 'updatedAt', width: 130, sortable: true },
    // { label: '审核人', property: 'auditorName' },
    // { label: '审核时间', property: 'auditTime', width: 130, sortable: true },
    { label: '状态', property: 'auditStatus' }
  ],
  tableButtons: [
    {
      label: '编辑',
      icon: '',
      role: '',
      operation: 'edit',
      component: 'video-manage-series',
      way: 'page',
      type: 'primary',
      path: 'questionGather-operation',
      params: ['id']
    },
    {
      label: '',
      icon: '',
      role: '',
      operation: 'editStatus',
      component: 'msQuestionGatherOperation',
      way: 'dialog',
      field: 'auditStatus',
      rule: {
        1: { label: '去审', type: '', operation: 'toreview' },
        0: { label: '审核', type: 'success', operation: 'approval' }
      }
    },
    {
      label: '删除',
      icon: '',
      role: '',
      operation: 'delete',
      component: 'msQuestionGatherOperation',
      way: 'dialog',
      type: 'danger'
    }
  ],
  soltButtons: [
    {
      label: '创建试题集',
      type: 'primary',
      icon: 'el-icon-plus',
      title: '创建试题集',
      operation: 'created',
      component: 'msQuestionGatherCreate',
      way: 'dialog',
    },
    {
      label: '批量审核',
      type: 'primary',
      operation: 'approval',
      component: 'msQuestionGatherOperation',
      way: 'batch'
    },
    {
      label: '批量去审',
      type: 'primary',
      operation: 'toreview',
      component: 'msQuestionGatherOperation',
      way: 'batch'
    },
    // {
    //   title: '批量添加分类',
    //   label: '批量添加分类',
    //   type: 'success',
    //   operation: 'add',
    //   component: 'msQuestionGatherCategory',
    //   way: 'batch',
    //   identify: 'batch_add_category',
    //   width: '70%'
    // },
    // {
    //   title: '批量移除分类',
    //   label: '批量移除分类',
    //   type: 'success',
    //   operation: 'delete',
    //   component: 'msQuestionGatherCategory',
    //   way: 'batch',
    //   identify: 'batch_delete_category',
    //   width: '70%'
    // },
    {
      label: '批量删除',
      type: 'info',
      icon: 'el-icon-close',
      operation: 'delete',
      component: 'msQuestionGatherOperation',
      way: 'batch'
    }
  ]
}

export default domConfig;
