<template>
  <ms-right-content class="right">
    <question-gather-upload @up-date="submitForm" style="margin-bottom: 20px"></question-gather-upload>
    <el-button style="margin-right: 20px" type="success" plain :disabled="loading" @click="downTmp()">下载导入模版</el-button>
    <span>支持扩展名：.xls .xlsx</span>
    <template slot="footer">
      <div style="text-align:right; margin-right: 20px">
        <!-- <el-button @click="submitForm"
              :loading="loading"
              type="primary">确 认</el-button> -->
        <el-button @click="$emit('close')" >取 消</el-button>
      </div>
    </template>
  </ms-right-content>
</template>

<script>
import tableMixins  from "../../../common/mixins/table"
import questionGatherMixin from "../questionGather-mixin"
import questionGatherUpload from "./ms-questionGather-upload"
import { mapGetters } from "vuex";
export default {
	name: "ms-questionGather-add",
  mixins: [tableMixins, questionGatherMixin],
	data () {
		return {
      loading: false
		}
	},
	props: {
		model: Object,
		operation: String
  },
  computed: {
    ...mapGetters(["info"])
  },
  components: {
   questionGatherUpload
  },
  created() {
  },
	methods: {
    apiInit() {
      this.loading = false
    },
    // 下载模版
    downTmp() {
      this.loading = true
      this.api.downQuestionTem({}).then(response => {
        this.loading = false
        window.location.href = response.data.url
        if (response.status !== 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
      }).catch(() => this.loading = false)
    },
    submitForm(data) {
      this.$emit('import-date', data)
    }
	}
}
</script>
