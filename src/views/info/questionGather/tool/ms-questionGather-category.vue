<template>
  <ms-operation-dialog>
    <template slot="content">
      <ms-info-setting :categoryModel.sync="submitData.categoryList" :categoryType="'question'" :categoryConfigChild="{moduleName: 'article'}" headerShow></ms-info-setting>
    </template>
    <template slot="footer">
      <el-button @click="submitForm"
                 :loading="loading"
                 size="mini"
                 type="primary">确 定</el-button>
      <el-button @click="$emit('close')"
                 size="mini">取 消</el-button>
    </template>
  </ms-operation-dialog>
</template>

<script>
import { mapGetters } from "vuex";
import msInfoSetting from '@/components/MsCommon/ms-info-setting'
export default {
	name: "ms-question-category",
	data () {
		return {
            getLoading: false,
            loading: false,
            submitData: {
                categoryList: [],
                ids: [],
                dealType: 1
            }
		}
	},
	props: {
		model: Array,
		operation: String
    },
    computed: {
        ...mapGetters(["info"])
    },
    components: {
        msInfoSetting
    },
    created() {
        this.submitData.ids = this.model.map(v => {
            return v.id
        })
        this.submitData.dealType = this.operation === "add" ? 1 : this.operation === "delete" ? 2 : 0
    },
	methods: {
        submitForm () {
            let params = {
                userId: this.info.userId,
                username: this.info.userName,
                ...this.submitData
            }
            this.loading = true
            this.api.videosExercisesBatchModifyCategory(params).then( response => {
                if(response.status === 200) {
                    this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
                    this.$emit('up-date')
                } else {
                    this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
                }
                this.loading = false
            }).catch( () => {
                this.loading = false;
                this.$emit('close')
            })
        }
	}
}
</script>
