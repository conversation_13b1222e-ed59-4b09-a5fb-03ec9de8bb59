<template>
  <section>
    <el-form :model="submitData"
             ref="submitRef"
             class="rule-form info-form"
             :rules="rules"
             label-width="90px">
      <el-row :gutter="20">
        <el-col :span="16">
          <el-row>
            <el-col :span="24">
              <el-form-item label="试题集名称" prop="title">
                <el-input v-model="submitData.title" placeholder="请输入20字以内的试题集名称" style="width: 100%"  maxlength="20" show-word-limit></el-input>
              </el-form-item>
              <el-form-item label="添加题目">
                <template v-for="(item, index) in formConfig.soltButtons">
                  <el-button :key="index" 
                            :type="item.type" 
                            size="mini"
                            :icon="item.icon"
                            :disabled="uploadDisabled"
                            @click="operation_change({operation: item})"
                            >{{ item.label }}</el-button>
                </template>
              </el-form-item>
            </el-col>
          </el-row>
        </el-col>
      </el-row>
      <div class="flex-progress"  v-show="showProgress">
        <div class="progress" style="margin-bottom:10px">
          <span>{{typeName}}进度</span>
          <el-progress :stroke-width="20" :percentage="percentage" :color="colors" ></el-progress>
        </div>
        <div style="margin-left:20px;position: relative">成功：{{successNum}} / 失败：{{failNum}} / 总计：{{importTotal}}</div>
      </div> 
      <ms-table
          :currentPage="searchParams.pageIndex"
          :loading="loading"
          :operationButtons="formConfig.tableButtons"
          :pageSize="searchParams.pageSize"
          :scopeConfig="scopeConfig.show"
          :scopeHeader="scopeConfig.headerShow"
          :tableData="list"
          :tableHeader="formConfig.tableHeader"
          :total="total"
          :showSelection="true"
          @current-change="current_change"
          @operation-change="operation_change"
          @size-change="size_change"
          @header-operation="header_operation"
          @handleSelectionChange="handleSelectionChange">
            <template slot="ms-table-header">
              <div class="slot-button-article clearfix">
                <template v-for="(item, index) in formConfig.shipButtons">
                  <el-button :key="index" 
                            :type="item.type" 
                            size="mini"
                            :icon="item.icon"
                            @click="operation_change({operation: item})"
                            plain>{{ item.label }}</el-button>
                </template>
              </div>
              <el-dialog :visible.sync="dialog" 
                        closeable 
                        show-close
                        :close-on-click-modal="false"
                        :width="dialogWidth"
                        :title="dialogTitle">
                <component :is="dialogComponent" 
                          :model="scopeInfo" 
                          :operation="dialogOperation" 
                          @close="dialog = !dialog" 
                          @up-date="init" 
                          v-if="dialog"></component>
              </el-dialog>
            </template>
      </ms-table>
    </el-form>
    <ms-right-dialog :visible.sync="r_dialog" :width="dialogWidth" :title="dialogTitle">
      <component
        :is="dialogComponent"
        :model="scopeInfo"
        :operation="dialogOperation"
        @close="r_dialog = !r_dialog"
        @up-date="init"
        @import-date="uploadSuccess"
        v-if="r_dialog"
      ></component>
    </ms-right-dialog>
  </section>
</template>

<script>
import { mapGetters } from "vuex";
import tableMixins  from "../../../common/mixins/table"
import questionGatherMixin from "../questionGather-mixin"
import msQuestionGatherAdd from "./ms-questionGather-add"
import msQuestionGatherImport from "./ms-questionGather-import"
export default {
	name: "ms-question-gather-content",
  mixins: [tableMixins, questionGatherMixin],
	data () {
		return {
      rules: {
        title: [
          { required: true, message: "请输入", trigger: 'blur' }
        ],
      },
      r_dialog: false,
      dialogWidth: '45%',
      dialogTitle: '',
      dialogComponent: '',
      scopeInfo: {},
      dialogOperation: '',
      colors: [
          {color: '#409EFF', percentage: 99},
          {color: '#67C23A', percentage: 100}
      ],
      showProgress: false,
      percentage: 0,
      uploadDisabled: false,
      successNum: 0,
      failNum: 0,
      importTotal: 0,
      typeName: '读取excel'
		}
  },
  computed: {
    ...mapGetters(["submitData", "info", "localCode"])
  },
  components: { msQuestionGatherAdd, msQuestionGatherImport },
  created() {
    this.showProgress = false
  },
  mounted() {
      if (this.$store.getters.questionImportData.uuid) {
          this.viewStatus(this.$store.getters.questionImportData.uuid)
      }
  },
	methods: {
    // 数据校验
    validateData(callback) {
      let params = {
        ...this.submitData,
        userId: this.info.userId,
        username: this.info.userName
      }
      this.$refs["submitRef"].validate( valid => {
        if (valid) {
          callback(params)
        }
      })
    },
    apiInit (params) {
      let searchParams = {
        gatherId: this.$route.query.id,
        ...params
      }
      this.uploadDisabled = false
      this.api.getQuestionGatherShipPage(searchParams).then(response => {
        this.loading = false
        this.total = response.totalSize || 0;
        this.list = response.data || []
        if (response.status !== 200 && response.status !== 204) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
      }).catch(() => this.loading = false)
    },
    uploadSuccess(data) {
      this.dialog = false;
      this.r_dialog = false;
      this.uploadDisabled = true
      this.$store.dispatch('SetQuestionImport', {uuid: data.uuid ? data.uuid :0})
      this.viewStatus(data.uuid ? data.uuid :0)
    },
    // 查看执行状态
    viewStatus(id) {
        this.showProgress = true
        this.api.questionFileGetStatus({uuid: id}).then(res => {
            if (res.status === 200) {
                if (!(res.data.status === 3 && res.data.type === 3)) {
                    this.percentage = Math.floor(res.data.successNum/res.data.total * 100)
                    this.successNum = res.data.successNum
                    this.failNum = res.data.failNum
                    this.importTotal = res.data.total
                    this.typeName = res.data.type == 1 ? '读取excel' : res.data.type == 2 ? '校验数据' : res.data.type == 3 ? '导入数据' : '暂无'
                     setTimeout(()=> {
                        this.viewStatus(id)
                        this.loading = true
                    }, 1000)
                } else {
                    this.uploadDisabled = false
                    this.percentage = 100
                    this.successNum = res.data.successNum
                    this.failNum = res.data.failNum
                    this.importTotal = res.data.total
                    this.$store.dispatch('SetQuestionImport', {uuid: 0})
                    if(res.data.failNum > 0) {
                      this.$alert(res.data.allFailMsg, '温馨提示', {
                        dangerouslyUseHTMLString: true
                      })
                    }else {
                      this.$alert(`批量导入成功，共计${res.data.successNum}条数据`, '温馨提示', {
                        confirmButtonText: '确定',
                      })
                    }
                    this.init()
                }
            } else {
                this.showProgress = false
                this.uploadDisabled = false
                this.$store.dispatch('SetQuestionImport', {uuid: 0})
                this.$message({
                    message: '未获取到文件导入状态',
                    type: 'success'
                })
                this.init()
            }
        })
    }
	}
}
</script>

<style lang="scss" scoped>
.info-form{
  /deep/ .el-checkbox {
    margin-right: 0px;
  }
  /deep/ .el-checkbox.width80 {
    width: 80%;
    margin-right: 30px;
  }
  /deep/ .el-checkbox.width80 .el-checkbox__label {
    width: 100%;
  }
  /deep/ .el-radio.width80 {
    width: 80%;
  }
  /deep/ .el-radio.width80 .el-radio__label {
    width: 100%;
  }
  &-right {
    /deep/ .el-form-item {
      margin-bottom: 16px;
    }
  }
  .expand {
    .label {
      color: #829FFF; 
      font-size: 12px;
      width: 40px;
      display: inline-block;
      text-align: right;
    }
    /deep/ .el-form-item {
      padding: 4px 0;
      margin-bottom: 14px;
      margin-top: -4px;
      background-color: #E5E6E9;
      border-radius: 4px;
    }
  }
  /deep/ .addBtn {
    margin-bottom: 20px;
  }
}
/deep/ .right-content {
  height: calc(100vh - 205px);
}
/deep/ .flex-progress {
  display: flex;
  flex-direction: row;
  align-items: center;
}
</style>
