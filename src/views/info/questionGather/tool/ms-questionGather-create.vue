<template>
  <ms-operation-dialog>
    <template slot="content">
      <el-form ref="submitRef"
               :model="submitData"
               :rules="rules"
               label-width="85px"
               class="rule-form">
        <el-row>
          <el-col :span="18" :offset="2">
            <el-form-item label="试题集名称" prop="title">
              <el-input v-model="submitData.title" placeholder="请输入20个字符以内的文本"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </template>
    <template slot="footer">
      <el-button @click="submitForm"
                 :loading="loading"
                 size="mini"
                 type="primary">提交</el-button>
      <el-button @click="$emit('close')"
                 size="mini">取 消</el-button>
    </template>
  </ms-operation-dialog>
</template>

<script> 
import { mapGetters } from "vuex";
export default {
	name: "ms-questionGather-create",
	data () {
		return {
      loading: false,
      submitData: {
        title: ""
      },
      rules: {
        title: [
          { required: true, message: "请输入试题集名称", trigger: 'blur' },
          { max: 20, message: '请输入20个字符以内的文本', trigger: 'blur' }
        ]
      },
    }
  },
	props: {
		model: Object,
		operation: String
  },
  computed: {
    ...mapGetters(["info"])
  },
  created() {
    this.init()
  },
	methods: {
    init() {
      this.operationLocal = this.operation || this.$route.query.operation
      if(this.operationLocal === 'edit' && this.model.id) {
        this.submitData = {...this.submitData,...this.model}
      } 
    },
		submitForm() {
      this.$refs.submitRef.validate( valid => {
        if(valid) {
          this.loading = true;
          this.submitAdd()
        }
      })
    },
    submitAdd() {
      let params = {
        userId: this.info.userId,
        username: this.info.userName,
        ...this.submitData
      }
      this.api.addGather(params).then(response => {
        if(response.status === 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
          this.$router.push({
            path: '/questionGather-operation',
            query: {
              operation: 'edit',
              component: 'video-manage-series',
              id: response.data.id
            }
          })
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
        this.$emit('up-date')
        this.loading = false
      }).catch( () => {
        this.loading = false;
        this.$emit('close')
      })
    },
	}
}
</script>
