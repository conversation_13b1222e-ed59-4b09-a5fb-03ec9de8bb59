<template>
  <div>
    <ms-right-content class="right">
      <ms-table
        style="height: calc(100vh - 150px);"
        :currentPage="searchParams.pageIndex"
        :loading="loading"
        :pageSize="searchParams.pageSize"
        :scopeConfig="scopeConfig.show"
        :scopeHeader="scopeConfig.headerShow"
        :tableData="list"
        :tableHeader="formConfig.tableHeader"
        :total="total"
        :showSelection="true"
        @current-change="current_change"
        @operation-change="operation_change"
        @size-change="size_change"
        @header-operation="header_operation"
        @handleSelectionChange="handleSelectionChange"
      >
        <!-- 列表搜索去区域插槽 -->
        <template slot="ms-table-header">
          <div class="slot-search">
            <template v-for="(searchItem, key) in formConfig.listSearch">
              <component
                :index="searchItem.index || ''"
                :is="searchItem.component"
                :key="key"
                :width="searchItem.width || '150px'"
                :model.sync="searchParams[searchItem.model]"
                :label="searchItem.label"
                :operation="searchItem.operation || ''"
                :options="searchItem.options || []"
                :placeholder="searchItem.placeholder || ''"
                :type="searchItem.type || ''" 
              ></component>
            </template>
            <div class="inlineBlock">
              <el-button @click="handleClick('query')" type="primary" plain icon="el-icon-search">查询</el-button>
              <el-button @click="handleClick('reset')">重置</el-button>
            </div>
          </div>
        </template>
      </ms-table>
    </ms-right-content>
    <div style="text-align:right; margin-right: 20px">
      <el-button @click="submitForm"
                :loading="loading"
                type="primary">保 存</el-button>
      <el-button @click="$emit('close')" >取 消</el-button>
    </div>
  </div>
</template>

<script>

import tableMixins  from "../../../common/mixins/table"
import questionGatherMixin from "../questionGather-mixin"
import msCompanySearch from '@/components/MsCommon/ms-company-search'
import msQuestionDifficulty from '@/components/MsCommon/ms-question-difficulty'
import msQuestionTagSearch from '@/components/MsCommon/ms-question-tag-search'
import msQuestionType from '@/components/MsCommon/ms-question-type'
import { mapGetters } from "vuex";
export default {
	name: "ms-questionGather-add",
  mixins: [tableMixins, questionGatherMixin],
	data () {
		return {
      loading: false,
      getLoading: false,
      questionIds: []
		}
	},
	props: {
		model: Object,
		operation: String
  },
  computed: {
    ...mapGetters(["info"])
  },
  components: {
    msCompanySearch,
    msQuestionDifficulty,
    msQuestionType,
    msQuestionTagSearch
  },
  created() {
  },
	methods: {
    apiInit (params) {
      let searchParams = {
        operSource: 1,
        gatherId: this.$route.query.id,
        ...params
      }
      this.api.getQuestionByGatherPage(searchParams).then(response => {
        this.loading = false
        this.total = response.totalSize || 0;
        this.list = response.data || []
        if (response.status !== 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
      }).catch(() => this.loading = false)
    },
		submitForm () {
      if (this.selectList.length > 0) {
        this.loading = true;
        this.questionIds = this.selectList.map(n=> {return n.id})
        this.submit()
      } else {
        this.$message.warning('请选择至少一条数据')
      }
    },
    async submit() {
      let params = {
        userId: this.info.userId,
        username: this.info.userName,
        gatherId: this.$route.query.id,
        questionIds: this.questionIds
      }
      await this.api.batchAddQuestionGatherList(params).then(response => {
        if(response.status === 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
          this.$emit('up-date')
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
        this.loading = false
      }).catch( () => {
        this.loading = false;
        this.$emit('close')
      })
    },
	}
}
</script>
