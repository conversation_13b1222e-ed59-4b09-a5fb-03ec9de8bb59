const domConfig = {
  listSearch: [
    {
      label: '类别',
      placeholder: '请输入',
      model: 'csTypeName',
      component: 'ms-select-local',
      options: [
        {label: 'introduction',value:'introduction'},
        {label: 'methods',value:'methods'},
        {label: 'statistic',value:'statistic'},
        {label: 'results',value:'results'},
        {label: 'discussion',value:'discussion'},
        {label: 'acknowledge及其它',value:'acknowledge及其它'}
      ]
    },
    {
      label: '标题',
      placeholder: '请输入',
      model: 'csTitle',
      component: 'ms-input'
    },
    {
      label: '内容关键字',
      placeholder: '请输入',
      model: 'csContent',
      component: 'ms-input'
    }
  ],
  tableHeader: [
    { label: 'ID', property: 'id', sortable: true, width: 80 },
    { label: '标题', property: 'csTitle', width: 200 },
    { label: '内容', property: 'csContent', width: 250 },
    { label: '类别', property: 'csTypeName'},
    { label: '用户', property: 'createdName' },
    { label: '创建时间', property: 'createdTime', sortable: true }
  ],
  tableButtons: [
    {
      label: '编辑',
      icon: '',
      role: '',
      operation: 'edit',
      component: 'sci-manage-operation',
      way: 'page',
      type: 'primary',
      path: 'sci-manage-operation',
      params: ['id']
    },
    {
      label: '',
      icon: '',
      role: '',
      operation: 'editStatus',
      component: 'msSciOperation',
      way: 'dialog',
      field: 'csStatus',
      rule: {
        1: { label: '去审', type: '', operation: 'toreview' },
        0: { label: '审核', type: 'success', operation: 'approval' }
      }
    },
    {
      label: '删除',
      operation: 'delete',
      type: 'danger',
      way: 'dialog',
      component: 'msSciDelete',
    }
  ],
  soltButtons: [
    { 
      label: '添加句子', 
      type: 'primary', 
      icon: 'el-icon-plus',
      operation: 'created',
      component: 'sci-manage-operation',
      way: 'page',
      path: 'sci-manage-operation',
      params: ['id']
    },
    { 
      label: '批量审核', 
      type: 'primary',
      operation: 'approval',
      component: 'msSciOperation',
      way: 'batch'
    },
    { 
      label: '批量去审', 
      type: 'primary',
      operation: 'toreview',
      component: 'msSciOperation',
      way: 'batch'
    },
    { 
      label: '批量删除',
      type: 'info',
      icon: 'el-icon-close',
      operation: 'delete',
      component: 'msSciDelete',
      way: 'batch'
    }
  ]
}

export default domConfig;
