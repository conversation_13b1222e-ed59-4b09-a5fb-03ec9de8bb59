<template>
  <section v-loading="getLoading" class="form-tab">
    <!-- 表单内容 -->
    <el-form ref="submitRef"
          class="rule-form"
          :model="submitData"
          :rules="rules"
          label-width="80px">
      <el-row>
        <!-- <template v-for="(item, index) in formConfig.formField">
          <el-col :key="index"
                  :span="item.colSpan">
            <el-form-item :prop="item.prop"
                          :label="item.label">
              <component :is="item.component"
                        :model.sync="submitData[item.prop]"
                        v-model="submitData[item.prop]"
                        :width="item.width || '100%'"
                        :disabled="item.disabled"
                        :type="item.type">
              </component>
            </el-form-item>
          </el-col>
        </template> -->
        <el-col :span="12">
          <el-form-item label="标题" prop="csTitle">
            <el-input v-model="submitData.csTitle" style="width: 100%"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="来源" prop="csSource">
            <el-input v-model="submitData.csSource" style="width: 100%"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="类别" prop="csTypeName">
            <el-select v-model="submitData.csTypeName" placeholder="请选择">
              <el-option label="introduction" value="introduction"></el-option>
              <el-option label="methods" value="methods"></el-option>
              <el-option label="statistic" value="statistic"></el-option>
              <el-option label="results" value="results"></el-option>
              <el-option label="discussion" value="discussion"></el-option>
              <el-option label="acknowledge及其它" value="acknowledge及其它"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="学科" prop="csSubjectName">
            <ms-sci-category :model.sync="submitData.csSubjectName"></ms-sci-category>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="关键词" prop="title">
            <MsTagSearch v-model="submitData.tagList" :notMul="false"></MsTagSearch>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="内容" prop="csContent">
            <ms-editor v-model="submitData.csContent"></ms-editor>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="开放" prop="csStatus">
            <el-switch v-model="submitData.csStatus"  :active-value="1" :inactive-value="0"></el-switch>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <!-- 提交按钮 -->
    <footer-tool-bar v-loading="buttonLoading">
      <template slot="tool-content">
        <el-button type="primary" @click="info_operation('save')">保存</el-button>
        <el-button type="info" @click="info_operation('back')">返回</el-button>
      </template>
    </footer-tool-bar>
  </section>
</template>

<script>
import FooterToolBar from '@/components/ToolBar/footer-tool-bar'
import MsTagSearch from '@/components/MsCommon/ms-tag-search'
import MsEditor from '@/components/MsEditor'
import msSciCategory from '@/components/MsCommon/ms-sci-category'
import { mapGetters } from "vuex";
export default {
  name: "sci-operation",
	data () {
		return {
      buttonLoading: false,
      getLoading: false,
      dataId: this.$route.query.id ? this.$route.query.id : 0,
      submitData: { 
        csTitle: "",
        csSource: "",
        csTypeId: 0,
        csTypeName: "",
        csSubjectName: "",
        csContent: "",
        csStatus: 0,
      },
      rules: {
        csTitle: [
          { required: true, message: "请填写标题", trigger: 'blur' }
        ],
        csContent: [
          { required: true, message: "请填写内容", trigger: 'change' }
        ]
      }
		}
  },
  components: {
    FooterToolBar,
    MsTagSearch,
    MsEditor,
    msSciCategory
  },
  computed: {
    ...mapGetters(["info"])
  },
  created() {
    this.init()
  },
  methods: {
    init() {
      this.dialog = false
      let id = this.dataId
      if(id !== 0) {
        this.getLoading = true;
        this.submitData.id = id;
        this.api.getClassicSentencesById({id: id}).then( response => {
          this.getLoading = false;
          if(response.status === 200) {
            let res = response.data
            this.submitData = {
              ...this.submitData,
              ...res
            }
          } else {
            this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
          }
        }).catch(() => {
          this.getLoading = false;
        })
      } 
    },
    info_operation(val) {
      switch (val) {
        case 'save': 
          this.$refs.submitRef.validate( valid => {
            if(valid) {
              this.dataId ? this.updateOperation() : this.createOperration()
            }
          })
          break;
        case 'back':
          this.$router.back();
          break;
        default: break;
      }
    },
    createOperration() {
      this.buttonLoading = true;
      let params = {
        userId: this.info.userId,
        username: this.info.userName,
        ...this.submitData,
        csContent: this.PUBLIC_Methods.excapeHtml(this.submitData.csContent),
      }
      this.api.addClassicSentences(params).then(response => {
        this.buttonLoading = false
        if(response.status === 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
          this.$router.back()
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
      }).catch(() => this.buttonLoading = false)
    },
    updateOperation() {
      this.buttonLoading = true;
      let params = {
        userId: this.info.userId,
        username: this.info.userName,
        ...this.submitData,
        csContent: this.PUBLIC_Methods.excapeHtml(this.submitData.csContent),
      }
      this.api.updateClassicSentences(params).then(response => {
        this.buttonLoading = false
        if(response.status === 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
          this.$router.back()
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
      }).catch(() => this.buttonLoading = false)
    }
  }
}
</script>
