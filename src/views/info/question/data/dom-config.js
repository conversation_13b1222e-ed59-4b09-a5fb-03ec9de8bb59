const domConfig = {
  listSearch: [
    {
      label: '题干',
      placeholder: '请输入',
      model: 'title',
      component: 'ms-input'
    },
    {
      label: '创建人',
      placeholder: '请选择',
      model: 'createdBy',
      component: 'ms-createby-search'
    },
    {
      label: '发布时间',
      placeholder: '请选择时间段',
      model: 'createTime',
      component: 'ms-picker',
      type: 'daterange',
      width: '240px'
    },
    {
      label: '分类',
      placeholder: '请选择',
      model: 'categoryId',
      level: 1,
      component: 'ms-category-cascader'
    }
  ],
  tableHeader: [
    { label: 'ID', property: 'id', sortable: true, width: 80 },
    { label: '题干', property: 'title', width: 250 },
    { label: '题型', property: 'type', width: 100 },
    { label: '难度', property: 'difficulty', width: 100 },
    { label: '创建人', property: 'createdName' },
    { label: '发布时间', property: 'publishTime', width: 130, sortable: true },
    { label: '创建时间', property: 'createdTime', width: 130, sortable: true },
    { label: '状态', property: 'status' }
  ],
  tableButtons: [
    {
      label: '编辑',
      icon: '',
      role: '',
      operation: 'edit',
      component: 'video-manage-series',
      way: 'page',
      type: 'primary',
      path: 'question-operation',
      params: ['id']
    },
    {
      label: '',
      icon: '',
      role: '',
      operation: 'editStatus',
      component: 'msQuestionOperation',
      way: 'dialog',
      field: 'status',
      rule: {
        1: { label: '去审', type: '', operation: 'toreview' },
        0: { label: '审核', type: 'success', operation: 'approval' }
      }
    },
    {
      label: '删除',
      icon: '',
      role: '',
      operation: 'delete',
      component: 'msQuestionOperation',
      way: 'dialog',
      type: 'danger'
    }
  ],
  soltButtons: [
    {
      label: '添加题库',
      type: 'primary',
      icon: 'el-icon-plus',
      operation: 'created',
      component: 'video-manage-series',
      way: 'page',
      path: 'question-operation',
      params: ['id']
    },
    {
      label: '批量审核',
      type: 'primary',
      operation: 'approval',
      component: 'msQuestionOperation',
      way: 'batch'
    },
    {
      label: '批量去审',
      type: 'primary',
      operation: 'toreview',
      component: 'msQuestionOperation',
      way: 'batch'
    },
    {
      title: '批量添加分类',
      label: '批量添加分类',
      type: 'success',
      operation: 'add',
      component: 'msQuestionCategory',
      way: 'batch',
      identify: 'batch_add_category',
      width: '70%'
    },
    {
      title: '批量移除分类',
      label: '批量移除分类',
      type: 'success',
      operation: 'delete',
      component: 'msQuestionCategory',
      way: 'batch',
      identify: 'batch_delete_category',
      width: '70%'
    },
    {
      label: '批量删除',
      type: 'info',
      icon: 'el-icon-close',
      operation: 'delete',
      component: 'msQuestionOperation',
      way: 'batch'
    }
  ]
}

export default domConfig;
