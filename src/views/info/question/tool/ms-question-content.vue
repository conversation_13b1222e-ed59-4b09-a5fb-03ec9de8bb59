<template>
  <section>
    <el-form :model="submitData"
             ref="submitRef"
             class="rule-form info-form"
             :rules="rules"
             label-width="70px">
      <el-row :gutter="20"> 
        <el-col :span="16">
          <el-row>
            <el-col :span="24">
              <el-form-item label="题型" prop="type">
                <el-select v-model="submitData.type" style="width: 100%" @change="changeType">
                  <el-option :value="1" label="单选题"></el-option>
                  <el-option :value="2" label="多选题"></el-option>
                  <el-option :value="3" label="主观题"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="题干" prop="title">
                <ms-editor v-model="submitData.title" :height="280" style="width: 100%"></ms-editor>
              </el-form-item>
            </el-col>
            <template v-if="[1,2].includes(submitData.type)">
               <el-col :span="24">
                <el-form-item label="选项内容" prop="options">
                  <el-radio-group v-model="submitData.radioOptions" v-if="submitData.type==1" style="width:100%">
                    <div v-for="(item,index) in submitData.options" :key="index" style="margin-bottom:10px">
                      <span style="font-size:14px;">{{sort[index]}}、</span>
                      <el-radio :label="index" class="width80">
                        <el-input v-model="item.content"></el-input>
                      </el-radio>
                    </div>
                  </el-radio-group>
                  <el-checkbox-group v-model="submitData.checkboxOptions" v-if="submitData.type==2" style="width:100%">
                    <div v-for="(item,index) in submitData.options" :key="index" style="margin-bottom:10px">
                      <span style="font-size:14px;">{{sort[index]}}、</span>
                      <el-checkbox :label="index" class="width80">
                        <el-input v-model="item.content"></el-input>
                      </el-checkbox>
                    </div>
                  </el-checkbox-group>
                </el-form-item>
                <el-button @click="addOptions" class="addBtn">添加选项</el-button>
              </el-col>
            </template>
            <el-col :span="24">
              <el-form-item label="解析" prop="analysis">
                <ms-editor v-model="submitData.analysis" :height="280" style="width: 100%"></ms-editor>
              </el-form-item>
            </el-col>
          </el-row>
        </el-col>
        <el-col :span="8" class="info-form-right">
          <el-row>
            <el-col :span="24">
              <el-form-item label="题目难度" prop="difficulty">
                <el-select v-model="submitData.difficulty" style="width: 100%">
                  <el-option :value="1" label="简单"></el-option>
                  <el-option :value="2" label="中等"></el-option>
                  <el-option :value="3" label="困难"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- <el-col :span="24">
              <el-form-item label="题目分类" prop="category">
                <ms-category-cascader :model.sync="submitData.category" :level='1' :multiple="true"></ms-category-cascader>
              </el-form-item>
            </el-col> -->
            <el-col :span="24">
              <el-form-item label="关键词" prop="tag">
                <MsTagSearch v-model="submitData.tag" :notMul="false"></MsTagSearch>
              </el-form-item>
            </el-col>
          </el-row>
        </el-col>
      </el-row>
    </el-form>
  </section>
</template>

<script>
import MsTagSearch from '@/components/MsCommon/ms-tag-search'
import MsEditor from '@/components/MsEditor'
import { mapGetters } from "vuex";
export default {
	name: "ms-video-series-content",
	data () {
		return {
      testLoading: false,
      rules: {
        type: [
          { required: true, message: "请选择", trigger: 'blur' }
        ],
        title: [
          { required: true, message: "请输入", trigger: 'blur' }
        ],
        options: [
          { required: true, message: "请输入", trigger: 'blur' }
        ],
        analysis: [
          { required: true, message: "请输入", trigger: 'blur' }
        ],
        difficulty: [
          { required: true, message: "请选择", trigger: 'blur' }
        ],
        category: [
          { required: true, message: "请选择", trigger: 'blur' }
        ],
        tag: [
          { required: true, message: "请输入", trigger: 'blur' }
        ],
      },
      formatDisabled: false,
      editorDisplay: false,
      summaryKeyUp: false,
      sort:['A','B','C','D','E','F','G','H','I','J','K','L','M','N','O','P','Q','R','S','T','U','V','W','X','Y','Z']
		}
  },
  components: {
    MsTagSearch,
    MsEditor
  },
  computed: {
    ...mapGetters(["submitData", "info", "localCode"])
  },
  watch: {
    'submitData.id': function (val) {
      if (val) {
        this.formatDisabled = true
        this.editorDisplay = true
      } 
    },
  },
  created() {
    console.log(this.$route.query.operation)
    if(this.$route.query.operation == 'created') {
      this.submitData.difficulty = 2
    }
  },
	methods: {
    changeType() {
      this.submitData.radioOptions = ''
      this.submitData.checkboxOptions = []
      this.submitData.options = [{
        content: "",
        isSelected: false
      }]
    },
    addOptions() {
      if(this.submitData.options.length > 9) {
        this.PUBLIC_Methods.apiNotify('最多添加10个选项！')
      }else {
        this.submitData.options.push({
          content: "",
          isSelected: false
        })
      }
    },
    getNodeData(val) {
      this.submitData.videoId = val.id
      this.submitData.title = val.title
      this.submitData.lecturerName = val.lecturerName
    },
    // 数据校验
    validateData(callback) {
      let params = {
        ...this.submitData,
        userId: this.info.userId,
        username: this.info.userName
      }
      this.$refs["submitRef"].validate( valid => {
        if (valid) {
          callback(params)
        }
      })
    }
	}
}
</script>

<style lang="scss" scoped>
.info-form{
  /deep/ .el-checkbox {
    margin-right: 0px;
  }
  /deep/ .el-checkbox.width80 {
    width: 80%;
    margin-right: 30px;
  }
  /deep/ .el-checkbox.width80 .el-checkbox__label {
    width: 100%;
  }
  /deep/ .el-radio.width80 {
    width: 80%;
  }
  /deep/ .el-radio.width80 .el-radio__label {
    width: 100%;
  }
  &-right {
    /deep/ .el-form-item {
      margin-bottom: 16px;
    }
  }
  .expand {
    .label {
      color: #829FFF; 
      font-size: 12px;
      width: 40px;
      display: inline-block;
      text-align: right;
    }
    /deep/ .el-form-item {
      padding: 4px 0;
      margin-bottom: 14px;
      margin-top: -4px;
      background-color: #E5E6E9;
      border-radius: 4px;
    }
  }
  /deep/ .addBtn {
    margin-bottom: 20px;
  }
}
</style>
