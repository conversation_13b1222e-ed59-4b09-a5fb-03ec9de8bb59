<template>
  <section class="form-tab" v-loading="getLoading">
    <el-tabs v-model="activeName">
      <el-tab-pane label="题库操作信息" name="content">
        <ms-question-content ref="contentTemp" @handle-click="handle_click"></ms-question-content>
      </el-tab-pane>
    </el-tabs>

    <!-- 弹出层 -->
    <el-dialog :visible.sync="dialog" 
               closeable 
               :show-close="false"
               :close-on-click-modal="false"
               width="50%"
               :title="dialogTitle">
      <component :is="dialogComponent" 
                   :model="dialogInfo" 
                   :operation="dialogOperation" 
                   :isNeedApi="false"
                   @close="dialog = !dialog" 
                   @up-date="init" 
                   v-if="dialog"></component>
    </el-dialog>
    <!-- 提交按钮 -->
    <footer-tool-bar v-loading="buttonLoading">
      <template slot="tool-content">
        <!-- <el-button plain>提交</el-button> -->
        <el-button plain v-show="videoId" @click="info_operation('approval')">{{submitData.status === 1 ? '去审' : '审核'}}</el-button>
        <el-button type="primary" @click="info_operation('save')">保存</el-button>
        <el-button type="info" @click="info_operation('back')">返回</el-button>
      </template>
    </footer-tool-bar>
  </section>
</template>

<script>
import msQuestionContent from './tool/ms-question-content'
import questionMixin from "./question-mixin"
import FooterToolBar from '@/components/ToolBar/footer-tool-bar'
import { mapGetters } from "vuex";
export default {
  name: "video-special-operation",
  mixins: [questionMixin],
	data () {
		return {
      activeName: 'content',
      videoId: this.$route.query.id ? this.$route.query.id : 0,
      buttonLoading: false,
      getLoading: false,

      submitData: { // => userId、username、integral、waterMark、formatted 、expandRead (保存添加属性)
        userId: '', // => 用户Id
        username: '', // => 用户名称 (string)
        title: '', // => 标题 (string)
        options: [], // =>  答案选项
        type: '', // => 题目类型
        difficulty: '', // => 题目难度
        analysis: '', // => 解析
        tag: [], // => 标签集合
        category: [], // => 专题分类集合
        radioOptions: '', // => 单选答案index
        checkboxOptions: [], // => 多选答案index
        status: 0
      },

      dialogComponent: '',
      dialog: false,
      dialogTitle: '',
      dialogOperation: '',
      dialogInfo: {},
      sort:['A','B','C','D','E','F','G','H','I','J','K','L','M','N','O','P','Q','R','S','T','U','V','W','X','Y','Z']
		}
  },
  computed: {
    ...mapGetters(["info"])
  },
  created() {
    this.init()
  },
  components: {
    msQuestionContent,
    FooterToolBar
  },
  methods: {
    init() {
      let id = this.videoId
      this.dialog = false;
      this.$store.dispatch('SetSubmitData', this.submitData)
      if(id !== 0) {
        this.getLoading = true;
        this.submitData.id = id;
        let params = {
          id: id
        }
        this.api.exercisesDetails(params).then( response => {
          this.getLoading = false;
          if(response.status === 200) {
            let res = response.data
            if(res.options && res.options.length > 0){
              if(res.type == 1){
                res.options.forEach((item,index)=>{
                  if(item.isSelected){
                    this.submitData.radioOptions = index
                  } 
                })
              }
              if(res.type == 2) {
                res.options.forEach((item,index)=>{
                  if(item.isSelected){
                    this.submitData.checkboxOptions.push(index)
                  } 
                })
              }
            }else {
              this.submitData.radioOptions = ''
              this.submitData.checkboxOptions = []
            }
            this.submitData = {
              ...this.submitData,
              ...res
            }
          } else {
            this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
          }
          this.$store.dispatch('SetSubmitData', this.submitData)
        }).catch(() => {
          this.getLoading = false;
          this.$store.dispatch('ClearSubmitData')
        })
      }
    },
    info_operation(val) {
      switch (val) {
        case 'save': 
          this.$refs['contentTemp'].validateData(this.videoId ? this.updateData: this.createData)
          break;
        case 'back':
          this.$router.back();
          break;
        case 'approval':
          var params = {
            way: 'dialog',
            operation: this.submitData.status === 1 ? 'toreview' : 'approval',
            component: 'msQuestionContent',
            data: this.submitData
          }
          this.handle_click(params)
          break;
        default: break;
      }
    },
    createData(params) {
      for(let index=0;index<params.options.length;index++) {
        if(params.options[index].content=='' && [1,2].includes(params.type)) {
          this.PUBLIC_Methods.apiNotify('请填写选项内容')
          return false
        }
        if(params.type == 1) {
          params.options[index].isSelected = false
          if(params.radioOptions!=='') {
            params.options[params.radioOptions].isSelected = true
            params.options.map((n,i)=>{
              n.option = this.sort[i]
            })
          }else {
            this.PUBLIC_Methods.apiNotify('请选择正确选项')
            return false
          }
        }else if (params.type == 2) {
          params.options[index].isSelected = false
          if(params.checkboxOptions.length > 0) {
             params.checkboxOptions.map(n=>{
              params.options[n].isSelected = true
            })
            params.options.map((n,i)=>{
              n.option = this.sort[i]
            })
          }else {
            this.PUBLIC_Methods.apiNotify('请选择正确选项')
            return false
          }
        }else {
          params.options = []
        }
      }
      this.buttonLoading = true;
      let paramsData = {
        ...params
      }
      this.api.addExercises(paramsData).then(response => {
        this.buttonLoading = false
        if(response.status === 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
          this.$router.back()
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
      }).catch(() => this.buttonLoading = false)
    },
    updateData(params) {
      if(params.options && params.options.length > 0) {
        for(let index=0;index<params.options.length;index++) {
          if(params.options[index].content=='' && [1,2].includes(params.type)) {
            this.PUBLIC_Methods.apiNotify('请填写选项内容')
            return false
          }
          if(params.type == 1) {
            params.options[index].isSelected = false
            if(params.radioOptions!=='') {
              params.options[params.radioOptions].isSelected = true
              params.options.map((n,i)=>{
                n.option = this.sort[i]
              })
            }else {
              this.PUBLIC_Methods.apiNotify('请选择正确选项')
              return false
            }
          }else if (params.type == 2) {
            params.options[index].isSelected = false
            if(params.checkboxOptions.length > 0) {
              params.checkboxOptions.map(n=>{
                params.options[n].isSelected = true
              })
              params.options.map((n,i)=>{
                n.option = this.sort[i]
              })
            }else {
              this.PUBLIC_Methods.apiNotify('请选择正确选项')
              return false
            }
          }else {
            params.options = []
          }
        }
      }else {
        params.options = []
      }
      
      this.buttonLoading = true;
      let paramsData = {
        ...params
      }
      this.api.editorExercises(paramsData).then(response => {
        this.buttonLoading = false
        if(response.status === 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
          this.$router.back()
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
      }).catch(() => this.buttonLoading = false)
    },
    handle_click(val) {
      switch (val.way) {
        case 'dialog': 
          this.dialog = true;
          this.dialogInfo = val.data
          this.dialogOperation = val.operation;
          this.dialogComponent = val.component;
          this.dialogTitle = val.title;
          break;
        default: break;
      }
    }
  }
}
</script>
