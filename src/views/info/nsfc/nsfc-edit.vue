<template>
  <div class="container">
    <el-form
      :rules="rules"
      label-width="85px"
      class="rule-form"
      :model="submitData"
      v-loading="loading"
       ref="submitRef"
    >
      <el-row>
        <el-col :span="11">
          <el-form-item label="基金名称" prop="projectName">
            <el-input
              v-model="submitData.projectName"
              placeholder="请输入"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="11">
          <el-form-item label="项目类别">
            <el-select
              laceholder="请选择活动区域"
              class="down"
              v-model="submitData.projectClassname"
              v-el-select-loadmore="loadmore"
              :clearable='true'
            >
              <el-option v-for='(item ,index) in codeOptions' :key='index' :label="item.projectName" :value="item.projectName"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="11">
          <el-form-item label="基金编号" prop="projectNo">
            <el-input
              v-model="submitData.projectNo"
              placeholder="请输入"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="11">
          <el-form-item label="资助金额" prop="cost" class="mon">
            <el-input
              type="number" 
              v-model="submitData.cost"
              placeholder="请输入"
            ></el-input>
            <span>万元</span>
          </el-form-item>
        </el-col>
         <el-col :span="11">
          <el-form-item label="学科类别" prop="department" class="dep-content">
          <el-select placeholder="请选择学部" v-model="submitData.classname"  @change='change(0)' :clearable='true'>
            <el-option v-for="(item,index) in subjectData" :key="index" :label="item.subjectName" :value="item.subjectName">{{"【"+ item.code+ '】' + item.subjectName}}</el-option>
          </el-select>
          <el-select placeholder="一级学科" v-model="submitData.classname1" @change='change(1)' :clearable='true'>
            <el-option v-for="(son1Item,son1Index) in son1LevelData"  :key="son1Index" :label="son1Item.subjectName" :value="son1Item.subjectName"></el-option>
          </el-select>
           <el-select placeholder="二级学科" v-model="submitData.classname2" @change='change(2)' :clearable='true'>
            <el-option v-for="(son2Item,son2Index) in son2LevelData" :key="son2Index" :label="son2Item.subjectName" :value="son2Item.subjectName"></el-option>
          </el-select>
          <el-select placeholder="三级学科" v-model="submitData.classname3" @change='change(3)' :clearable='true'>
            <el-option v-for="(son3Item,son3Index) in son3LevelData" :key="son3Index" :label="son3Item.subjectName" :value="son3Item.subjectName"></el-option>
          </el-select> 
          </el-form-item>
        </el-col>
        <el-col :span="11">
          <el-form-item label="负责人" prop="projectPi">
            <el-input v-model="submitData.projectPi" placeholder="请输入"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="11">
          <el-form-item label="中标单位" prop="projectUni">
            <el-input v-model="submitData.projectUni" placeholder="请输入"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="11">
          <el-form-item label="批准时间" prop="year">
            <el-date-picker
              type="year"
              placeholder="年份"
              format="yyyy"
              value-format="yyyy"
              v-model="submitData.year"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="11">
          <el-form-item label="项目起止时间" prop="timeRange">
            <el-date-picker
              value-format="yyyyMM"
              v-model="submitData.timeRange"
              type="monthrange" 
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="22">
          <el-form-item label="关键词" prop="keywordList">
            <el-tag  v-for="(tag,index) in submitData.keywordList"  :key="index" closable :disable-transitions="false" @close="handleClose(tag)">
            {{tag}}
            </el-tag>
            <el-input class="input-new-tag"  v-model.trim="inputValue" ref="saveTagInput"  @keyup.enter.native="handleInputConfirm" @blur="handleInputConfirm" placeholder="请输入">
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="22">
          <el-form-item label="结题摘要" prop="summary">
            <el-input
              v-model="submitData.summary"
              placeholder="请输入"
              type="textarea"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <footer-tool-bar>
      <template slot="tool-content">
        <el-button type="primary" @click="submitForm"  :loading="loadingBut">保存</el-button>
        <el-button type="info" @click="close">返回</el-button>
      </template>
    </footer-tool-bar>
  </div>
</template>
<script>
import FooterToolBar from "@/components/ToolBar/footer-tool-bar";
export default {
  name: "nsfc-edit",
  components: {
    FooterToolBar,
  },
  data() {
    return {
      loading: false,
      loadingBut: false,
      submitData: {
      projectName: '',
      projectClassname: '',
      projectNo:'',
      cost: null,
      classname:'',
      classname1:'',
      classname2:'',
      classname3:'',
      projectid:'',
      projectUni:'',
      projectPi:'',
      code:'',
      keywordList: [],
      summary:'',
      timeRange:[],
      year: '',
      },
      rules: {
        projectName: [
          { required: true, message: "请输入基金名称", trigger: "blur" },
        ]
      },
      projectid: "",
      subjectData: [{id:''}],
      son1LevelData: [],
      son2LevelData: [],
      son3LevelData: [],
      pageIndex: 1,
      codeOptions: [],
      normal:false,
      inputValue: ''
    };
  },
 async created() {
    this.projectid = this.$route.query.projectid;
    this.$route.meta.title = this.projectid ? "基金管理-编辑基金" : "基金管理-添加基金";
    this.$router.replace({
      query: {
        temp: Date.now(),
        projectid: this.projectid,
      },
    });
    await this.getSubject()
     if (this.projectid) {
      this.init();
    }
  },
    mounted() {
   this.getProjectType()
  },
  directives: {
    'el-select-loadmore': {
      bind (el, binding) {
        const SELECTWRAP_DOM = el.querySelector(
          '.el-select-dropdown .el-select-dropdown__wrap'
        )
        SELECTWRAP_DOM.addEventListener('scroll', function () {
          const condition =
            this.scrollHeight - this.scrollTop <= this.clientHeight
          if (condition) {
            binding.value()
          }
        })
      }
    }
  },
  methods: {
    init() {
      if (this.projectid) {
        this.loading = true;
        const params = {
          projectid: this.projectid,
        };
        this.api.nsfcGetDetail(params).then((resp) => {
            this.loading = false;
            if (resp.status === 200) {
              let obj = resp.data;     
              this.submitData = {
                ...this.submitData,
                ...obj,
                year: obj.year? obj.year.toString(): '',
                timeRange:obj.begindate && obj.enddate? [obj.begindate.toString(), obj.enddate.toString()]: [],
              };
            this.querySubjectData(resp.data.classname)
            }else {
              this.loading = false;
              this.PUBLIC_Methods.apiNotify(resp.message || '请求出错', 'warning')
            }
          })
          .catch(() => {
            this.loading = false;
          });
      }
    },
 async getSubject() {
    let res = await this.api.getNsfcSubjectRelationInfo()
        if (res.status === 200) {
          this.subjectData = res.data;
        }
    },
    querySubjectData(val) {
      for(let i = 0; i < this.subjectData.length;i++) {
        if(this.subjectData[i].subjectName === val) {
          this.api.getNsfcSubjectRelationInfo({parentId:this.subjectData[i].id}).then(res=> {
            if(res.status == 200) this.son1LevelData = res.data
             for(let j = 0; j < this.son1LevelData.length;j++) {
               if(this.son1LevelData[j].subjectName === this.submitData.classname1) {
                 this.api.getNsfcSubjectRelationInfo({parentId:this.son1LevelData[j].id}).then(resp => {
                  if(resp.status === 200)  this.son2LevelData = resp.data
                  for(let k = 0;k < this.son2LevelData.length; k++) {
                    if(this.son2LevelData[k].subjectName === this.submitData.classname2) {
                      this.api.getNsfcSubjectRelationInfo({parentId:this.son2LevelData[k].id}).then(response=> {
                          if(response.status === 200)  this.son3LevelData = response.data
                      })
                    }
                  }
                 })
               }
             }
          })
        }
      }
    },
    change(num) {
      if(num === 0) {
       this.submitData.classname1 = ''
       this.submitData.classname2 = ''
       this.submitData.classname3 = ''
       this.son1LevelData = []
       this.son2LevelData = []
       this.son3LevelData = []
       this.subjectData.forEach(item => {
        if(item.subjectName === this.submitData.classname) {
          this.submitData.code = item.code
          this.getSubjectChild(item.id,0)
        }
      })
      } 
      if(num === 1){
        this.son1LevelData.forEach(item => {
        if(item.subjectName === this.submitData.classname1) {
          this.submitData.code = item.code
          this.getSubjectChild(item.id,1)
        }
      })
      }
      if(num === 2) {
         this.son2LevelData.forEach(item => {
        if(item.subjectName === this.submitData.classname2) {
          this.submitData.code = item.code
          this.getSubjectChild(item.id,2)
        }
      })
      }
      if(num=== 3) {
         this.son3LevelData.forEach(item=> {
          if(item.subjectName === this.submitData.classname3) {
            this.submitData.code = item.code
          }
         })
      }
    },
  async  getSubjectChild(id,num) {
      let params = {
        parentId: id
      };
    let res =  await this.api.getNsfcSubjectRelationInfo(params)
    if(res.status === 200) {
         if(num === 0) {
          this.son1LevelData = res.data
        } 
        if(num === 1) {
          this.son2LevelData = res.data
        }
         if(num === 2) {
          this.son3LevelData = res.data
        }
    }
    },
    loadmore() {
       this.getProjectType()
    },
    getProjectType() {
      if(this.normal) return
      this.codeLoading = true
      let params = {
        pageIndex: this.pageIndex,
        pageSize: 20
      }
      this.api.getNsfcProjectCategoryPage( params).then(res=> {
        if (res.status === 200) {
          this.pageIndex++;
          this.codeOptions = [...this.codeOptions,...res.data]
          if(this.codeOptions.length === res.totalSize) this.normal = true
        } 
      })
    },
    submitForm() {
       this.$refs.submitRef.validate(valid => {
        if(valid) {
           if (this.submitData.timeRange && this.submitData.timeRange.length > 0) {
                        this.submitData.begindate = this.submitData.timeRange[0]
                        this.submitData.enddate = this.submitData.timeRange[1]
           }
         this.loadingBut = true
         this.projectid ? this.updateNsfc() : this.createNsfc()
        }
       })
    },
    close() {
      this.$router.back();
    },
    createNsfc() {
       let params = {
                ...this.submitData,
                userId: this.$store.getters.info.userId,
                username: this.$store.getters.info.userName
            }
           this.api.nsfcCreate(params).then(resp => {
             if(resp.status === 200) {
                    this.PUBLIC_Methods.apiNotify(resp.message || '请求成功', 'success')
                } else {
                    this.PUBLIC_Methods.apiNotify(resp.message || '请求出错', 'warning')
                }
                this.loadingBut = false
                setTimeout(()=> {
                  this.$router.back();
                },200)
            }).catch(() => {
               this.loadingBut = false
            })
    },
     updateNsfc() {
       let params = {
                ...this.submitData,
                userId: this.$store.getters.info.userId,
                username: this.$store.getters.info.userName
            }
            this.api.nsfcEdit(params).then(resp => {
                if(resp.status === 200) {
                    this.PUBLIC_Methods.apiNotify(resp.message || '请求成功', 'success')
                } else {
                    this.PUBLIC_Methods.apiNotify(resp.message || '请求出错', 'warning')
                }
                this.loadingBut = false
                  setTimeout(()=> {
                  this.$router.back();
                },200)
            }).catch(() => {
               this.loadingBut = false
            }) 
     },
      handleClose(tag) {
        this.submitData.keywordList.splice(this.submitData.keywordList.indexOf(tag), 1);
      },
      handleInputConfirm() {
        if(this.submitData.keywordList === null) this.submitData.keywordList = []
        let inputValue = this.inputValue;
        if (inputValue) {
          this.submitData.keywordList.push(inputValue);
        }
        this.inputValue = '';
      }
  },
};
</script>
<style scoped lang="scss">
.down {
  width: 100%;
}
.el-date-picker {
  width: 100%;
}
/deep/.el-cascader--mini {
  width: 100%;
}
.el-date-editor.el-input,
.el-date-editor.el-input__inner {
  width: 100%;
}
/deep/ input::-webkit-outer-spin-button,
/deep/ input::-webkit-inner-spin-button {
  -webkit-appearance: none !important;
}
/deep/ input[type='number'] {
  -moz-appearance: textfield !important;
}
.mon /deep/.el-form-item__content {
  display: flex;
}
.mon span {
width: 34px;
margin-left: 10px;
font-size: 12px;
color: #606266;
}
.dep-content /deep/.el-form-item__content {
  display: flex;
}
 .el-tag {
    margin: 5px 10px;
  }
  .input-new-tag {
    width: 180px;
  }
</style>
