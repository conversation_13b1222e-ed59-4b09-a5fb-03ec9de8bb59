<template>
	<ms-table
		:currentPage="searchParams.pageIndex"
		:loading="loading"
		:operationButtons="domConfig.tableButtons"
		:pageSize="searchParams.pageSize"
		:scopeConfig="scopeConfig.show"
        :scopeHeader="scopeConfig.headerShow"
		:tableData="list"
		:tableHeader="domConfig.tableHeader"
		:total="total"
        :showSelection="true"
		@current-change="current_change"
		@operation-change="homeOperation"
		@size-change="size_change"
        @header-operation="header_operation"
        @handleSelectionChange="handleSelectionChange"
        class="table-svg"
	>
    <!-- 列表搜索去区域插槽 -->
		<template slot="ms-table-header">
			<div class="slot-search">
				<template v-for="(searchItem, key) in domConfig.listSearch">
					<component
						:index="searchItem.index || ''"
						:is="searchItem.component"
						:key="key"
                        :width="searchItem.width || '150px'"
                        :model.sync="searchParams[searchItem.model]"
                        :label="searchItem.label"
						:operation="searchItem.operation || ''"
						:options="searchItem.options || []"
						:placeholder="searchItem.placeholder || ''"
						:type="searchItem.type || ''"
                        :multiple="searchItem.multiple"
                        :disabled="searchItem.disabled"
                        :code="searchItem.code"
					></component>
				</template>
                <div class="inlineBlock">
                    <el-button @click="handleClick('query')" type="primary" plain icon="el-icon-search">查询</el-button>
                    <el-button @click="handleClick('reset')">重置</el-button>
                </div>
        
			</div>
			<div class="slot-button-article clearfix">
				<template v-for="(item, index) in domConfig.soltButtons">
					<el-button :key="index" 
                     :type="item.type" 
                     size="mini"
                     :icon="item.icon"
                     @click="homeOperation({operation: item})"
                     :disabled="item.roleDisabled"
                     plain>{{ item.label }}</el-button>
				</template>
			</div>
			<el-dialog :visible.sync="dialog" 
                 closeable 
                 show-close
                 :close-on-click-modal="false"
                 :width="dialogWidth"
                 :title="dialogTitle">
				<component :is="dialogComponent" 
                   :model="scopeInfo" 
                   :operation="dialogOperation" 
                   @close="dialog = !dialog" 
                   @up-date="init" 
                   v-if="dialog"></component>
			</el-dialog>
		</template>
	</ms-table>
</template>

<script>
import nsfcMixin from "./nsfc-mixin"
import tableMixins from "../../common/mixins/table"
export default {
    name: "ms-order-manage",
    mixins: [tableMixins,nsfcMixin],
    data () {
        return {}
    },
    methods: {
        apiInit (params) {
            let searchParams = {...params}
            this.api.nsfcPageList(searchParams).then(response => {
                this.loading = false
                this.total = response.totalSize || 0;
                this.list = response.data || []
                if (response.status !== 200) {
                    this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
                }
            }).catch(() => this.loading = false)
        },
         homeOperation(val) {
      console.log(val, "val");
      switch (val.operation.way) {
        case "batch":
          if (this.selectList.length > 0) {
            this.dialog = true;
            this.scopeInfo = this.selectList;
            this.dialogOperation = val.operation.operation;
            this.dialogComponent = val.operation.component;
            this.dialogTitle = val.operation.title;
            this.dialogWidth = val.operation.width || "40%";
          } else {
            this.$message.warning("请选择至少一条数据");
          }
          break;
        case "dialog":
          if (val.operation.label=== "编辑") {
            this.$router.push({
              path: "/nsfc-edit",
              query: {
                projectid: val.model.projectid,
              }
            });
          } else if(val.operation.label=== "手工添加") {
            this.$router.push({
              path: "/nsfc-edit",
              });
          }else {
            if (val.operation.position === "right") {
              this.r_dialog = true;
            } else {
              this.dialog = true;
            }
            this.scopeInfo = val.model ? val.model : {};
            this.dialogOperation = val.operation.operation;
            this.dialogComponent = val.operation.component;
            this.dialogTitle = val.operation.title;
            this.dialogWidth = val.operation.width || "40%";
          }
          break;
        case "page":
          var paramsObjs = {};
          if (val.operation.params) {
            val.operation.params.forEach((item) => {
              let keyName = item.keyName || item;
              let valName = item.valName || item;
              paramsObjs[keyName] = val.model ? val.model[valName] : "";
            });
          }
          this.$store.dispatch("SetActive", val.operation.path);
          if (val.operation.setVuex) {
            this.$store.dispatch("SetPageParams", { ...paramsObjs });
          }
          this.$router.push({
            path: val.operation.path,
            query: {
              operation: val.operation.operation,
              component: val.operation.component,
              ...paramsObjs,
            },
          });
          break;
        case "link":
          window.open(`${val.model[val.operation.pathKey]}`, "_blank");
          break;
        case "query":
          this.searchParams[val.operation.params] =
            val.model[val.operation.params];
          this.searchParams.pageIndex = 1;
          this.searchParams.pageSize = 20;
          this.init();
          break;
        case "preview":
          var statusVal = val.operation.field && val.model[val.operation.field];
          if (statusVal === 1 || val.operation.noCheck) {
            if (val.operation.paramsVal) {
              if (val.operation.query) {
                window.open(
                  `${val.operation.pageUrl}${
                    val.model[val.operation.paramsVal]
                  }${val.operation.query}`,
                  "_blank"
                );
              } else {
                window.open(
                  `${val.operation.pageUrl}${
                    val.model[val.operation.paramsVal]
                  }`,
                  "_blank"
                );
              }
            } else {
              if (window.location.href.includes("Bioon")) {
                window.open(
                  `${val.operation.pageUrl}${val.model.encodeId}.html`,
                  "_blank"
                );
              } else {
                window.open(
                  `${val.operation.pageUrl}${val.model.encodeId}`,
                  "_blank"
                );
              }
            }
          } else if (statusVal === 0 || statusVal === 2) {
            // 未审核
            if (!val.operation.previewName) {
              return this.$message({
                showClose: true,
                message: "该数据未审核",
                type: "warning",
              });
            }
            this.dialog = true;
            this.scopeInfo = val.model ? val.model : {};
            this.dialogComponent =
              val.operation.previewName || "MsPreviewDialog";
            this.dialogTitle = val.operation.title || "文章预览";
            this.dialogWidth = "60%";
          }
          break;
        case "activePreview":
          if (val.operation.paramsVal) {
            if (val.operation.query) {
              window.open(
                `${val.operation.pageUrl}${val.model[val.operation.paramsVal]}${
                  val.operation.query
                }`,
                "_blank"
              );
            } else {
              window.open(
                `${val.operation.pageUrl}${val.model[val.operation.paramsVal]}`,
                "_blank"
              );
            }
          } else {
            if (window.location.href.includes("Bioon")) {
              window.open(
                `${val.operation.pageUrl}${val.model.encodeId}.html`,
                "_blank"
              );
            } else {
              window.open(
                `${val.operation.pageUrl}${val.model.encodeId}`,
                "_blank"
              );
            }
          }
          break;
        default:
          this.operation_change_module ? this.operation_change_module(val) : "";
          break;
      }
    },
    }
}
</script>
