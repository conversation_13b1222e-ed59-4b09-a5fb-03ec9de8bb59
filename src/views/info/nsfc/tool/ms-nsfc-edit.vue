<template>
    <ms-operation-dialog v-loading="getLoading">
        <template slot="content">
        <el-form ref="submitRef"
                :model="submitData"
                :rules="rules"
                label-width="85px"
                class="rule-form">
            <el-row>
                <el-col :span="12">
                    <el-form-item label="基金名称" prop="projectName">
                        <el-input v-model="submitData.projectName" placeholder="请输入"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="code" prop="code">
                        <el-input v-model="submitData.code" placeholder="请输入" style="width: 150px"></el-input> &nbsp;&nbsp;一般与领域有关
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="基金编号" prop="projectNo">
                        <el-input v-model="submitData.projectNo" placeholder="请输入"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="基金类别" prop="projectClassname">
                        <el-input v-model="submitData.projectClassname" placeholder="请输入"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="学科类别" prop="classname">
                        <el-input v-model="submitData.classname" placeholder="请输入"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="中标人" prop="projectPi">
                        <el-input v-model="submitData.projectPi" placeholder="请输入"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="中标单位" prop="projectUni">
                        <el-input v-model="submitData.projectUni" placeholder="请输入"></el-input>
                    </el-form-item>
                </el-col> 
                <el-col :span="12">
                    <el-form-item label="项目金额" prop="cost">
                        <el-input-number v-model="submitData.cost" :min="0" placeholder="请输入" style="width: 150px"></el-input-number> &nbsp;&nbsp;万元
                    </el-form-item>
                </el-col>    
                <el-col :span="12">
                    <el-form-item label="时间" prop="cost">
                        <el-date-picker value-format="yyyyMM" v-model="submitData.timeRange" type="monthrange" range-separator="~" start-placeholder="开始月份" end-placeholder="结束月份" style="width: 100%"></el-date-picker>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="年份" prop="year">
                        <el-input v-model="submitData.year" placeholder="请输入"></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        </template>
        <template slot="footer">
        <el-button @click="submitForm"
                    :loading="loading"
                    size="mini"
                    type="primary">确 定</el-button>
        <el-button @click="$emit('close')"
                    size="mini">取 消</el-button>
        </template>
    </ms-operation-dialog>
</template>

<script> 
export default {
	name: "ms-nsfc-edit",
	data () {
		return {
            loading: false,
            submitData: {
                "userId": 0,
                "username": "",
                "projectName": "",
                "code": "",
                "projectNo": "",
                "projectClassname": "",
                "classname": "",
                "projectPi": "",
                "projectUni": "",
                "cost": 0,
                "begindate": "",
                "enddate": "",
                "year": "",
                "timeRange": []
            },
            rules: {
                projectName: [
                    { required: true, message: "请输入基金名称", trigger: 'blur' }
                ],
                expressType: [
                    { required: true, message: "请选择快递类型", trigger: 'blur' }
                ]
            },
        }
    },
	props: {
		model: Object,
		operation: String
    },
    created() {
        this.init()
    },
	methods: {
        init() {
            let id = this.model.projectid
            if(id) {
                this.getLoading = true;
                this.api.nsfcGetDetail({projectid: id}).then( response => {
                    this.getLoading = false;
                    if(response.status === 200) {
                        let res = response.data
                        this.submitData = {
                            ...this.submitData,
                            ...res,
                            timeRange: res.begindate && res.enddate ? [res.begindate.toString(), res.enddate.toString()] : []
                        }
                    } else {
                        this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
                    }
                }).catch(() => {
                    this.getLoading = false;
                })
            } 
        },
		submitForm() {
            this.$refs.submitRef.validate( valid => {
                if(valid) {
                    if (this.submitData.timeRange && this.submitData.timeRange.length > 0) {
                        this.submitData.begindate = this.submitData.timeRange[0]
                        this.submitData.enddate = this.submitData.timeRange[1]
                    }
                    this.loading = true;
                    this.model.projectid ? this.updateNsfc() : this.createNsfc()
                }
            })
        },
        createNsfc() {
            let params = {
                ...this.submitData,
                userId: this.$store.getters.info.userId,
                username: this.$store.getters.info.userName
            }
            this.api.nsfcCreate(params).then( response => {
                if(response.status === 200) {
                    this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
                } else {
                    this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
                }
                this.$emit('up-date')
                this.loading = false
            }).catch( () => {
                this.loading = false;
                this.$emit('close')
            })
        },
        updateNsfc() {
            let params = {
                ...this.submitData,
                userId: this.$store.getters.info.userId,
                username: this.$store.getters.info.userName
            }
            this.api.nsfcEdit(params).then( response => {
                if(response.status === 200) {
                    this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
                } else {
                    this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
                }
                this.$emit('up-date')
                this.loading = false
            }).catch( () => {
                this.loading = false;
                this.$emit('close')
            })
        }
	}
}
</script>
