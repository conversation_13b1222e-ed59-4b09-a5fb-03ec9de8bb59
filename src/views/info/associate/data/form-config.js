import {ms_rule_url} from "@/utils/form-rule.js";
const formConfig = {
  formField: [
    {
      label: '协会名称',
      prop: 'name',
      colSpan: 24,
      component: 'ms-input'
    },
    {
      label: 'URL',
      prop: 'website',
      colSpan: 24,
      component: 'ms-input'
    },
    {
      label: '分类',
      prop: 'classId',
      colSpan: 24,
      component: 'ms-category-cascader'
    },
    {
      label: '简介',
      prop: 'intro',
      colSpan: 24,
      component: 'ms-input',
      type: 'textarea',
      maxRows: 6
    }
  ],
  rule: {
    name: [
      { required: true, message: "请输入协会名称", trigger: 'blur' }
    ],
    website: [
      { validator: ms_rule_url, trigger: 'blur' }
    ],
    intro: [
      { required: true, message: "请输入简介", trigger: 'blur' }
    ],
  }
}

export default formConfig;
