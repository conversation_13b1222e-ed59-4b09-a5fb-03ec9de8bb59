const domConfig = {
  listSearch: [
    {
      label: '协会名称',
      placeholder: '请输入',
      model: 'name',
      component: 'ms-input'
    },
    {
      label: '分类',
      placeholder: '请输入',
      model: 'classId',
      component: 'ms-category-cascader'
    }
  ],
  tableHeader: [
    { label: 'ID', property: 'id', sortable: true, width: 80 },
    { label: '协会名称', property: 'name', width: 150 },
    { label: 'URL', property: 'website', width: 200  },
    { label: '分类', property: 'className' }
  ],
  tableButtons: [
    {
      label: '编辑',
      icon: '',
      role: '',
      operation: 'edit',
      component: 'msAssociateEdit',
      way: 'dialog',
      type: 'primary',
      title: '协会编辑',
      width: '45%'
    },
    {
      label: '删除',
      icon: '',
      role: '',
      type: 'danger',
      way: 'dialog',
      operation: 'delete',
      component: 'msAssociateDelete',
    }
  ],
  soltButtons: [
    { 
      label: '添加协会', 
      icon: 'el-icon-plus',
      type: 'primary', 
      operation: 'created',
      component: 'msAssociateEdit',
      way: 'dialog',
      title: '协会新建',
      width: '45%'
    },
    { 
      label: '批量删除', 
      type: 'info', 
      icon: 'el-icon-close',
      operation: 'delete',
      component: 'msAssociateDelete',
      way: 'batch'
    }
  ]
}

export default domConfig;
