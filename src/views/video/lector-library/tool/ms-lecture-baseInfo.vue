<template>
  <ms-operation-dialog>
    <template slot="content">
      <el-col :span="16">
        <el-form
          ref="submitRef"
          class="rule-form"
          :model="submitData"
          label-width="110px"
          v-loading="getLoading"
        >
          <el-row>
            <template v-for="(item, index) in formConfig.formField">
              <el-col :key="index" :span="item.colSpan">
                <el-form-item :prop="item.prop" :label="item.label">
                  <component
                    :is="item.component"
                    v-model="submitData[item.prop]"
                    :model.sync="submitData[item.prop]"
                    :width="item.width || '100%'"
                    :disabled="item.disabled "
                    :rows="item.rows"
                    :type="item.type || 'text'"
                    :active="item.active"
                    :inactive="item.inactive"
                  ></component>
                </el-form-item>
              </el-col>
            </template>
          </el-row>
          <el-col :span="8">
            <el-form-item label="最后更新人：" label-width="150px">{{submitData.updatedName}}</el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="最后更新时间：" label-width="150px">{{submitData.updatedTime}}</el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="专家创建时间：" label-width="150px">{{submitData.createdTime}}</el-form-item>
          </el-col>
        </el-form>
      </el-col>
      <el-col :span="8">
        <el-form
          ref="submitRef"
          class="rule-form"
          :model="submitData"
          label-width="110px"
          v-loading="getLoading"
        >
          <el-row>
            <template v-for="(item, index) in formConfig.exFormField">
              <el-col :key="index" :span="20" :offset="2">
                <el-form-item :prop="item.prop" :label="item.label">
                  <component
                    :is="item.component"
                    v-model="submitData[item.prop]"
                    :model.sync="submitData[item.prop]"
                    :width="item.width || '100%'"
                    :disabled="item.disabled"
                    :rows="item.rows"
                    :options="item.options"
                    :type="item.type || 'text'"
                    :active="item.active"
                    :inactive="item.inactive"
                  ></component>
                </el-form-item>
              </el-col>
            </template>
            <el-col :span="20" :offset="2">
              <el-form-item label="头像">
                <img
                  style="width:150px;"
                  :src="submitData.avatar"
                  @click="dialog = !dialog"
                  alt
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-col>
      <el-dialog title="查看头像" :visible.sync="dialog" width="60%" v-if="dialog">
        <img style="width:100%" :src="submitData.avatar" alt />
      </el-dialog>
    </template>
  </ms-operation-dialog>
</template>

<script>
import formConfig from "../data/form-config";
import msEditor from '@/components/MsEditor'
import { mapGetters } from "vuex";
export default {
  name: "ms-company-edit",
  data() {
    return {
      dialog: false,
      loading: false,
      getLoading: false,
      formConfig: formConfig,
      submitData: {
        companyName: "",
        companyEnglishName: "",
        detailedIntroduction: "",
        briefIntroduction: "",
        updatedName: "",
        updatedTime: "",
        createdTime: "",
        website: "",
        adress: "",
        telephone: "",
        fax: "",
        mailbox: "",
        logo: ""
      },

      //common
      operationLocal: ""
    };
  },
  components: {
    msEditor
  },
  props: {
    model: Object,
    operation: String
  },
  computed: {
    ...mapGetters(["info"])
  },
  created() {
    this.getUserInfo();
  },
  methods: {
    getUserInfo() {
      let params = {
        // userName: this.submitData.userName,
        projectId: this.info.projectId,
        userId: this.$route.query.userId
      };
      this.api.getUserEeatil(params).then(res => {
        let getUserResponse =
          res.data && res.data.getUserResponse ? res.data.getUserResponse : {};
        let getUserExtResponse =
          res.data && res.data.getUserExtResponse
            ? res.data.getUserExtResponse
            : {};
        this.submitData = Object.assign(
          getUserResponse,
          getUserExtResponse,
          {}
        );
      });
    }
  }
};
</script>
