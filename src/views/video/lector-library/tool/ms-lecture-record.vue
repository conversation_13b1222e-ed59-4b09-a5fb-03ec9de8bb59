/* 单节课程组件 */
<template>
  <ms-table
    :currentPage="searchParams.pageIndex"
    :loading="loading"
    :pageSize="searchParams.pageSize"
    :scopeConfig="scopeConfig.show"
    :scopeHeader="scopeConfig.headerShow"
    :tableData="list"
    :tableHeader="domConfig.tableHeader"
    :total="total"
    @current-change="current_change"
    @operation-change="operation_change"
    @size-change="size_change"
    @header-operation="header_operation"
  ></ms-table>
</template>

<script>
import tableMixins from "@/views/mixins/table";
export default {
  name: "ms-lecture-record",
  mixins: [tableMixins],
  data() {
    return {
      loading: false,
      searchParams: {
        // => 列表查询传参
      },
      domConfig: {
        tableHeader: [
          { label: "活动名称", property: "name", sortable: false },
          { label: "活动类型", property: "type", sortable: false },
          { label: "活动时间", property: "time", sortable: true }
        ]
      },
      scopeConfig: {
        headerShow: {
          type: () => {
            return {
              type: "dropdown",
              icon: "icon-funnel",
              options: [
                { label: "全部", value: null },
                { label: "会议", value: 0 },
                { label: "直播", value: 1 },
                { label: "课程", value: 2 }
              ],
              operation: "query"
            };
          }
        }
      }
    };
  },
  methods: {
    init() {
      this.loading = true;
      this.dialog = false;
      if (this.searchParams.createTime) {
        this.searchParams.createdStartTime =
          this.searchParams.createTime[0] || "";
        this.searchParams.createdEndTime =
          this.searchParams.createTime[1] || "";
      }
      let searchParams = { ...this.searchParams };
      this.api
        .getVideoPage(searchParams)
        .then(response => {
          this.loading = false;
          this.total = response.totalSize || 0;
          // this.list = response.data || [];
          if (response.status !== 200) {
            this.PUBLIC_Methods.apiNotify(
              response.message || "请求出错",
              "warning"
            );
          }
        })
        .catch(() => (this.loading = false));
    },
    operation_else(val) {
      if (val.operation.way === "delete") {
        this.$confirm("此操作将永久删除视频信息，是否继续", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(() => {
          let param = {
            userId: this.info.userId,
            username: this.info.userName,
            id: val.model && val.model.id
          };
          this.api.deleteVideoById(param).then(response => {
            if (response.status === 200) {
              this.PUBLIC_Methods.apiNotify(
                response.message || "请求成功",
                "success"
              );
            } else {
              this.PUBLIC_Methods.apiNotify(
                response.message || "请求出错",
                "warning"
              );
            }
            this.init();
          });
        });
      }
    }
  }
};
</script>

<style lang="scss" scoped>
</style>

