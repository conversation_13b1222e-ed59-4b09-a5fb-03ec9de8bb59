<template>
  <div>
    <el-tabs v-model="activeName">
      <el-tab-pane label="基本信息" name="baseInfo">
        <msLectureBaseInfo></msLectureBaseInfo>
      </el-tab-pane>
      <!-- <el-tab-pane label="参与记录" name="participateRecord">
        <msLectureRecord></msLectureRecord>
      </el-tab-pane>-->
    </el-tabs>
    <div class="buttons">
      <el-button type="info" @click="goBack">返回</el-button>
    </div>
  </div>
</template>

<script>
import formConfig from "../data/form-config";
import { mapGetters } from "vuex";
import msLectureBaseInfo from "./ms-lecture-baseInfo";
// import msLectureRecord from "./ms-lecture-record";

export default {
  name: "ms-lecture-show",
  data() {
    return {
      loading: false,
      getLoading: false,
      formConfig: formConfig,
      activeName: "baseInfo",
      //common
      operationLocal: "",
      formData: {}
    };
  },
  props: {
    model: Object,
    operation: String
  },
  components: {
    msLectureBaseInfo
    // msLectureRecord
  },
  computed: {
    ...mapGetters(["info"])
  },
  created() {
    this.init();
    if (this.operation === "edit") {
      this.submitData = this.model;
    }
  },
  methods: {
    init() {
      this.operationLocal = this.operation || this.$route.query.operation;
      if (this.operationLocal === "edit" && this.model.id) {
        this.getDetail();
      }
    },
    getDetail() {
      this.submitData = { ...this.model, timeRange: [] };
      this.submitData.timeRange[0] = this.model.startTime || "";
      this.submitData.timeRange[1] = this.model.endTime || "";
    },
    goBack() {
      window.history.go(-1);
    }
  }
};
</script>

<style lang="scss" scoped>
.buttons {
  position: fixed;
  bottom: 20px;
  right: 20px;
}
</style>
