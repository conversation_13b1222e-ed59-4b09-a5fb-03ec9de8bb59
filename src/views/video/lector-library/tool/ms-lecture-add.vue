<template>
  <div class="lecture" v-loading="even()">
    <el-form
          label-width="100px"
          class="rule-form"
          ref="submitRef"
          :rules="rule"
          :model="submitData"
        >
      <el-row>
        <el-col :span="16">
            <el-col :span="12">
              <el-form-item
                label="真实姓名(中文名):"
                prop="name"
                label-width="150px"
              >
                <el-input
                  v-model="submitData.name"
                  placeholder="请输入真实姓名"
                  clearable
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="英文名:" prop="enName">
                <el-input
                  v-model="submitData.enName"
                  placeholder="请输入英文名"
                  clearable
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="个人介绍:" prop="description" clearable>
                <ms-editor
                  :height="200"
                  v-model="submitData.description"
                ></ms-editor>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="简介:">
                <el-input
                  v-model="submitData.introduction"
                  type="textarea"
                  :rows="4"
                  placeholder="请输入医生简介"
                  clearable
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="最后更新人：" label-width="150px">{{
                submitData.updatedName
              }}</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="最后更新时间：" label-width="150px">{{
                submitData.updatedTime
              }}</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="专家创建时间：" label-width="150px">{{
                submitData.createdTime
              }}</el-form-item>
            </el-col> 
        </el-col>
        <el-col :span="8">
            <el-col>
              <el-form-item label="性别：">
                <el-select v-model="submitData.sex" style="width: 100%;">
                  <el-option :value="0" label="男"></el-option>
                  <el-option :value="1" label="女"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item label="专家类型：">
                <el-select v-model="submitData.speakerType" style="width: 100%;">
                  <el-option :value="0" label="国内专家"></el-option>
                  <el-option :value="1" label="国际专家"></el-option>
                  <el-option :value="2" label="基层医生"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item label="国籍：" prop="countryId">
                <ms-country-search
                  :model.sync="submitData.countryId"
                  @bindData="get_countryList"
                ></ms-country-search>
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item label="城市：">
                <ms-area-cascader
                  v-model="submitData.areaMap"
                  :model.sync="submitData.areaMap"
                  :countryId="submitData.countryId"
                  :disabled="submitData.countryId?false:true"
                  @bindData="get_cityList"
                ></ms-area-cascader>
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item label="医院/单位：">
                <ms-hospital-search
                  v-model="submitData.unit"
                  :model.sync="submitData.unit"
                  :allowCreate="true"
                ></ms-hospital-search>
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item label="科室/部门：">
                <ms-category-cascader
                  :model.sync="submitData.categoryList" :multiple="true"
                ></ms-category-cascader>
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item label="职称：">
                <ms-professional-cascader
                  v-model="submitData.professionalMap"
                  :model.sync="submitData.professionalMap"
                  @bindData="get_professional"
                ></ms-professional-cascader>
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item label="擅长领域：">
                <el-input
                  v-model="submitData.researchDirection"
                  clearable
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item label="固定电话：">
                <el-input
                  v-model="submitData.telephone"
                  clearable
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item label="手机号：" prop="mobilephone">
                <el-input v-model="submitData.mobilephone" clearable></el-input>
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item label="邮箱：">
                <el-input v-model="submitData.email" clearable></el-input>
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item label="微信：">
                <el-input v-model="submitData.wechat" clearable></el-input>
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item label="个人主页：">
                <el-input v-model="submitData.personalHomepage" clearable></el-input>
              </el-form-item>
            </el-col>
             <el-col>
              <el-form-item label="证件类型：">
                <el-select v-model="submitData.certificatesType" style="width: 100%;">
                  <el-option :value="0" label="身份证"></el-option>
                  <el-option :value="1" label="护照"></el-option>
                  <el-option :value="2" label="军官证"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item label="证件号码：">
                <el-input v-model="submitData.certificatesNum" clearable></el-input>
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item label="点赞数：">
                <el-switch v-model="submitData.isShowLike" :active-value="1" :inactive-value="0"></el-switch>
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item label="浏览量：">
                <el-switch v-model="submitData.isShowViews" :active-value="1" :inactive-value="0"></el-switch>
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item label="个人账户：">
                <ms-dictionary-search :model.sync='submitData.personalBankType' type="bank_type" style="width: 100%;"></ms-dictionary-search>
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item label="银行卡号：">
                <el-input v-model="submitData.bankCardNum" clearable></el-input>
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item label="ERP编号：">
                <el-input v-model="submitData.erpAccount" clearable></el-input>
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item label="信息来源：">
                <el-input v-model="submitData.infoSources" clearable></el-input>
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item label="头像：">
                <ms-single-image v-model="submitData.avatar"></ms-single-image>
              </el-form-item>
            </el-col>
        </el-col>
      </el-row>
    </el-form>
    <div class="buttons">
      <el-button type="primary" @click="submit">保存</el-button>
      <el-button type="info" @click="close">返回</el-button>
    </div>
  </div>
</template>

<script>
import formConfig from "../data/form-config"
import msAreaCascader from "@/components/MsCommon/ms-area-lazycascader"
import msCountrySearch from "@/components/MsCommon/ms-country-search"
import msHospitalSearch from "@/components/MsCommon/ms-hospital-search"
import msCategoryCascader from "@/components/MsCommon/ms-category-cascader"
import msProfessionalCascader from "@/components/MsCommon/ms-professional-lazycascader"
import msDictionarySearch from '@/components/MsCommon/ms-dictionary-search'
import msEditor from '@/components/MsEditor'
import { mapGetters } from "vuex"

export default {
  name: "ms-lecture-add",
  data() {
    return {
      show: false,
      loading: false,
      getLoading: false,
      formConfig: formConfig,
      submitData: {
        userName: "", // 梅斯账号
        name: "", // 真实名字
        enName: "", // 英文名
        briefIntroduction: "", // 简介briefIntroduction
        selfDescription: "", // 个人介绍
        sex: "", // 性别
        speakerType: 0, //专家类型
        countryId: 100000, //国籍id
        countryName: '中国', //国籍
        cityName: "", // 城市
        unit: "", // 医院/单位
        categoryList: [], // 科室/部门
        researchDirection: "", // 擅长领域
        mobilephone: "", // 固定电话
        mobile: "", // 手机号
        certificatesType: "", //证件类型
        certificatesNum: "", //证件号码
        isShowLike: 0, //点赞数
        isShowViews: 0, //浏览量
        personalBankType: "", //个人帐户类型
        bankCardNum: "", //银行卡号
        erpAccount: "", //ERP编号
        infoSources: "", //信息来源
        email: "", // 邮箱
        wechat: "", // 微信
        personalHomepage: "", // 个人主页
        isLecturer: 1,
        avatar: "", // 头像
      },
      departmentList: [],

      options: [],
      //common
      operationLocal: "",
      rule: {
        countryId: [
          { required: true, message: "请选择国籍", trigger: "change" },
        ],
        name: [
          { required: true, message: "请输入姓名", trigger: "change" },
        ],
        description: [
          { required: true, message: "请输入个人介绍", trigger: "change" },
        ]
      },
    }
  },
  components: {
    msAreaCascader,
    msCountrySearch,
    msHospitalSearch,
    msCategoryCascader,
    msProfessionalCascader,
    msDictionarySearch,
    msEditor
  },
  props: {
    model: Object,
    operation: String,
  },
  computed: {
    ...mapGetters(["info"]),
  },
  created() {
    if (this.$route.query.operation === "edit") {
      this.show = true
      this.getUserId()
    }
  },
  methods: {
    even() {
      if (this.$route.query.operation === "edit") {
        return this.show
      } else {
        return false
      }
    },
    getUserId() {
      this.api.getMedsciSpeakeDetail({ id: this.$route.query.id }).then(res => {
        this.show = false
        let getUserResponse = res.data ? res.data : {}
        this.submitData = {
          ...getUserResponse,
        }
        this.submitData.areaMap = [getUserResponse.provinceId,getUserResponse.cityId]
        if (getUserResponse.districtId !== 0) {
          this.submitData.areaMap.push(getUserResponse.districtId)
        }
        this.submitData.professionalMap = getUserResponse.professionalId ? [getUserResponse.professionalCatid, getUserResponse.professionalId]:[getUserResponse.professionalCatid]
      })
    },
    submit() {
      this.$refs["submitRef"].validate(valid => {
        if (valid) {
          let userInfo = this.$store.getters.info || {}
          this.submitData.isLecturer = 1
          this.submitData.operatorBy = userInfo.userId
          this.submitData.operatorName = userInfo.userName
          this.submitData.userId = userInfo.userId
          this.submitData.username = userInfo.userName
          this.submitData.updatedName = userInfo.userName
          this.api[
            this.$route.query.operation === "edit"
              ? "updateMedsciSpeake"
              : "saveMedsciSpeake"
          ](this.submitData).then(response => {
            if (response.status === 200) {
              this.PUBLIC_Methods.apiNotify(
                response.message || "添加成功",
                "success"
              )
              this.close()
              this.$emit("up-date")
            } else {
              this.PUBLIC_Methods.apiNotify(
                response.message || "添加失败",
                "warning"
              )
            }
          })
        }
      })
    },
    close() {
      if (this.operation === "edit") {
        this.$emit("close")
      } else {
        window.history.go(-1)
      }
    },
    get_countryList(val) {
      this.submitData.countryId = val.model.id
      this.submitData.countryName = val.model.name
    },
    get_cityList(val) {
      this.submitData.provinceId = val.model[0].id || 0
      this.submitData.provinceName = val.model[0].name || ''
      this.submitData.cityName = val.model[1] ? val.model[1].name : ''
      this.submitData.cityId = val.model[1] ? val.model[1].id : 0
      this.submitData.districtId = val.model[2] ? val.model[2].id : 0
      this.submitData.districtName = val.model[2] ? val.model[2].name : ''
    },
    get_professional(val) {
      this.submitData.professionalCatid = val.model[0].id
      this.submitData.professionalCatName = val.model[0].name
      this.submitData.professionalCatEname = val.model[0].en
      this.submitData.professionalId = val.model[1] ? val.model[1].id : 0
      this.submitData.professionalName = val.model[1] ? val.model[1].name : ""
      this.submitData.professionalEname = val.model[1] ? val.model[1].en : ""
    },
  },
}
</script>

<style lang="scss" scoped>
.buttons {
  position: fixed;
  bottom: 20px;
  right: 20px;
}
</style>
