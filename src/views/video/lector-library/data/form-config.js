const formConfig = {
  formField: [
    {
      label: '梅斯账号',
      prop: 'userName',
      colSpan: 8,
      component: 'ms-input',
      disabled: true
    },
    {
      label: '真实姓名(中文名)',
      prop: 'realName',
      colSpan: 8,
      component: 'ms-input',
      disabled: true
    },
    {
      label: '英文名',
      prop: 'englishName',
      colSpan: 8,
      component: 'ms-input',
      disabled: true
    },
    {
      label: '个人介绍',
      prop: 'selfDescription',
      colSpan: 24,
      component: 'ms-editor',
      // type: 'textarea',
      // rows: 8,
      disabled: true
    },
    {
      label: '简介',
      prop: 'briefIntroduction',
      colSpan: 24,
      component: 'ms-input',
      type: 'textarea',
      rows: 4,
      disabled: true
    },
    // {
    //   label: '最后更新人',
    //   prop: 'updatedName',
    //   colSpan: 8,
    //   disabled: true
    // },
    // {
    //   label: '最后更新时间',
    //   prop: 'updatedTime',
    //   colSpan: 8,
    //   disabled: true
    // },
    // {
    //   label: '公司创建时间',
    //   prop: 'createdTime',
    //   colSpan: 8,
    //   disabled: true
    // }
  ],
  exFormField: [
    {
      label: '性别',
      prop: 'sex',
      component: 'ms-radio',
      options: [
        { label: '男', value: 0, disabled: true },
        { label: '女', value: 1, disabled: true }
      ],
      disabled: true
    },
    {
      label: '城市',
      prop: 'cityName',
      component: 'ms-input',
      disabled: true
    },
    {
      label: '医院/单位',
      prop: 'companyName',
      component: 'ms-input',
      disabled: true
    },
    {
      label: '科室/部门',
      prop: 'departmentName',
      component: 'ms-input',
      disabled: true
    },
    {
      label: '擅长领域',
      prop: 'researchDirection',
      component: 'ms-input',
      disabled: true
    },
    {
      label: '固定电话',
      prop: 'fixedTelephone',
      component: 'ms-input',
      disabled: true
    },
    {
      label: '手机号',
      prop: 'mobile',
      component: 'ms-input',
      disabled: true
    },
    {
      label: '邮箱',
      prop: 'email',
      component: 'ms-input',
      disabled: true
    },
    {
      label: '微信',
      prop: 'wechatAccount',
      component: 'ms-input',
      disabled: true
    },
    // {
    //   label: '传真',
    //   prop: 'fax',
    //   component: 'ms-input',
    //   disabled: true
    // },
    {
      label: '个人主页',
      prop: 'selfHomepage',
      component: 'ms-input',
      disabled: true
    },
    // {
    //   label: '是否名师',
    //   prop: 'isFamousTeacher',
    //   component: 'ms-radio',
    //   options: [
    //     { label: '是', value: 2, disabled: true },
    //     { label: '否', value: 1, disabled: true }
    //   ],
    //   disabled: true
    // },
    // {
    //   label: '头像',
    //   prop: 'avatarFileId',
    //   component: 'ms-single-image'
    // }
  ],
  // lectureFormFeild: [
  //   {
  //     label: '梅斯账号',
  //     prop: 'userName',
  //     placeholder: '请输入梅斯账号',
  //     component: 'ms-input',
  //     disabled: true
  //   },
  //   {
  //     label: '真实姓名(中文名)',
  //     prop: 'realName',
  //     component: 'ms-input',
  //     disabled: true
  //   },
  //   {
  //     label: '个人主页',
  //     prop: 'selfHomepage',
  //     component: 'ms-input',
  //     disabled: true
  //   },
  //   {
  //     label: '个人主页',
  //     prop: 'selfHomepage',
  //     component: 'ms-input',
  //     disabled: true
  //   },
  //   {
  //     label: '个人主页',
  //     prop: 'selfHomepage',
  //     component: 'ms-input',
  //     disabled: true
  //   },
  //   {
  //     label: '个人主页',
  //     prop: 'selfHomepage',
  //     component: 'ms-input',
  //     disabled: true
  //   },
  //   {
  //     label: '个人主页',
  //     prop: 'selfHomepage',
  //     component: 'ms-input',
  //     disabled: true
  //   },
  //   {
  //     label: '是否名师',
  //     prop: 'isLecturer',
  //     component: 'ms-radio',
  //     options: [{ label: '是', value: 1 }, { label: '否', value: 0 }],
  //     disabled: true
  //   }
  // ]
}

export default formConfig
