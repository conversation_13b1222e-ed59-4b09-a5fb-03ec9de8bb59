const scopeConfig = {
  show: {
    createdTime: () => {
      return {
        type: 'formatTime',
        cFormat: '{y}-{m}-{d} {h}:{i}'
      }
    },
    status: () => {
      return {
        type: 'status',
        rule: {
          0: { label: '禁用', background: '#A7ADBD' },
          1: { label: '启用', background: '#40A23F' },
          2: { label: '未激活', background: '#A7ADBD' }
        }
      }
    },
    realName: () => {
      return {
        type: 'fidd',
        fields: [
          {
            way: 'page',
            path: 'lecture-show',
            name: 'realName',
            params: [{ keyName: 'userId', valName: 'userId' }, { keyName: 'userName', valName: 'userName' }]
          }
        ]
      }
    }
  },
  headerShow: {
    status: () => {
      return {
        type: 'dropdown',
        icon: 'icon-funnel',
        options: [
          { label: '全部', value: null },
          { label: '禁用', value: 0 },
          { label: '启用', value: 1 },
          { label: '未激活', value: 2 }
        ],
        operation: 'query'
      }
    }
  }
}

export default scopeConfig
