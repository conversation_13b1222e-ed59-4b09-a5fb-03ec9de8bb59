const domConfig = {
  // 搜索配置
  listSearch: [
    {
      label: '真实姓名',
      placeholder: '请输入专家姓名',
      model: 'name',
      component: 'ms-input'
    },
    // {
    //   label: '科室',
    //   placeholder: '请输入科室',
    //   model: 'department',
    //   component: 'ms-department-search'
    // },
    {
      label: '单位',
      placeholder: '请输入单位',
      model: 'unit',
      component: 'ms-hospital-search'
    },
  ],

  // 表头配置
  tableHeader: [
    { label: 'ID', property: 'id', width: '80' },
    { label: '真实姓名', property: 'name', sortable: false, width: '150' },
    { label: '职称', property: 'professionalName', sortable: false, width: '150' },
    // { label: '科室', property: 'department', sortable: false },
    { label: '单位', property: 'unit', sortable: false, width: '150' },
    { label: '引用次数', property: 'dig', sortable: true },
    { label: '账号状态', property: 'status', sortable: false, width: '120' },
    { label: '创建时间', property: 'createdTime', sortable: true, width: 120 }
  ],

  // 行内列表按钮配置
  tableButtons: [
    {
      label: '编辑',
      icon: '',
      role: '',
      operation: 'edit',
      component: 'msLectureAdd',
      way: 'page',
      path: 'lecture-edit',
      params: ['id'],
      type: 'primary',
      title: '编辑',
      position: 'right',
      width: '100%'
    },
    {
      icon: '',
      role: '',
      operation: 'editStatus',
      component: 'msLectureStatus',
      way: 'dialog',
      field: 'status',
      rule: {
        0: { label: '启用', type: 'success' },
        1: { label: '禁用', type: 'info' }
      }
    },
    {
      label: '删除',
      icon: '',
      role: '',
      operation: 'delete',
      component: '',
      way: 'delete',
      type: 'danger',
      title: '删除评论',
      width: '45%'
    }
  ],

  // 新建项目按钮
  soltButtons: [
    {
      label: '添加专家',
      icon: 'el-icon-plus',
      type: 'primary',
      operation: 'created',
      // component: 'msLectureAdd',
      way: 'page',
      path: 'lecture-add',
      params: ['userId'],
      title: '添加专家',
      position: 'right',
      width: '100%'
    },
    {
      title: '批量添加专题',
      label: '批量添加专题',
      type: 'success',
      operation: 'add',
      component: 'msLectureCategory',
      way: 'batch',
      identify: 'batch_add_category',
      width: '70%'
    },
    {
      title: '批量移除专题',
      label: '批量移除专题',
      type: 'success',
      operation: 'delete',
      component: 'msLectureCategory',
      way: 'batch',
      identify: 'batch_delete_category',
      width: '70%'
    }
  ]
}
export default domConfig
