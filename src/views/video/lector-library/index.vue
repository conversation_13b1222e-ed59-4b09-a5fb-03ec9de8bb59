/* 专家库组件 */
<template>
  <ms-table
    :currentPage="searchParams.pageIndex"
    :loading="loading"
    :operationButtons="domConfig.tableButtons"
    :pageSize="searchParams.pageSize"
    :scopeConfig="scopeConfig.show"
    :scopeHeader="scopeConfig.headerShow"
    :tableData="list"
    :tableHeader="domConfig.tableHeader"
    :total="total"
    :showSelection="true"
    :expandList="expandList"
    @current-change="current_change"
    @operation-change="operation_change"
    @size-change="size_change"
    @header-operation="header_operation"
    @handleSelectionChange="handleSelectionChange"
  >
    <!-- 列表搜索去区域插槽 -->
    <template slot="ms-table-header">
      <div class="slot-search">
        <template v-for="(searchItem, key) in domConfig.listSearch">
          <component
            :index="searchItem.index || ''"
            :is="searchItem.component"
            :key="key"
            v-if="key <= 2"
            :width="searchItem.width || '150px'"
            :model.sync="searchParams[searchItem.model]"
            :label="searchItem.label"
            :operation="searchItem.operation || ''"
            :options="searchItem.options || []"
            :placeholder="searchItem.placeholder || ''"
            :type="searchItem.type || ''"
            :multiple="searchItem.multiple"
            :disabled="searchItem.disabled"
          ></component>
        </template>
        <div class="inlineBlock">
          <el-button
            @click="handleClick('query')"
            type="primary"
            plain
            icon="el-icon-search"
            >查询</el-button
          >
          <el-button @click="handleClick('reset')">重置</el-button>
        </div>
      </div>
      <div class="slot-button-article clearfix">
        <template v-for="(item, index) in domConfig.soltButtons">
          <el-button
            :key="index"
            :type="item.type"
            :icon="item.icon"
            @click="operation_change({ operation: item })"
            plain
            >{{ item.label }}</el-button
          >
        </template>
      </div>
      <!-- 换行 -->
      <div class="slot-search">
        <div style="width: 100vw;"></div>
      </div>
      <div class="slot-search">
        <template v-for="(searchItem, key) in domConfig.listSearch">
          <component
            :index="searchItem.index || ''"
            :is="searchItem.component"
            :key="key"
            v-if="key > 2 && show"
            :width="searchItem.width || '150px'"
            :model.sync="searchParams[searchItem.model]"
            :label="searchItem.label"
            :operation="searchItem.operation || ''"
            :options="searchItem.options || []"
            :placeholder="searchItem.placeholder || ''"
            :type="searchItem.type || ''"
            :multiple="searchItem.multiple"
            :disabled="searchItem.disabled"
          ></component>
        </template>
      </div>
      <el-dialog
        :visible.sync="dialog"
        closeable
        show-close
        :close-on-click-modal="false"
        :width="dialogWidth"
        :title="dialogTitle"
      >
        <component
          :is="dialogComponent"
          :model="scopeInfo"
          :operation="dialogOperation"
          @close="dialog = !dialog"
          @up-date="init"
          v-if="dialog"
        ></component>
      </el-dialog>
      <ms-right-dialog
        :visible.sync="r_dialog"
        :width="dialogWidth"
        :title="dialogTitle"
      >
        <component
          :is="dialogComponent"
          :model="scopeInfo"
          :operation="dialogOperation"
          @close="r_dialog = !r_dialog"
          @up-date="init"
          v-if="r_dialog"
        ></component>
      </ms-right-dialog>
    </template>
  </ms-table>
</template>

<script>
import dataMixin from "./dataMixin"
import tableMixins from "@/views/mixins/table"
import msLectureAdd from "./tool/ms-lecture-add"
import msLectureStatus from "./tool/ms-lecture-status"
import msDepartmentSearch from "@/components/MsCommon/ms-department-search"
import msHospitalSearch from "@/components/MsCommon/ms-hospital-search"
import msLectureCategory from "./tool/ms-lecture-category"
export default {
  name: "lector-library",
  mixins: [dataMixin, tableMixins],
  components: {
    msLectureAdd,
    msLectureStatus,
    msDepartmentSearch,
    msHospitalSearch,
    msLectureCategory
  },
  data() {
    return {
      show: false,
      loading: false,
      searchParams: {
        // => 列表查询传参
      },
      expandList: [
        { label: "职称", property: "professionalName" },
        { label: "固定电话", property: "telephone" },
        { label: "手机号", property: "mobilephone" },
        { label: "邮箱", property: "email" },
        { label: "简介", property: "introduction" },
      ],
    }
  },
  methods: {
    apiInit(params) {
      let searchParams = { ...params }
      this.api
        .getMedsciSpeakePage(searchParams)
        .then(response => {
          this.loading = false
          this.total = response.totalSize || 0
          this.list = response.data || []
          if (response.status !== 200) {
            this.PUBLIC_Methods.apiNotify(
              response.message || "请求出错",
              "warning"
            )
          }
        })
        .catch(() => (this.loading = false))
    },
    operation_else(val) {
      if (val.operation.way === "delete") {
        this.$confirm("此操作将永久删除专家信息，是否继续", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }).then(() => {
          let param = {
            id: val.model.id,
          }
          this.api.deleteMedsciSpeake(param).then(response => {
            if (response.status === 200) {
              this.PUBLIC_Methods.apiNotify(
                response.message || "请求成功",
                "success"
              )
            } else {
              this.PUBLIC_Methods.apiNotify(
                response.message || "请求出错",
                "warning"
              )
            }
            this.init()
          })
        })
      }
    },
  },
}
</script>

<style lang="scss" scoped>
/deep/ .label_value {
  max-width: 500px;
}

/deep/ .label {
  height: 100%;
  display: inline-block;
}
</style>
