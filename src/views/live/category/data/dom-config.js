const domConfig = {
  listSearch: [
    
  ],
  tableHeader: [
    { label: '类型ID', property: 'id', width: '80' },
    { label: '类型编码', property: 'code' },
    { label: '类型名称', property: 'name' },
    { label: '创建时间', property: 'createdTime' }
  ],
  tableButtons: [
    {
      label: '编辑',
      type: 'primary', 
      icon: '',
      title: '编辑直播类型',
      operation: 'edit',
      component: 'msCategoryCreate',
      way: 'dialog',
    }
  ],
  soltButtons: [
    { 
      label: '创建类型', 
      type: 'primary', 
      icon: 'el-icon-plus',
      title: '创建直播类型',
      operation: 'created',
      component: 'msCategoryCreate',
      way: 'dialog',
    }
  ]
}

export default domConfig;
