<template>
  <ms-operation-dialog>
    <template slot="content">
      <el-form ref="submitRef"
               :model="submitData"
               :rules="rules"
               label-width="85px"
               class="rule-form">
        <el-row>
          <el-col :span="18" :offset="2">
            <el-form-item label="类型名称" prop="name">
              <el-input v-model="submitData.name" placeholder="请输入20个字符以内的文本"></el-input>
            </el-form-item>
            <el-form-item label="类型描述" prop="describes">
              <el-input v-model="submitData.describes" placeholder="请输入200个字符以内的文本" type="textarea" :autosize="{ minRows: 3, maxRows: 6}"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </template>
    <template slot="footer">
      <el-button @click="submitForm"
                 :loading="loading"
                 size="mini"
                 type="primary">提交</el-button>
      <el-button @click="$emit('close')"
                 size="mini">取 消</el-button>
    </template>
  </ms-operation-dialog>
</template>

<script> 
import { mapGetters } from "vuex";
export default {
	name: "ms-category-create",
	data () {
		return {
      loading: false,
      submitData: {
        name: "",
        describes: ""
      },
      rules: {
        name: [
          { required: true, message: "请输入直播类型名称", trigger: 'blur' },
          { max: 20, message: '请输入20个字符以内的文本', trigger: 'blur' }
        ],
        describes: [
          { max: 200, message: '请输入200个字符以内的文本', trigger: 'blur' }
        ]
      },
    }
  },
	props: {
		model: Object,
		operation: String
  },
  computed: {
    ...mapGetters(["info"])
  },
  created() {
    this.init()
  },
	methods: {
    init() {
      this.operationLocal = this.operation || this.$route.query.operation
      if(this.operationLocal === 'edit' && this.model.id) {
        this.submitData = {...this.submitData,...this.model}
      } 
    },
		submitForm() {
      this.$refs.submitRef.validate( valid => {
        if(valid) {
          this.loading = true;
          if(this.operationLocal === 'edit') {
            this.submitEdit()
          } else {
            this.submitAdd()
          }
        }
      })
    },
    submitEdit() {
      let params = {
        userId: this.info.userId,
        username: this.info.userName,
        ...this.submitData
      }
      this.api.editLiveCategory(params).then(response => {
        if(response.status === 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
        this.$emit('up-date')
        this.loading = false
      }).catch( () => {
        this.loading = false;
        this.$emit('close')
      })
    },
    submitAdd() {
      let params = {
        userId: this.info.userId,
        username: this.info.userName,
        ...this.submitData
      }
      this.api.addLiveCategory(params).then(response => {
        if(response.status === 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
        this.$emit('up-date')
        this.loading = false
      }).catch( () => {
        this.loading = false;
        this.$emit('close')
      })
    },
	}
}
</script>
