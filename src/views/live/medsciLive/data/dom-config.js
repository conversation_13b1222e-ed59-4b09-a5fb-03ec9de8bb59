const domConfig = {
  listSearch: [
    {
      label: '直播名称',
      placeholder: '请输入',
      model: 'searchTitle',
      component: 'ms-input'
    },
    {
      label: '创建人',
      placeholder: '请输入',
      model: 'createdName',
      component: 'ms-input'
    }
  ],
  tableHeader: [
    { label: '直播ID', property: 'id', width: '80' },
    { label: '直播名称', property: 'name', width: '120' },
    { label: '推流地址', property: 'pushUrl', width: '80' },
    { label: '播放地址', property: 'playUrl', width: '80' },
    { label: '拉流地址', property: 'm3u8PlayUrl', width: '80' },
    // { label: '直播房间号', property: 'roomNo' },
    { label: '审核状态', property: 'examineStatus', width: '80' },
    { label: '直播类型', property: 'categoryStr', width: '80' },
    { label: '直播状态', property: 'statusName', width: '80' },
    { label: '开播时间', property: 'liveStartTime', width: '80' },
    { label: '创建人', property: 'createdName' },
    { label: 'PV/UV', property: 'pvUv', width: '80' },
    { label: '并发量/人均观看时长(分钟)', property: 'allTime', width: '90' },
    { label: '弹幕次数', property: 'commentsCount', width: '80' },
    { label: '聊天室', property: 'chatRome', width: '120' }
  ],
  tableButtons: [
    {
      label: '编辑',
      icon: '',
      role: '',
      operation: 'edit',
      component: 'medscilive-operation',
      way: 'page',
      type: 'primary',
      path: 'medsciLive-operation',
      params: ['id']
    },
    {
      label: '',
      icon: '',
      role: '',
      operation: 'editStatus',
      component: 'msLiveOperation',
      way: 'dialog',
      field: 'examineStatus',
      rule: {
        1: { label: '去审', type: '', operation: 'toreview' },
        0: { label: '审核', type: 'success', operation: 'approval' }
      }
    },
    {
      label: '删除',
      way: 'dialog',
      type: 'danger',
      operation: 'delete',
      component: 'msLiveOperation',
      showCallback: (val) => {
        if (val.examineStatus == 0) {
          return true
        } else {
          return false
        }
      }
    },
    {
      label: '',
      way: 'stopAndRecovery',
      type: 'danger',
      operation: 'delete',
      tdWidth: '50',
      field: 'examineStatus',
      rule: {
        1: { label: '暂停', type: 'warning', operation: 'stop' },
        2: { label: '播放', type: 'success', operation: 'recovery' }
      },
      showCallback: (val) => {
        if (val.isForbid == 0 && val.status != 1 && val.examineStatus != 0) {
          return true
        } else {
          return false
        }
      }
    },
    {
      label: '结束',
      way: 'live-forbid',
      type: 'danger',
      operation: 'delete',
      tdWidth: '50',
      showCallback: (val) => {
        if (val.isForbid == 0 && val.status != 1 && val.examineStatus != 0) {
          return true
        } else {
          return false
        }
      }
    },
    {
      label: '更多',
      operation: 'more',
      type: 'info',
      children: [
        {
          label: '设置片头',
          component: 'ms-setting-titles',
          way: 'dialog',
          params: ['id'],
          type: 'primary',
          title: '设置片头',
          position: 'right',
          width: '50%',
          showCallback: (val) => {
            if (val.status == 1) {
              return true
            } else {
              return false
            }
          }
        },
        {
          label: '效果预览',
          way: 'previewPage',
          type: 'primary',
          title: '效果预览',
          showCallback: (val) => {
            if (val.status == 1) {
              return true
            } else {
              return false
            }
          }
        },
        {
          label: '设置商品',
          component: 'ms-setting-product',
          way: 'dialog',
          params: ['id'],
          type: 'primary',
          title: '设置商品',
          position: 'right',
          width: '50%'
        },
        {
          label: '设置管理员',
          component: 'ms-setting-manager',
          way: 'dialog',
          params: ['id'],
          type: 'primary',
          title: '设置管理员',
          position: 'right',
          width: '50%'
        },
        {
          label: '设置公告',
          component: 'ms-setting-advertising',
          way: 'dialog',
          params: ['id'],
          type: 'primary',
          title: '设置公告',
          position: 'right',
          width: '60%'
        },
        {
          label: '导入水军',
          icon: '',
          role: '',
          operation: 'edit',
          component: 'msLiveImport',
          way: 'dialog',
          type: 'primary',
          position: 'right',
          title: '导入水军',
          width: '55%',
          params: ['id']
        },
        {
          label: '设置禁言',
          component: 'ms-setting-shutUp',
          way: 'dialog',
          params: ['id'],
          type: 'primary',
          title: '设置禁言',
          position: 'right',
          width: '50%'
        },
        {
          label: '设置调研',
          component: 'ms-setting-form',
          way: 'dialog',
          params: ['id'],
          type: 'primary',
          title: '设置调研',
          position: 'right',
          width: '60%'
        },
        {
          label: '设置投票',
          component: 'ms-vote-form',
          way: 'dialog',
          params: ['id'],
          type: 'primary',
          title: '设置投票',
          position: 'right',
          width: '60%'
        },
        {
          label: '设置评论',
          // component: 'ms-setting-form',
          way: 'page',
          params: ['chatroomid'],
          type: 'primary',
          // title: '设置评论',
          // position: 'right',
          width: '60%',
          path: 'medscilive-comment',
        },
        {
          label: '导出明细',
          way: 'export',
          params: ['id'],
          type: 'primary',
          title: '导出明细',
          showCallback: (val) => {
            if (val.status == 3 || val.status == 4) {
              return true
            } else {
              return false
            }
          }
        },
        {
          label: '复制直播',
          icon: '',
          role: '',
          operation: 'copy',
          component: 'medscilive-operation',
          way: 'page',
          type: 'primary',
          path: 'medsciLive-operation',
          params: ['id']
        },
      
      ]
    }
  ],
  soltButtons: [
    { 
      label: '创建直播', 
      type: 'primary', 
      icon: 'el-icon-plus',
      title: '',
      operation: 'created',
      component: 'medscilive-operation',
      way: 'page',
      path: 'medsciLive-operation',
      params: ['id']
    },
    { 
      label: '批量审核', 
      type: 'primary',
      operation: 'approval',
      component: 'msLiveOperation',
      way: 'batch'
    },
    { 
      label: '批量去审', 
      type: 'primary',
      operation: 'toreview',
      component: 'msLiveOperation',
      way: 'batch'
    },
    { 
      label: '批量删除',
      type: 'info',
      icon: 'el-icon-close',
      operation: 'delete',
      component: 'msLiveOperation',
      way: 'batch'
    },
    {
      label: '回收站',
      type: 'info',
      operation: '',
      component: 'msLiveRecycle',
      way: 'dialog',
      title: '直播回收站',
      width: '90%'
    }
  ]
}

export default domConfig;
