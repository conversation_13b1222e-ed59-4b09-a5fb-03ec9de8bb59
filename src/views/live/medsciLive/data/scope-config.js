import store from "@/store/index.js"
import serveUrl from '@/store/data/serveUrl.js'

const scopeConfig = {
  show: {
    name: () => {
      return {
        type: 'preview',
        config: {
          noCheck: true,
          pageUrl: `${serveUrl['live']}`,
          paramsVal: ['encodeId']
        }
      }
    },
    examineStatus: () => {
      return {
        type: 'status',
        rule: {
          0: { label: '待审核', background: '#A7ADBD' },
          1: { label: '审核通过', background: '#40A23F' },
          2: { label: '已暂停', background: '#A7ADBD' },
        }
      }
    },
    playUrl: () => {
      return {
        type: 'copyLink',
        hiddenText: true
      }
    },
    m3u8PlayUrl: () => {
      return {
        hiddenText: true,
        type: 'copyLink'
      }
    },
    pushUrl: () => {
      return {
        type: 'copyLink',
        hiddenText: true,
        // subStr: 32
      }
    },
    pvUv: () => {
      return {
        type: 'fidd',
        fields: [
          {name: 'detailClickHits', way: 'text'},
          {name: 'actualMaxOnlineHits', way: 'page', path: 'medsciLive-details',params: ['id']},
        ]
      }
    },
    allTime: (row) => {
      return {
        type: 'fidd',
        fields: [
          {name: (row.status == 3 || row.status == 4) ?'concurrency':null, way: 'text'},
          {name: 'averageViewTimes', way: 'text'},
        ]
      }
    },
    commentsCount: (row) => {
      return {
        type: 'fidd',
        fields: [
          {name: (row.status == 3 || row.status == 4) ?'commentsCount':null, way: 'text'},
        ]
      }
    },
    chatRome: () => {
      return {
        type: 'render',
        render: (h, scope) => {
          const { chatroomid, id } = scope.row;
          if (chatroomid) {
            return h("div", scope.row.chatroomid)
          } else {
            return h(
              "el-button",{
                props: {
                  plain: true,
                  type: 'primary',
                  size: 'mini'
                },
                on: {
                  click: async () => {
                    const {userId, userName} = store.getters.info;
                    const res = await window.vm.api.GenerateChatRoom({
                      userId,
                      userName,
                      liveId: id
                    })
                    scope.row.chatroomid = res.data
                  }
                }
              }, '生成聊天室'
            )
          }
        }
      }
    },
    customData: () => {
      return {
        type: "nick"
      }
    }
  },
  headerShow: {
    playUrl: () => {
      return {
        type: 'tip',
        text: '点击图标可复制链接'
      }
    },
    name: () => {
      return {
        type: 'tip',
        text: '点击可跳转直播链接'
      }
    },
    m3u8PlayUrl: () => {
      return {
        type: 'tip',
        text: '点击图标可复制链接'
      }
    },
    pushUrl: () => {
      return {
        type: 'tip',
        text: '点击图标可复制链接'
      }
    },
    // examineStatus: () => {
    //   return {
    //     type: 'dropdown',
    //     icon: 'icon-funnel',
    //     options: [
    //       { label: '全部', value: null },
    //       { label: '待审核', value: 0 },
    //       { label: '审核通过', value: 1 },
    //       { label: '已暂停', value: 2 },
    //     ],
    //     operation: 'query'
    //   }
    // },
  }
}

export default scopeConfig;
