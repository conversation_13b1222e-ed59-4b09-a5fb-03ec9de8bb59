<template>
	<ms-table
		:currentPage="searchParams.pageIndex"
		:loading="loading"
		:operationButtons="domConfig.tableButtons"
		:pageSize="searchParams.pageSize"
		:scopeConfig="scopeConfig.show"
    :scopeHeader="scopeConfig.headerShow"
		:tableData="list"
		:tableHeader="domConfig.tableHeader"
		:total="total"
    :showSelection="true"
		@current-change="current_change"
		@operation-change="operation_change"
		@size-change="size_change"
    @header-operation="header_operation"
    @handleSelectionChange="handleSelectionChange"
    :rowColor="rowColor"
	>
    <!-- 列表搜索去区域插槽 -->
		<template slot="ms-table-header">
			<div class="slot-search">
				<template v-for="(searchItem, key) in domConfig.listSearch">
					<component
						:index="searchItem.index || ''"
						:is="searchItem.component"
						:key="key"
            :width="searchItem.width || '150px'"
            :model.sync="searchParams[searchItem.model]"
            :label="searchItem.label"
						:operation="searchItem.operation || ''"
						:options="searchItem.options || []"
						:placeholder="searchItem.placeholder || ''"
						:type="searchItem.type || ''"
					></component>
				</template>
        <div class="inlineBlock">
          <el-button @click="handleClick('query')" type="primary" plain icon="el-icon-search">查询</el-button>
          <el-button @click="handleClick('reset')">重置</el-button>
        </div>
			</div>
      <div class="slot-button-article flex-row">
        <div>
          <template v-for="(item, index) in domConfig.soltButtons" >
            <el-button :key="index" :type="item.type" :icon="item.icon" @click="operation_change({operation: item})" plain>{{ item.label }}</el-button>
          </template>
        </div>
        <div style="color: #999">
          <p>说明：1、列表中的UV按日求和、未去重；</p>
          <p class="text">2、导出的明细中，1个用户只有1条记录，并已将用户的观看时长求和</p>
        </div>
      </div>
			<el-dialog :visible.sync="dialog" 
                 closeable 
                 show-close
                 :close-on-click-modal="false"
                 :width="dialogWidth"
                 :title="dialogTitle">
				<component :is="dialogComponent" 
                   :model="scopeInfo" 
                   :operation="dialogOperation" 
                   @close="dialog = !dialog" 
                   @up-date="init" 
                   v-if="dialog"></component>
			</el-dialog>
      <ms-right-dialog
        :visible.sync="r_dialog"
        :width="dialogWidth"
        :title="dialogTitle"
      >
        <component
          :is="dialogComponent"
          :model="scopeInfo"
          :operation="dialogOperation"
          @close="r_dialog = !r_dialog"
          @up-date="init"
          @import-date="exportPeople"
          v-if="r_dialog"
        ></component>
      </ms-right-dialog>
		</template>
	</ms-table>
</template>

<script>
import liveMixin from "./live-mixin"
import tableMixins  from "../../common/mixins/table"
import serveUrl from '@/store/data/serveUrl.js'
import msLiveImport from "./tool/ms-live-import"
export default {
  name: "ms-medscilive-manage",
  mixins: [tableMixins,liveMixin],
  data () {
    return {
      timer:''
    }
  },
  components:{
    msLiveImport
  },  
  created(){
    this.tipFlag = this.$store.getters.infoListTip.liveTip
    if (this.tipFlag) {
      this.notifyInst = this.$notify({
        position: 'bottom-left',
        dangerouslyUseHTMLString: true,
        message: '<div><p style="color: #E6A23C">黄色栏：直播列表页置顶（至多置顶3条）</p></div>',
        duration: 10000,
        customClass: 'notify-info'
      })
      this.$store.dispatch('SetInfoTip', 'liveTip')
    }
  },  
  mounted() {
   this.timer = setInterval(() => {
      this.apiInit(this.searchParams)
    }, 60000);
  },
  beforeDestroy() {
    if(this.timer){
      clearInterval(this.timer)
    }
  },
  methods: {
    // 导入水军
    exportPeople(){
        this.dialog = false;
        this.r_dialog = false;
    },
    rowColor ({ row }) {
     if (row.recomStartTime) {
        return 'sticky-row'
      } 
      return ''
    },
    apiInit (params) {
      let searchParams = {...params}
      this.api.medsciLiveList(searchParams).then(response => {
        this.loading = false
        this.total = response.totalSize || 0;
        this.list = response.data || []
        if (response.status !== 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
      }).catch(() => this.loading = false)
    },
    operation_change_module(val) {
      if (val.operation.way === 'live-forbid') {
        this.$confirm(`是否禁流直播间（房间号：${val.model.roomNo}）`, {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.api.liveForbid({id: val.model.id}).then(response => {
            if(response.status === 200) {
              this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
            } else {
              this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
            }
            this.init()
          })
        })
      } else if(val.operation.way === 'stopAndRecovery') {
        let type = val.operation.operation == 'stop' ? 0 : 1
        this.$confirm(`是否${type==0?'暂停':'恢复'}直播间（房间号：${val.model.roomNo}），${type==0?'暂停直播后，播放端将看不到直播画面':'恢复直播后，播放端将恢复视频流的播放'}`, {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.api.stopAndRecoveryLive({id: val.model.id,type:type}).then(response => {
            if(response.status === 200) {
              this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
            } else {
              this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
            }
            this.init()
          })
        })
      } else if(val.operation.way === 'export') {
        this.api.exportUserInfoSync(val.model.id).then(response => {
          if(response.status === 200) {
            this.userInfoSyncQuery(response.data.taskId)
            this.PUBLIC_Methods.apiNotify('导出数据处理中，请耐心等待', 'success')
          } else {
            this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
          }
        })
      } else if(val.operation.way === 'previewPage') {
        window.open(`${serveUrl['live']}${val.model.encodeId}?type=previewLive`)
      }

    },
    userInfoSyncQuery(taskId) {
      this.api.userInfoSyncQuery(taskId).then(response => {
        if(response.status === 200) {
          window.location.href = response.data
          this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
        } else if (response.status === 102) {
          let _this = this
          setTimeout(() => {
            _this.userInfoSyncQuery(taskId)
          }, 2000);
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
      })
    }
  }
};
</script>
<style scoped>
.flex-row {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}
.text {
  text-indent: 3em;
}
</style>
