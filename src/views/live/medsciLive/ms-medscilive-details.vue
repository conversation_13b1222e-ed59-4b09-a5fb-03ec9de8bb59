<template>
	<ms-table
		:currentPage="searchParams.pageIndex"
		:loading="loading"
		:operationButtons="detailsConfig.tableButtons"
		:pageSize="searchParams.pageSize"
		:scopeConfig="scopeConfig.show"
    :scopeHeader="scopeConfig.headerShow"
		:tableData="list"
		:tableHeader="detailsConfig.tableHeader"
		:total="total"
		@current-change="current_change"
		@operation-change="operation_change"
		@size-change="size_change"
    @header-operation="header_operation"
    @handleSelectionChange="handleSelectionChange"
    class="table-svg"
	>
	</ms-table>
</template>

<script>
import liveMixin from "./live-mixin"
import tableMixins  from "../../common/mixins/table"
export default {
  name: "ms-yxd-manage",
  mixins: [tableMixins,liveMixin],
  data () {
    return {
    }
  },
  created() {
    this.handleClick('reset')
  },
  methods: {
    apiInit (params) {
      let searchParams = {
        ...params,
        liveId: this.$route.query.id
      }
      this.api.liveUvList(searchParams).then(response => {
        this.loading = false
        this.total = response.totalSize || 0;
        this.list = response.data || []
        if (response.status !== 200) {
          // this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
      }).catch(() => this.loading = false)
    }
  }
};
</script>
