<template>
  <div class="lecture">
    <el-form
          label-width="100px"
          class="rule-form"
          ref="submitRef"
          :model="submitData"
        >
      <el-row>
          <el-form-item label="直播名称" prop="name">
            <el-input
              v-model="model.name"
              disabled
            ></el-input>
          </el-form-item>
          <el-form-item label="商品类型" prop="orderType">
            <el-select v-model="submitData.orderType" placeholder="请选择" style="width:100%" clearable @change="changeOrderType">
              <el-option :key="1" label="课程" :value="1"></el-option>
              <el-option :key="2" label="会员" :value="2"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="商品名称" prop="item" class="flex-item">
            <el-select v-model="submitData.item" value-key="id" filterable :remote="submitData.orderType==1" :remote-method="initVideoList" placeholder="请选择" style="width:100%">
              <template v-if="submitData.orderType==1">
                <el-option  v-for="item in videoList" :key="item.id" :label="item.title" :value="item"></el-option>
              </template>
              <template v-if="submitData.orderType==2">
                <el-option  v-for="item in memberList" :key="item.id" :label="item.cardName" :value="item"></el-option>
              </template>
            </el-select>
            <el-button :disabled="JSON.stringify(submitData.item) === '{}'" style="flex:1;margin-left:10px" type="primary" @click="addContent">添加</el-button>
          </el-form-item>
          <el-form-item label="">
            <el-table ref="dragTable" 
              v-loading="getLoading" 
              row-key="additionId" 
              :data="contentData" 
              :header-cell-style="headerCellStyle" 
              :header-row-style="headerRowStyle" 
              style="width: 100%;border-left: 1px solid #EBEEF5;border-right: 1px solid #EBEEF5">
              <el-table-column align="center" label="序号" type="index" min-width="50px"></el-table-column>
              <el-table-column label="商品类型" min-width="100px">
                <template slot-scope="scope" lign="center">
                  <span v-if="scope.row.itemType==1">课程</span>
                  <span v-if="scope.row.itemType==2">会员</span>
                </template>
              </el-table-column>
              <el-table-column align="center" label="商品名称" min-width="300px">
                <template slot-scope="scope">
                  <span>{{ scope.row.itemTitle }}</span>
                </template>
              </el-table-column>
              <el-table-column align="center" label="操作" min-width="80px">
                <template slot-scope="scope">
                  <el-tooltip effect="dark" content="删除" placement="bottom">
                    <span @click="deleteRow(scope.$index)">
                      <svg-icon class-name="drag-handler" icon-class="icon-shanchu" />
                    </span>
                  </el-tooltip>
                </template>
              </el-table-column>
            </el-table>
            <div class="tips">说明：上下拖动记录，调整展示顺序</div>
          </el-form-item>
      </el-row>
    </el-form>
    <div class="buttons">
      <el-button type="primary" @click="submit">保存</el-button>
      <el-button type="info" @click="close">返回</el-button>
    </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex"
import Sortable from 'sortablejs'
export default {
  name: "ms-setting-product",
  data() {
    return {
      submitData: {
        orderType: "",
        item: {},
      },
      memberList: [],
      videoList: [],
      contentData: [],
      getLoading: false,
      headerCellStyle: {
        "background-color": "#EBEEF5"
      },
      headerRowStyle: {
        "background-color": "#EBEEF5"
      },
    }
  },
  props: {
    model: Object
  },
  computed: {
    ...mapGetters(["info"]),
  },
  created() {
    this.init()
    this.initMemberList()
  },
  methods: {
    init() {
      let id = this.model.id
      if(id !== 0) {
        this.getLoading = true;
        this.api.selectLiveItemList({liveId: id}).then( response => {
          this.getLoading = false;
          if(response.status === 200) {
            this.contentData = response.data
            this.$nextTick(()=>{
              this.setSort()
            })
          } else {
            this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
          }
        }).catch(() => {
          this.getLoading = false;
        })
      } 
    },
    changeOrderType() {
      this.submitData.item = {}
    },
    addContent() {
      // 去重
      if(this.contentData.some(item=>item.itemId==this.submitData.item.id)) {
        this.PUBLIC_Methods.apiNotify("此商品已存在，请勿重复添加", "warning")
        return
      }
      // if(this.contentData.length > 9) {
      //   this.PUBLIC_Methods.apiNotify("最多选择10个商品", "warning")
      //   return
      // }
      let data = {}
      if(this.submitData.orderType == 1) {
        data = {
          itemType: 1,
          itemTitle: this.submitData.item.title,
          itemId: this.submitData.item.id,
          liveId: this.model.id
        }
      }else if (this.submitData.orderType == 2) {
        data = {
          itemType: 2,
          itemTitle: this.submitData.item.cardName,
          itemId: this.submitData.item.id,
          liveId: this.model.id
        }
      }
      this.contentData.push(data)
      this.videoList = []
      this.submitData = {
        orderType: null,
        item: {}
      }
      this.setSort()
    },
    deleteRow(index) {
      this.contentData.splice(index,1)
    },
    setSort() {
      const el = this.$refs.dragTable.$el.querySelectorAll('.el-table__body-wrapper > table > tbody')[0]
      this.sortable = Sortable.create(el, {
        ghostClass: 'sortable-ghost', 
        setData: function(dataTransfer) {
          dataTransfer.setData('Text', '')
        },
        onEnd: evt => {
          let drapObj = this.contentData[evt.oldIndex]
          this.contentData.splice(evt.oldIndex, 1)
          this.contentData.splice(evt.newIndex, 0, drapObj)
          var newArray = this.contentData.slice(0);
          this.contentData = [];
          this.$nextTick(() => {
            newArray.forEach((item)=>{
              this.contentData.push({
                itemType: item.itemType,
                itemTitle: item.itemTitle,
                itemId: item.itemId,
                liveId: this.model.id
              })
            })
          })
        }
      })
    },
    initMemberList() {
      let searchParams = {
        pageIndex: 0,
        pageSize: 99,
        status: 1
      }
      this.api.getCardByCardDurationList(searchParams).then(response => {
        this.memberList = response.data || []
        if (response.status !== 200) {
          this.PUBLIC_Methods.apiNotify(
            response.message || "请求出错",
            "warning"
          )
        }
      })
      .catch((err) => (console.log(err)))
    },
    initVideoList(keyword) {
      if(!keyword || this.submitData.orderType == 2) {
        return
      }
      let searchParams = {
        pageIndex: 1,
        pageSize: 20,
        status: 1,
        permission: 3,
        title: keyword
      }
      this.api.videoPageList(searchParams).then(response => {
        this.videoList = response.data || []
        if (response.status !== 200) {
          this.PUBLIC_Methods.apiNotify(
            response.message || "请求出错",
            "warning"
          )
        }
      })
      .catch((err) => (console.log(err)))
    },
    submit() {
      this.$refs["submitRef"].validate(valid => {
        if (valid) {
          let params = {
            itemList: this.contentData,
            liveId: this.model.id,
            userid: this.info.userId,
            userName: this.info.userName
          }
          this.api.updateLiveItemList(params).then(response => {
            if (response.status === 200) {
              this.PUBLIC_Methods.apiNotify(
                response.message || "设置成功",
                "success"
              )
              this.close()
              this.$emit("up-date")
            } else {
              this.PUBLIC_Methods.apiNotify(
                response.message || "设置失败",
                "warning"
              )
            }
          })
        }
      })
    },
    close() {
      this.$emit("close");
    },
  },
}
</script>

<style scoped>
.buttons {
  position: fixed;
  bottom: 20px;
  right: 20px;
}
.hint{
  color: rgba(0,0,0,0.4);
}
.flex-item /deep/ .el-form-item__content {
  display: flex;
  flex-direction: row;
}
.tips {
  font-size: 14px;
  margin-top: 6px;
  color: #999;
  text-align: right;
}
</style>
