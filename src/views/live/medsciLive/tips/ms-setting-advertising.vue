<template>
  <div class="lecture" v-loading="getLoading">
    <el-form
          label-width="80px"
          class="rule-form"
          ref="submitRef"
          :rules="rules"
          :model="submitData"
        >
      <el-row>
          <el-form-item label="直播名称" prop="name">
            <el-input
              v-model="submitData.name"
              disabled
            ></el-input>
          </el-form-item>
          <el-form-item label="设置公告" prop="notice">
            <ms-editor style="width:99.5%" v-model="submitData.notice" :height="280"></ms-editor>
          </el-form-item>
      </el-row>
    </el-form>
    <div class="buttons">
      <el-button type="primary" @click="submit">保存</el-button>
      <el-button type="info" @click="close">返回</el-button>
    </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex"
import MsEditor from '@/components/MsEditor'
export default {
  name: "ms-setting-advertising",
  data() {
    return {
      submitData: {},
      getLoading: false,
      rules: {
        notice: [
          { required: true, message: "请输入公告", trigger: 'blur' },
        ],
      },

    }
  },
  components: {
    MsEditor
  },
  props: {
    model: Object
  },
  computed: {
    ...mapGetters(["info"]),
  },
  created() {
    this.init()
  },
  methods: {
    init() {
      let id = this.model.id
      if(id !== 0) {
        this.getLoading = true;
        this.submitData.id = id;
        this.api.medsciLiveById(id).then( response => {
          this.getLoading = false;
          if(response.status === 200) {
            let res = response.data
            this.submitData = {
              ...this.submitData,
              ...res,
              notice: this.PUBLIC_Methods.unexcapeHtml(res.notice),
            }
          } else {
            this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
          }
        }).catch(() => {
          this.getLoading = false;
        })
      } 
    },
    submit() {
      this.$refs["submitRef"].validate(valid => {
        if (valid) {
          let params = {
            ...this.submitData,
            userid: this.info.userId,
            userName: this.info.userName,
            notice: this.PUBLIC_Methods.excapeHtml(this.submitData.notice),
            content: this.PUBLIC_Methods.excapeHtml(this.submitData.content)
          }
          this.api.medsciLiveUpdate(params).then(response => {
            if (response.status === 200) {
              this.PUBLIC_Methods.apiNotify(
                response.message || "设置成功",
                "success"
              )
              this.close()
              this.$emit("up-date")
            } else {
              this.PUBLIC_Methods.apiNotify(
                response.message || "设置失败",
                "warning"
              )
            }
          })
        }
      })
    },
    close() {
      this.$emit("close");
    },
  },
}
</script>

<style scoped>
.buttons {
  position: fixed;
  bottom: 20px;
  right: 20px;
}
.hint{
  color: rgba(0,0,0,0.4);
}
.flex-item /deep/ .el-form-item__content {
  display: flex;
  flex-direction: row;
}
.tips {
  font-size: 14px;
  margin-top: 6px;
  color: #999;
  text-align: right;
}
.lecture /deep/ .mce-tinymce {
  width: auto;
}
</style>
