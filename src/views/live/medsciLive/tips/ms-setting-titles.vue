<template>
  <div class="lecture" v-loading="getLoading">
    <el-form
          label-width="100px"
          class="rule-form"
          ref="submitRef"
          :rules="rules"
          :model="submitData"
        >
      <el-row>
          <el-form-item label="直播名称" prop="name">
            <el-input
              v-model="submitData.name"
              disabled
            ></el-input>
          </el-form-item>
          <el-form-item label="选择片头视频" prop="video">
            <ms-video-search style="width:100%" :model.sync="submitData.preVideoName" @getNodeData="getNodeData"></ms-video-search>
          </el-form-item>
           <el-form-item label="已选片头视频" prop="preVideo" v-if="submitData.preVideoName && submitData.preVideo" class="flex-item">
            <el-input
              v-model="submitData.preVideoName"
              disabled
            ></el-input>
            <el-button v-if="submitData.preVideo" style="flex:1;margin-left:10px" type="primary" @click="previewPeriodOrVideo">预览</el-button>
          </el-form-item>
          <p class="tips">需要先将视频上传至视频素材</p>
      </el-row>
    </el-form>
    <el-dialog 
          v-if="dialog"
          :visible.sync="dialog"
          closeable 
          show-close
          append-to-body
          :close-on-click-modal="false"
          width="60%"
          title="片头视频预览">
          <component :is="msVideoPreview" 
                    :model="submitData" 
                    @close="dialog = !dialog" 
          ></component>
    </el-dialog>
    <div class="buttons">
      <el-button type="primary" @click="submit">保存</el-button>
      <el-button type="info" @click="close">返回</el-button>
    </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex"
import MsVideoSearch from '@/components/MsCommon/ms-video-search'
import msVideoPreview from './ms-video-preview'
export default {
  name: "ms-setting-titles",
  data() {
    return {
      dialog: false,
      msVideoPreview,
      submitData: {},
      getLoading: false,
      rules: {
        preVideo: [
          { required: true, message: "请选择片头视频", trigger: 'blur' },
        ],
      }
    }
  },
  components: {
    MsVideoSearch
  },
  props: {
    model: Object
  },
  computed: {
    ...mapGetters(["info"]),
  },
  created() {
    this.init()
  },
  methods: {
    init() {
      let id = this.model.id
      if(id !== 0) {
        this.getLoading = true;
        this.submitData.id = id;
        this.api.medsciLiveById(id).then( response => {
          this.getLoading = false;
          if(response.status === 200) {
            let res = response.data
            this.submitData = {
              ...this.submitData,
              ...res,
            }
          } else {
            this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
          }
        }).catch(() => {
          this.getLoading = false;
        })
      } 
    },
    // 获取选择视频的参数
    getNodeData(val) {
      this.submitData.preVideo = val.videoUrl;
      this.submitData.preVideoName = val.title;
    },
    // 预览视频
    previewPeriodOrVideo() {
      this.dialog = true;
    },
    submit() {
      this.$refs["submitRef"].validate(valid => {
        if (valid) {
          let params = {
            ...this.submitData,
            userid: this.info.userId,
            userName: this.info.userName,
            preVideo: this.submitData.preVideo ? this.submitData.preVideo : '',
            preVideoName: this.submitData.preVideoName ? this.submitData.preVideoName : '',
            notice: this.PUBLIC_Methods.excapeHtml(this.submitData.notice),
            content: this.PUBLIC_Methods.excapeHtml(this.submitData.content)
          }
          this.api.medsciLiveUpdate(params).then(response => {
            if (response.status === 200) {
              this.PUBLIC_Methods.apiNotify(
                response.message || "设置成功",
                "success"
              )
              this.close()
              this.$emit("up-date")
            } else {
              this.PUBLIC_Methods.apiNotify(
                response.message || "设置失败",
                "warning"
              )
            }
          })
        }
      })
    },
    close() {
      this.$emit("close");
    },
  },
}
</script>

<style scoped>
.buttons {
  position: fixed;
  bottom: 20px;
  right: 20px;
}
.hint{
  color: rgba(0,0,0,0.4);
}
.flex-item /deep/ .el-form-item__content {
  display: flex;
  flex-direction: row;
}
.tips {
  font-size: 14px;
  margin-top: 6px;
  color: #999;
  text-align: right;
}
</style>
