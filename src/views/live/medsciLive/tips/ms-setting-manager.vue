<template>
  <div class="lecture" v-loading="getLoading">
    <el-form
          label-width="100px"
          class="rule-form"
          ref="submitRef"
          :model="submitData"
        >
      <el-row>
          <el-form-item label="直播名称" prop="name">
            <el-input
              v-model="model.name"
              disabled
            ></el-input>
          </el-form-item>
          <el-form-item label="选择管理员" class="flex-item">
            <el-select style="width:100%" v-model="userData" value-key="id" :loading="getLoading" filterable remote :remote-method="getUser" placeholder="请输入管理员手机号码并添加">
              <el-option
                v-for="item in userList"
                :key="item.id"
                :label="item.userName"
                :value="item">
              </el-option>
            </el-select>
            <el-button style="flex:1;margin-left:10px" :disabled="JSON.stringify(userData) === '{}'" type="primary" @click="addContent(1)">添加</el-button>
            <el-button style="flex:1;margin-left:10px" :disabled="JSON.stringify(userData) === '{}'" type="primary" @click="addContent(2)">添加全局</el-button>
          </el-form-item>
          <el-form-item label="已选择">
            <el-table ref="dragTable" 
              v-loading="getLoading" 
              row-key="additionId" 
              :data="contentData" 
              :header-cell-style="headerCellStyle" 
              :header-row-style="headerRowStyle" 
              style="width: 100%;border-left: 1px solid #EBEEF5;border-right: 1px solid #EBEEF5">
              <el-table-column align="center" label="序号" type="index" min-width="50px"></el-table-column>
              <el-table-column align="center" label="用户名" min-width="200px">
                <template slot-scope="scope">
                  <span>{{ scope.row.userName }}</span>
                </template>
              </el-table-column>
              <el-table-column label="管理范围" min-width="100px">
                <template slot-scope="scope">
                  <span v-if="scope.row.authScope==1">本场直播</span>
                  <span v-if="scope.row.authScope==2">全局直播</span>
                </template>
              </el-table-column>
              <el-table-column align="center" label="操作" min-width="80px">
                <template slot-scope="scope">
                  <el-tooltip effect="dark" content="删除" placement="bottom">
                    <span @click="deleteRow(scope.row.id)">
                      <svg-icon class-name="drag-handler" icon-class="icon-shanchu" />
                    </span>
                  </el-tooltip>
                </template>
              </el-table-column>
            </el-table>
            <div class="tips">说明：上下拖动记录，调整展示顺序</div>
          </el-form-item>
      </el-row>
    </el-form>
    <div class="buttons">
      <el-button type="primary" @click="submit">保存</el-button>
      <el-button type="info" @click="close">返回</el-button>
    </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex"
import Sortable from 'sortablejs'
export default {
  name: "ms-setting-product",
  data() {
    return {
      submitData: {},
      contentData: [],
      getLoading: false,
      headerCellStyle: {
        "background-color": "#EBEEF5"
      },
      headerRowStyle: {
        "background-color": "#EBEEF5"
      },
      userData: {},
      userList: []
    }
  },
  props: {
    model: Object
  },
  computed: {
    ...mapGetters(["info"]),
  },
  created() {
    this.init()
    this.getUser()
  },
  methods: {
    init() {
      this.contentData = []
      let id = this.model.id
      if(id !== 0) {
        this.getLoading = true;
        let params = {
          liveId: id,
          operationType: 1,
          pageIndex: 1,
          pageSize: 99
        }
        this.api.getLiveUserAuthList(params).then( response => {
          this.getLoading = false;
          if(response.status === 200) {
            this.contentData = response.data
            this.$nextTick(()=>{
              this.setSort()
            })
          } else {
            this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
          }
        }).catch(() => {
          this.getLoading = false;
        })
      } 
    },
    check(val) {
      if ((/^1[3456789]\d{9}$/.test(val))) {
        return true
      }else {
        return false
      }
    },
    getUser(keyWord) {
      this.userList = []
      if(!(keyWord && this.check(keyWord))) {
        return
      }
      this.loading = true;
      let searchParams = { mobile:keyWord,pageSize: 99,pageIndex:1 };
      this.api
        .listMedsciUsers(searchParams)
        .then(response => {
          this.loading = false;
          this.userList = response.data || [];
          if (response.status !== 200) {
            this.PUBLIC_Methods.apiNotify(
              response.message || "请求出错",
              "warning"
            );
          }
        })
        .catch(() => (this.loading = false));
    },
    addContent(type) {
      this.getLoading = true
      let data = {
        authScope: type,
        liveId: this.model.id,
        chatroomid: this.model.chatroomid,
        operationType: 1,
        userId: this.userData.id,
        userName: this.userData.userName,
      }
      this.api
        .insertLiveUserAuth(data)
        .then(response => {
          this.getLoading = false
          if (response.status !== 200) {
            this.PUBLIC_Methods.apiNotify(
              response.message || "请求出错",
              "warning"
            );
          }else {
            this.init()
          }
        })
        .catch(() => (this.getLoading = false));
    },
    deleteRow(id) {
      this.getLoading = true
      this.api
        .remonveLiveUserAuth({id: id,chatroomid: this.model.chatroomid})
        .then(response => {
          this.getLoading = false
          if (response.status !== 200) {
            this.PUBLIC_Methods.apiNotify(
              response.message || "请求出错",
              "warning"
            );
          }else {
            this.init()
          }
        })
        .catch(() => (this.getLoading = false));
    },
    setSort() {
      const el = this.$refs.dragTable.$el.querySelectorAll('.el-table__body-wrapper > table > tbody')[0]
      this.sortable = Sortable.create(el, {
        ghostClass: 'sortable-ghost', 
        setData: function(dataTransfer) {
          dataTransfer.setData('Text', '')
        },
        onEnd: evt => {
          let drapObj = this.contentData[evt.oldIndex]
          this.contentData.splice(evt.oldIndex, 1)
          this.contentData.splice(evt.newIndex, 0, drapObj)
          var newArray = this.contentData.slice(0);
          this.contentData = [];
          this.$nextTick(() => {
            newArray.forEach((item)=>{
              this.contentData.push({
                id: item.id,
                authScope: item.authScope,
                operationType: item.operationType,
                userId: item.userId,
                userName: item.userName,
                chatroomid: this.model.chatroomid,
                liveId: this.model.id
              })
            })
          })
        }
      })
    },
    submit() {
      this.close()
    },
    close() {
      this.$emit("close");
    },
  },
}
</script>

<style scoped>
.buttons {
  position: fixed;
  bottom: 20px;
  right: 20px;
}
.hint{
  color: rgba(0,0,0,0.4);
}
.flex-item /deep/ .el-form-item__content {
  display: flex;
  flex-direction: row;
}
.tips {
  font-size: 14px;
  margin-top: 6px;
  color: #999;
  text-align: right;
}
</style>
