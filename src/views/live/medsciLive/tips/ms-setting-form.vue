<template>
  <div class="lecture">
    <el-form
          label-width="100px"
          class="rule-form"
          ref="submitRef"
          :model="submitData"
        >
      <el-row>
          <el-form-item label="直播名称" prop="name">
            <el-input
              v-model="model.name"
              disabled
            ></el-input>
          </el-form-item>
          <el-form-item label="直播问卷" prop="item" class="flex-item">
            <ms-survey-search v-model="templateName" :model.sync="templateName" @bindData="bindForm" :placeholder="'请输入关键词搜索，并选择'" :isOpenResult="0" style="width:100%"></ms-survey-search>
            <el-button :disabled="JSON.stringify(submitData) === '{}' || (model.interactionType == 2 && contentData.length >= 1)" style="flex:1;margin-left:10px" type="primary" @click="addContent">添加</el-button>
          </el-form-item>
          <el-form-item label="" v-if="!templateName">
            没搜到？<el-button @click="createForm" type="primary">去创建</el-button>
          </el-form-item>
          <el-form-item  label="交互方式" class="flex-item interaction">
            <el-radio-group v-model="model.interactionType">
              <el-radio :label="0">弹窗 系统自动弹出问卷正文</el-radio>
              <el-radio :label="1">漂浮入口  图标icon漂浮在直播详情页右侧</el-radio>
              <el-radio :label="2">独立调研详情页</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="">
            <el-table ref="dragTable" 
              v-loading="getLoading" 
              row-key="additionId" 
              :data="contentData" 
              :header-cell-style="headerCellStyle" 
              :header-row-style="headerRowStyle" 
              style="width: 100%;border-left: 1px solid #EBEEF5;border-right: 1px solid #EBEEF5">
              <el-table-column align="center" label="序号" type="index" min-width="50px"></el-table-column>
              <el-table-column align="center" label="调研问卷名称" min-width="120px">
                <template slot-scope="scope">
                  <span>{{ scope.row.templateName }}</span>
                </template>
              </el-table-column>
              <el-table-column align="center" label="上线时间" min-width="350px">
                <template slot-scope="scope">
                  <el-date-picker
                    style="width: 100%"
                    v-model="scope.row.timeRange"
                    @input="(val) => changeDate(val, scope.$index)"
                    :picker-options="startDatePicker"
                    type="datetimerange"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期">
                  </el-date-picker>
                </template>
              </el-table-column>
              <el-table-column align="center" label="操作" min-width="80px">
                <template slot-scope="scope">
                  <el-tooltip effect="dark" content="删除" placement="bottom">
                    <span @click="deleteRow(scope.$index)">
                      <svg-icon class-name="drag-handler" icon-class="icon-shanchu" />
                    </span>
                  </el-tooltip>
                </template>
              </el-table-column>
            </el-table>
            <div class="tips">温馨提示：上下拖动可排序</div>
          </el-form-item>
      </el-row>
    </el-form>
    <div class="buttons">
      <el-button type="primary" @click="submit">保存</el-button>
      <el-button type="info" @click="close">返回</el-button>
    </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex"
import Sortable from 'sortablejs'
import MsSurveySearch from '@/components/MsCommon/ms-survey-search'
export default {
  name: "ms-setting-form",
  data() {
    return {
      submitData: {},
      templateName: '',
      contentData: [],
      getLoading: false,
      startDatePicker: this.beginDate(),
      headerCellStyle: {
        "background-color": "#EBEEF5"
      },
      headerRowStyle: {
        "background-color": "#EBEEF5"
      },
    }
  },
  props: {
    model: Object
  },
  computed: {
    ...mapGetters(["info"]),
  },
  components: {
    MsSurveySearch
  },
  created() {
    this.init()
    console.log(this.model);
  },
  methods: {
    init() {
      let id = this.model.id
      if(id !== 0) {
        if(this.model.liveSurveyDtoList && this.model.liveSurveyDtoList.length) {
          this.model.liveSurveyDtoList.forEach(item=>{
            item.timeRange = [item.openStartTime, item.openEndTime]
          })
          this.contentData = this.model.liveSurveyDtoList.filter(item=>item.isOpenResult===0)
          console.log(this.model.liveSurveyDtoList,'66');
        }
        this.$nextTick(()=>{
          this.setSort()
        })
      } 
    },
    createForm() {
      this.$router.push('/form-operation?operation=created')
    },
    beginDate(){
			const self = this
			return {
				disabledDate(time){
					return new Date(self.model.detailStartTime).getTime() > time.getTime() || new Date(self.model.detailEndTime).getTime() < time.getTime()
				}
			}
		},
    changeDate(val, index) {
      let item = {
        id: this.contentData[index].id,
        templateName: this.contentData[index].templateName,
        timeRange: val,
        openStartTime: val[0],
        openEndTime: val[1],
        isOpenResult:0
      }
      this.$set(this.contentData, index, item)
    },
    bindForm(val) {
      this.submitData.id = val.model.id
      this.submitData.templateName = val.model.templateName
    },
    addContent() {
      // 去重
      // if(this.contentData.some(item=>item.id==this.submitData.id)) {
      //   this.PUBLIC_Methods.apiNotify("此问卷已存在，请勿重复添加", "warning")
      //   return
      // }
      let data = {
        id: this.submitData.id,
        templateName: this.submitData.templateName,
        timeRange: [],
        openStartTime: '',
        openEndTime: '',
        isOpenResult:0
      }
      this.contentData.push(data)
      this.submitData = {}
      this.templateName = null
      this.setSort()
    },
    deleteRow(index) {
      this.contentData.splice(index,1)
    },
    setSort() {
      const el = this.$refs.dragTable.$el.querySelectorAll('.el-table__body-wrapper > table > tbody')[0]
      this.sortable = Sortable.create(el, {
        ghostClass: 'sortable-ghost', 
        setData: function(dataTransfer) {
          dataTransfer.setData('Text', '')
        },
        onEnd: evt => {
          let drapObj = this.contentData[evt.oldIndex]
          this.contentData.splice(evt.oldIndex, 1)
          this.contentData.splice(evt.newIndex, 0, drapObj)
          var newArray = this.contentData.slice(0);
          this.contentData = [];
          this.$nextTick(() => {
            newArray.forEach((item)=>{
              this.contentData.push({
                id: item.id,
                templateName: item.templateName,
                openStartTime: item.openStartTime,
                openEndTime: item.openEndTime,
                timeRange: item.timeRange,
                isOpenResult:0
              })
            })
          })
        }
      })
    },
    submit() {
      this.$refs["submitRef"].validate(valid => {
        if (valid) {
          console.log(this.model,'11');
          let liveSurveyDtoList = []
          if(this.model.liveSurveyDtoList && this.model.liveSurveyDtoList.length) {
            liveSurveyDtoList = [...this.contentData,...this.model.liveSurveyDtoList.filter(item=>item.isOpenResult===1)]
          }else {
            liveSurveyDtoList = [...this.contentData]
          }
          let params = {
            liveSurveyDtoList: liveSurveyDtoList,
            liveId: this.model.id,
            interactionType:this.model.interactionType
          }
          this.api.insertSurvey(params).then(response => {
            if (response.status === 200) {
              this.PUBLIC_Methods.apiNotify(
                response.message || "设置成功",
                "success"
              )
              this.close()
              this.$emit("up-date")
            } else {
              this.PUBLIC_Methods.apiNotify(
                response.message || "设置失败",
                "warning"
              )
            }
          })
        }
      })
    },
    close() {
      this.$emit("close");
    },
  },
}
</script>

<style scoped>
.buttons {
  position: fixed;
  bottom: 20px;
  right: 20px;
}
.hint{
  color: rgba(0,0,0,0.4);
}
.flex-item /deep/ .el-form-item__content {
  display: flex;
  flex-direction: row;
}
.tips {
  font-size: 14px;
  margin-top: 6px;
  color: #999;
  text-align: right;
}
.interaction /deep/ .el-form-item__label{
   line-height: 16px;
}
.interaction /deep/ .el-radio-group{
  height: 100px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
</style>
