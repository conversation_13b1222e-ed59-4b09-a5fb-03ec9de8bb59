<template>
  <section>
    <el-form ref="submitRef"
          class="rule-form"
          :model="submitData"
          :rules="rules"
          label-width="100px">
      <el-row :gutter="20">
        <el-col :span="18">
          <el-row>
            <el-col :span="12">
              <el-form-item label="直播名称" prop="name">
                <el-input v-model="submitData.name" style="width: 100%" placeholder="请输入100个字符以内的文本"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="直播开始时间" prop="liveStartTime">
                <!-- <ms-picker :model.sync="submitData.liveStartTime" type="datetime" style="width: 100%" :disabled="startTimeDisabled"></ms-picker> -->
                <ms-picker :model.sync="submitData.liveStartTime" type="datetime" style="width: 100%"></ms-picker>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="直播类型" prop="categoryList">
                <ms-livecategory-cascader :model.sync="submitData.categoryList" :multiple="true"></ms-livecategory-cascader>
              </el-form-item>
            </el-col>
            <!-- <el-col :span="12">
              <el-form-item label="直播分类" prop="sndCategoryList">
                <ms-category-cascader :model.sync="submitData.sndCategoryList" :multiple="true" :config="{moduleName: 'course'}"></ms-category-cascader>
              </el-form-item>
            </el-col> -->
            <el-col :span="12">
              <el-form-item label="详情页上线时间" prop="detailTime">
                <ms-picker :model.sync="submitData.detailTime" type="datetimerange" style="width: 100%"></ms-picker>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12" v-if="!!dataId">
              <el-form-item label="直播回放地址" prop="replayUrl">
                <el-input v-model="submitData.replayUrl" style="width: 100%" placeholder="请输入腾讯云控制台生成的直播回放地址">
                  <template slot="append">
                    <el-button type="primary" @click="getLiveUrl">获取地址</el-button>
                  </template>
                </el-input>
              </el-form-item>
            </el-col>

            <el-col :span="12" v-if="!!dataId">
              <el-form-item label="直播房间号" prop="replayUrl">
                <el-input v-model="submitData.roomNo" style="width: 100%" disabled></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row v-if="submitData.status == 3 && submitData.outlinkDTO">
            <el-col :span="12">
              <el-form-item label="外向地址" class="flex-item">
                <el-select  style="width:100%" v-model="submitData.outlinkDTO.type" clearable placeholder="请选择类型" @change="changeType">
                  <el-option :label="'视频'" :value="1"></el-option>
                  <el-option :label="'课程'" :value="2"></el-option>
                  <el-option :label="'其他'" :value="3"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="" label-width="20px" class="flex-item">
                <el-input v-if="submitData.outlinkDTO.type == 3" style="width:100%" v-model="submitData.outlinkDTO.url" :placeholder="'请手动输入跳转地址'"></el-input>
                <el-select v-else style="width:100%" v-model="submitData.outlinkDTO.title" value-key="id" clearable filterable remote :remote-method="getCourse" @change="changeCourse" placeholder="请输入课程标题搜索选择">
                  <el-option
                    v-for="item in courseList"
                    :key="item.id"
                    :label="item.title"
                    :value="item.title">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="直播描述" prop="description">
                <el-input v-model="submitData.description" style="width: 100%" type="textarea" :rows="4" placeholder="请输入200个字符以内的文本"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="用户须知" prop="userNoticeStatus">
                <el-radio-group v-model="submitData.userNoticeStatus" @change="changeUserNoticeStatus">
                  <el-radio :label="1">开启须知</el-radio>
                  <el-radio :label="0">关闭须知</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row v-if="submitData.userNoticeStatus == 1">
            <el-col :span="24">
              <el-form-item label="" prop="userNoticeContent">
                <el-input v-model="submitData.userNoticeContent" class="xuzhi" style="width: 100%" type="textarea" :rows="4" placeholder="请输入"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="详情介绍" prop="content">
                <ms-editor v-model="submitData.content" :height="280"></ms-editor>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="tab名称" prop="tabName">
                <el-input v-model="submitData.tabName" style="width: 100%" placeholder="请输入"></el-input>
              </el-form-item>
            </el-col> 
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="内容" prop="tabContent">
                <ms-editor v-model="submitData.tabContent" :height="280"></ms-editor>
              </el-form-item>
            </el-col>
          </el-row>
        </el-col>
      </el-row>
    </el-form>
  </section>
</template>

<script>
import MsEditor from '@/components/MsEditor'
import MsLivecategoryCascader from '@/components/MsCommon/ms-livecategory-cascader'
export default {
	name: "ms-live-content",
	data () {
		return {
      dataId: this.$route.query.id || 0,
      rules: {
        name: [
          { required: true, message: "请输入直播名称", trigger: 'blur' },
          { max: 100, message: '请输入100个字符以内的文本', trigger: 'blur' }
        ],
        categoryList: [
          { required: true, message: "请选择一级分类", trigger: 'blur' }
        ],
        liveStartTime: [
          { required: true, message: "请选择直播开始时间", trigger: 'blur' }
        ],
        description: [
          { max: 200, message: '请输入200个字符以内的文本', trigger: 'blur' }
        ],
        content: [
          { required: true, message: "请输入直播详情", trigger: 'blur' }
        ],
        replayUrl: [
          { validator: (rule,value,callback) => {
            if (value !== '' && value.indexOf('https')) {
              callback(new Error('请填写安全域名'));
            } else {
              callback();
            }
          }, trigger: 'blur'}
        ],
        userNoticeContent: [
          { validator: (rule,value,callback) => {
            console.log(this.submitData, 'vvvvv')
            console.log(value, 'vvvvv')
            console.log(this.submitData.userNoticeStatus, 'vvvvv')
            if (!value && this.submitData.userNoticeStatus == 1) {
              callback(new Error('请填写用户须知'));
            } else {
              callback();
            }
          }, trigger: 'blur'}
        ],
      },
      courseList: []
		}
  },
  components: {
    MsEditor,
    MsLivecategoryCascader,
  },
  props:["submitData"],
  mounted() {
    if(this.dataId) {
      this.getCourse(this.submitData.outlinkDTO.title)
    }
  },
	methods: {
    changeUserNoticeStatus(val) {
      console.log(val, 'vvvvvvvvv')
      // if(val == 1) {
      //   this.submitData.userNoticeStatus = 1
      // }
    },
    getLiveUrl() {
      if(this.submitData.replayStatus == 0) {
        this.PUBLIC_Methods.apiNotify(
          "请先在直播设置中开启直播回放",
          "warning"
        )
        return
      }
      let searchParams = {
        id: this.submitData.id
      }
      this.api.medsciLivePlayback(searchParams).then(response => {
        if (response.status !== 200) {
          this.PUBLIC_Methods.apiNotify(
            response.message || "请求出错",
            "warning"
          )
        } else {
          if(!response.data.replayUrl) {
            this.PUBLIC_Methods.apiNotify(
              "直播回放地址还在生成中...",
              "warning"
            )
          }
          this.submitData.replayUrl = response.data.replayUrl || ''
        }
      })
      .catch((err) => (console.log(err)))
    },
    getCourse(keyword) {
      if(!keyword) {
        return
      }
      let searchParams = {
        pageIndex: 1,
        pageSize: 20,
        status: 1,
        type: this.submitData.outlinkDTO.type,
        title: keyword
      }
      this.api.videoPageList(searchParams).then(response => {
        this.courseList = response.data || []
        if (response.status !== 200) {
          this.PUBLIC_Methods.apiNotify(
            response.message || "请求出错",
            "warning"
          )
        }
      })
      .catch((err) => (console.log(err)))
    },
    changeCourse(item) {
      this.submitData.outlinkDTO.url = this.courseList.filter(row=>{return row.title==item})[0].url
      this.submitData.outlinkDTO.objectId = this.courseList.filter(row=>{return row.title==item})[0].id
    },
    changeType() {
      this.submitData.outlinkDTO.url = ''
      this.submitData.outlinkDTO.title = ''
      this.submitData.outlinkDTO.objectId = null
    },
    // 数据校验
    validateData() {
      return new Promise((resolve) => {
        this.$refs["submitRef"].validate( valid => {
          if (valid) {
            resolve()
          }else {
            this.$emit('changeTab','content')
          }
        })
      })
    }
	}
}
</script>
<style scoped>
.flex-item /deep/ .el-form-item__content {
  display: flex;
  flex-direction: row;
}
.tips {
  font-size: 14px;
  margin-top: 6px;
  color: #999;
  text-align: right;
}
.xuzhi::before{
  content: '*';
  position: absolute;
  left: -12px;
  color: red;
}
</style>