<template>
  <section>
    <el-form ref="submitRef"
             class="rule-form"
             :model="submitData"
             :rules="rules"
             label-width="150px">
      <el-row :gutter="20">
        <el-col :span="20">
          <el-row>
            <el-col :span="12">
              <el-form-item label="直播属性">
                <el-radio-group v-model="submitData.liveType" :disabled="!!dataId">
                  <el-radio :label="0">普通直播</el-radio>
                  <!-- <el-radio :label="1">互动直播</el-radio> -->
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <div class="flex-tips ">
                <el-form-item label="观看属性" >
                <el-radio-group v-model="submitData.watchType" @change="changeWatchType">
                  <el-radio :label="0">开放观看</el-radio>
                  <el-radio :label="1">专享观看</el-radio>
                </el-radio-group>
              </el-form-item>
              <div class="tips" v-if="submitData.watchType === 1">
                  <div>1、指定科室观看，科室不匹配无法观看，没有科室信息需要走认证流程；</div>
                  <div>2、已认证成功才能观看，没认证需要走认证流程；</div>
                  <div>3、两个同时设置时，科室+已认证需同时满足才能看。</div>
                </div>
              </div>
            </el-col>

            <el-col :span="12" v-if="submitData.watchType == 1">
            </el-col>
            <el-col :span="12" v-if="submitData.watchType == 1">
              <el-form-item label="限制科室">
                <ms-department-search style="width: 100%" :model.sync="departmentList" @bindData="bindDepartment" :level='2' :multiple="true" :placeholder="'请选择科室'"></ms-department-search>
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="submitData.watchType == 1">
            </el-col>
            <el-col :span="12" v-if="submitData.watchType == 1">
              <el-form-item label="限制认证">
                <el-switch :active-value="1" :inactive-value="0" v-model="submitData.limitAuth"></el-switch>
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="submitData.watchType == 1">
            </el-col>
            <el-col :span="12" v-if="submitData.watchType == 1">
              <el-form-item label="限制会员">
                <el-radio-group v-model="submitData.limitMemberStatus">
                  <el-radio :label="0">不限制</el-radio>
                  <el-radio :label="1">限制</el-radio>
                </el-radio-group>
                <el-radio-group v-model="limitMember" v-if="submitData.limitMemberStatus == 1" class="limit-wrap" @change="changeLimitMember">
                  <div v-for="(item,index) in memberData"
                       style="margin: 4px 20px 0 0;"
                       :key="'m'+index">
                    <el-radio :label="item.id">{{item.cardName}}</el-radio>
                  </div>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="submitData.watchType == 1">
            </el-col>
            <el-col :span="12" v-if="submitData.watchType == 1">
              <el-form-item label="限制付费">
                <el-radio-group v-model="submitData.limitPayStatus">
                  <el-radio :label="0">不限制</el-radio>
                  <el-radio :label="1">限制</el-radio>
                </el-radio-group>
                <template v-if="submitData.limitPayStatus == 1">
                  <el-select
                    style="margin-left: 16px;width: 300px;"
                    v-model="limitPay"
                    filterable
                    remote
                    reserve-keyword
                    placeholder="请输入课程关键词下拉选择课程"
                    :remote-method="getCoureByTitle"
                    @change="changeLimitPay"
                    :loading="loading">
                    <el-option
                      v-for="item in payData"
                      :key="'pay'+item.id"
                      :label="item.title"
                      :value="item.id">
                    </el-option>
                  </el-select>
                </template>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item required label="主播、嘉宾">
                <el-link type="primary" @click="handle_click('member')">设置</el-link>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="关键词" prop="tagList" style="width: 100%">
                <MsTagSearch v-model="submitData.tagList" :notMul="false"></MsTagSearch>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="显示选项">
                <el-checkbox v-model="submitData.isPc" :true-label="1" :false-label="0">官网首页</el-checkbox>
                <el-checkbox v-model="submitData.isApp" :true-label="1" :false-label="0">APP</el-checkbox>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="H5唤醒app" class="flex-item">
                <el-radio-group v-model="submitData.appWatchSwitch">
                  <el-radio :label="0">不显示</el-radio>
                  <el-radio :label="1">显示</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="推至首页">
                <el-switch :active-value="1" :inactive-value="0" v-model="submitData.drainage"></el-switch>
              </el-form-item>
            </el-col>
            
            <el-col :span="12">
              <el-form-item label="推荐">
                <ms-picker :model.sync="submitData.publishedTime" type="datetimerange"></ms-picker>
              </el-form-item>
            </el-col>
            <el-col :span="24" v-if="submitData.watchType == 1">
              <el-form-item label="推广弹窗">
                <el-radio-group v-model="submitData.isSpread" @change="changeVideo">
                  <el-radio :label="0" style="width: 60px;">否</el-radio>
                  <el-radio :label="1" style="width: 60px;">是</el-radio>
                </el-radio-group>
                <br/>
                <el-radio-group @change="changeVideo1" v-model="submitData.qrCodeType" v-if="submitData.isSpread == 1" style="margin: 15px 0;">
                  <el-radio :label="0" style="width: 60px;">APP推广</el-radio>
                  <el-radio :label="1" style="width: 60px;"><span class="red" v-if="submitData.qrCodeType">*</span>自定义</el-radio>
                </el-radio-group>
                <ms-single-image v-if="submitData.isSpread == 1&&submitData.qrCodeType == 1" v-model="submitData.qrCodeUrl"></ms-single-image>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="目标访问量">
                <el-input-number v-model="submitData.destVisitNum" step-strictly :step="1" :min="0"></el-input-number>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="目标时长">
                <el-input-number v-model="submitData.destDuration" step-strictly :step="1" :min="0"></el-input-number>分钟
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="基础人数">
                <el-input-number v-model="submitData.basisNumber" step-strictly :step="1" :min="0"></el-input-number>
              </el-form-item>
            </el-col>
            <!-- <el-col :span="12">
              <el-form-item label="自定义登录">
                <el-switch :active-value="1" :inactive-value="0" v-model="submitData.isTob"></el-switch>
              </el-form-item>
            </el-col> -->
            <el-col :span="12"  >
              <el-form-item label="免登录">
                <el-switch :active-value="1" :inactive-value="0" v-model="submitData.notLoginView" :disabled="submitData.watchType == 1"></el-switch>
              </el-form-item>
            </el-col>
            <el-col :span="3">
              <el-form-item label="预约">
                <el-switch :active-value="1" :inactive-value="0" v-model="submitData.isAppointment"></el-switch>
              </el-form-item>
            </el-col>
            <!-- <el-col :span="12" v-if="submitData.isAppointment === 1" >
              <el-form-item label="未预约用户能否观看直播">
                <el-radio-group v-model="submitData.notAppointmentView" :disabled="!!dataId">
                  <el-radio :label="1">是</el-radio>
                  <el-radio :label="0">否</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col> -->
            <el-col :span="9" v-if="submitData.isAppointment === 1">
              <el-form-item label="预约短信" >
                <el-switch :active-value="1" :inactive-value="0" :disabled="submitData.status == 2 || submitData.status == 3 || submitData.status == 4" v-model="submitData.isSendMessage"></el-switch>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="直播回放">
                <el-switch :active-value="1" :inactive-value="0" v-model="submitData.replayStatus"></el-switch>
              </el-form-item>
            </el-col>
            
            <!-- <el-col :span="12">
              <el-form-item label="直播观看渠道">
                <el-checkbox v-model="submitData.isMedsci" style="width: 45%" :true-label="1" :false-label="0" :disabled="!!dataId">梅斯官网</el-checkbox>
                <el-checkbox v-model="submitData.isBioon" style="width: 45%" :true-label="1" :false-label="0" :disabled="!!dataId">生物谷</el-checkbox>
              </el-form-item>
            </el-col> -->
            <!-- <el-col :span="12">
              <el-form-item label="嘉宾名称" prop="guestName">
                <el-input v-model="submitData.guestName" style="width: 100%" placeholder="请输入20个字符以内的文本"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="嘉宾照片" prop="guestImg">
                <ms-single-image v-model="submitData.guestImg"></ms-single-image>
              </el-form-item>
            </el-col> -->
            
            <!-- <el-col :span="12">
              <el-form-item label="人数倍数">
                <el-input-number v-model="submitData.multipleNumber" :precision="2" :step="1" :min="0"></el-input-number>
              </el-form-item>
            </el-col> -->
            

            
            <el-col :span="12">
              <el-form-item label="直播封面" prop="detailImg">
                <ms-single-image v-model="submitData.detailImg" :upFileSize="0.5"></ms-single-image>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="详情页背景" prop="backImg">
                <ms-single-image v-model="submitData.backImg"></ms-single-image>
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="submitData.isTob === 1">
              <el-form-item label="自定义登录背景" prop="loginImg">
                <ms-single-image v-model="submitData.loginImg"></ms-single-image>
              </el-form-item>
            </el-col>
          </el-row>
        </el-col>
      </el-row>
    </el-form>
    <!-- 设置弹框 -->
    <el-dialog :visible.sync="dialog"
               closeable
               :close-on-click-modal="false"
               :width="dialogWidth || '50%'"
               :title="dialogTitle">
      <ms-live-member v-if="dialogComponent='msLiveMember' && dialog" :model="submitData" @close="dialog = false" @getData="getData"></ms-live-member>
    </el-dialog>
  </section>
</template>

<script>
import msLiveMember from "./ms-live-member"
import MsDepartmentSearch from '@/components/MsCommon/ms-department-search'
import MsTagSearch from '../../../../components/MsCommon/ms-tag-search'
// import { Loading } from 'element-ui'
export default {
  name: "ms-live-content",
  data () {
    return {
      dataId: this.$route.query.id || 0,
      dialog: false,
      dialogWidth: '',
      dialogTitle: '',
      dialogComponent: '',
      rules: {
        detailImg: [
          { required: true, message: "请上传详情页图片", trigger: 'blur' }
        ],
        loginImg: [
          { required: true, message: "请上传自定义登录背景", trigger: 'change' }
        ]
      },
      departmentList: [],
      memberData: [],
      limitMember: '',
      payData: [],
      limitPay: '',
      loading: false
    }
  },
  components: {
    MsTagSearch,
    msLiveMember,
    MsDepartmentSearch
  },
  props:["submitData"],
  watch: {
    'submitData.departmentList': {
      handler: function() {
        this.departmentList = []
        if(this.submitData.departmentList && this.submitData.departmentList.length) {
          this.submitData.departmentList.forEach(i=>{
            this.departmentList.push(i.departmentId)
          })
        }
      },
      deep: true,
      immediate: true
    },
    'submitData.limitMemberStatus': {
      handler: function() {
        if(this.submitData.limitMemberStatus == 0) {
          this.limitMember = '';
          this.submitData.limitMemberList = [];
        }
        if(this.submitData.limitMemberStatus == 1 && this.submitData.limitMemberList && this.submitData.limitMemberList.length) {
          this.limitMember = this.submitData.limitMemberList[0].id
        }
      },
      deep: true,
      immediate: true
    },
    'submitData.limitPayStatus': {
      handler: function() {
        if(this.submitData.limitPayStatus == 0) {
          this.limitPay = '';
          this.submitData.limitPay = {};
        }
        if(this.submitData.limitPayStatus == 1 && this.submitData.limitPay) {
          this.payData.push(this.submitData.limitPay);
          this.limitPay = this.submitData.limitPay.id;
          console.log(this.payData, this.limitPay);
        }
      },
      deep: true,
      immediate: true
    }
  },
  mounted() {
    this.getMemberCardQuery();
    // this.getCoureByTitle();
  },
  methods: {
    changeVideo(val){
      if(val == 0){
        this.submitData.qrCodeType = null
        this.submitData.qrCodeUrl = null
      }
    },
    changeVideo1(val){
      if(val == 0){
        this.submitData.qrCodeUrl = null
      }
    },
    
    changeLimitMember(val) {
      this.submitData.limitMemberList = [];
      this.memberData.forEach((el) => {
        if(val == el.id) {
          this.submitData.limitMemberList.push({
            id: el.id,
            propertyName: el.propertyName,
            cardName: el.cardName,
            cardImage: el.cardImage,
            cardPrice: el.cardPrice,
            validTime: el.validTime,
            validTimeUnit: el.validTimeUnit,
            activityPrice: el.activityPrice,
            activityStartTime: el.activityStartTime,
            activityEndTime: el.activityEndTime,
            firstPrice: el.firstPrice,
          });
        }
      })
      console.log(this.submitData.limitMemberList)
    },
    changeLimitPay(val) {
      this.submitData.limitPay = {};
      this.payData.forEach((el) => {
        if(val == el.id) {
          this.submitData.limitPay = {
            id: el.id,
            projectId: el.projectId,
            title: el.title,
            summary: el.summary,
            tags: el.tags,
            categorys: el.categorys,
            price: el.price,
            activityPrice: el.activityPrice,
            buyCount: el.buyCount,
            cover: el.cover,
          };
        }
      })
    },
    getMemberCardQuery() {
      let searchParams = {
        projectId: 1,
        status: 1,
      }
      this.api.memberCardQuery(searchParams).then(response => {
        console.log(response, 'response')
        this.memberData = response.data || []
        if(this.submitData.limitMemberStatus == 1 && this.submitData.limitMemberList.length) {
          this.limitMember = this.submitData.limitMemberList[0].id;
        }
        if (response.status !== 200) {
          this.PUBLIC_Methods.apiNotify(
            response.message || "请求出错",
            "warning"
          )
        }
      })
        .catch((err) => (console.log(err)))
    },
    getCoureByTitle(query) {
      if(query !== '') {
        this.loading = true
        let searchParams = {
          title: query,
        }
        this.api.getCoureByTitle(searchParams).then(response => {
          console.log(response, 'response')
          this.loading = false
          this.payData = response.data || []
          if (response.status !== 200) {
            this.PUBLIC_Methods.apiNotify(
              response.message || "请求出错",
              "warning"
            )
          }
        })
          .catch((err) => (console.log(err)))
      } else {
        this.payData = []
      }
    },
    handle_click(way) {
      switch (way) {
        case 'member':
          this.dialog = true;
          this.dialogWidth = '40%';
          this.dialogTitle =  '直播主讲人嘉宾设置';
          this.dialogComponent = 'msLiveMember';
          break;
        default: break;
      }
    },
    changeWatchType(val) {
      if(val == 1) {
        this.submitData.notLoginView = 0
        this.submitData.limitMemberStatus = 0
        this.submitData.limitPayStatus = 0
        this.submitData.departmentList = []
        this.submitData.limitAuth = 0
      }
      if(val == 0) {
        this.submitData.qrCodeType = null
        this.submitData.qrCodeUrl = null
        this.submitData.isSpread = 0
        
      }
    },
    bindDepartment(val) {
      let selectArr = []
      val.model.map(v => {
        selectArr.push({
          departmentId: v.categoryId,
          departmentName: v.categoryName
        })
      })
      this.submitData.departmentList = selectArr
    },
    getData (data) {
      if (data.way === 'member') {
        this.submitData.guests = data.list
      }
      this.dialog = false
    },
    // 数据校验
    validateData() {
      return new Promise((resolve) => {
        this.$refs["submitRef"].validate( valid => {
          if (valid) {
            resolve()
          }else {
            this.$emit('changeTab','setting')
          }
        })
      })
    }
  }
}
</script>
<style scoped>
  .flex-item /deep/ .el-form-item__content {
    position: relative;
  }
  .rule-form /deep/ .el-row{
    display: flex;
    flex-wrap: wrap;
  }
  .flex-tips{
    display: flex;
    align-items: center;
    
  }
  .tips {
    text-align: left;
    width: 48%;
    margin:0 20px 18px 10px;
  }
  .tips div{
    font-size: 12px;
    color: #ccc;
    line-height: 16px;
  }
  .limit-wrap{
    display: flex;
  }
  .rule-form /deep/.red{
    color: #F56C6C;
    font-size: 12px;
    font-weight: 500;
    margin-right: 4px;
  }
</style>
