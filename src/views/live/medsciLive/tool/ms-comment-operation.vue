<template>
  <ms-operation-dialog :title="`是否要${type === 3 ? '审核' : type === 4 ? '去审' : type === 1 ? '删除' : ''}以下数据${!PUBLIC_Methods.isArrayFn(this.model)&&model.status==2&&type === 4?'，当前直播正在直播中，确定去审么？':''}`">
    <template slot="content">
      <el-tag v-for="(id, index) in modelArr" 
              :key="index" 
              type="info" 
              style="margin: 0 5px 5px 0">{{id}}</el-tag>
    </template>
    <template slot="footer">
      <el-button @click="submitForm"
                 :loading="loading"
                 size="mini"
                 type="primary">确 定</el-button>
      <el-button @click="$emit('close')"
                 size="mini">取 消</el-button>
    </template>
  </ms-operation-dialog>
</template>

<script>
export default {
	name: "ms-comment-operation",
	data () {
		return {
      loading: false,
      userInfo: {},
      type: null,
      ids: [],
      modelArr: []
		}
	},
	props: [
		"model",
		"operation"
  ],
  created() {
    this.userInfo = this.$store.getters.info || {}
    let arr = []
    let showArr = []
    let operationLocal = this.operation || this.$route.query.operation
    this.type = operationLocal === 'approval' ? 3 : operationLocal === 'toreview' ? 4 : operationLocal === 'delete' ? 1 : null;
    if (this.PUBLIC_Methods.isArrayFn(this.model)) {
      this.model.forEach(item => {
        this.ids = this.groupId
        if(item.customData && item.customData.nick){
         showArr.push(item.customData.nick)
        } 
      });
    } else {
      console.log(13,this.model);
      arr.push(this.model.id)
      if(this.model.customData && this.model.customData.nick){
         showArr.push(this.model.customData.nick)
      } 
    }
    this.ids = arr
    this.modelArr = showArr
  },
	methods: {
		submitForm () {
      this.loading = true;
      let params = {
        // userId: this.userInfo.userId,
        // username: this.userInfo.userName,
        // type: this.type,
        msgIds: this.ids
      }
      this.api.DealMedsciChat(params).then(response => {
        if(response.status === 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
        this.$emit('up-date')
        this.loading = false
      }).catch( () => {
        this.loading = false;
        this.$emit('close')
      })
		}
	}
}
</script>
