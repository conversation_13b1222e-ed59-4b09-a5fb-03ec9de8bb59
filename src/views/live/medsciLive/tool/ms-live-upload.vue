<template>
    <el-upload
        class="upload-journal"
        drag
        action=""
        :file-list="fileList"
        :http-request="upLoad"
        :before-upload="beforeUpload"
        v-loading="buttonLoading"
        element-loading-text="文件上传中">
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em>，将自动校验数据</div>
    </el-upload>
</template>

<script>
import { mapGetters } from "vuex";
import upload from "@/components/UpFile/upload"
export default {
    name: 'ms-questionGather-upload',
    mixins: [upload],
    data() {
        return {
            buttonLoading: false,
            fileList: []
        }
    },
    computed: {
        ...mapGetters(["info"])
    },
    props: {
        disabled: {
            type: Boolean,
            default: false
        },
        id:{
            type: Number,
            default: null
        }
    },
    methods: {
        uploadSuccess(res) {
            this.buttonLoading = true
            let params = {
                id:this.id,
                waterInfo:`https://${res.name}`
            }
            this.api.liveWater(params).then(res => {
                if (res.status === 200) {
                    this.PUBLIC_Methods.apiNotify('上传成功', 'success')
                    this.$emit('up-date', res.data)
                }else {
                    this.$alert(res.message, '温馨提示', {
                        dangerouslyUseHTMLString: true
                    });
                    this.fileList = []
                }
                this.buttonLoading = false
            }).catch(() => this.buttonLoading = false)
        },
        uploadFiled(err) {
            this.$message.error(err.message || '上传失败');
        }
    }
}
</script>

<style lang="scss">
.upload-journal {
    .el-upload {
        width: 100%;
    }
    .el-upload-dragger {
        width: 100%;
    }
}
</style>
