<template>
    <ms-operation-dialog>
        <template slot="content">
            <el-table :data="list" 
                        v-loading="getLoading" 
                        style="width: 100%">
                <el-table-column label="身份" align="center" min-width="100px">
                    <template slot-scope="{row}">
                        <span>{{row.identity == 1 ? '主播' : '嘉宾'}}</span>
                    </template>
                </el-table-column>
                <el-table-column label="讲师" align="center" min-width="150px">
                    <template slot-scope="{row}">
                        <ms-speaker-search :modelId.sync="row.speakerId" :model.sync="row.name"></ms-speaker-search>
                    </template>
                </el-table-column>
                <el-table-column label="操作" width="70px" align="center">
                    <template slot-scope="{row, $index}">
                        <div class="table-operation" v-if="row.identity != 1">
                            <el-button @click="delete_flow(row, $index)" type="danger" class="scope-btn">删除</el-button>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
            <button class="add-flow" @click="add_flow()" type="button">添加嘉宾</button>
        </template>
        <template slot="footer">
            <el-button @click="submitForm"
                        :loading="loading"
                        size="mini"
                        type="primary">确 定</el-button>
            <el-button @click="$emit('close')"
                        size="mini">取 消</el-button>
        </template>
    </ms-operation-dialog>
</template>

<script>
import msSpeakerSearch from '@/components/MsCommon/ms-speaker-search'
export default {
	name: "ms-live-member",
	data () {
		return {
            getLoading: false,
            loading: false,
            list: [],
		}
	},
	props: ["model"],
    components: {
        msSpeakerSearch
    },
    mounted () {
        if (this.model.guests && this.model.guests.length > 0) {
            this.list = this.model.guests.map(v => {
                return {
                    identity: v.identity,
                    speakerId: v.speakerId,
                    // name: v.name + ' ' + v.unit
                    name: v.unit ? `${v.name}-${v.unit}` : v.name
                }
            })
        } else {
            this.list = [{
                identity: 1,
                name: '',
                speakerId: null
            }]
        }
        console.log(this.model, 'model')
        console.log(this.list, 'list')
    },
	methods: {
        add_flow() {
            if (this.list.length > 4) {
                return this.$message({
                    message: '至多添加4个嘉宾',
                    type: 'warning'
                });
            }
            this.list.push({
                identity: 0,
                img: '',
                name: '',
                decodeUserId: ''
            })
        },
        delete_flow(row,index) {
            this.list.splice(index, 1)
        },
        submitForm() {
            let chooseFlag = true;
            this.list.forEach(v => {
                if (!v.speakerId) {
                    chooseFlag = false;
                }
            })
            if (!chooseFlag) {
                return this.$message({
                    message: '主播或者嘉宾未选择讲师',
                    type: 'warning'
                });
            }
            if (this.model.liveType === 1 && this.list.length < 2) {
                return this.$message({
                    message: '互动直播至少添加1个嘉宾',
                    type: 'warning'
                });
            }
            let params = {
                way: 'member',
                list: this.list
            }
            this.$emit('getData', params)
        }
	}
}
</script>

<style lang="scss" scoped>
    .add-flow {
        cursor: pointer;
        margin: 10px 0 30px;
        width: 100%;
        height: 30px;
        line-height: 28px;
        border: 1px dashed #DCDFE6;
        border-radius: 4px;
        text-align: center;
        font-size: 12px;
        color: #333;
        outline: none;
        margin-top: 10px;
        background-color: rgba(0,0,0, 0);
    }
</style>
