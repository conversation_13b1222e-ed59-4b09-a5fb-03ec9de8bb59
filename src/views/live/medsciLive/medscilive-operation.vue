<template>
  <section class="form-tab" v-loading="getLoading">
    <el-tabs v-model="activeName">
      <el-tab-pane label="直播内容" name="content">
        <ms-live-content ref="content" @changeTab="changeTab" :submitData.sync="submitData"></ms-live-content>
      </el-tab-pane>
      <el-tab-pane label="直播设置" name="setting">
        <ms-live-setting ref="setting" @changeTab="changeTab" :submitData.sync="submitData"></ms-live-setting>
      </el-tab-pane>
      <el-tab-pane label="显示栏目" name="category">
        <ms-info-setting :categoryModel.sync="submitData.sndCategoryList"
                         :drainageModel.sync="submitData.drainageInfoDtoList"
                         :categoryConfigChild="{moduleName: 'live_info'}" headerShow></ms-info-setting>
      </el-tab-pane>
    </el-tabs>
    <!-- 提交按钮 -->
    <footer-tool-bar v-loading="buttonLoading">
      <template slot="tool-content">
        <el-button type="primary" @click="info_operation('save')">保存</el-button>
        <el-button type="info" @click="info_operation('back')">返回</el-button>
      </template>
    </footer-tool-bar>
  </section>
</template>

<script>
import FooterToolBar from '@/components/ToolBar/footer-tool-bar'
import msInfoSetting from '@/components/MsCommon/ms-info-setting'
import msLiveContent from './tool/ms-live-content'
import msLiveSetting from './tool/ms-live-setting'
import { mapGetters } from 'vuex'

export default {
  name: 'medscilive-operation',
  data () {
    return {
      buttonLoading: false,
      getLoading: false,
      activeName: 'content',
      dataId: this.$route.query.id || 0,
      submitData: {
        isTob: 0,
        loginImg: '',
        categoryList: [],
        content: '',
        description: '',
        detailEndTime: '',
        detailImg: '',
        detailStartTime: '',
        detailTime: [],
        guestImg: null,
        guestName: '',
        isBioon: 0,
        isMedsci: 1,
        listImg: '',
        liveEndTime: '',
        liveStartTime: '',
        liveTime: [],
        name: '',
        sndCategoryList: [],
        userName: '',
        userid: 0,
        isAppointment: 0,
        notAppointmentView: 1,
        notLoginView: 1,
        replayStatus: 1,
        drainage:0,
        isSendMessage: 1,
        guests: [],
        liveType: 0,
        isApp: 1,
        isPc: 1,
        backImg: '',
        preVideo: '',
        preVideoName: '',
        watchType: 0,
        departmentList: [],
        limitAuth: 0,
        outlinkDTO: {
          title: '',
          type: null,
          url: '',
          objectId: null
        },
        userNoticeStatus: 0,
        limitMemberStatus: 0,
        limitPayStatus: 0,
        appWatchSwitch: 1,
        isSpread: 0,
        qrCodeType: 0,
        tagList: [], // => 标签集合
        drainageInfoDtoList: [],
        publishedTime:[],
        recomStartTime:"",
        recomEndTime:"",
        destVisitNum:"",
        destDuration:"",
        basisNumber:""
      },
      departmentIds: [],
      pushDrainage:1,
    }
  },
  components: {
    FooterToolBar,
    msInfoSetting,
    msLiveContent,
    msLiveSetting
  },
  created () {
    this.init()
  },
  computed: {
    ...mapGetters(['info']),
  },
  methods: {
    init () {
      this.dialog = false
      let id = this.dataId
      if (id !== 0) {
        this.getLoading = true
        this.submitData.id = id
        this.api.medsciLiveById(id).then(response => {
          this.getLoading = false
          if (response.status === 200) {
            let res = response.data
            response.data.publishedTime = [response.data.recomStartTime,response.data.recomEndTime]
            this.submitData = {
              ...this.submitData,
              ...res,
              content: this.PUBLIC_Methods.unexcapeHtml(res.content),
              detailTime: res.detailStartTime && res.detailEndTime ? [res.detailStartTime, res.detailEndTime] : []
            }
            if(this.$route.query.operation == 'copy'){
              this.submitData.name = this.submitData.name + '(副本)'
            }
          } else {
            this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
          }
        }).catch(() => {
          this.getLoading = false
        })
      }
    },
    changeTab (val) {
      this.activeName = val
      this.buttonLoading = false
    },
    info_operation (val) {
      if (this.buttonLoading) {
        return
      }
      switch (val) {
        case 'save':
          this.buttonLoading = true
          try {
            Promise.all([
              this.$refs['setting'].validateData().then(data => {
                console.log(data,'data');
                // 校验data是否正常
              })
              .catch(err => {
                console.log(err,'err');
                // 处理异常
              }),
              this.$refs['content'].validateData().then(data => {
                console.log(data,'data');
                // 校验data是否正常
              })
              .catch(err => {
                console.log(err,'err');
                // 处理异常
              }),
            ]).then(() => {
              if (this.submitData.detailTime) {
                this.submitData.detailStartTime = this.submitData.detailTime[0] || ''
                this.submitData.detailEndTime = this.submitData.detailTime[1] || ''
                this.submitData.id = this.submitData.id || this.$route.query.id
              }
              if(this.$route.query.operation == 'copy'){
                this.createSubmit()
              }else{
                this.dataId ? this.updateSubmit() : this.createSubmit()
              }
            })
          } catch (error) {
            this.buttonLoading = false
            return
          }
          break
        case 'back':
          this.$router.back()
          break
        default:
          break
      }
    },
    createSubmit () {
      if(this.submitData.drainageInfoDtoList && this.submitData.drainageInfoDtoList.length != 0 && this.submitData.drainageInfoDtoList[0].categoryName){
        this.submitData.drainageInfoDtoList = [{
          categoryId:this.submitData.drainageInfoDtoList[0].categoryId,
          switchDrainage:1,
          projectName:this.submitData.drainageInfoDtoList[0].categoryName,
          url:this.submitData.drainageInfoDtoList[0].url+'/live/',
        }]
        if(this.submitData.drainageInfoDtoList[0].url.indexOf('https://') == -1 && this.submitData.drainageInfoDtoList[0].url.indexOf('http://') == -1){
          this.submitData.drainageInfoDtoList[0].url = 'https://' + this.submitData.drainageInfoDtoList[0].url
        }
      }else{
        this.submitData.drainageInfoDtoList = []
      }
      if(this.submitData.watchType == 1 && this.submitData.isSpread == 1 && this.submitData.qrCodeType == 1 && !this.submitData.qrCodeUrl){
        this.PUBLIC_Methods.apiNotify( '若要设置自定义的推广弹窗，请上传图片', 'warning')
      }
      this.submitData.recomStartTime = this.submitData.publishedTime[0]
      this.submitData.recomEndTime = this.submitData.publishedTime[1]
      let params = {
        ...this.submitData,
        userid: this.info.userId,
        userName: this.info.userName,
        content: this.PUBLIC_Methods.excapeHtml(this.submitData.content)
      }
      this.api.medsciLiveSave(params).then(response => {
        this.buttonLoading = false
        if (response.status === 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
          this.$router.back()
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
      }).catch(() => this.buttonLoading = false)
    },
    updateSubmit () {
      console.log(this.submitData.drainageInfoDtoList,'qwer1');
      if(this.submitData.drainageInfoDtoList && this.submitData.drainageInfoDtoList.length != 0 && this.submitData.drainageInfoDtoList[0].categoryName){
        this.submitData.drainageInfoDtoList = [{
          categoryId:this.submitData.drainageInfoDtoList[0].categoryId,
          switchDrainage:1,
          projectName:this.submitData.drainageInfoDtoList[0].categoryName,
          url:this.submitData.drainageInfoDtoList[0].url+'/live/'+this.submitData.encryptionId,
        }]
        if(this.submitData.drainageInfoDtoList[0].url.indexOf('https://') == -1 && this.submitData.drainageInfoDtoList[0].url.indexOf('http://') == -1){
          this.submitData.drainageInfoDtoList[0].url = 'https://' + this.submitData.drainageInfoDtoList[0].url
        }
      }else{
        this.submitData.drainageInfoDtoList = []
      }
      if(this.submitData.watchType == 1 && this.submitData.isSpread == 1 && this.submitData.qrCodeType == 1 && !this.submitData.qrCodeUrl){
        this.PUBLIC_Methods.apiNotify('若要设置自定义的推广弹窗，请上传图片', 'warning')
        this.buttonLoading = false
        return
      }
      console.log(this.submitData.drainageInfoDtoList,'qwer2');
      this.submitData.recomStartTime = this.submitData.publishedTime.length>0&&this.submitData.publishedTime[0] || ''
      this.submitData.recomEndTime = this.submitData.publishedTime.length>0&&this.submitData.publishedTime[1] || ''
      let params = {
        ...this.submitData,
        userid: this.info.userId,
        userName: this.info.userName,
        content: this.PUBLIC_Methods.excapeHtml(this.submitData.content),
        notice: this.PUBLIC_Methods.excapeHtml(this.submitData.notice),
      }
      this.api.medsciLiveUpdate(params).then(response => {
        this.buttonLoading = false
        if (response.status === 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
          this.$router.back()
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
      }).catch(() => this.buttonLoading = false)
    },
  }
}
</script>
