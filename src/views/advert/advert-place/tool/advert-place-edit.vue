<template>
  <ms-operation-dialog>
    <template slot="content">
      <el-form ref="submitRef"
               class="rule-form"
               :model="submitData"
               :rules="formConfig.rule"
               label-width="85px"
               v-loading="getLoading">
        <el-row>
          <template v-for="(item, index) in formConfig.formField">
            <el-col :key="index"
                    :span="item.colSpan">
              <el-form-item :prop="item.prop"
                            :label="item.label">
                <component :is="item.component"
                           :model.sync="submitData[item.prop]"
                           :width="item.width || '100%'"
                           :disabled="item.disabled "
                           :type="item.type || 'text'"
                           :options="item.options"
                           :modelName.sync="submitData[item.modelName]"
                           :active="item.active"
                           :inactive="item.inactive">
                </component>
              </el-form-item>
            </el-col>
          </template>
          <el-col :span="24">
            <el-form-item label="广告位模版" prop="templateContent">
              <prism-editor v-model="submitData.templateContent" language="html" class="prise-editor"></prism-editor>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </template>
    <template slot="footer">
      <el-button @click="submitForm"
                 :loading="loading"
                 size="mini"
                 type="primary">确 定</el-button>
      <el-button @click="$emit('close')"
                 size="mini">取 消</el-button>
    </template>
  </ms-operation-dialog>
</template>

<script>
import formConfig from '../data/form-config' 
import PrismEditor from 'vue-prism-editor'
import { mapGetters } from "vuex";
import "prismjs"
import "prismjs/themes/prism.css";
export default {
	name: "advert-place-edit",
	data () {
		return {
      loading: false,
      getLoading: false,
      formConfig: formConfig,
      submitData: {
      },
      //common
      operationLocal: ""
		}
  },
  components: {
    PrismEditor
  },
	props: {
		model: Object,
		operation: String
  },
  computed: {
    ...mapGetters(["info"])
  },
  created() {
    this.init()
  },
	methods: {
    init() {
      this.operationLocal = this.operation || this.$route.query.operation
      if(this.operationLocal === 'edit' && this.model.id) {
        this.getDetail(this.model.id)
      } 
    },
    getDetail(id) {
      this.getLoading = true;
      this.api.getAdvertisementSpaceById({id: id}).then( response => {
        this.getLoading = false;
        if(response.status === 200) {
          this.submitData = {...this.model,...response.data}
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
      }).catch(() => this.getLoading = false)
    },
		submitForm () {
      this.$refs["submitRef"].validate(valid => {
        if(valid) {
          this.loading = true;
          this.submitData.templateContent = this.PUBLIC_Methods.excapeHtml(this.submitData.templateContent)
          if(this.operationLocal === 'edit') {
            this.submitEdit()
          } else {
            this.submitAdd()
          }
        }
      })
    },
    submitEdit() {
      let params = {
        userId: this.info.userId,
        username: this.info.userName,
        ...this.submitData,
      }
      this.api.updateAdvertisementSpaceById(params).then(response => {
        if(response.status === 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
        this.$emit('up-date')
        this.loading = false
      }).catch( () => {
        this.loading = false;
        this.$emit('close')
      })
    },
    submitAdd() {
      let params = {
        userId: this.info.userId,
        username: this.info.userName,
        ...this.submitData
      }
      this.api.saveAdvertisementSpace(params).then(response => {
        if(response.status === 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
        this.$emit('up-date')
        this.loading = false
      }).catch( () => {
        this.loading = false;
        this.$emit('close')
      })
    },
	}
}
</script>

<style>
.prise-editor pre{
  margin: 0 !important;
  padding: 5px 15px 15px;
  min-height: 60px;
  border-radius: 4px;
}
</style>
