const scopeConfig = {
  show: {
    // identification: () => {
    //   return {
    //     type: 'query',
    //     config: {
    //       way: 'query',
    //       params: 'identification'
    //     }
    //   }
    // },
    adSpaceName: () => {
      return {
        type: 'webLink',
        config: {
          role: '',
          way: 'page',
          path: 'advert',
          params: [{
            keyName: 'identification',
            valName: 'identification'
          }],
        }
      }
    }
  },
  headerShow: {
    status: () => {
      return {
        type: 'dropdown',
        icon: 'icon-funnel',
        options: [
          { label: '全部', value: null },
          { label: '禁用', value: 0 },
          { label: '启用', value: 1 }
        ],
        operation: 'query'
      }
    }
  }
}

export default scopeConfig;
