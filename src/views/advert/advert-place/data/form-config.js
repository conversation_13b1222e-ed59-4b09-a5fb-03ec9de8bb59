const formConfig = {
  formField: [
    {
      label: '广告位名称',
      prop: 'adSpaceName',
      colSpan: 12,
      component: 'ms-input'
    },
    {
      label: '广告位标识',
      prop: 'identification',
      colSpan: 12,
      component: 'ms-input'
    },
    {
      label: '轮播数量',
      prop: 'adSum',
      colSpan: 12,
      component: 'ms-input-number',
    },
    {
      label: '广告价格',
      prop: 'adSpacePrice',
      colSpan: 12,
      placeholder: '3000元/月',
      component: 'ms-input-number',
    },
    {
      label: '宽',
      prop: 'adSpaceWidth',
      colSpan: 12,
      component: 'ms-input-number',
    },
    {
      label: '高',
      prop: 'adSpaceHeight',
      colSpan: 12,
      component: 'ms-input-number',
    },
    {
      label: '广告说明',
      prop: 'adSpaceExplain',
      colSpan: 24,
      component: 'ms-input',
      type: 'textarea',
      maxRows: 5
    },
    {
      label: '负责人',
      placeholder: '请选择',
      colSpan: 12,
      prop: 'principalId',
      component: 'ms-createby-search',
      modelName: 'principalName'
    },
    {
      label: '广告类型',
      placeholder: '请选择',
      colSpan: 12,
      prop: 'adType',
      component: 'ms-select-local',
      options:[
        {label: '图片', value: 1},
        {label: '文字', value: 2},
        {label: '图文', value: 3}
      ]
    },
  ],
  rule: {
    adSpaceName: [
      { required: true, message: "请输入广告位名称", trigger: 'blur' }
    ],
    identification: [
      { required: true, message: "请输入广告位标识", trigger: 'blur' }
    ],
    adSum: [
      { required: true, message: "请输入轮播数量", trigger: 'blur' }
    ],
    adSpaceWidth: [
      { required: true, message: "请输入宽", trigger: 'blur' }
    ],
    adSpaceHeight: [
      { required: true, message: "请输入高", trigger: 'blur' }
    ],
    principalId: [
      { required: true, message: "请选择负责人", trigger: 'blur' }
    ],
    adType: [
      { required: true, message: "请选择广告类型", trigger: 'blur' }
    ],
    templateContent: [
      { required: true, message: "请输入广告位模版", trigger: 'blur' }
    ]
  }
}

export default formConfig;
