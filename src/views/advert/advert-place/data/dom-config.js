const domConfig = {
  listSearch: [
    {
      label: '广告位',
      placeholder: '请输入',
      model: 'searchValue',
      component: 'ms-input'
    },
    {
      label: '负责人',
      placeholder: '请输入',
      model: 'principalName',
      component: 'ms-input'
    }
  ],
  tableHeader: [
    { label: 'ID', property: 'id', sortable: true, width: '80' },
    { label: '广告位名称', property: 'adSpaceName' },
    { label: '广告位标识', property: 'identification' },
    { label: '价格（元/月）', property: 'adSpacePrice' },
    { label: '最多轮播数', property: 'adSum', sortable: true },
    { label: '负责人', property: 'principalName' }
  ],
  tableButtons: [
    {
      label: '投放',
      operation: 'created',
      type: 'success',
      role: '',
      way: 'page',
      path: 'advert-operation',
      params: [{
        keyName: 'adSpaceId',
        valName: 'id'
      }],
    },
    {
      label: '编辑',
      icon: '',
      role: '',
      operation: 'edit',
      component: 'advertPlaceEdit',
      way: 'dialog',
      type: 'primary',
      title: '编辑广告位',
      width: '70%'
    },
    {
      label: '删除',
      icon: '',
      role: '',
      type: 'danger',
      way: 'delete',
    }
  ],
  soltButtons: [
    { 
      label: '添加广告位', 
      icon: 'el-icon-plus',
      type: 'primary', 
      operation: 'created',
      component: 'advertPlaceEdit',
      way: 'dialog',
      title: '新建广告位',
      width: '70%'
    }
  ]
}

export default domConfig;
