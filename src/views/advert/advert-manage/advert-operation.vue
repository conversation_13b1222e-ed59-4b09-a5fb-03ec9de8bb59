<template>
  <section v-loading="getLoading" class="form-tab">
    <!-- 表单内容 -->
    <el-row :gutter="10">
      <el-tabs v-model="activeName">
        <el-tab-pane label="广告信息" name="first">
          <el-col :span="16">
            <el-form
              ref="submitRef"
              class="rule-form"
              :model="submitData"
              :rules="rule"
              label-width="95px"
            >
              <el-row>
                <el-col :span="12">
                  <el-form-item prop="adName" label="广告名称">
                    <el-input
                      v-model="submitData.adName"
                      style="width: 100%"
                    ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item prop="liveTime" label="投放时间">
                    <el-row>
                      <el-date-picker
                        v-model="submitData.liveTime"
                        type="datetimerange"
                        clearable
                        class="w100"
                        value-format="yyyy-MM-dd HH:mm:ss"
                        start-placeholder="开始时间"
                        end-placeholder="结束时间"
                      >
                      </el-date-picker>
                    </el-row>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="12">
                  <el-form-item prop="adUrl" label="跳转地址">
                    <el-input v-model="submitData.adUrl" style="width: 100%">
                      <template slot="prepend">
                        <ms-dictionary-search
                          :model.sync="submitData.adType"
                          type="advertisement_type"
                          width="100px"
                        ></ms-dictionary-search>
                      </template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item prop="channel" label="渠道">
                    <el-radio v-model="submitData.channel" :label="1"
                      >PC</el-radio
                    >
                    <el-radio v-model="submitData.channel" :label="2"
                      >APP</el-radio
                    >
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row >
                <template>
                  <el-col :span="12" v-if="submitData.adType == 'live_info'">
                    <el-form-item label="选择直播" class="flex-item">
                      <el-select
                        style="width: 100%"
                        v-model="liveData"
                        value-key="id"
                        :loading="getLoading"
                        filterable
                        remote
                        :remote-method="getLiveList"
                        placeholder="请输入直播名称并添加"
                      >
                        <el-option
                          v-for="item in liveDataList"
                          :key="item.id"
                          :label="item.name"
                          :value="item"
                        >
                        </el-option>
                      </el-select>
                      <el-button
                        style="flex: 1; margin-left: 10px"
                        :disabled="JSON.stringify(liveData) === '{}'"
                        type="primary"
                        @click="addLiveData"
                        >添加</el-button
                      >
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                  <el-form-item prop="adSpaceId" label="广告位">
                    <MsAdvertSearch
                      :model.sync="submitData.adSpaceId"
                    ></MsAdvertSearch>
                  </el-form-item>
                </el-col>
                  <el-col :span="12" v-if="submitData.adType == 'live_info'">
                    <el-form-item label="已选择">
                      <el-table
                        row-key="additionId"
                        v-loading="getLoading"
                        :data="submitData.liveInfoList"
                        :header-cell-style="headerCellStyle"
                        :header-row-style="headerRowStyle"
                        style="
                          width: 100%;
                          border-left: 1px solid #ebeef5;
                          border-right: 1px solid #ebeef5;
                        "
                      >
                        <el-table-column
                          align="center"
                          label="序号"
                          type="index"
                          min-width="50px"
                        ></el-table-column>
                        <el-table-column
                          align="center"
                          label="直播ID"
                          min-width="80px"
                        >
                          <template slot-scope="scope">
                            <span>{{ scope.row.liveId }}</span>
                          </template>
                        </el-table-column>
                        <el-table-column
                          align="center"
                          label="直播名称"
                          min-width="200px"
                        >
                          <template slot-scope="scope">
                            <span>{{ scope.row.liveName }}</span>
                          </template>
                        </el-table-column>
                        <el-table-column
                          align="center"
                          label="操作"
                          min-width="80px"
                        >
                          <template slot-scope="scope">
                            <el-tooltip
                              effect="dark"
                              content="删除"
                              placement="bottom"
                            >
                              <span @click="deleteRow(scope.$index)">
                                <svg-icon
                                  class-name="drag-handler"
                                  icon-class="icon-shanchu"
                                />
                              </span>
                            </el-tooltip>
                          </template>
                        </el-table-column>
                      </el-table>
                    </el-form-item>
                  </el-col>
                    <el-col :span="12">
                  <el-form-item prop="launchAuthority" label="投放权限">
                    <el-radio v-model="submitData.launchAuthority" :label="1"
                      >免登录</el-radio
                    >
                    <el-radio v-model="submitData.launchAuthority" :label="2"
                      >登录</el-radio
                    >
                    <el-radio v-model="submitData.launchAuthority" :label="3"
                      >已完善信息</el-radio
                    >
                    <el-radio v-model="submitData.launchAuthority" :label="4"
                      >未完善信息</el-radio
                    >
                  </el-form-item>
                </el-col>
                
                </template>
              </el-row>
              <el-row>
                <el-col :span="12">
                  <el-form-item prop="adShareUrl" label="分享地址URL">
                    <el-input
                      placeholder="请输入内容"
                      v-model="submitData.adShareUrl"
                      style="width: 100%"
                    ></el-input>
                  </el-form-item>
                </el-col>
                 <el-col :span="12">
                  <el-form-item prop="customerId" label="客户名称">
                    <MsCompanySearch
                      :model.sync="submitData.customerId"
                      :modelName.sync="submitData.customerName"
                    ></MsCompanySearch>
                  </el-form-item>
                </el-col>
                
              </el-row>
              <el-row>
                <el-col :span="6">
                  <el-form-item label="广告封面" prop="jsContent">
                     <el-tooltip
                  popper-class="atooltip"
                  effect="light"
                  placement="top"
                >
                  <i
                    class="el-icon-question"
                    style="font-size: 14px; vertical-align: middle"
                  ></i>
                  <template #content>
                    <div>
                      <div>尺寸：582*371px</div>
                    </div>
                  </template>
                </el-tooltip>
                    <!-- <ms-editor v-model="submitData.jsContent" :toolbar="editorToolBar" :isBlock="false"></ms-editor> -->
                    <ms-single-image
                      v-model="submitData.adImage"
                      :tip="imgUploadTip"
                    ></ms-single-image>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item prop="imageText" label="代码">
                    <prism-editor
                      v-model="submitData.imageText"
                      language="html"
                      class="prise-editor"
                    ></prism-editor>
                    <!-- <el-input v-model="submitData.imageText"  type="textarea" :rows="5" style="width: 100%"></el-input> -->
                  </el-form-item>
                </el-col>
              <el-col :span="12">
                  <el-form-item prop="erpQuotationNo" label="ERP报价单号">
                    <el-input
                      v-model="submitData.erpQuotationNo"
                      style="width: 100%"
                    ></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="12" :offset="12">
                  <el-form-item prop="erpOrderNo" label="ERP订单号">
                    <el-input
                      v-model="submitData.erpOrderNo"
                      style="width: 100%"
                    ></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="12" :offset="12">
                  <el-form-item prop="executorId" label="广告执行">
                    <ms-createby-search
                      :model.sync="submitData.executorId"
                      :modelName.sync="submitData.executorName"
                    ></ms-createby-search>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="12" :offset="12">
                  <el-form-item prop="directorId" label="销售负责">
                    <ms-createby-search
                      :model.sync="submitData.directorId"
                      :modelName.sync="submitData.directorName"
                    ></ms-createby-search>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </el-col>
        </el-tab-pane>
        <el-tab-pane label="投放栏目" name="second">
          <el-col :span="16">
            <ms-info-setting
              :categoryModel.sync="submitData.categoryList"
              :headerShow="false"
              :categoryConfigChild="{ moduleName: 'advertisement' }"
            ></ms-info-setting>
          </el-col>
        </el-tab-pane>
      </el-tabs>
    </el-row>

    <!-- 提交按钮 -->
    <footer-tool-bar v-loading="buttonLoading">
      <template slot="tool-content">
        <el-popover
          placement="top"
          width="300"
          trigger="manual"
          v-model="popVisible"
          v-show="dataId"
        >
          <el-row :gutter="20" type="flex" justify="center">
            <el-col :span="12">功能开发中~~</el-col>
          </el-row>
          <el-button slot="reference" @click="popVisible = !popVisible"
            >预览</el-button
          >
        </el-popover>
        <!-- <el-button plain v-show="submitData.status !== 2">提交</el-button> -->
        <el-button plain v-show="dataId" @click="info_operation('approval')">{{
          submitData.status === 2 ? "去审" : "审核"
        }}</el-button>
        <el-button type="primary" @click="info_operation('save')"
          >保存</el-button
        >
        <el-button type="info" @click="info_operation('back')">返回</el-button>
      </template>
    </footer-tool-bar>
  </section>
</template>

<script>
import FooterToolBar from "@/components/ToolBar/footer-tool-bar";
import MsTagSearch from "@/components/MsCommon/ms-tag-search";
import msInfoSetting from "@/components/MsCommon/ms-info-setting";
import MsAdvertSearch from "@/components/MsCommon/ms-advertplace-search";
import MsCompanySearch from "@/components/MsCommon/ms-company-search";
import msDictionarySearch from "@/components/MsCommon/ms-dictionary-search";
import PrismEditor from "vue-prism-editor";
import "prismjs";
import "prismjs/themes/prism.css";
// import {ms_rule_url} from "@/utils/form-rule.js";
// import MsEditor from '@/components/MsEditor'
import { mapGetters } from "vuex";
export default {
  name: "advert-operation",
  data() {
    return {
      activeName: "first",
      buttonLoading: false,
      getLoading: false,
      dataId: this.$route.query.adId ? this.$route.query.adId : 0,
      popVisible: false,
      editorToolBar: ["bullist numlist link image charmap code fullscreen"],
      submitData: {
        channel: 1,
        status: 1,
        tagList: [],
        categoryList: [],
        liveInfoList: [],
        adType: "h5",
        launchAuthority: 1,
        liveTime:""
      },
      rule: {
        adName: [
          { required: true, message: "请输入广告名称", trigger: "blur" },
        ],
        adUrl: [{ required: true, message: "请输入跳转地址", trigger: "blur" }],
        launchAuthority: [
          { required: true, message: "请选择投放权限", trigger: "blur" },
        ],
        liveTime:[
          { required: true, message: "请选择投放时间", trigger: "blur" },
        ],
        adSpaceId:[
          { required: true, message: "请选择广告位", trigger: "blur" },
        ],
      },
      imgUploadTip: "",

      categoryConfig: {
        // moduleName: ['Internal Medicine', 'Surgery', 'Other Departments', 'Critical Care', 'hot']
      },

      liveData: {},
      liveDataList: [],
      headerCellStyle: {
        "background-color": "#EBEEF5",
      },
      headerRowStyle: {
        "background-color": "#EBEEF5",
      },
    };
  },
  components: {
    // MsEditor,
    MsTagSearch,
    MsAdvertSearch,
    MsCompanySearch,
    msInfoSetting,
    FooterToolBar,
    msDictionarySearch,
    PrismEditor,
  },
  computed: {
    ...mapGetters(["info"]),
  },
  created() {
    if (this.$route.query.adSpaceId) {
      this.submitData.adSpaceId = this.$route.query.adSpaceId;
    }
    this.init();
  },
  methods: {
    init() {
      this.dialog = false;
      let id = this.dataId;
      if (id !== 0) {
        this.getLoading = true;
        this.submitData.id = id;
        this.api
          .getAdvertisementById({ id: id })
          .then((response) => {
            this.getLoading = false;
            if (response.status === 200) {
              let res = response.data;
              this.submitData = {
                ...this.submitData,
                ...res,
                liveInfoList: res.liveInfoList ? res.liveInfoList : [],
                categoryList: res.categoryList ? res.categoryList : [],
              };
             this.submitData.imageText = this.PUBLIC_Methods.unexcapeHtml(this.submitData.imageText)
             this.submitData.liveTime = [this.submitData.startAt,this.submitData.endAt]
            } else {
              this.PUBLIC_Methods.apiNotify(
                response.message || "请求出错",
                "warning"
              );
            }
          })
          .catch(() => {
            this.getLoading = false;
          });
      }
    },
    getLiveList(keyWord) {
      if (!(keyWord && keyWord.length > 1)) {
        return;
      }
      this.getLoading = true;
      let searchParams = {
        searchTitle: keyWord,
        pageSize: 20,
        pageIndex: 1,
        examineStatus: 1,
      };
      this.api
        .medsciLiveList(searchParams)
        .then((response) => {
          this.getLoading = false;
          this.liveDataList = response.data || [];
          if (response.status !== 200) {
            this.PUBLIC_Methods.apiNotify(
              response.message || "请求出错",
              "warning"
            );
          }
        })
        .catch(() => (this.loading = false));
    },
    addLiveData() {
      if (this.submitData.liveInfoList.length > 10) {
        this.PUBLIC_Methods.apiNotify("最多选择10个直播", "warning");
        return;
      }
      let data = {
        liveId: this.liveData.id,
        liveName: this.liveData.name,
      };
      this.submitData.liveInfoList.push(data);
      this.liveDataList = [];
      this.liveData = {};
    },
    deleteRow(index) {
      this.submitData.liveInfoList.splice(index, 1);
    },
    info_operation(val) {
      switch (val) {
        case "save":
          this.$refs["submitRef"].validate((valid) => {
            if (valid) {
              this.dataId ? this.updateForm() : this.createForm();
            }
          });
          break;
        case "back":
          this.$router.back();
          break;
        case "approval":
          var opeParams = {
            userId: this.info.userId,
            username: this.info.userName,
            dealType: this.submitData.status === 2 ? 2 : 1,
            ids: [this.dataId],
          };
          this.$confirm(
            `是否${this.submitData.status === 2 ? "去审" : "审核"}广告`,
            {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning",
            }
          )
            .then(() => {
              this.api
                .dealAdvertisementByBatchId(opeParams)
                .then((response) => {
                  if (response.status === 200) {
                    this.PUBLIC_Methods.apiNotify(
                      response.message || "请求成功",
                      "success"
                    );
                  } else {
                    this.PUBLIC_Methods.apiNotify(
                      response.message || "请求出错",
                      "warning"
                    );
                  }
                  this.init();
                });
            })
            .catch(() => {});
          break;
        default:
          break;
      }
    },
    createForm() {
      this.buttonLoading = true;
      this.submitData.imageText = this.PUBLIC_Methods.excapeHtml(
        this.submitData.imageText
      );
      this.submitData.startAt = this.submitData.liveTime[0]
      this.submitData.endAt = this.submitData.liveTime[1]
      let params = {
        userId: this.info.userId,
        username: this.info.userName,
        ...this.submitData,
      };
      this.api
        .saveAdvertisement(params)
        .then((response) => {
          this.buttonLoading = false;
          if (response.status === 200) {
            this.PUBLIC_Methods.apiNotify(
              response.message || "请求成功",
              "success"
            );
            this.$router.back();
          } else {
            this.PUBLIC_Methods.apiNotify(
              response.message || "请求出错",
              "warning"
            );
          }
        })
        .catch(() => (this.buttonLoading = false));
    },
    updateForm() {
      this.buttonLoading = true;
      this.submitData.imageText = this.PUBLIC_Methods.excapeHtml(
        this.submitData.imageText
      );
      this.submitData.startAt = this.submitData.liveTime[0]
      this.submitData.endAt = this.submitData.liveTime[1]
      this.submitData.jsContent = this.PUBLIC_Methods.excapeHtml(this.submitData.jsContent)
      let params = {
        userId: this.info.userId,
        username: this.info.userName,
        ...this.submitData,
      };
      this.api
        .updateAdvertisementById(params)
        .then((response) => {
          this.buttonLoading = false;
          if (response.status === 200) {
            this.PUBLIC_Methods.apiNotify(
              response.message || "请求成功",
              "success"
            );
            this.$router.back();
          } else {
            this.PUBLIC_Methods.apiNotify(
              response.message || "请求出错",
              "warning"
            );
          }
        })
        .catch(() => (this.buttonLoading = false));
    },
  },
};
</script>

<style scoped>
.prise-editor pre {
  margin: 0 !important;
  padding: 5px 15px 15px;
  min-height: 102px;
  height: 102px;
  border-radius: 4px;
}
.flex-item /deep/ .el-form-item__content {
  display: flex;
  flex-direction: row;
}
</style>