const domConfig = {
  listSearch: [
    {
      label: '广告名称',
      placeholder: '请输入',
      model: 'searchValue',
      component: 'ms-input'
    },
    {
      label: '客户名称',
      placeholder: '请输入',
      model: 'customerName',
      component: 'ms-input'
    },
    {
      label: '负责人',
      placeholder: '请输入',
      model: 'directorName',
      component: 'ms-input'
    }
  ],
  tableHeader: [
    { label: 'ID', property: 'adId', sortable: true, width: '60' },
    { label: '广告位', property: 'adSpaceName', width: '150'},
    { label: '广告名称', property: 'adName', width: '120' },
    { label: '客户名', property: 'customerName', width: '120' },
    { label: '开始时间', property: 'startAt', sortable: true, width: '80' },
    { label: '结束时间', property: 'endAt', sortable: true, width: '80' },
    { label: '负责人', property: 'executorName',  width: '80' },
    { label: '点击/展示数', property: 'fields', sortable: true, width: '90' },
    { label: '状态', property: 'status'},
  ],
  tableButtons: [
    {
      label: '编辑',
      icon: '',
      role: '',
      operation: 'edit',
      component: 'advert-operation',
      way: 'page',
      type: 'primary',
      path: 'advert-operation',
      params: ['adId'],
      identify: 'edit'
    },
    {
      label: '',
      icon: '',
      role: '',
      operation: 'editStatus',
      component: 'msAdvertOperation',
      way: 'dialog',
      field: 'status',
      rule: {
        2: { label: '去审', type: '', operation: 'toreview' },
        1: { label: '审核', type: 'success', operation: 'approval' }
      },
      identify: 'status'
    },
    {
      label: '删除',
      way: 'dialog',
      operation: 'delete',
      type: 'danger',
      component: 'msAdvertOperation',
      identify: 'single_delete'
    }
  ],
  soltButtons: [
    { 
      label: '广告投放', 
      type: 'primary', 
      icon: 'el-icon-plus',
      operation: 'created',
      component: 'advert-operation',
      way: 'page',
      path: 'advert-operation',
      params: ['id'],
      identify: 'created'
    },
    { 
      label: '批量审核', 
      type: 'primary',
      operation: 'approval',
      component: 'msAdvertOperation',
      way: 'batch',
      identify: 'batch_approval'
    },
    { 
      label: '批量去审', 
      type: 'primary',
      operation: 'toreview',
      component: 'msAdvertOperation',
      way: 'batch',
      identify: 'batch_toreview'
    },
    { 
      label: '批量删除',
      type: 'info',
      icon: 'el-icon-close',
      operation: 'delete',
      component: 'msAdvertOperation',
      way: 'batch',
      identify: 'batch_delete'
    }
  ]
}

export default domConfig;
