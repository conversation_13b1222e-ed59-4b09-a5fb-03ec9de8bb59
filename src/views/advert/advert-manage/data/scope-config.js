const scopeConfig = {
  show: {
    status: () => {
      return {
        type: 'status',
        rule: {
          0: { label: '已预约', background: '#E6A23C' },
          1: { label: '待审核', background: '#A7ADBD' },
          2: { label: '审核通过', background: '#40A23F' }
        }
      }
    },
    adSpaceName: () => {
      return {
        type: 'query',
        config: {
          way: 'query',
          params: 'identification'
        }
      }
    },
    // adName: () => {
    //   return {
    //     type: 'other',
    //     config: {
    //       label: '查看',
    //       operation: 'edit',
    //       component: 'MsCommentsInfo',
    //       way: 'dialog',
    //       type: 'primary',
    //       title: '查看详情',
    //       width: '45%'
    //     }
    //   }
    // },
    startAt: () => {
      return {
        type: 'formatTime',
        cFormat: '{y}-{m}-{d}'
      }
    },
    endAt: () => {
      return {
        type: 'formatTime',
        cFormat: '{y}-{m}-{d}'
      }
    },
    fields: () => {
      return {
        type: 'fidd',
        fields: [
          {name: 'clickHits', way: 'text'},
          {name: 'showCount', way: 'text'}
        ]
      }
    },
  },
  headerShow: {
    status: () => {
      return {
        type: 'dropdown',
        icon: 'icon-funnel',
        options: [
          { label: '全部', value: null },
          { label: '已预约', value: 0 },
          { label: '待审核', value: 1 },
          { label: '审核通过', value: 2 }
        ],
        operation: 'query'
      }
    }
  }
}

export default scopeConfig;
