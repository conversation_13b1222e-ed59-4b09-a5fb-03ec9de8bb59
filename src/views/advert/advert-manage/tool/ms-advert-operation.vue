<template>
  <ms-operation-dialog :title="`是否要${dealType === 1 ? '审核' : dealType === 2 ? '去审' : dealType === 3 ? '删除' : ''}以下广告`">
    <template slot="content">
      <el-tag v-for="(id, index) in tagArr" 
              :key="index" 
              type="info" 
              style="margin: 0 5px 5px 0">{{id}}</el-tag>
    </template>
    <template slot="footer">
      <el-button @click="submitForm"
                 :loading="loading"
                 size="mini"
                 type="primary">确 定</el-button>
      <el-button @click="$emit('close')"
                 size="mini">取 消</el-button>
    </template>
  </ms-operation-dialog>
</template>

<script>
export default {
	name: "ms-advert-operation",
	data () {
		return {
      loading: false,
      userInfo: {},
      dealType: null,
      ids: [],
      tagArr: []
		}
	},
	props: [
		"model",
		"operation"
  ],
  created() {
    this.userInfo = this.$store.getters.info || {}
    let arr = []
    let showArr = []
    let operationLocal = this.operation || this.$route.query.operation
    this.dealType = operationLocal === 'approval' ? 1 : operationLocal === 'toreview' ? 2 : operationLocal === 'delete' ? 3 : null;
    if (this.PUBLIC_Methods.isArrayFn(this.model)) {
      this.model.forEach(item => {
        arr.push(item.adId)
        showArr.push(item.adName)
      });
    } else {
      arr.push(this.model.adId)
      showArr.push(this.model.adName)
    }
    this.ids = arr
    this.tagArr = showArr
  },
	methods: {
		submitForm () {
      this.loading = true;
      let params = {
        userId: this.userInfo.userId,
        username: this.userInfo.userName,
        dealType: this.dealType,
        ids: this.ids
      }
      this.api.dealAdvertisementByBatchId(params).then(response => {
        if(response.status === 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
        this.$emit('up-date')
        this.loading = false
      }).catch( () => {
        this.loading = false;
        this.$emit('close')
      })
		}
	}
}
</script>
