import { mapGetters } from "vuex";

export default {
  data () {
    return {
      loading: false, // => 列表加载动画控制
      list: [], // => 列表数据
      total: 0, // => 列表总数
      searchParams: {
        currentUserId: Number,
        pageIndex: 1,
        pageSize: 20
      }, // => 列表查询参数

      scopeInfo: {},
      r_dialog: false,
      dialog: false,
      dialogOperation: "",
      dialogComponent: "",
      dialogWidth: "",
      dialogTitle: "",
      dialogField: "", //新加的，存按钮字段类型，短信模版

      searchCacheFlag: true,

      selectList: [] // => 列表多选选中数组
    }
  },
  computed: {
    ...mapGetters(["permissions","info", "pageSize", "tableDropId", "listSearchParams"])
  },
  watch: {
    listSearchParams: function(val) {
      if (val && JSON.stringify(val) !== '{}' && this.searchCacheFlag) {
        this.searchParams = {...this.searchParams, ...this.listSearchParams}
      }
    }
  },
  created () {
    this.searchParams.currentUserId = this.info.userId
    this.searchParams.pageSize = this.pageSize || 20
    this.$store.dispatch('SetDropId', null)
    if (this.listSearchParams && this.searchCacheFlag) {
      this.searchParams = {...this.searchParams, ...this.listSearchParams}
    }
    this.init();
  },
  methods: {
    //初始化数据
    init() {
      this.loading = true;
      this.dialog = false;
      let searchParams = {...this.searchParams}
      this.$store.dispatch('SetSearchParams', searchParams)
      this.apiInit(searchParams)
    },
    
    /**
     * @name handleClick
     * @param val 搜索列表点击选择参数
     */
    handleClick(val) {
      switch (val) {
        case "reset":
          this.searchParams = this.PUBLIC_Methods.resetParams(this.searchParams, ["pid"])
          this.searchParams.currentUserId = this.info.userId
          this.searchParams.pageIndex = 1;
          this.searchParams.pageSize = this.pageSize || 20;
          if (this.tableDropId === -1) {
            this.$store.dispatch('SetDropId', null)
          } else {
            this.$store.dispatch('SetDropId', -1)
          }
          this.init()
          break;
        case "query":
          this.searchParams.pageIndex = 1;
          this.searchParams.pageSize = this.pageSize || 20;
          this.init()
          break;
        default: break;
      }
    },
    /**
     * @name header_operation
     */
    header_operation(val) {
      switch (val.operation) {
        case "query":
          var fieldName = val.config && val.config.params || val.index
          this.searchParams[fieldName] = val.value
          this.init()
          break;
        default: break;
      }
    },
    /**
     * @name 分页方法
     */
    size_change (val) {
      this.searchParams.pageSize = val;
      this.$store.dispatch('SetPagesize', val)
      this.init();
    },
    current_change (val) {
      this.searchParams.pageIndex = val;
      this.init();
    },
    /**
     * @name 列表操作点击方法
     * @param {*} val 
     */
    operation_change (val) {
      let opera = val && val.operation || {}
      switch (opera.way) {
        case "batch":
          if (this.selectList.length > 0) {
            this.dialog = true;
            this.scopeInfo = this.selectList;
            this.dialogOperation = opera.operation;
            this.dialogComponent = opera.component;
            this.dialogTitle = opera.title;
            this.dialogWidth = opera.width || '40%';
          } else {
            this.$message.warning('请选择至少一条数据')
          }
          break;
        case "dialog":
          if (opera.position === 'right') {
            this.r_dialog = true;
          } else {
            this.dialog = true;
          }
          this.scopeInfo = val.model ? val.model : {};
          this.dialogOperation = opera.operation;
          this.dialogComponent = opera.component;
          this.dialogTitle = opera.title;
          this.dialogWidth = opera.width || '40%';
          this.dialogField = opera.field; //新加的，存新建按钮类型，短信模版
          break;
        case "page":
          var paramsObjs = {}
          if (opera.params) {
            opera.params.forEach(item => {
              let keyName = item.keyName || item
              let valName = item.valName || item
              paramsObjs[keyName] = val.model ? val.model[valName] : ""
            });
          }
          this.$router.push({
            path: opera.path,
            query: {
              operation: opera.operation,
              component: opera.component,
              ...paramsObjs
            }
          });
          break;
        case "link":
          window.open(`${val.model[val.operation.pathKey]}`, "_blank")
          break;
        case "preview":
          this.dialog = true;
          this.scopeInfo = val.model ? val.model : {};
          this.dialogComponent = opera.previewName || 'MsPreviewDialog';
          this.dialogTitle = opera.title || "预览";
          this.dialogWidth = '60%';
          break;
        default: 
          this.operation_else(val)
          break;
      }
    },
    /**
     * @name 按钮其他操作
     * @param {*} val
     */
    operation_else() {
      console.log("其他操作")
    },
    /**
     * @name 列表多选选中值
     * @param {*} val 
     */
    handleSelectionChange(val) {
      this.selectList = [...val]
    }
  }
}
