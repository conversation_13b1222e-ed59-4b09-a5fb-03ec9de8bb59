const durationConfig = {
    listSearch: [],
    tableHeader: [
      { label: '会员名称', property: 'name', width: '100' },
      { label: '会员卡时长价格', property: 'price', width: '100' },
      { label: '会员时长卡类型', property: 'priceType', width: '100' },
      { label: '有效时长', property: 'validTime', width: '130' },
      { label: '创建时间', property: 'activityStartTime', width: '130' },
      { label: '状态', property: 'status', width: '100' },
    ],
    tableButtons: [
      {
        label: '修改',
        type: 'primary', 
        operation: 'edit',
        component: 'msMemberDuration',
        // params: ['validTime'],
        way: 'dialog',
        title: '修改会员时长',
        width: '60%',
      },
      {
        icon: '',
        role: '',
        operation: 'editStatus',
        component: 'msMemberDurationDel',
        way: 'dialog',
        field: 'status',
        rule: {
          1: { label: '禁用', type: 'info' ,operation: 'toreview'},
          0: { label: '启用', type: 'success' ,operation: 'approval' }
        }
      },
      {
        label: '删除',
        icon: '',
        role: '',
        operation: 'delete',
        component: 'msMemberDurationDel',
        way: 'dialog',
        type: 'danger',
      }
    ],
    soltButtons: [
      {
        label: '添加会员卡时长',
        type: 'primary', 
        icon: 'el-icon-plus',
        operation: 'add',
        component: 'msMemberDuration',
        way: 'dialog',
        title: '添加会员卡时长',
        width: '60%',
      },
    ]
  }
  
  export default durationConfig;
  