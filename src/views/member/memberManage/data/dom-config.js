const domConfig = {
    listSearch: [],
    tableHeader: [
      { label: '会员ID', property: 'id', width: '130' },
      { label: '会员名称', property: 'cardName', width: '100' },
      { label: '会员属性', property: 'propertyName', width: '100' },
      { label: '创建时间', property: 'createdTime', width: '130' },
      { label: '修改时间', property: 'updatedTime', width: '130' },
      { label: '创建人', property: 'createdName', width: '100' },
      { label: '展示顺序', property: 'sort', width: '100' },
      { label: '状态', property: 'status', width: '100' },
    ],
    tableButtons: [
      {
        label: '编辑',
        operation: 'edit',
        component: 'memberManage-operation',
        path: 'memberManage-operation',
        way: 'page',
        type: 'primary',
        params: ['id']
      },
      {
        icon: '',
        role: '',
        operation: 'editStatus',
        component: 'msMemberStatus',
        way: 'dialog',
        field: 'status',
        rule: {
          0: { label: '启用', type: 'success' },
          1: { label: '禁用', type: 'info' }
        }
      },
      {
        label: '时长',
        // operation: 'edit',
        // component: 'memberManage-duration',
        path: 'memberManage-duration',
        way: 'page',
        // type: 'primary',
        params: ['id']
      },
      // {
      //   label: '删除',
      //   icon: '',
      //   role: '',
      //   operation: 'delete',
      //   component: '',
      //   way: 'delete',
      //   type: 'danger',
      //   title: '删除评论',
      //   showCallback: (val) => {
      //     if (val.status == 1) {
      //       return false
      //     } else {
      //       return true
      //     }
      //   },
      //   width: '45%'
      // }
    ],
    soltButtons: [
      { 
        label: '添加会员', 
        type: 'primary', 
        icon: 'el-icon-plus',
        title: '添加会员',
        operation: 'created',
        component: 'memberManage-operation',
        path: 'memberManage-operation',
        way: 'page',
      }
    ]
  }
  
  export default domConfig;
  