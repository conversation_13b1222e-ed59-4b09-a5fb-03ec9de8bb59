<template>
  <section>
    <el-form :model="submitData"
      ref="submitRef"
      class="rule-form info-form"
      :rules="rules"
      label-width="100px">
        <el-row :gutter="10"> 
          <el-col :span="20">
            <el-row>
              <el-form-item label="会员权益">
                <el-button @click="editEquities" size="mini" type="primary">添加</el-button>
              </el-form-item>
            </el-row>
            <el-row>
              <el-col :span="24">
                <el-form-item label="已添加">
                  <el-table ref="dragTable" 
                    v-loading="getLoading" 
                    row-key="additionId" 
                    :data="submitData.rights" 
                    :header-cell-style="headerCellStyle" 
                    :header-row-style="headerRowStyle" 
                    style="width: 100%;border-left: 1px solid #EBEEF5;border-right: 1px solid #EBEEF5">
                    <el-table-column align="center" label="序号" min-width="50px">
                      <template slot-scope="scope">
                        <span>{{ scope.row.sort }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column label="权益名称">
                      <template slot-scope="scope">
                        <span>{{ scope.row.rightName }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column align="center" label="权益描述">
                      <template slot-scope="scope">
                        <span>{{ scope.row.rightDescription }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column align="center" label="跳转地址"  min-width="120px">
                      <template slot-scope="scope">
                        <a v-if="scope.row.hrefUrl" :href="scope.row.hrefUrl" target="_blank" style="text-decoration: underline;color: #409EFF;">预览</a>
                        <span v-else>--</span>
                      </template>
                    </el-table-column>
                    <el-table-column align="center" label="权益图标">
                      <template slot-scope="scope">
                        <img :src="scope.row.rightPic" style="width: 80px">
                      </template>
                    </el-table-column>
                    <el-table-column align="center" label="操作" min-width="80px">
                      <template slot-scope="scope">
                        <el-tooltip effect="dark" content="编辑" placement="bottom">
                          <span @click="editRow(scope)">
                            <i class="el-icon-edit edit-icon"></i>
                          </span>
                        </el-tooltip>
                        <el-tooltip effect="dark" content="删除" placement="bottom">
                          <span @click="deleteRow(scope.$index)" style="cursor: pointer;">
                            <svg-icon class-name="drag-handler" icon-class="icon-shanchu" />
                          </span>
                        </el-tooltip>
                      </template>
                    </el-table-column>
                  </el-table>
                  <div class="tips">说明：上下拖动记录，调整展示顺序</div>
                </el-form-item>
              </el-col>
            </el-row>
          </el-col>
        </el-row>
      </el-form>
      <el-dialog :visible.sync="dialog" 
                  closeable 
                  show-close
                  :close-on-click-modal="false"
                  :width="dialogWidth"
                  :title="dialogTitle">
                  <component :is="dialogComponent" 
                  :model="scopeInfo" 
                  :operation="dialogOperation" 
                  @close="dialog = !dialog" 
                  @up-date="addContent" 
                  v-if="dialog"></component>
      </el-dialog>
  </section>
</template>

<script>
import Sortable from 'sortablejs'
import msEquitiesEdit from './ms-equities-edit'
import { mapGetters } from "vuex"
export default {
	name: "ms-article-content",
	data () {
		return {
      rules: {},
      getLoading: false,
      headerCellStyle: {
        "background-color": "#EBEEF5"
      },
      headerRowStyle: {
        "background-color": "#EBEEF5"
      },
      search: {
        contentTitle: "",
        contentType: ""
      },
      keywords: null,
      dialog: false,
      dialogWidth: '50%',
      dialogTitle: '',
      dialogOperation: '',
      dialogComponent: '',
      scopeInfo: {}
		}
  },
  props:["submitData"],
  computed: {
    ...mapGetters(["info"]),
  },
  mounted() {},
  components: {
    msEquitiesEdit
  },
	methods: {
    editEquities() {
      this.dialog = true
      this.dialogOperation = 'created'
      this.dialogComponent = 'msEquitiesEdit'
      this.dialogTitle = '添加会员权益'
    },
    addContent(row, type, index) {
      if(type === 'edit') {
        this.submitData.rights[index].hrefUrl = row.hrefUrl
        this.submitData.rights[index].rightDescription = row.rightDescription
        this.submitData.rights[index].rightName = row.rightName
        this.submitData.rights[index].rightPic = row.rightPic
        this.submitData.rights[index].sort = row.sort
      }else {
        let item = row
        item.sort = this.submitData.rights ? this.submitData.rights.length + 1 : 1
        this.submitData.rights.push(item)
      }
    },
    editRow(row) {
      this.dialog = true
      this.dialogOperation = 'edit'
      this.dialogComponent = 'msEquitiesEdit'
      this.dialogTitle = '编辑会员权益'
      this.scopeInfo = row
    },
    deleteRow(index) {
      this.submitData.rights.splice(index,1)
      this.resort()
    },
    resort() {
      var newArray = this.submitData.rights.slice(0);
      this.submitData.rights = [];
      newArray.forEach((item,index)=>{
        this.submitData.rights.push({
          hrefUrl: item.hrefUrl,
          rightDescription: item.rightDescription,
          rightName: item.rightName,
          rightPic: item.rightPic,
          sort: index + 1
        })
      })
    },
    setSort() {
      const el = this.$refs.dragTable.$el.querySelectorAll('.el-table__body-wrapper > table > tbody')[0]
      this.sortable = Sortable.create(el, {
        ghostClass: 'sortable-ghost', 
        setData: function(dataTransfer) {
          dataTransfer.setData('Text', '')
        },
        onEnd: evt => {
          let drapObj = this.submitData.rights[evt.oldIndex]
          this.submitData.rights.splice(evt.oldIndex, 1)
          this.submitData.rights.splice(evt.newIndex, 0, drapObj)
          var newArray = this.submitData.rights.slice(0);
          this.submitData.rights = [];
          this.$nextTick(() => {
            newArray.forEach((item,index)=>{
              this.submitData.rights.push({
                hrefUrl: item.hrefUrl,
                rightDescription: item.rightDescription,
                rightName: item.rightName,
                rightPic: item.rightPic,
                sort: index + 1
              })
            })
          })
        }
      })
    },
    // 数据校验
    validateData() {
      return new Promise((resolve) => {
        this.$refs["submitRef"].validate( valid => {
          if (valid) {
            resolve()
          }else {
            this.$emit('changeTab','setting')
          }
        })
      })
    }
	}
}
</script>
<style scoped>
.flex-item /deep/ .el-form-item__content {
  display: flex;
  flex-direction: row;
}
.tips {
  font-size: 14px;
  margin-top: 6px;
  color: #999;
  text-align: right;
}
.edit-icon {
  font-size: 18px;
  vertical-align: middle;
  margin-right:8px;
  cursor: pointer;
}
</style>