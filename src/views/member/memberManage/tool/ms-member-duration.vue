<template>
    <ms-operation-dialog>
        <template slot="content">
            <el-form class="rule-form" label-width="150px" :rules="rules"  ref="submitRef" :model="submitData">
                <el-row >
                    <el-col :span="20">
                        <el-form-item label="会员卡名称" prop="name">
                            <el-input v-model="submitData.name" placeholder="会员卡名称"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="20">
                    <el-form-item label="会员卡时长类型" prop="type">
                        <el-select v-model="submitData.type" clearable placeholder="请选择" style="width:100%">
                        <el-option label="常规会员" :value="1"></el-option>
                        <el-option label="体验卡会员" :value="2"></el-option>
                        </el-select>
                    </el-form-item>
                    </el-col>
                    <el-col :span="20">
                        <el-form-item label="会员时长" prop="validTime">
                            <el-input-number style="margin-right:10px; min-width: 150px" v-model="submitData.validTime" :precision="0" :min="1"></el-input-number>天
                        </el-form-item>
                    </el-col>
                    <el-col :span="20">
                        <el-form-item label="价格" prop="price" class="flex-item">
                            <el-input-number style="margin-right:10px; min-width: 150px" v-model="submitData.price" :precision="2" :min="0"></el-input-number>元
                            <el-input style="width:60%; margin-left:30px" v-model="submitData.priceDescription" placeholder="请输入价格说明"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="20">
                    <el-form-item  label="首期价" prop="firstPrice" class="flex-item" label-width="150px">
                        <el-input-number style="margin-right:10px; min-width: 150px" v-model="submitData.firstPrice" :precision="2" :min="0"></el-input-number>元
                        <el-input style="width:60%; margin-left:30px" v-model="submitData.firstPriceDescription" placeholder="请输入价格说明"></el-input>
                    </el-form-item>
                    </el-col>
                    <el-col :span="20">
                    <el-form-item label="活动价" prop="activityPrice" class="flex-item" label-width="150px">
                        <el-input-number style="margin-right:10px; min-width: 150px" v-model="submitData.activityPrice" :precision="2" :min="0"></el-input-number>元
                        <el-input style="width:60%; margin-left:30px" v-model="submitData.activityPriceDescription" placeholder="请输入价格说明"></el-input>
                    </el-form-item>
                    </el-col>
                    <el-col :span="20">
                    <el-form-item label="活动时间" prop="detailTime">
                        <ms-picker :model.sync="submitData.detailTime" :config="pickerConfig" type="datetimerange" style="width: 100%"></ms-picker>
                    </el-form-item>
                    </el-col>
                    <el-col :span="20">
                    <el-form-item label="会员卡时长价格类型" prop="priceType">
                        <el-select v-model="submitData.priceType" clearable placeholder="请选择" style="width:100%">
                        <el-option label="常规价格" :value="1"></el-option>
                        <el-option label="连续包价格" :value="2"></el-option>
                        </el-select>
                    </el-form-item>
                    </el-col>
                    <el-col :span="20">
                    <el-form-item label="展示顺序" prop="sort" class="flex-item">
                        <el-input-number style="margin-right:10px; min-width: 150px" v-model="submitData.sort" :step="1" step-strictly :min="0"></el-input-number>
                        <!-- <el-checkbox style="margin-left:80px" v-model="submitData.checked" :true-label='1' :false-label='0'>默认选中</el-checkbox> -->
                    </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
        </template>
        <template slot="footer">
            <el-button @click="submitForm"
                        :loading="loading"
                        size="mini"
                        type="primary">确 定</el-button>
            <el-button @click="$emit('close')"
                        size="mini">取 消</el-button>
        </template>
    </ms-operation-dialog>
</template>

<script>
import { mapGetters } from "vuex";
export default {
	name: "ms-member-duration",
	data () {
		return {
            loading: false,
            submitData: {},
            oldIntegral: 0,
            rules: {
             name: [
               { required: true, message: "请输入会员名称", trigger: 'blur' },
             ],
             validTime: [
               { required: true, message: "请选择会员时长", trigger: 'change' }
             ],
             price: [
               { required: true, message: "请输入价格", trigger: 'change' },
             ],
             type: [
               { required: true, message: "请选择会员卡时长类型", trigger: 'change' }
             ],
             priceType: [
               { required: true, message: "请选择会员卡时长价格类型", trigger: 'change' }
             ],
             sort: [
               { required: true, message: "请输入展示顺序", trigger: 'change' },
             ],
            },
            pickerConfig: {
                pickerOptions: {
                disabledDate: (time) => {
                    return time.getTime() < new Date().getTime() - 8.64e7;
                }
                }
            },
        }
	},
	props: {
		model: Object,
		operation: String
    },
    computed: {
        ...mapGetters(["info"])
    },
    watch:{
    // "submitData.type":function(value){
    //     this.submitData.price=''
    //     this.submitData.firstPrice=''
    //     this.submitData.activityPrice=''
    // },
    },
    created() { 
        if(this.operation==='edit'){
            this.submitData = JSON.parse(JSON.stringify(this.model))
            if(this.submitData.activityStartTime&&this.submitData.activityEndTime){
                this.submitData.detailTime = [this.submitData.activityStartTime,this.submitData.activityEndTime]
            }
            if(this.submitData.firstPrice === null){
                delete  this.submitData.firstPrice
            }
            if(this.submitData.activityPrice === null){
                delete  this.submitData.activityPrice
            }
            delete this.submitData.activityStartTime
            delete this.submitData.activityEndTime
            console.log(this.model);
            console.log(this.submitData);
        }else {
            this.submitData={
                priceDescription:'',
                // firstPrice:'',
                firstPriceDescription:'',
                // activityPrice:'',
                activityPriceDescription:'',
            }
        }
        this.submitData.userId = this.info.userId,
        this.submitData.username = this.info.userName
        // console.log(this.submitData,'sss')
        // this.submitData.name = this.model.name||''
        // this.submitData.validTime = this.model.validTime||''
        // this.api.getCardDurationDetails(this.$route.query.id).then(res => {
        //     console.log(res,'123')   
        // })
    },
	methods: {
		submitForm () {
            this.$refs.submitRef.validate((valid) => {
            if (valid) {
                if(this.submitData.type===1&&(this.submitData.firstPrice===0||this.submitData.activityPrice===0||this.submitData.price===0)){
                     return  this.$message({message: '常规会员价格，活动价格和首期价格不能为0',type: 'warning'})
                }
                if(this.operation==='add'){
                this.submitData.cardId = this.$route.query.id.toString()
                console.log(typeof this.submitData.cardId,'8888');
                if(this.submitData.detailTime){
                    this.submitData.activityStartTime=this.submitData.detailTime[0]
                    this.submitData.activityEndTime=this.submitData.detailTime[1]
                    delete this.submitData.detailTime
                }else{
                    delete this.submitData.activityStartTime
                    delete this.submitData.activityEndTime
                    delete this.submitData.detailTime
                }
                let params = {
                    activityEndTime:this.submitData.activityEndTime,
                    activityPrice:this.submitData.activityPrice===0?0:this.submitData.activityPrice||'',
                    activityPriceDescription:this.submitData.activityPriceDescription||'',
                    activityStartTime:this.submitData.activityStartTime,
                    cardId:this.submitData.cardId,
                    priceDescription:this.submitData.priceDescription,
                    firstPrice:this.submitData.firstPrice===0?0:this.submitData.firstPrice||'',
                    firstPriceDescription:this.submitData.firstPriceDescription||'',
                    name:this.submitData.name,
                    price:this.submitData.price===0?0:this.submitData.price||'',
                    priceType:this.submitData.priceType,
                    projectId:this.info.projectId,
                    remark:this.submitData.remark,
                    sort:this.submitData.sort,
                    type:this.submitData.type,
                    userId:this.submitData.userId,
                    username:this.submitData.username,
                    validTime:this.submitData.validTime
                }
                this.api.addCardDuration(params).then(response => {
                    if(response.status === 200) {
                        this.PUBLIC_Methods.apiNotify('添加会员卡时长成功', 'success')
                    } else {
                        this.PUBLIC_Methods.apiNotify('添加会员卡时长失败', 'warning')
                    }
                    this.$emit('up-date')
                    this.loading = false
                }).catch( () => {
                    this.loading = false;
                    this.$emit('close')
                })
                }else if(this.operation==='edit'){
                    console.log(this.submitData,'kkk')
                if(this.submitData.detailTime&&this.submitData.detailTime.length !== 0){
                    this.submitData.activityStartTime=this.submitData.detailTime[0]
                    this.submitData.activityEndTime=this.submitData.detailTime[1]
                }else{
                    this.submitData.activityStartTime = ''
                    this.submitData.activityEndTime = ''
                }
                let params = {
                    activityEndTime:this.submitData.activityEndTime,
                    activityPrice:this.submitData.activityPrice===0?0:this.submitData.activityPrice||'',
                    activityPriceDescription:this.submitData.activityPriceDescription||'',
                    activityStartTime:this.submitData.activityStartTime,
                    cardId:this.submitData.cardId,
                    priceDescription:this.submitData.priceDescription,
                    firstPrice:this.submitData.firstPrice===0?0:this.submitData.firstPrice||'',
                    firstPriceDescription:this.submitData.firstPriceDescription||'',
                    id:this.submitData.id,
                    name:this.submitData.name,
                    price:this.submitData.price===0?0:this.submitData.price||'',
                    priceType:this.submitData.priceType,
                    projectId:this.submitData.projectId,
                    remark:this.submitData.remark,
                    sort:this.submitData.sort,
                    type:this.submitData.type,
                    userId:this.submitData.userId,
                    username:this.submitData.username,
                    validTime:this.submitData.validTime
                }
                this.api.editCardDuration(params).then(response => {
                    if(response.status === 200) {
                        this.PUBLIC_Methods.apiNotify('修改会员卡时长成功', 'success')
                    } else {
                        this.PUBLIC_Methods.apiNotify('修改会员卡时长失败', 'warning')
                    }
                    this.$emit('up-date')
                    this.loading = false
                }).catch( () => {
                    this.loading = false;
                    this.$emit('close')
                }) 
            }
            } else {
                return false;
            }
            });
        },
	}
}
</script>
