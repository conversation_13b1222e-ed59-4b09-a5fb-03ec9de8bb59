<template>
  <section>
    <el-form :model="submitData"
             ref="submitRef"
             class="rule-form info-form"
             :rules="rules"
             label-width="100px">
      <el-row :gutter="20"> 
        <el-col :span="24">
          <el-row>
            <el-col :span="12">
              <el-form-item label="会员名称" prop="cardName">
                <el-input v-model="submitData.cardName" placeholder="请输入 ≤ 12字的会员名称" maxlength="12" show-word-limit></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="会员属性" prop="propertyName">
                <el-select v-model="submitData.propertyName" :disabled="!!dataId" clearable placeholder="请选择" style="width:100%">
                  <el-option  v-for="(item,index) in memberList" :key="index" :label="item.name" :value="item.name"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="会员说明" prop="remark">
                <el-input v-model="submitData.remark" placeholder="请输入关于该会员的说明"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <!-- <el-row>
            <el-col :span="12">
              <el-form-item label="会员时长" prop="validTime">
                <el-select v-model="submitData.validTime" clearable placeholder="请选择" style="width:100%">
                  <el-option label="月度（30天）" :value="30"></el-option>
                  <el-option label="季度（90天）" :value="90"></el-option>
                  <el-option label="年度（365天）" :value="365"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row> -->
          <!-- <el-row>
            <el-col :span="12">
              <el-form-item label="价格类型" prop="cardPriceType">
                <el-select v-model="submitData.cardPriceType" clearable placeholder="请选择" style="width:100%">
                  <el-option label="常规价格" :value="1"></el-option>
                  <el-option label="连续包价格" :value="2"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row> -->
          <!-- <el-row>
            <el-col :span="12">
              <el-form-item label="价格" prop="cardPrice" class="flex-item">
                <el-input-number style="margin-right:10px; min-width: 150px" v-model="submitData.cardPrice" :precision="2" :min="0"></el-input-number>元
                <el-input style="width:100%; margin-left:30px" v-model="submitData.cardPriceDescription" placeholder="请输入价格说明"></el-input>
              </el-form-item>
            </el-col>
          </el-row> -->
          <!-- <el-row>
            <el-col :span="12">
              <el-form-item label="首期价" prop="firstPrice" class="flex-item" label-width="150px">
                <el-input-number style="margin-right:10px; min-width: 150px" v-model="submitData.firstPrice" :precision="2" :min="0"></el-input-number>元
                <el-input style="width:100%; margin-left:30px" v-model="submitData.firstPriceDescription" placeholder="请输入价格说明"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="活动价" prop="activityPrice" class="flex-item" label-width="150px">
                <el-input-number style="margin-right:10px; min-width: 150px" v-model="submitData.activityPrice" :precision="2" :min="0"></el-input-number>元
                <el-input style="width:100%; margin-left:30px" v-model="submitData.activityPriceDescription" placeholder="请输入价格说明"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="活动时间" prop="detailTime">
                <ms-picker :model.sync="submitData.detailTime" :config="pickerConfig" type="datetimerange" style="width: 100%"></ms-picker>
              </el-form-item>
            </el-col>
          </el-row> -->
          <el-row>
            <el-col :span="12">
              <el-form-item label="展示顺序" prop="sort" class="flex-item">
                <el-input-number style="margin-right:10px; min-width: 150px" v-model="submitData.sort" :step="1" step-strictly :min="0"></el-input-number>
                <el-checkbox style="margin-left:80px" v-model="submitData.checked" :true-label='1' :false-label='0'>默认选中</el-checkbox>
              </el-form-item>
            </el-col>
          </el-row>
        </el-col>
      </el-row>
    </el-form>
  </section>
</template>

<script>
import { mapGetters } from "vuex";
export default {
	name: "ms-member-content",
	data () {
    var validateFirstPrice = (rule, value, callback) => {
      if (this.submitData.firstPrice && !(this.submitData.cardPrice > this.submitData.firstPrice)) {
        callback(new Error('首期价需小于价格'));
      } else {
        callback();
      }
    };
    var validateActivityPrice = (rule, value, callback) => {
      if (this.submitData.activityPrice && !(this.submitData.cardPrice > this.submitData.activityPrice)) {
        callback(new Error('活动价需小于价格'));
      } else {
        callback();
      }
    };
		return {
      dataId: this.$route.query.id || 0,
      rules: {
        cardName: [
          { required: true, message: "请输入会员名称", trigger: 'change' }
        ],
        propertyName: [
          { required: true, message: "请选择会员属性", trigger: 'change' }
        ],
        validTime: [
          { required: true, message: "请选择会员时长", trigger: 'change' }
        ],
        cardPriceType: [
          { required: true, message: "请选择价格类型", trigger: 'change' }
        ],
        cardPrice: [
          { required: true, message: "请输入价格", trigger: 'change' },
        ],
        sort: [
          { required: true, message: "请输入展示顺序", trigger: 'change' },
        ],
        firstPrice: [
          { validator: validateFirstPrice, trigger: 'blur' }
        ],
        activityPrice: [
          { validator: validateActivityPrice, trigger: 'blur' }
        ],
      },
      pickerConfig: {
        pickerOptions: {
          disabledDate: (time) => {
            return time.getTime() < new Date().getTime() - 8.64e7;
          }
        }
      },
      memberList: [],
		}
  },
  props:["submitData"],
  components: {
  },
  computed: {
    ...mapGetters(["info"])
  },
  created() {
    this.initMemberList()
  },
	methods: {
    initMemberList() {
      let searchParams = {
        pageIndex: 1,
        pageSize: 99,
        status: 1
      }
      this.api.memberCardProperty(searchParams).then(response => {
        this.memberList = response.data || []
        if (response.status !== 200) {
          this.PUBLIC_Methods.apiNotify(
            response.message || "请求出错",
            "warning"
          )
        }
      })
      .catch((err) => (console.log(err)))
    },
    // 数据校验
    validateData() {
      return new Promise((resolve) => {
        this.$refs["submitRef"].validate( valid => {
          if (valid) {
            resolve()
          }else {
            this.$emit('changeTab','info')
          }
        })
      })
    }
	}
}
</script>

<style scoped>
.add-mail {
  cursor: pointer;
  width: 100%;
  height: 30px;
  line-height: 28px;
  border: 1px dashed #DCDFE6;
  border-radius: 4px;
  text-align: center;
  font-size: 12px;
  color: #333;
  outline: none;
  margin-top: 10px;
  background-color: rgba(0,0,0, 0);
}
.link-pos {
  position: relative;
  top: -2px;
}
.info-form /deep/ .el-checkbox {
  margin-right: 0px;
}
.info-form-right /deep/ .el-form-item {
  margin-bottom: 16px;
}
.userName {
  padding-left: 20px;
  cursor: pointer;
  color: #409EFF;
  font-size: 14px;
}
.flex-item /deep/ .el-form-item__content {
  display: flex;
  flex-direction: row;
}
</style>
