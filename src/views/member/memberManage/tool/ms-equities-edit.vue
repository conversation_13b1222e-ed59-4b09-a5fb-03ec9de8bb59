<template>
  <ms-operation-dialog>
    <template slot="content">
      <el-form ref="submitRef"
               class="rule-form"
               :model="submitData"
               :rules="rules"
               label-width="90px">
        <el-row>
          <el-row>
            <el-col :span="18">
              <el-form-item label="权益名称" prop="rightName">
                <el-input v-model="submitData.rightName" placeholder="请输入10字以内的权益名称" maxlength="10" show-word-limit></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="18">
              <el-form-item label="权益描述" prop="rightDescription">
                <el-input v-model="submitData.rightDescription" placeholder="请输入20字以内的权益描述" maxlength="20" show-word-limit type="textarea" :rows="2"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="18">
              <el-form-item label="跳转地址" prop="hrefUrl">
                <el-input v-model="submitData.hrefUrl" placeholder="请输入跳转URL"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="18">
              <el-form-item label="权益图标" prop="rightPic">
                <ms-single-image v-model="submitData.rightPic"></ms-single-image>
                <p class="hint">格式：PNG</p>
                <p class="hint">尺寸：68*68px</p>
              </el-form-item>
            </el-col>
          </el-row>
        </el-row>
      </el-form>
    </template>
    <template slot="footer">
      <el-button @click="submitForm"
                 size="mini"
                 type="primary">确 定</el-button>
      <el-button @click="chanelDialog"
                 size="mini">取 消</el-button>
    </template>
  </ms-operation-dialog>
</template>

<script>
import { mapGetters } from "vuex";
import {ms_rule_url_http} from "@/utils/form-rule.js";
export default {
  name: "ms-image-edit",
	data () {
		return {
      submitData: {
        hrefUrl: "",
        rightDescription: "",
        rightName: "",
        rightPic: "",
        sort: null
      },
      rules: {
        rightName: [
          { required: true, message: "请输入权益名称", trigger: 'change' }
        ],
        hrefUrl: [
          { validator: ms_rule_url_http, trigger: 'change' }
        ],
        rightPic: [
          { required: true, message: "请上传权益图标", trigger: 'change' }
        ],
      }
		}
	},
	props: {
		model: Object,
		operation: String
  },
  computed: {
    ...mapGetters(["info"])
  },
  created() {
    this.init()
  },
	methods: {
    init() {
      if(this.operation ==='edit') {
        this.submitData = {...this.model.row}
      } 
    },
		submitForm () {
      this.$refs["submitRef"].validate(valid => {
        if(valid) {
          this.$emit('up-date', this.submitData, this.operation, this.model.$index)
          this.chanelDialog()
        }
      })
    },
    chanelDialog () {
      this.$emit('close')
    }
	}
}
</script>
<style scoped>
.hint{
  color: rgba(0,0,0,0.4);
}
</style>
