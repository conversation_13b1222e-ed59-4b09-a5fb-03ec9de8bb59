<template>
  <section class="form-tab" v-loading="getLoading">
    <el-tabs v-model="activeName">
      <el-tab-pane label="会员信息" name="info">
        <ms-member-content ref="info" @changeTab="changeTab" :submitData.sync="submitData"></ms-member-content>
      </el-tab-pane>
      <el-tab-pane label="配置权益" name="setting">
        <ms-equities-setting ref="setting" @changeTab="changeTab" :submitData.sync="submitData"></ms-equities-setting>
      </el-tab-pane>
    </el-tabs>
    <!-- 提交按钮 -->
    <footer-tool-bar v-loading="buttonLoading">
      <template slot="tool-content">
        <el-button type="primary" @click="info_operation('save')">保存</el-button>
        <el-button type="info" @click="info_operation('back')">返回</el-button>
      </template>
    </footer-tool-bar>
  </section>
</template>

<script>
import msMemberContent from './tool/ms-member-content'
import msEquitiesSetting from './tool/ms-equities-setting'
import memberMixin from "./member-mixin"
import FooterToolBar from '@/components/ToolBar/footer-tool-bar'
import { mapGetters } from "vuex";
export default {
  name: "member-operation",
  mixins: [memberMixin],
	data () {
		return {
      dataId: this.$route.query.id ? this.$route.query.id : 0,
      buttonLoading: false,
      getLoading: false,
      submitData: {
        "activityPrice": undefined,
        "activityPriceDescription": "",
        "activityEndTime": "",
        "activityStartTime": "",
        "cardPrice": undefined,
        "cardPriceDescription": "",
        "cardPriceType": 1,
        "cardName": "",
        "checked": 0,
        "firstPrice": undefined,
        "firstPriceDescription": "",
        "propertyName": "",
        "remark": "",
        "rights": [
          // {
          //   "hrefUrl": "",
          //   "rightDescription": "",
          //   "rightName": "",
          //   "rightPic": "",
          //   "sort": null
          // }
        ],
        "sort": null,
        "validTime": null,
        "detailTime": []
      },
      activeName: 'info',
		}
  },
  created() {
    this.init()
  },
  components: {
    msMemberContent,
    msEquitiesSetting,
    FooterToolBar
  },
  computed: {
    ...mapGetters(["info"])
  },
  methods: {
    init() {
      this.dialog = false
      let id = this.dataId
      if(id !== 0) {
        this.getLoading = true;
        this.submitData.id = id;
        this.api.memberCardDetail({id:id}).then( response => {
          this.getLoading = false;
          if(response.status === 200) {
            let res = response.data
            this.submitData = {
              ...this.submitData,
              ...res,
              activityPrice: res.activityPrice ? res.activityPrice : undefined,
              cardPrice: res.cardPrice ? res.cardPrice : undefined,
              firstPrice: res.firstPrice ? res.firstPrice : undefined,
              detailTime: res.activityStartTime && res.activityEndTime ? [res.activityStartTime,res.activityEndTime] : []
            }
            this.$nextTick(()=>{
              this.$refs.setting.setSort()
            })
          } else {
            this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
          }
        }).catch(() => {
          this.getLoading = false;
        })
      } 
    },
   info_operation(val) {
      switch (val) {
        case 'save': 
        try {
             Promise.all([
              this.$refs['setting'].validateData(),
              this.$refs['info'].validateData()
            ]).then(() => {
              this.saveData()
            });
          } catch (error) {
            return;
          }
          break;
        case 'back':
          this.$router.back();
          break;
        default: break;
      }
    },
    changeTab(val) {
      this.activeName = val
    },
    saveData() {
      this.buttonLoading = true;
      this.submitData.detailTime = this.submitData.activityPrice ? this.submitData.detailTime : []
      let params = {
        ...this.submitData,
        userId: this.info.userId,
        username: this.info.userName,
        activityStartTime: this.submitData.detailTime[0] || null,
        activityEndTime: this.submitData.detailTime[1] || null
      }
      let url = this.dataId ? "memberCardModifyInfo" : "memberCardSaveInfo"
      this.api[url](params).then(response => {
        this.buttonLoading = false
        if(response.status === 200) {
          let id = this.dataId ? this.dataId : response.data.id
          this.bundleRight(id)
          // this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
      }).catch(() => this.buttonLoading = false)
    },
    bundleRight(id) {
      this.buttonLoading = true;
      let params = {
        memberCardId: id,
        rights: this.submitData.rights
      }
      let url = "memberCardBundleRight"
       this.api[url](params).then(response => {
        if(response.status === 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
          this.$router.back()
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
      }).catch(() => this.buttonLoading = false)
    }
  }
}
</script>

<style scoped>
  .live-address, .live-img .label{
    font-size: 12px;
  }
  .live-img .label {
    text-align: center;
  }
  .live-img .img {
    padding: 10px;
    text-align: center;
    height: 100px
    
  }
  .live-img .img img{
    width: auto;
    height: 100%;
  }
</style>
