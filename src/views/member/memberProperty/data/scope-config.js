const scopeConfig = {
    show: {
      createdTime: () => {
        return {
          type: 'formatTime',
          cFormat: '{y}-{m}-{d} {h}:{i}:{s}'
        }
      },
      updatedTime: () => {
        return {
          type: 'formatTime',
          cFormat: '{y}-{m}-{d} {h}:{i}:{s}'
        }
      },
      status: () => {
        return {
          type: 'status',
          rule: {
            0: { label: '已禁用', background: '#A7ADBD' },
            1: { label: '已启用', background: '#40A23F' },
          }
        }
      },
    },
    headerShow: {
      status: () => {
        return {
          type: 'dropdown',
          icon: 'icon-funnel',
          options: [
            { label: '全部', value: null },
            { label: '已禁用', value: '0' },
            { label: '已启用', value: '1' },
          ],
          operation: 'query'
        }
      },
    }
  }
  
  export default scopeConfig;
  