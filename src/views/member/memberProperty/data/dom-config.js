const domConfig = {
    tableHeader: [
      { label: '会员属性', property: 'name', width: '100' },
      { label: '创建时间', property: 'createdTime', width: '130' },
      { label: '修改时间', property: 'updatedTime', width: '130' },
      { label: '权限', property: 'permissionDescription', width: '100' },
      { label: '状态', property: 'status', width: '100' },
    ],
    soltButtons: [
    ],
    tableButtons: [
      {
        label: '编辑',
        icon: '',
        role: '',
        operation: 'edit',
        component: 'ms-memberProperty-add',
        way: 'dialog',
        params: ['id'],
        type: 'primary',
        title: '编辑会员属性',
        position: 'right',
        width: '50%',
        identify: 'memberEdit'
      },
      {
        icon: '',
        role: '',
        operation: 'editStatus',
        component: 'msMemberPropertyStatus',
        way: 'dialog',
        field: 'status',
        rule: {
          0: { label: '启用', type: 'success' },
          1: { label: '禁用', type: 'info' }
        }
      },
    ],
  
  }
  
  export default domConfig;
  