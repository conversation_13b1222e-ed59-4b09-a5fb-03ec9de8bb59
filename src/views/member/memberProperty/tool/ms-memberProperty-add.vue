<template>
  <div class="lecture">
    <el-form
          label-width="100px"
          class="rule-form"
          ref="submitRef"
          :rules="rule"
          :model="submitData"
        >
      <el-row>
        <el-col :span="24">
          <el-form-item label="会员属性">
            <el-input v-model="submitData.name" disabled ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="属性说明" prop="description">
            <el-input
              v-model="submitData.description"
              placeholder="请输入 ≤ 16字的当前属性会员的说明，会在前台页面展示"
              :maxlength="16"
              show-word-limit
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="指南权益" prop="monthGuiderDownNum">
            每月下载指南 <el-input-number v-model="submitData.monthGuiderDownNum" :step="1" step-strictly :min="0"></el-input-number> 个
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div class="buttons">
      <el-button type="primary" @click="submit">保存</el-button>
      <el-button type="info" @click="close">返回</el-button>
    </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex"

export default {
  name: "ms-memberProperty-add",
  data() {
    return {
      submitData: {},
      rule: {},
    }
  },
  props: {
    model: Object,
    operation: String,
  },
  computed: {
    ...mapGetters(["info"]),
  },
  created() {
    if (this.operation === "edit") {
      let data = Object.assign({},this.model)
      this.submitData = data
    }
  },
  methods: {
    submit() {
      this.$refs["submitRef"].validate(valid => {
        if (valid) {
          let userInfo = this.$store.getters.info || {}
          this.submitData.projectId = userInfo.projectId;
          this.submitData.userId = userInfo.userId;
          this.submitData.username = userInfo.userName;
          this.api['memberUpdateProperty'](this.submitData).then(response => {
            if (response.status === 200) {
              this.PUBLIC_Methods.apiNotify(
                response.message || "编辑成功",
                "success"
              )
              this.close()
              this.$emit("up-date")
            } else {
              this.PUBLIC_Methods.apiNotify(
                response.message || "编辑失败",
                "warning"
              )
            }
          })
        }
      })
    },
    close() {
      this.$emit("close");
    },
  },
}
</script>

<style lang="scss" scoped>
.buttons {
  position: fixed;
  bottom: 20px;
  right: 20px;
}
.hint{
  color: rgba(0,0,0,0.4);
}
</style>
