<template>
  <el-card class="box-card">
    <div slot="header" class="change-password">
      <span>修改密码</span>
    </div>
    <el-row>
      <el-col :span="10">
        <el-form :model="submitData"
             ref="submitRef"
             size="medium"
             :rules="rules"
             class="rule-form info-form"
             label-width="100px">
            <el-form-item prop="oldPassword" label="原密码">
                <el-input v-model="submitData.oldPassword"
                          placeholder="请输入现在使用的密码"
                          size="mini"
                          show-password></el-input>
            </el-form-item>
            <el-form-item prop="newPassword" label="新密码">
                <el-input v-model="submitData.newPassword"
                          placeholder="请输入新密码，长度为6~20个字符"
                          size="mini"
                          show-password></el-input>
            </el-form-item>
            <el-form-item prop="newPasswordTwic" label="确认新密码">
                <el-input v-model="submitData.newPasswordTwic"
                          placeholder="请再次输入密码"
                          size="mini"
                          show-password>
                </el-input>
            </el-form-item>
        </el-form>
        <div>
          <el-button type="primary" @click="savePassword()" :loading="buttonLoading">保存</el-button>
          <el-button @click="cancelPassword()">取消</el-button>
        </div>
      </el-col>
    </el-row>
  </el-card>
</template>

<script>
import { mapGetters } from "vuex";
import {hex_md5} from "../../utils/md5";
export default {
  name: "ms-change-password",
	data () {
    var validatePass = (rule, value, callback) => {
      if (value !== this.submitData.newPassword) {
        callback('两次输入密码不一致')
      } else {
        callback()
      }
    };
		return {
      buttonLoading: false,
      submitData: {
        oldPassword: "",
        newPassword: "",
        newPasswordTwic: "",
      },
      rules: {
        oldPassword: [
          { required: true, message: "请输入原密码", trigger: 'blur' }
        ],
        newPassword: [
          { required: true, message: "请输入新密码", trigger: 'blur' },
          { message: '密码必须是8-20位字符且必须包含数字、大小写字母、特殊字符', pattern: /^(?=.*[A-Z])(?=.*[a-z])(?=.*[0-9])(?=.*[!@#$%^&*-.]).{8,20}$/ }
        ],
        newPasswordTwic: [
          { required: true, message: "请再次输入新密码", trigger: 'blur' },
          { message: '密码必须是8-20位字符且必须包含数字、大小写字母、特殊字符', pattern: /^(?=.*[A-Z])(?=.*[a-z])(?=.*[0-9])(?=.*[!@#$%^&*-.]).{8,20}$/ },
          { validator: validatePass, trigger: 'blur' }
        ],
      }
		}
  },
  computed: {
    ...mapGetters(["info"])
  },
  methods: {
    savePassword() {
      this.$refs.submitRef.validate( valid => {
        if(valid) {
          let params = {
            userId: this.info.userId,
            oldPassword: hex_md5(hex_md5(this.submitData.oldPassword).substr(8,16)),
            newPassword: hex_md5(hex_md5(this.submitData.newPassword).substr(8,16)),
          }
          this.buttonLoading = true;
          this.api.changePersonalUserPassword(params).then( response => {
            if(response.status === 200) {
              this.$confirm('修改密码成功，是否重新登录', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
              }).then(() => {
                this.$store.dispatch("LogOut").then(resp => {
                  this.$router.push(resp);
                });
              }).catch({})
            } else {
              this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
            }
            this.buttonLoading = false
          }).catch( () => {
            this.buttonLoading = false;
          })
        }
      })
    },
    cancelPassword() {
      this.$router.back()
    }
  }
}
</script>

<style scoped>
  .change-password {
    text-align: left;
    padding: 0;
  }
  .change-password span {
    font-weight: 500;
    font-size: 14px;
  }
</style>
