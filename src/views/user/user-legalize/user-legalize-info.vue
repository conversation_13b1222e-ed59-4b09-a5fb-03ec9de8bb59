<template>
    <section class="form-tab" v-loading="getLoading">
        <el-form ref="submitRef" class="rule-form" :model="submitData" label-width="110px">
            <el-row>
                <el-col class="col-header">认证信息</el-col>
                <template v-for="(item, index) in formConfig.formField">
                    <el-col :key="index"
                            :span="item.colSpan">
                        <el-form-item :prop="item.prop"
                                        :label="item.label">
                            <component :is="item.component"
                                    v-model="submitData[item.prop]"
                                    :model.sync="submitData[item.prop]"
                                    :width="item.width || '100%'"
                                    :disabled="item.disabled"
                                    :options="item.options || []"
                                    :type="item.type || 'text'"
                                    @bindData="bindData">
                            </component>
                        </el-form-item>
                    </el-col>
                </template>
                <el-col :span="12">
                  <el-form-item label="提交渠道">
                      <el-radio-group readonly v-model="qudao">
                        <el-radio label="梅斯医学app">梅斯医学app</el-radio>
                        <el-radio label="梅斯PC官网">梅斯PC官网</el-radio>
                        <el-radio label="梅斯H5官网">梅斯H5官网</el-radio>
                      </el-radio-group>
                  </el-form-item>
              </el-col>

                <el-col :span="24" disabled>
                    <el-form-item label="附件">
                        <div class="img-list" v-if="submitData.fileIdRequests && submitData.fileIdRequests.length > 0">
                        <el-image v-for="(item, index) in submitData.fileIdRequests" :key="index" :src="item.fileId" :preview-src-list="previewImg"></el-image>
                        </div>
                        <div class="no-img-list" v-else>暂无附件</div>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                <el-col class="col-header">审核信息</el-col>
                <el-col :span="12">
                    <el-form-item 
                      prop="authenticateStatus"
                      :rules="{
                        required: true, validator, trigger: 'change'
                      }"
                      label="审核结果">
                      <el-radio-group v-model="submitData.authenticateStatus">
                        <el-radio :label="1">审核通过</el-radio>
                        <el-radio :label="2">去除审核</el-radio>
                      </el-radio-group>
                    </el-form-item>
                </el-col>
                <template v-for="(item, index) in formConfig.formUserExtField">
                    <el-col :key="index"
                            :span="item.colSpan">
                        <el-form-item :prop="item.prop"
                                    :label="item.label">
                            <component :is="item.component"
                                    v-model="submitData[item.prop]"
                                    :model.sync="submitData[item.prop]"
                                    :width="item.width || '100%'"
                                    :disabled="item.disabled"
                                    :options="item.options || []"
                                    :type="item.type || 'text'"
                                    @bindData="bindData">
                            </component>
                        </el-form-item>
                    </el-col>
                </template>

                <el-col :span="24">
                    <el-form-item 
                      prop="authenticationRemark"
                      :rules="{
                        required: submitData.authenticateStatus == 2, message: '请填写说明', trigger: 'blur'
                      }"
                      label="原因">
                      <el-input 
                        placeholder="请输入不通过原因，字数 < 30"
                        maxlength=30
                        show-word-limit
                        v-model="submitData.authenticationRemark"
                        type="textarea">
                      </el-input>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <!-- 提交按钮 -->
        <footer-tool-bar v-loading="buttonLoading">
            <template slot="tool-content">
                <el-button type="primary" @click="saveInfo()">保存</el-button>
                <el-button type="info" @click="$router.back()">返回</el-button>
            </template>
        </footer-tool-bar>
    </section>
</template>

<script>
import FooterToolBar from '@/components/ToolBar/footer-tool-bar'
import MsDepartmentSearch from '@/components/MsCommon/ms-department-search'
import MsHospitalSearch from '@/components/MsCommon/ms-hospital-search'
import MsProfessionalCascader from '@/components/MsCommon/ms-professional-lazycascader'
import MsAreaCascader from '@/components/MsCommon/ms-area-lazycascader'
import formConfig from "./data/form-config";
import { mapGetters } from "vuex";
export default {
    name: "user-legalize-info",
    data() {
        return {
            qudao: "",
            getLoading: false,
            buttonLoading: false,
            formConfig: formConfig,
            //common
            operationLocal: "",
            submitData: {
                thisUserId: 0, // 当前记录用户id
                authenticationRemark: "", // 审核说明
                operationStatus: 0, // 操作状态
                userName: "", // 账号
                realName: "", // 真实姓名
                email: "", // 邮箱
                mobile: "", // 手机
                provinceId: 0, // 省ID
                provinceName: "", // 省名
                cityId: 0, // 市ID
                cityName: "", // 市名
                districtId: 0, // 区ID
                districtName: "", // 区名
                companyId: 0, // 公司Id
                companyName: "", // 公司名
                departmentId: 0, // 科室ID
                departmentName: "", // 科室名
                professionalId: 0, // 职称ID
                professionalName: "", // 职称名
                professionalCatId: 0, // 职称分类ID
                professionalCatName: "", // 职称分类名
                professionNumber: "", // 医生编号
                fileIdRequests: [], // 上传附件

                authenticateStatus: ""
            },
            previewImg: []
        };
    },
    components: {
        MsDepartmentSearch,
        MsHospitalSearch,
        MsProfessionalCascader,
        MsAreaCascader,
        FooterToolBar
    },
    computed: {
      ...mapGetters(["info"])
    },
    created() {
        this.getUserId();
    },
    methods: {
        validator(rule, value, callback) {
          if (!value || value == 0) {
            return callback(new Error('请选择审核操作'));
          } else {
            callback();
          }
        },
        getUserId() {
            let params = {
                id: this.$route.query.id
            };
            this.getLoading = true;
            this.api.getCertificationProjectDetail(params).then(res => {
                if (res.status === 200) {
                    this.qudao = res.data.authenticationSource
                    this.submitData = {
                        ...res.data,
                        areaMap: [res.data.provinceId, res.data.cityId, res.data.districtId],
                        professionalMap: res.data.professionalId ? [res.data.professionalCatId, res.data.professionalId] : [res.data.professionalCatId]
                    };
                    this.previewImg = res.data.fileIdRequests && res.data.fileIdRequests.map(v=> {
                        return v.fileId
                    })
                }
                this.getLoading = false;
                
            });
        },
        saveInfo() {
          this.$refs["submitRef"].validate(valid => {
            if (valid) {
              let params = {
                  ...this.submitData,
                  thisUserId: this.submitData.userId,
                  operationStatus: this.submitData.authenticateStatus,
                  operationType: 2,
                  userId: this.info.userId, // 本账号的id
                  username: this.info.userName, // 本账号的用户名
              };
              delete params.mobile
              this.buttonLoading = true
              this.api.examineUserCertification(params).then(response => {
                  if (response.status === 200) {
                      this.PUBLIC_Methods.apiNotify(response.message || "请求成功","success");
                  } else {
                      this.PUBLIC_Methods.apiNotify(response.message || "请求出错","warning");
                  }
                  this.$router.back()
                  this.buttonLoading = false;
              }).catch(() => {
                  this.buttonLoading = false;
              });
            }
          });

        },
        bindData(val) {
            if(val.operation === 'department') {
                this.submitData.departmentId = val.model.id;
                this.submitData.departmentName = val.model.name;
            } else if (val.operation === 'hospital') {
                this.submitData.companyId = val.model.id;
                this.submitData.companyName = val.model.name;
            } else if (val.operation === 'area') {
                this.submitData.provinceId = val.model[0].id;
                this.submitData.provinceName = val.model[0].name;
                this.submitData.cityId = val.model[1].id;
                this.submitData.cityName = val.model[1].name;
                this.submitData.districtId = val.model[2].id;
                this.submitData.districtName = val.model[2].name;
            } else if (val.operation === 'professional') {
                this.submitData.professionalCatId = val.model[0].id;
                this.submitData.professionalCatName = val.model[0].name;
                this.submitData.professionalId = val.model[1] ? val.model[1].id : 0;
                this.submitData.professionalName = val.model[1] ? val.model[1].name : '';
            }
        } 
    }
}
</script>

<style scope>
    .col-header {
        width:100%;
        background-color:#DFE3EB;
        padding:10px;color:#000;
        font-weight:600;
        font-size:14px;
        margin-bottom:10px;
        text-align: left;
    }
    .img-list .el-image>img {
        width: auto;
        height: 150px;
        margin: 0 10px 10px 0;
    }
    .no-img-list {
        width: 120px;
        height: 120px;
        line-height: 120px;
        border-radius: 3px;
        background-color: #F5F7FA;
        color: #C0C4CC;
        text-align: center;
        font-size: 14px;
    }
</style>

