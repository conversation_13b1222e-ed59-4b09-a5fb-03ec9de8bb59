<template>
	<ms-table
		:currentPage="searchParams.pageIndex"
		:loading="loading"
		:operationButtons="domConfig.tableButtons"
		:pageSize="searchParams.pageSize"
		:scopeConfig="scopeConfig.show"
    :scopeHeader="scopeConfig.headerShow"
		:tableData="list"
		:tableHeader="domConfig.tableHeader"
		:total="total"
    :showSelection="true"
		@current-change="current_change"
		@operation-change="operation_change"
		@size-change="size_change"
    @header-operation="header_operation"
    @handleSelectionChange="handleSelectionChange"
    class="table-svg"
	>
        <!-- 列表搜索去区域插槽 -->
		<template slot="ms-table-header">
			<div class="slot-search">
				<template v-for="(searchItem, key) in domConfig.listSearch">
					<component
						:index="searchItem.index || ''"
						:is="searchItem.component"
						:key="key"
            :width="searchItem.width || '150px'"
            :model.sync="searchParams[searchItem.model]"
            :label="searchItem.label"
						:operation="searchItem.operation || ''"
						:options="searchItem.options || []"
						:placeholder="searchItem.placeholder || ''"
						:type="searchItem.type || ''"
            :multiple="searchItem.multiple"
            :disabled="searchItem.disabled" 
					></component>
				</template>
                <div class="inlineBlock">
                <el-button @click="handleClick('query')" type="primary" plain icon="el-icon-search">查询</el-button>
                <el-button @click="handleClick('reset')">重置</el-button>
                </div>
			</div>
      <div class="slot-button-article clearfix">
				<template v-for="(item, index) in domConfig.soltButtons">
					<el-button :key="index" 
                     :type="item.type" 
                     size="mini"
                     :icon="item.icon"
                     @click="operation_change({operation: item})"
                     plain>{{ item.label }}</el-button>
				</template>
			</div>
			<el-dialog :visible.sync="dialog" 
                 closeable 
                 show-close
                 :close-on-click-modal="false"
                 :width="dialogWidth"
                 :title="dialogTitle">
				<component :is="dialogComponent" 
                   :model="scopeInfo" 
                   :operation="dialogOperation" 
                   @close="dialog = !dialog" 
                   @up-date="init" 
                   v-if="dialog"></component>
			</el-dialog>
      <!--大图--> 
      <el-image-viewer
        v-if="showImageViewer"
        :on-close="closeImageViewer"
        :url-list="viewerImgList"
      />
		</template>
	</ms-table>
</template>

<script>
import ElImageViewer from 'element-ui/packages/image/src/image-viewer'
import domConfig from './data/dom-config'
import scopeConfig from './data/scope-config'
import tableMixins  from "../../common/mixins/table"
import userLegalizeOpera from "./tool/user-legalize-opera"
import msLegalizeOperation from "./tool/ms-legalize-operation"
export default {
  name: "ms-user-legalize",
  mixins: [tableMixins],
  data () {
    return {
        domConfig,
        scopeConfig,
        searchParams: { // => 用户列表查询传参
          userName: "",
          email: "",
          mobile: ""
        },
        showImageViewer: false,
        viewerImgList: [],
    }
  },
  components: {
      ElImageViewer,
      userLegalizeOpera,
      msLegalizeOperation
  },
  methods: {
    apiInit (params) {
      this.dialog = false;
      let searchParams = {...params}
      if (searchParams.createTime) {
        searchParams.startDate = searchParams.createTime[0] || ''
        searchParams.endDate = searchParams.createTime[1] || ''
      }
      this.api.getUserCertificationPage(searchParams).then(response => {
        this.loading = false
        this.total = response.totalSize || 0;
        if (response.status === 200) {
          this.list = response.data.map(v => {
            return {
              ...v,
              fileIdItems: !v.fileIdItems ? '':JSON.parse(v.fileIdItems).map(n=> {return n.fileId})
            }
          })
        } else {
          this.list = []
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
      }).catch(() => this.loading = false)
    },
    operation_change_module(val) {
      console.log(val)
      if (val.operation.way === 'image-preview') {
        this.viewerImgList = val.model.fileIdItems
        this.showImageViewer = true;
      }
    },
    closeImageViewer () {
      this.showImageViewer = false;
    }
  }
};
</script>
