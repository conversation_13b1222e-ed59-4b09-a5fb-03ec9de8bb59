const formConfig = {
  formField: [
    {
      label: '认证来源',
      prop: 'authenticationSource',
      colSpan: 12,
      component: 'ms-input',
      disabled: true
    },
    {
      label: 'ID',
      prop: 'userId',
      colSpan: 12,
      component: 'ms-input',
      disabled: true
    },
    {
      label: '账号',
      prop: 'userName',
      colSpan: 12,
      component: 'ms-input'
    },
    {
      label: '真实姓名',
      prop: 'realName',
      colSpan: 12,
      component: 'ms-input'
    },
    {
      label: '城市',
      prop: 'areaMap',
      colSpan: 12,
      component: 'ms-area-cascader'
    },
    {
      label: '单位',
      prop: 'companyName',
      colSpan: 12,
      component: 'ms-hospital-search'
    },
    {
      label: '专业背景(科室)',
      prop: 'departmentName',
      colSpan: 12,
      component: 'ms-department-search'
    },
    {
      label: '手机号',
      prop: 'mobile',
      colSpan: 12,
      component: 'ms-input',
      disabled: true
    },
    {
      label: '邮箱',
      prop: 'email',
      colSpan: 12,
      component: 'ms-input'
    },
    {
      label: '职业编号',
      prop: 'professionNumber',
      colSpan: 12,
      component: 'ms-input'
    },
  ],
  formUserExtField: [
    // {
    //   label: '审核操作',
    //   prop: 'authenticateStatus',
    //   colSpan: 12,
    //   component: 'ms-radio',
    //   options: [
    //     { label: '审核通过', value: 1 },
    //     { label: '去除审核', value: 2 }
    //   ]
    // },
    {
      label: '认证类别',
      prop: 'professionalMap',
      colSpan: 12,
      component: 'ms-professional-cascader'
    },
    {
      label: '操作人',
      prop: 'submissionBy',
      colSpan: 12,
      component: 'ms-input',
      disabled: true
    },
    {
      label: '操作时间',
      prop: 'submissionTime',
      colSpan: 12,
      component: 'ms-input',
      disabled: true
    },
    // {
    //   label: '说明',
    //   prop: 'authenticationRemark',
    //   colSpan: 24,
    //   type: 'textarea',
    //   maxRows: 4,
    //   component: 'ms-input'
    // }
  ]
}

export default formConfig;
