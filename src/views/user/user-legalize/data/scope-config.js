const scopeConfig = {
  show: {
    fileIdItems: () => {
      return {
        type: 'image',
        config: {
          way: 'image-preview'
        }
      }
    },
    submissionTime: () => {
      return {
        type: 'formatTime',
        cFormat: '{y}-{m}-{d} {h}:{i}'
      }
    },
    authenticateStatus: () => {
      return {
        type: 'status',
        rule: {
          1: { label: '已认证', background: '#40A23F' },
          0: { label: '未认证', background: '#A7ADBD' },
          2: { label: '认证失败', background: '#C17CA3' }
        }
      }
    },
    userName: () => {
      return {
        type: 'webLink',
        config: {
          role: '',
          operation: 'edit',
          way: 'page',
          path: 'user-detail',
          params: [{
            keyName: 'id',
            valName: 'userId'
          }],
        }
      }
    }
  },
  headerShow: {
    authenticateStatus: () => {
      return {
        type: 'dropdown',
        icon: 'icon-funnel',
        options: [
          { label: '全部', value: null },
          { label: '已认证', value: 1 },
          { label: '未认证', value: 0 },
          { label: '认证失败', value: 2 }
        ],
        operation: 'query'
      }
    },
    professionalCatName: () => {
      return {
        type: 'dropdown',
        icon: 'icon-funnel',
        options: [
          { label: '全部', value: null },
          { label: '医生', value: '医生' },
          { label: '护士', value: '护士' },
          { label: '药师', value: '药师' },
          { label: '技师', value: '技师' },
          { label: '学生', value: '学生' },
          { label: '企业', value: '企业' },
          { label: '科研', value: '科研' },
          { label: '教学', value: '教学' }
        ],
        operation: 'query'
      }
    }
  },
}

export default scopeConfig
