const searchConfig = {
  listSearch: [
    {
      label: '用户名',
      placeholder: '请输入用户名',
      model: 'userName',
      component: 'ms-input',
      width: '130px'
    },
    {
      label: '手机号',
      placeholder: '请输入手机名',
      model: 'mobile',
      component: 'ms-input',
      width: '130px'
    },
    {
      label: '邮箱',
      placeholder: '请输入邮箱',
      model: 'email',
      component: 'ms-input',
      width: '130px'
    },
    {
      label: '操作时间',
      placeholder: '请选择时间段',
      model: 'createTime',
      component: 'ms-picker',
      type: 'daterange',
      width: '240px'
    }
  ],
  tableHeader: [
    { label: 'ID', property: 'id', sortable: true, width: 80 },
    { label: '手机号', property: 'mobile' },
    { label: '用户名', property: 'userName' },
    { label: '真实姓名', property: 'realName' },
    { label: '单位', property: 'companyName' },
    { label: '科室', property: 'departmentName' },
    { label: '认证类别', property: 'professionalCatName' },
    { label: '状态', property: 'authenticateStatus' },
    { label: '附件', property: 'fileIdItems', width: '100' },
    { label: '操作时间', property: 'submissionTime', sortable: true, width: 130 },
    { label: '操作人', property: 'submissionBy' }
  ],
  tableButtons: [
    {
      label: '查看详情',
      icon: '',
      role: '',
      operation: 'edit',
      component: 'msUserInfo',
      way: 'page',
      path: 'legalize-info',
      params: ['id'],
      type: 'primary',
      title: '认证信息',
      tdWidth: '100px',
    },
    {
      icon: '',
      role: '',
      operation: 'edit',
      component: 'userLegalizeOpera',
      title: "审核操作",
      way: 'dialog',
      field: 'authenticateStatus',
      rule: {
        0: { label: '审核', type: 'success' },
        2: { label: '审核', type: 'success' },
        1: { label: '去审', type: 'info' },
      }
    },
  ],
  soltButtons: [
    {
      label: '批量审核',
      type: 'primary',
      operation: 'approval',
      component: 'msLegalizeOperation',
      way: 'batch',
      identify: 'batch_approval'
    },
    {
      label: '批量去审',
      type: 'primary',
      operation: 'toreview',
      component: 'msLegalizeOperation',
      way: 'batch',
      identify: 'batch_toreview'
    }
  ]
}


export default searchConfig;
