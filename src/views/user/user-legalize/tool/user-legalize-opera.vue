<template>
  <ms-operation-dialog>
    <template slot="content">
      <el-form
        ref="submitRef"
        class="rule-form"
        :model="submitData"
        label-width="80px"
      >
        <el-col :span="20">
          <el-form-item 
            label="操作" 
            prop="authenticateStatus"
            :rules="{
              required: true, validator, trigger: 'change'
            }"
          >
            <el-radio-group v-model="submitData.authenticateStatus">
              <el-radio :label="1">通过审核</el-radio>
              <el-radio :label="2">去除审核</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="20">
          <el-form-item 
            label="说明"
            prop="authenticationRemark"
            :rules="{
              required: submitData.authenticateStatus == 2, message: '请填写说明', trigger: 'blur'
            }"
          >
            <el-input
              type="textarea"
              :rows="4"
              v-model="submitData.authenticationRemark"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-form>
    </template>
    <template slot="footer">
      <el-button
        style="margin-top:20px"
        @click="submitForm"
        :loading="loading"
        size="mini"
        type="primary"
      >保 存</el-button>
      <el-button @click="$emit('close')" size="mini">关 闭</el-button>
    </template>
  </ms-operation-dialog>
</template>

<script>
import { mapGetters } from "vuex";
export default {
  name: "user-legalize-opera",
  data() {
    return {
      loading: false,
      //common
      operationLocal: "",
      submitData: {
        authenticateStatus: "",
        authenticationRemark: "", // 审核说明
      }
    };
  },
  props: {
    model: Object,
    operation: String
  },
  computed: {
    ...mapGetters(["info"])
  },
  created() {
    this.init()
  },
  methods: {
    validator(rule, value, callback) {
      if (!value || value == 0) {
        return callback(new Error('请选择审核操作'));
      } else {
        callback();
      }
    },
    init() {
      this.operationLocal = this.operation || this.$route.query.operation;
      this.submitData = { ...this.model };
    },
    submitForm() {
      console.log(this.submitData.authenticateStatus)
      this.$refs["submitRef"].validate(valid => {
        if (valid) {
          console.log(valid)
          this.loading = true;
          this.submitEdit();
        }
      });
    },
    submitEdit() {
      let params = {
        userId: this.info.userId, // 本账号的id
        username: this.info.userName, // 本账号的用户名
        thisUserId: this.model.userId, // 被认证的用户id
        id: this.model.id, // 被认证的id -- 主键
        authenticationRemark: this.submitData.authenticationRemark,
        operationStatus: this.submitData.authenticateStatus,
        operationType: 1
      };
      this.api
        .examineUserCertification(params)
        .then(response => {
          if (response.status === 200) {
            this.PUBLIC_Methods.apiNotify(
              response.message || "请求成功",
              "success"
            );
          } else {
            this.PUBLIC_Methods.apiNotify(
              response.message || "请求出错",
              "warning"
            );
          }
          this.$emit("up-date");
          this.loading = false;
        })
        .catch(() => {
          this.loading = false;
          this.$emit("close");
        });
    }
  }
};
</script>
