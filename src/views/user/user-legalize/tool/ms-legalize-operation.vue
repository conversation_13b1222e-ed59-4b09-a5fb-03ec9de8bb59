<template>
  <ms-operation-dialog :title="`是否要${operationStatus === 1 ? '审核' : operationStatus === 2 ? '去审' : ''}以下用户`">
    <template slot="content">
      <el-tag v-for="(item, index) in nameArr" 
              :key="index" 
              type="info" 
              style="margin: 0 5px 5px 0">{{item}}</el-tag>
    </template>
    <template slot="content">
      <div style="display:flex;flex-direction:row;margin-top:20px">
        <span >说明</span>
        <el-input
          style="flex:1;margin-left:10px"
          type="textarea"
          :rows="4"
          v-model="authenticationRemark"
        ></el-input>
      </div>
    </template>
    <template slot="footer">
      <el-button @click="submitForm"
                 :loading="loading"
                 size="mini"
                 type="primary">确 定</el-button>
      <el-button @click="$emit('close')"
                 size="mini">取 消</el-button>
    </template>
  </ms-operation-dialog>
</template>

<script>
export default {
	name: "ms-legalize-operation",
	data () {
		return {
      loading: false,
      userInfo: {},
      operationStatus: null,
      ids: [],
      nameArr: [],
      authenticationRemark: ''
		}
	},
	props: [
		"model",
		"operation"
  ],
  created() {
    this.userInfo = this.$store.getters.info || {}
    let arr = []
    let showArr = []
    let operationLocal = this.operation || this.$route.query.operation
    this.operationStatus = operationLocal === 'approval' ? 1 : operationLocal === 'toreview' ? 2 : null;
    if (this.PUBLIC_Methods.isArrayFn(this.model)) {
      this.model.forEach(item => {
        arr.push(item.id)
        showArr.push(item.userName)
      });
    } else {
      arr.push(this.model.id)
      showArr.push(this.model.userName)
    }
    this.ids = arr
    this.nameArr = showArr
  },
	methods: {
		submitForm () {
      this.loading = true;
      let params = {
        userId: this.userInfo.userId,
        username: this.userInfo.userName,
        operationStatus: this.operationStatus,
        ids: this.ids,
        authenticationRemark: this.authenticationRemark
      }
      this.api.batchExamineUserCertification(params).then(response => {
        if(response.status === 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
        this.$emit('up-date')
        this.loading = false
      }).catch( () => {
        this.loading = false;
        this.$emit('close')
      })
		}
	}
}
</script>
