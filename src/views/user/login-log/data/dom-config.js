const searchConfig = {
  listSearch: [
    {
      label: '用户加密Id',
      placeholder: '请输入用户加密Id',
      model: 'encryptionUserId',
      component: 'ms-input'
    },
    {
      label: '登录时间',
      placeholder: '请选择登录时间段',
      model: 'auditTime',
      component: 'ms-picker',
      type: 'daterange',
      width: '240px'
    },
    {
      label: '登录状态',
      placeholder: '请选择登录状态',
      model: 'login',
      component: 'ms-select-filter',
      options: [
        { name: '登录', label: '0' },
        { name: '未登录', label: '1' },
      ],
      tacitlyApprove: '登录'
    }
  ],
  tableHeader: [
    { label: '用户加密Id', property: 'encryptionUserId' },
    { label: '登录ip', property: 'loginIp' },
    { label: '用户名', property: 'userName' },
    { label: '项目名称', property: 'projectName' },
    { label: '登录时间', property: 'loginTime', sortable: true },
    { label: '登录来源', property: 'loginSrc'},
    { label: '登录备注', property: 'loginRemark'}
  ],
  tableButtons: [
  ],
}


export default searchConfig
