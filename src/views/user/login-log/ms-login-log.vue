<template>
  <ms-table
    :currentPage="searchParams.pageIndex"
    :loading="loading"
    :operationButtons="domConfig.tableButtons"
    operation="operationScopeButtonInfo"
    :pageSize="searchParams.pageSize"
    :scopeConfig="scopeConfig.show"
    :scopeHeader="scopeConfig.headerShow"
    :tableData="list"
    :tableHeader="domConfig.tableHeader"
    :total="total"
    @current-change="current_change"
    @operation-change="operation_change"
    @size-change="size_change"
    @header-operation="header_operation"
  >
    <!-- 列表搜索去区域插槽 -->
    <template slot="ms-table-header">
      <div class="slot-search">
        <template v-for="(searchItem, key) in domConfig.listSearch">
          <component
            :index="searchItem.index || ''"
            :is="searchItem.component"
            :key="key"
            :width="searchItem.width || '150px'"
            :model.sync="searchParams[searchItem.model]"
            :label="searchItem.label"
            :operation="searchItem.operation || ''"
            :options="searchItem.options || []"
            :placeholder="searchItem.placeholder || ''"
            :type="searchItem.type || ''"
            :tacitlyApprove = "searchItem.tacitlyApprove"
          ></component>
        </template>
        <div class="inlineBlock">
          <el-button @click="handleClick('query')" type="primary" plain icon="el-icon-search">查询</el-button>
          <el-button @click="handleClick('reset')">重置</el-button>
        </div>
      </div>
      <el-dialog :visible.sync="dialog"
                 closeable
                 show-close
                 :close-on-click-modal="false"
                 :width="dialogWidth"
                 :title="dialogTitle">
        <component :is="dialogComponent"
                   :model="scopeInfo"
                   :operation="dialogOperation"
                   @close="dialog = !dialog"
                   @up-date="init"
                   v-if="dialog"></component>
      </el-dialog>
    </template>
  </ms-table>
</template>

<script>
import loginMixin from './login-mixin'
import tableMixins from '../../common/mixins/table'

export default {
  name: 'ms-user-manage',
  mixins: [tableMixins, loginMixin],
  data () {
    return {
      searchParams: { // => 用户列表查询传参
        // status: null,
        // userName: "",
        // realName: "",
        // email: "",
        // mobile: "",
        // createTime: [],
        // strStartCreateTime: "",
        // strEndCreateTime: ""
      }
    }
  },
  methods: {
    apiInit () {
      this.dialog = false
      this.loading = true
      let currentTime =  `${new Date().getFullYear()}-${new Date().getMonth() + 1}-${new Date().getDate()}`
      this.searchParams['startTime'] = `${currentTime} 00:00:00`
      this.searchParams['endTime'] =  `${currentTime} 23:59:59`
      if (Array.isArray(this.searchParams.auditTime) && this.searchParams.auditTime.length) {
        this.searchParams.startTime = this.searchParams.auditTime[0]
        this.searchParams.endTime = this.searchParams.auditTime[1]
      }
      if(this.searchParams.login==='未登录'){
        delete  this.searchParams.login
        let params = { ...this.searchParams }
        console.log(params,'kkk');
        
        this.api.getLoginErrorLog(params).then(response => {
        this.loading = false
        this.total = response.totalSize || 0
        this.list = response.data || []
        if (response.status !== 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
      }).catch(() => this.loading = false)
      }else{
        delete  this.searchParams.login
        let params = { ...this.searchParams }
        console.log(params,'kkk');
        this.api.getLoginLog(params).then(response => {
        this.loading = false
        this.total = response.totalSize || 0
        this.list = response.data || []
        if (response.status !== 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
      }).catch(() => this.loading = false)
      }
      
     
    }
  }
}
</script>
