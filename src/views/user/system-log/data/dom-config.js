const searchConfig = {
  listSearch: [
    {
      label: '用户加密Id',
      placeholder: '请输入用户加密Id',
      model: 'encryptionUserId',
      component: 'ms-input'
    },
    {
      label: '路由地址',
      placeholder: '请输入路由地址',
      model: 'routeAddress',
      component: 'ms-input'
    },
    {
      label: '操作时间',
      placeholder: '请选择操作时间段',
      model: 'auditTime',
      component: 'ms-picker',
      type: 'daterange',
      width: '240px'
    }
  ],
  tableHeader: [
    { label: '路由地址', property: 'routeAddress', width: '180' },
    // { label: '请求类型', property: 'requestType' },
    { label: 'ip地址', property: 'ipAddress' },
    { label: '操作类型（文案）', property: 'operateType' },
    { label: '操作加密id', property: 'encryptionUserId', sortable: true },
    { label: '操作人', property: 'userName' },
    { label: '参数json', property: 'paramsJson'},
    { label: '操作时间', property: 'createdTime' }
  ],
  tableButtons: [],
}


export default searchConfig
