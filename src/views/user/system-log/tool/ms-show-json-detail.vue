<template>
  <ms-operation-dialog>
    <template slot="content">
      <p class="content-top">
        <span class="top-label" v-html="JSON.parse(model.paramsJson)"></span>
      </p>
    </template>
  </ms-operation-dialog>
</template>

<script>
export default {
  name: 'ms-show-json-detail',
  data () {
    return {
      getLoading: true,
      content: ''
    }
  },
  props: {
    model: Object,
  },
  watch: {
    model: {
      handler (val) {
        this.model = val
      },
      deep: true
    }
  },
}
</script>

<style scoped>
  .content-top {
    margin-bottom: 10px;
    font-size: 14px;
    text-align: left;
  }

  .content-top .top-label {
    font-weight: bold;
    margin-right: 12px;
    display: inline-block;
  }
</style>
