<template>
    <ms-operation-dialog :title="title">
        <template slot="content">
        <el-tag v-for="(id, index) in strArr" 
                :key="index" 
                type="info" 
                style="margin: 0 5px 5px 0">{{id}}</el-tag>
        </template>
        <template slot="footer">
        <el-button @click="submitForm"
                    :loading="loading"
                    size="mini"
                    type="primary">确 定</el-button>
        <el-button @click="$emit('close')"
                    size="mini">取 消</el-button>
        </template>
    </ms-operation-dialog>
</template>

<script>
export default {
	name: "ms-userlabel-operation",
	data () {
		return {
      loading: false,
      userInfo: {},
      dealType: null,
      ids: [],
      strArr: [],
      title: ''
		}
	},
	props: [
		"model",
		"operation"
  ],
  created() {
    this.userInfo = this.$store.getters.info || {}
    let arr = []
    let showArr = []
    let operationLocal = this.operation || this.$route.query.operation
    if (operationLocal === 'approval') {
      this.dealType = 2
      this.title = '是否要审核以下用户标签'
    } else if (operationLocal === 'toreview') {
      this.dealType = 3
      this.title = '是否要去审以下用户标签'
    } else if (operationLocal === 'delete') {
      this.dealType = 1
      this.title = '是否要删除以下用户标签'
    }

    if (this.PUBLIC_Methods.isArrayFn(this.model)) {
      this.model.forEach(item => {
        arr.push(item.id)
        showArr.push(item.name)
      });
    } else {
      arr.push(this.model.id)
      showArr.push(this.model.name)
    }
    this.ids = arr
    this.strArr = showArr
  },
	methods: {
		submitForm () {
      this.loading = true;
      let params = {
        userId: this.userInfo.userId,
        username: this.userInfo.userName,
        optType: this.dealType,
        ids: this.ids
      }
      this.api.operationLabel(params).then(response => {
        if(response.status === 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
        this.$emit('up-date')
        this.loading = false
      }).catch( () => {
        this.loading = false;
        this.$emit('close')
      })
		}
	}
}
</script>
