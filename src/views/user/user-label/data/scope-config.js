const scopeConfig = {
  show: {
    status: () => {
      return {
        type: 'status',
        rule: {
          0: { label: '待审核', background: '#A7ADBD' },
          1: { label: '审核通过', background: '#40A23F' }
        }
      }
    },
    createdTime: () => {
      return {
        type: 'formatTime',
        cFormat: '{y}-{m}-{d} {h}:{i}'
      }
    }
  },
  headerShow: {
    status: () => {
      return {
        type: 'dropdown',
        icon: 'icon-funnel',
        options: [
          { label: '全部', value: null },
          { label: '待审核', value: 0 },
          { label: '审核通过', value: 1 }
        ],
        operation: 'query'
      }
    },
    labelType: () => {
      return {
        type: 'dropdown',
        icon: 'icon-funnel',
        options: [
          { label: '全部', value: null },
          { label: '临床', value: 0 },
          { label: '科研', value: 1 }
        ],
        operation: 'query'
      }
    },
    type: () => {
      return {
        type: 'dropdown',
        icon: 'icon-funnel',
        options: [
          { label: '全部', value: null },
          { label: '系统', value: 0 },
          { label: '用户系统', value: 1 }
        ],
        operation: 'query'
      }
    }
  }
}

export default scopeConfig;
