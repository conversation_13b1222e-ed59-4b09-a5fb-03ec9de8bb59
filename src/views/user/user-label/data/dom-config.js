const domConfig = {
  // 搜索配置
  listSearch: [
    {
      label: '标签名',
      placeholder: '请输入',
      model: 'name',
      component: 'ms-input'
    },
    {
      label: '创建人',
      placeholder: '请输入',
      model: 'createdName',
      component: 'ms-input'
    },
    {
      label: '创建时间',
      placeholder: '请选择时间段',
      model: 'createTime',
      component: 'ms-picker',
      type: 'daterange',
      width: '240px'
    }
  ],

  // 表头配置
  tableHeader: [
    { label: 'ID', property: 'id', width: '80' },
    { label: '标签名', property: 'name' },
    { label: '标签类型', property: 'labelType' },
    { label: '添加方式', property: 'type' },
    { label: '创建人', property: 'createdName' },
    { label: '创建时间', property: 'createdTime' },
    { label: '状态', property: 'status' }
  ],

  // 行内列表按钮配置
  tableButtons: [
    {
      label: '',
      icon: '',
      role: '',
      operation: '',
      component: 'msUserlabelOperation',
      way: 'dialog',
      field: 'status',
      rule: {
        1: { label: '去审', type: '', operation: 'toreview' },
        0: { label: '审核', type: 'success', operation: 'approval' }
      },
    },
    {
      label: '删除',
      way: 'dialog',
      type: 'danger',
      operation: 'delete',
      component: 'msUserlabelOperation'
    }
  ],

  // 新建项目按钮
  soltButtons: [
    { 
      label: '批量审核', 
      type: 'primary',
      operation: 'approval',
      component: 'msUserlabelOperation',
      way: 'batch',
    },
    { 
      label: '批量去审', 
      type: 'primary',
      operation: 'toreview',
      component: 'msUserlabelOperation',
      way: 'batch',
    },
    { 
      label: '批量删除', 
      type: 'info', 
      icon: 'el-icon-close',
      operation: 'delete',
      component: 'msUserlabelOperation',
      way: 'batch'
    }
  ]
}
export default domConfig