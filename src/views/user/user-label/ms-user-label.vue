<template>
  <div class="comment_manage">
    <ms-table
      :currentPage="searchParams.pageIndex"
      :loading="loading"
      :operationButtons="domConfig.tableButtons"
      :pageSize="searchParams.pageSize"
      :scopeConfig="scopeConfig.show"
      :scopeHeader="scopeConfig.headerShow"
      :tableData="list"
      :tableHeader="domConfig.tableHeader"
      :total="total"
      @current-change="current_change"
      @operation-change="operation_change"
      @size-change="size_change"
      @header-operation="header_operation"
      :showSelection="true"
      @handleSelectionChange="handleSelectionChange"
    >
      <template slot="ms-table-header">
        <div class="slot-search">
          <template v-for="(searchItem, key) in domConfig.listSearch">
            <component
              :index="searchItem.index || ''"
              :is="searchItem.component"
              :key="key"
              :width="searchItem.width || '150px'"
              :model.sync="searchParams[searchItem.model]"
              :label="searchItem.label"
              :operation="searchItem.operation || ''"
              :options="searchItem.options || []"
              :placeholder="searchItem.placeholder || ''"
              :type="searchItem.type || ''"
              :multiple="searchItem.multiple"
              :disabled="searchItem.disabled"
            ></component>
          </template>
          <div class="inlineBlock">
            <el-button @click="handleClick('query')" type="primary" plain icon="el-icon-search">查询</el-button>
            <el-button @click="handleClick('reset')">重置</el-button>
          </div>
        </div>
        <div class="slot-button-article clearfix">
          <template v-for="(item, index) in domConfig.soltButtons">
            <el-button :key="index" 
                      :type="item.type" 
                      size="mini"
                      :icon="item.icon"
                      :disabled="item.roleDisabled"
                      @click="operation_change({operation: item})"
                      plain>{{ item.label }}</el-button>
          </template>
        </div>
        <el-dialog
          :visible.sync="dialog"
          closeable
          show-close
          :close-on-click-modal="false"
          :width="dialogWidth"
          :title="dialogTitle"
        >
          <component
            :is="dialogComponent"
            :model="scopeInfo"
            :operation="dialogOperation"
            @close="dialog = !dialog"
            @up-date="init"
            v-if="dialog"
          ></component>
        </el-dialog>
      </template>
    </ms-table>
  </div>
</template>

<script>
import tableMixins from "../../common/mixins/table";
import dataMixin from "./data-mixin"
export default {
    name: "ms-user-label",
    mixins: [dataMixin,tableMixins],
    data() {
        return {
            loading: false,
            searchParams: {},
        };
    },
    methods: {
        apiInit() {
            let searchParams = { ...this.searchParams  };
            if (searchParams.createTime) {
              searchParams.startTime = searchParams.createTime[0] || ''
              searchParams.stopTime = searchParams.createTime[1] || ''
            }
            this.api.getProjectUserLabelPageList(searchParams).then(response => {
                this.loading = false;
                this.total = response.totalSize || 0;
                this.list = []
                if (response.status === 200) {
                    this.list = response.data || [];
                } else {
                    this.PUBLIC_Methods.apiNotify(response.message || "请求出错","warning");
                }
            })
            .catch(() => (this.loading = false));
        }
    }
};
</script>
