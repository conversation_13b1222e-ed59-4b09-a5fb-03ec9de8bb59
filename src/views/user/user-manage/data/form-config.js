// import {ms_rule_phone, ms_rule_mail} from "@/utils/form-rule.js";
const formConfig = {
  formField: [
    {
      label: '用户名',
      prop: 'userName',
      colSpan: 6,
      component: 'ms-input',
      disabled: true
    },
    {
      label: '真实姓名',
      prop: 'realName',
      colSpan: 6,
      component: 'ms-input'
    },
    {
      label: 'ID',
      prop: 'id',
      colSpan: 6,
      component: 'ms-input',
      disabled: true
    },
    {
      label: '注册时间',
      prop: 'createdTime',
      colSpan: 6,
      component: 'ms-picker',
      type: 'date',
      disabled: true
    },
    {
      label: '手机号',
      prop: 'mobile',
      colSpan: 12,
      component: 'ms-input',
      disabled: true
    },
    {
      label: '邮箱',
      prop: 'email',
      colSpan: 12,
      component: 'ms-input',
      disabled: true
    },
    {
      label: '微信号',
      prop: 'wechatAccount',
      colSpan: 12,
      component: 'ms-input'
    },
    {
      label: 'QQ',
      prop: 'qqAccount',
      colSpan: 12,
      component: 'ms-input'
    },
    {
      label: '状态',
      prop: 'status',
      colSpan: 12,
      component: 'ms-radio',
      options: [
        { label: '启用', value: 1 },
        { label: '禁用', value: 0 }
      ]
    },
    {
      label: '微信unionid',
      prop: 'wechatUnionid',
      component: 'ms-input',
      disabled: true,
      colSpan: 12,
    },
    // {
    //   label: '微信昵称',
    //   prop: 'wechatNickName',
    //   colSpan: 12,
    //   component: 'ms-input',
    //   disabled: true
    // },
  ],
  formUserExtField: [
    {
      label: '积分',
      prop: 'integralBase',
      colSpan: 6,
      component: 'ms-input-number',
      disabled: true
    },
    {
      label: '梅花',
      prop: 'integralUme',
      colSpan: 6,
      component: 'ms-input-number'
    },
    {
      label: '城市',
      prop: 'cityId',
      colSpan: 6,
      component: 'ms-area-cascader',
    },
    {
      label: '认证状态',
      prop: 'authenticateStatus',
      colSpan: 6,
      component: 'ms-select-local',
      options: [
        { label: '认证中', value: 0 },
        { label: '已认证', value: 1 },
        { label: '认证失败', value: 2 },
        { label: '未认证', value: -1 }
      ],
      disabled: true
    },
    {
      label: '单位',
      prop: 'companyName',
      colSpan: 12,
      component: 'ms-hospital-search'
    },
    {
      label: '科室',
      prop: 'departmentId',
      colSpan: 6,
      component: 'ms-department-search'
    },
    {
      label: '身份/职称',
      prop: 'professionalMap',
      colSpan: 6,
      component: 'ms-professional-cascader'
    },
    {
      label: '疾病名称',
      prop: 'diseaseName',
      colSpan: 12,
      component: 'ms-input'
    },
    {
      label: '医保状况',
      prop: 'socialSecurityId',
      colSpan: 6,
      component: 'ms-select-local',
      options:[
        {label: '城镇职工医保', value: 1},
        {label: '城镇居民医保', value: 2},
        {label: '新农合', value: 3},
        {label: '商业保险', value: 4},
        {label: '其他', value: 5}
      ]
    },
    {
      label: '疾病时长',
      prop: 'diseaseTime',
      colSpan: 6,
      component: 'ms-input'
    },
    {
      label: '临床擅长标签',
      prop: 'clinicalTags',
      colSpan: 24,
      component: 'ms-input'
    },
    {
      label: '研究擅长标签',
      prop: 'researchTags',
      colSpan: 24,
      component: 'ms-input'
    },
    {
      label: '个人介绍',
      prop: 'briefIntroduction',
      colSpan: 24,
      component: 'ms-input',
      type: "textarea"
    },
    {
      label: '头像',
      prop: 'avatar',
      colSpan: 12,
      component: 'ms-single-image'
    }
  ],
  rule: {
    userName: [
      { required: true, message: "请输入用户名", trigger: 'blur' }
    ],
    // realName: [
    //   { required: true, message: "请输入姓名", trigger: 'blur' }
    // ],
    email: [
      // { validator: ms_rule_mail, trigger: 'blur' }
    ],
    mobile: [
      // { validator: ms_rule_phone, trigger: 'blur' }
    ]
  }
}

export default formConfig;
