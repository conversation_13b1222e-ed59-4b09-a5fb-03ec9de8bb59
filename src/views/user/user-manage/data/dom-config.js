const searchConfig = {
  listSearch: [
    {
      label: '用户名',
      placeholder: '请输入用户名',
      model: 'userName',
      component: 'ms-input'
    },
    {
      label: '手机号',
      placeholder: '请输入手机名',
      model: 'mobile',
      component: 'ms-input'
    },
    // {
    //   label: '微信昵称',
    //   placeholder: '请输入微信昵称',
    //   model: 'wechatNickName',
    //   component: 'ms-input'
    // },
    {
      label: '邮箱',
      placeholder: '请输入邮箱',
      model: 'email',
      component: 'ms-input'
    },
    {
      label: '创建时间',
      placeholder: '请选择时间段',
      model: 'createTime',
      component: 'ms-picker',
      type: 'daterange',
      width: '240px'
    },
    {
      label: '身份',
      placeholder: '请选择身份标识',
      model: 'identityFlag',
      component: 'ms-select-local',
      options: [
        { label: '全部', value: null },
        { label: '前台用户', value: 0 },
        { label: '后台管理账户', value: 1 }
      ]
    },
    {
      label: '是否查询子项目用户',
      placeholder: '请选择',
      model: 'projectId',
      component: 'ms-select-local',
      options: [
        { label: '是', value: -1 },
        { label: '否', value: 1 },
      ]
    }
  ],
  tableHeader: [
    { label: 'ID', property: 'id', sortable: true, width: 80},
    { label: '项目ID', property: 'projectId', sortable: true, width: 70},
    { label: '手机号', property: 'mobile'},
    { label: '用户名', property: 'userName', width: 120},
    { label: '真实姓名', property: 'realName'},
    // { label: '微信昵称', property: 'wechatNickName'},
    { label: '账号状态', property: 'status' },
    { label: '最近登录时间', property: 'lastLoginTime', width: 130, sortable: true},
    { label: '创建时间', property: 'createdTime', width: 130, sortable: true}
  ],
  tableButtons: [
    {
      label: '查看',
      role: '',
      operation: 'edit',
      component: 'msUserEdit',
      way: 'page',
      path: 'user-detail',
      params: [{keyName:'id', valName:'id'}, {keyName:'flag', valName:'id'}],
      type: 'primary',
    },
    {
      label: '编辑',
      role: '',
      operation: 'edit',
      component: 'msUserEdit',
      way: 'page',
      path: 'user-detail',
      params: ['id'],
      type: 'primary',
    },
    {
      icon: '',
      role: '',
      operation: 'editStatus',
      component: 'msUserStatus',
      way: 'dialog',
      field: 'status',
      rule: {
        1: { label: '禁用', type: 'info' },
        0: { label: '启用', type: 'success' }
      }
    },
    {
      label: '积分',
      role: '',
      operation: 'edit',
      component: 'msUserIntegral',
      way: 'dialog',
      type: 'warning',
      title: '修改用户积分',
      width: '40%',
      showCallback: (val) => {
        if (val.status === 1 ) {
          return true
        } else {
          return false
        }
      },
    }
  ],
}


export default searchConfig;
