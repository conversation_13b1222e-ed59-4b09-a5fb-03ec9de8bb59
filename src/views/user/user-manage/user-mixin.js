import msUserStatus from './tool/ms-user-status'
import msUserPassword from './tool/ms-user-password'
import msUserIntegral from './tool/ms-user-integral'
// import msUserEdit from './tool/ms-user-edit'
import domConfig from "./data/dom-config";
import bioonConfig from "./data/bioon-config";
import scopeConfig from "./data/scope-config";

export default {
  data() {
    return {
      domConfig: domConfig, // => dom展示数据
      bioonConfig: bioonConfig, // => dom展示数据
      scopeConfig: scopeConfig, // => 显示信息配置 
    }
  },
  components: {
    msUserPassword,
    msUserStatus,
    // msUserEdit,
    msUserIntegral
  }
}
