<template>
  <ms-table
    :currentPage="searchParams.pageIndex"
    :loading="loading"
    :operationButtons="$route.path.includes('Bioon')?bioonConfig.tableButtons:domConfig.tableButtons"
    :pageSize="searchParams.pageSize"
    :scopeConfig="scopeConfig.show"
    :scopeHeader="scopeConfig.headerShow"
    :tableData="list"
    :tableHeader="domConfig.tableHeader"
    :total="total"
    @current-change="current_change"
    @operation-change="operation_change"
    @size-change="size_change"
    @header-operation="header_operation"
  >
    <!-- 列表搜索去区域插槽 -->
    <template slot="ms-table-header">
      <div class="slot-search">
        <template v-for="(searchItem, key) in domConfig.listSearch">
          <component
            :index="searchItem.index || ''"
            :is="searchItem.component"
            :key="key"
            :width="searchItem.width || '150px'"
            :model.sync="searchParams[searchItem.model]"
            :label="searchItem.label"
            :operation="searchItem.operation || ''"
            :options="searchItem.options || []"
            :placeholder="searchItem.placeholder || ''"
            :type="searchItem.type || ''"
          ></component>
        </template>
        <div class="inlineBlock">
          <el-button @click="handleClick('query')" type="primary" plain icon="el-icon-search">查询</el-button>
          <el-button @click="handleClick('reset')">重置</el-button>
        </div>
        <p>
          <el-link type="danger"
                   :underline="false">{{statisticTip}}
          </el-link>
        </p>
      </div>
      <el-dialog :visible.sync="dialog"
                 closeable
                 show-close
                 :close-on-click-modal="false"
                 :width="dialogWidth"
                 :title="dialogTitle">
        <component :is="dialogComponent"
                   :model="scopeInfo"
                   :operation="dialogOperation"
                   @close="dialog = !dialog"
                   @up-date="init"
                   v-if="dialog"></component>
      </el-dialog>
    </template>
  </ms-table>
</template>

<script>
import userMixin from './user-mixin'
import tableMixins from '../../common/mixins/table'

export default {
  name: 'ms-user-manage',
  mixins: [tableMixins, userMixin],
  data () {
    return {
      statisticTip: '',
      searchParams: { // => 用户列表查询传参
        status: null,
        userName: '',
        realName: '',
        email: '',
        mobile: '',
        createTime: [],
        strStartCreateTime: '',
        strEndCreateTime: ''
      }
    }
  },
  mounted () {
    this.getUserCount()
  },
  methods: {
    getUserCount () {
      this.api.getUserCount().then(response => {
        if (response.data.deadlineDate && response.data.count) {
          const reg = /(\d)(?=(?:\d{3})+$)/g;
          const newNumber = response.data.count.toString().replace(reg,'$1,');
          this.statisticTip = `截止到 ${response.data.deadlineDate} 总用户量 ${newNumber}`
        }
      }).catch(() => {
      })
    },
    apiInit () {

      this.dialog = false
      this.loading = true
      if (this.searchParams.mobile && !(/^1[3456789]\d{9}$/.test(this.searchParams.mobile))) {
        this.loading = false
        this.$message({
          message: '手机号格式错误，请重新输入',
          type: 'warning'
        })
        return
      }
      if (this.searchParams.email && !(/^\w+((.\w+)|(-\w+))@[A-Za-z0-9]+((.|-)[A-Za-z0-9]+).[A-Za-z0-9]+$/.test(this.searchParams.email))) {
        this.loading = false
        this.$message({
          message: '邮箱格式错误，请重新输入',
          type: 'warning'
        })
        return
      }

      if (this.searchParams.createTime) {
        this.searchParams.strStartCreateTime = this.searchParams.createTime[0] || ''
        this.searchParams.strEndCreateTime = this.searchParams.createTime[1] || ''
      }
      let params = {
        ...this.searchParams,
      }
      this.api.getUserList(params).then(response => {
        this.loading = false
        this.total = response.totalSize || 0
        this.list = response.data || []
        if (response.status !== 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
      }).catch(() => this.loading = false)
    }
  }
}
</script>
