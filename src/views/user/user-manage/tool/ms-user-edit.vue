<template>
  <ms-operation-dialog>
    <template slot="content">
      <div v-loading="getLoading">
      <div class="user-form">
        <div class="user-form-header" @click="userShow = !userShow">
          <span>基础信息</span>
          <svg-icon icon-class="icon-xiala2-01" class-name="down-svg"></svg-icon>
        </div>
        <el-collapse-transition>
          <div v-show="userShow">
            <el-form ref="submitRef"
                  class="rule-form"
                  :model="userData"
                  :rules="formConfig.rule"
                  label-width="80px">
              <el-row>
                <template v-for="(item, index) in formConfig.formField">
                  <el-col :key="index"
                          :span="item.colSpan">
                    <el-form-item :prop="item.prop"
                                  :label="item.label">
                      <component :is="item.component"
                                :model.sync="userData[item.prop]"
                                :width="item.width || '100%'"
                                :disabled="item.disabled"
                                :options="item.options || []"
                                :type="item.type || 'text'">
                      </component>
                    </el-form-item>
                  </el-col>
                </template>
              </el-row>
            </el-form>
          </div>
        </el-collapse-transition>
      </div>
      <div class="user-form">
        <div class="user-form-header" @click="userExtShow = !userExtShow">
          <span>扩展信息</span>
          <svg-icon icon-class="icon-xiala2-01" class-name="down-svg"></svg-icon>
        </div>
        <el-collapse-transition>
          <div v-show="userExtShow">
            <el-form ref="submitRexRef"
                  class="rule-form"
                  :model="userExtData"
                  :rules="formConfig.rule"
                  label-width="85px">
              <el-row>
                <template v-for="(item, index) in formConfig.formUserExtField">
                  <el-col :key="index"
                          :span="item.colSpan">
                    <el-form-item :prop="item.prop"
                                  :label="item.label">
                      <component :is="item.component"
                                v-model="userExtData[item.prop]"
                                :model.sync="userExtData[item.prop]"
                                :width="item.width || '100%'"
                                :disabled="item.disabled"
                                :options="item.options || []"
                                :type="item.type || 'text'"
                                :showFileList="false"
                                :operation="item.operation || ''"
                                :loading="selectLoading"
                                @bindData="bindData">
                      </component>
                    </el-form-item>
                  </el-col>
                </template>
              </el-row>
            </el-form>
          </div>
        </el-collapse-transition>
      </div>
      </div>
    </template>
    <template slot="footer">
      <el-button @click="submitForm"
                 :loading="loading"
                 size="mini"
                 type="primary">确 定</el-button>
      <el-button @click="$emit('close')"
                 size="mini">取 消</el-button>
    </template>
  </ms-operation-dialog>
</template>

<script>
import {mapGetters} from 'vuex';
import MsDepartmentSearch from '@/components/MsCommon/ms-department-search'
import MsHospitalSearch from '@/components/MsCommon/ms-hospital-search'
import MsProfessionalCascader from '@/components/MsCommon/ms-professional-cascader'
import MsAreaCascader from '@/components/MsCommon/ms-area-cascader'
import formConfig from '../data/form-config' 
export default {
	name: "ms-user-edit",
	data () {
		return {
      loading: false,
      getLoading: false,
      selectLoading: false,
      userShow: true,
      userExtShow: true,
      formConfig: formConfig,
      userInfo: {},
      userData: {
        userName: "", // => 用户名
        realName: "", // => 姓名
        email: "", // => 邮箱
        wechatAccount: "", // => 微信账号
        mobile: "", // => 电话号码
        status: 1, // => 用户状态 (默认启用)
        createdTime: "", // => 注册时间
      },
      userExtData: {
        //id: 0, // => 用户单位id
        provinceId: 0, // => 省份id
        provinceName: "", // => 省份
        cityId: 0, // => 城市id
        cityName: "", // => 城市
        districtId: 0, // => 区县id
        districtName: "", // => 区县
        companyId: 0, // => 公司id
        companyName: "", // => 公司名称
        departmentId: 0, // => 科室id
        departmentName: "", // => 科室
        professionalId: 0, // => 职称id
        professionalName: "", // => 职称
        professionalCatId: 0, // => 职称分类id
        professionalCatName: "", // => 职称分类
        clinicalTags: "", // => 临床擅长标签
        researchTags: "", // => 研究擅长标签
        briefIntroduction: "", // => 个人介绍
        authenticateStatus: 0, // => 认证状态
        avatar: "", // => 头像文件url
        integralBase: 0, // => 积分
        integralUme: 0, // => 梅花
      },

      operationLocal: ""
		}
  },
	props: {
		model: Object,
		operation: String
  },
  components: {
    MsDepartmentSearch,
    MsHospitalSearch,
    MsProfessionalCascader,
    MsAreaCascader
  },
  computed: {
    ...mapGetters(["code"])
  },
  created() {
    this.init()
  },
	methods: {
    init() {
      this.userInfo = this.$store.getters.info || {}
      this.operationLocal = this.operation || this.$route.query.operation
      if(this.operationLocal === 'edit' && this.model.id) {
        this.getUserDetail(this.model.id)
      } 
    },
    getUserDetail(id) {
      let params = {
        userId: id
      }
      this.getLoading = true;
      this.api.getUserEeatil(params).then( response => {
        this.getLoading = false;
        if(response.status === 200) {
          var userRes = response.data.getUserResponse || {}
          var userExtRes = response.data.getUserExtResponse || {}
          // 积分、梅花、头像url、性别移入
          this.userData = {
            ...this.userData, 
            ...userRes, 
            id: id
          }
          this.userExtData = {
            ...this.userExtData,
            ...userExtRes,
            authenticateStatus: userRes.authenticateStatus,
            avatar: userRes.avatar, 
            integralBase: userRes.integralBase,
            integralUme: userRes.integralUme, 
          }
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '获取信息失败')
        }
      }).catch(() => this.getLoading = false)
    },
		submitForm() {
      this.$refs.submitRef.validate( valid => {
        if(valid) {
          this.loading = true;
          if( this.model.id ) {
            this.updateUserInfo()
          } else {
            this.addUserInfo()
          }
        }
      })
    },
    // 新增用户信息
    addUserInfo() {
      let userParams = {
        updateMedsciUserRequest: {
          ...this.userData,
          avatar: this.userExtData.avatar,
          integralBase: this.userExtData.integralBase,
          integralUme: this.userExtData.integralUme
        },
        updateMedsciExtRequest: this.userExtData,
        userId: this.userInfo.userId,
        username: this.userInfo.userName,
      }
      this.api.addMedsciUserAndExt(userParams).then(response => {
        if(response.status === 200) {
          this.PUBLIC_Methods.apiNotify(response.message, 'success')
        } else {
          this.PUBLIC_Methods.apiNotify(response.message, 'warning')
        }
        this.loading = false;
        this.$emit('up-date')
      })
      
    },
    // 编辑用户信息
    updateUserInfo() {
      delete this.userData['password']
      let userExtParams = {
        updateMedsciUserRequest: {
          ...this.userData,
          avatar: this.userExtData.avatar,
          integralBase: this.userExtData.integralBase,
          integralUme: this.userExtData.integralUme
        },
        updateMedsciExtRequest: this.userExtData,
        userId: this.userInfo.userId,
        username: this.userInfo.userName,
      }
      this.api.updateMedsciUserAndExt(userExtParams).then(response => {
        if(response.status === 200) {
          this.PUBLIC_Methods.apiNotify(response.message, 'success')
        } else {
          this.PUBLIC_Methods.apiNotify(response.message, 'warning')
        }
        this.loading = false;
        this.$emit('up-date')
      })
    },
    bindData(val) {
      if(val.operation === 'department') {
        this.userExtData.departmentId = val.model.id;
        this.userExtData.departmentName = val.model.name;
      } else if (val.operation === 'hospital') {
        this.userExtData.companyId = val.model.id;
        this.userExtData.companyName = val.model.name;
      } else if (val.operation === 'area') {
        this.userExtData.cityId = val.model.id;
        this.userExtData.cityName = val.model.name;
      } else if (val.operation === 'professional') {
        this.userExtData.professionalId = val.model.id;
        this.userExtData.professionalName = val.model.name;
      }
    } 
	}
}
</script>

<style lang="scss" scoped>
  .user-form {
    &-header {
      width: 100%;
      height: 34px;
      line-height: 34px;
      background: rgba(216, 221, 230, 0.8);
      color: rgba(32, 39, 61, 1);
      font-size: 12px;
      font-weight: bold;
      padding: 0 12px;
      position: relative;
      margin-bottom: 10px;
      cursor: pointer;
      .down-svg {
        position: absolute;
        right: 12px;
        width: 14px;
        height: 14px;
        top: 10px;
      }
    }
  }
</style>
