<template>
  <ms-operation-dialog>
    <template slot="content">
      <el-form ref="submitRef"
               :model="submitData"
               :rules="rules"
               label-width="85px"
               class="rule-form">
        <el-row>
          <el-col :span="14" :offset="5">
            <el-form-item label="新密码" prop="newPassword">
              <el-input v-model="submitData.newPassword" placeholder="请输入密码" show-password ></el-input>
            </el-form-item>
            <el-form-item label="确认新密码" prop="againPassword">
              <el-input v-model="submitData.againPassword" placeholder="请重新输入密码" show-password ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </template>
    <template slot="footer">
      <el-button @click="submitForm"
                 :loading="loading"
                 size="mini"
                 type="primary">确 定</el-button>
      <el-button @click="$emit('close')"
                 size="mini">取 消</el-button>
    </template>
  </ms-operation-dialog>
</template>

<script> 
import {hex_md5} from "../../../../utils/md5";
export default {
	name: "ms-user-password",
	data () {
    var againPw = (rule, value, callback) => {
      if (value !== this.submitData.newPassword) {
        callback('两次输入密码不一致')
      } else {
        callback()
      }
    };
		return {
      loading: false,
      userInfo: {},
      submitData: {
        newPassword: '',
        againPassword: ''
      },
      rules: {
        newPassword: [
          { required: true, message: "请输入新密码", trigger: 'blur' }
        ],
        againPassword: [
          { required: true, message: "请再次输入密码", trigger: 'blur' },
          { validator: againPw, trigger: 'blur' }
        ]
      },
    }
  },
	props: {
		model: Object,
		operation: String
  },
	methods: {
		submitForm() {
      let params = {
        userName: this.model.userName,
        password: hex_md5(hex_md5(this.submitData.newPassword).substr(8,16)),
      }
      this.$refs.submitRef.validate( valid => {
        if(valid) {
          this.loading = true;
          this.api.changeUserPwd(params).then( response => {
            if(response.status === 200) {
            this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
          } else {
            this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
          }
          this.$emit('up-date')
          this.loading = false
        }).catch( () => {
          this.loading = false;
          this.$emit('close')
        })
        }
      })
    }
	}
}
</script>
