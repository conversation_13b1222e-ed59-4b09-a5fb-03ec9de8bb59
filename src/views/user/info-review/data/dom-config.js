const searchConfig = {
  listSearch: [
    {
      label: '用户名',
      placeholder: '请输入用户名',
      model: 'username',
      component: 'ms-input'
    },
    {
      label: '手机号',
      placeholder: '请输入手机名',
      model: 'mobile',
      component: 'ms-input'
    },
    {
      label: '审核状态',
      placeholder: '请选择',
      model: 'status',
      component: 'ms-select-local',
      clearable: 'clearable',
      options: [
        // { label: '全部', value: '' },
        { label: '待审核', value: 0 },
        { label: '已审核', value: 1 },
        { label: '审核不通过', value: 2 }
      ]
    },
    {
      label: '审核时间',
      placeholder: '请选择时间段',
      model: 'createTime',
      component: 'ms-picker',
      type: 'daterange',
      width: '240px'
    }
  ],
  tableHeader: [
    { label: 'ID', property: 'id', sortable: true, width: 80},
    { label: '手机号', property: 'mobile'},
    { label: '用户名', property: 'createdName'},
    { label: '单位（用户提交）', property: 'hospitalName', width: 120},
    { label: '单位（医院库）', property: 'standardLibraryHospitalName' },
    { label: '状态', property: 'status'},
    { label: '审核时间', property: 'examineTime', width: 140},
    { label: '审核人', property: 'examineName'}
  ],
  tableButtons: [
    // {
    //   label: '查看',
    //   role: '',
    //   operation: 'edit',
    //   component: 'msUserEdit',
    //   way: 'page',
    //   path: 'user-detail',
    //   params: ['id'],
    //   type: 'primary',
    // },
    {
      icon: '',
      role: '',
      operation: 'editStatus',
      component: 'msInfoStatus',
      way: 'dialog',
      field: 'status',
      rule: {
        0: { label: '审核', type: 'success' },
        1: { label: '去审', type: 'info' },
        2: { label: '去审', type: 'info' },
      },
      width: '45%'
    },
  ],
}


export default searchConfig;
