<template>
  <ms-operation-dialog :title="model.status !=0 ? '确定去审吗' : `审核数据`">
    <template slot="content" v-if="model.status == 0">
      <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="150px" class="demo-ruleForm">
        <el-form-item label="单位（用户提交）">
          <!-- <el-input v-model="ruleForm.name"></el-input> -->
          <span>{{model.hospitalName}}</span>
        </el-form-item>
        <el-form-item label="单位（关联医院库）">
					<el-select
						v-model="ruleForm.standardLibraryHospitalId"
						placeholder="请选择您的企业"
						filterable
						remote
            :remote-method="remoteName"
						:loading="loading"
						@change="changeName"
						>
						<el-option
							v-for="item in data"
							:key="item.id"
							:label="item.hospitalName"
							:value="item.id"
						>
						</el-option>
					</el-select>
				</el-form-item>
      </el-form>
    </template>
    <template slot="footer" v-if="model.status == 0">
      <el-button @click="submitForm(2)"
        type="danger"
        size="mini">审核不通过</el-button>
      <el-button @click="submitForm(1)"
        :loading="loading"
        size="mini"
        type="primary">审核通过</el-button>
    </template>
    <template slot="footer" v-else>
      <el-button @click="$emit('close')"
        type="danger"
        size="mini">取消</el-button>
      <el-button @click="submitForm(0)"
        :loading="loading"
        size="mini"
        type="primary">确定</el-button>
    </template>
  </ms-operation-dialog>
</template>

<script>
import {mapGetters} from 'vuex';
export default {
	name: "ms-info-status",
	data () {
		return {
			loading: false,
      ruleForm: {
        hospitalName: '',
        standardLibraryHospitalName: '',
        standardLibraryHospitalId: '',
      },
      rules: {
				standardLibraryHospitalName: [{ required: true, message: "请选择", trigger: "change" }],

      },
      data: []
		}
	},
	props: {
		model: Object,
		operation: String
  },
  computed: {
    ...mapGetters(["info"])
  },
  mounted() {
    if(this.model.standardLibraryHospitalName && this.model.standardLibraryHospitalId) {
      this.ruleForm.standardLibraryHospitalName = this.model.standardLibraryHospitalName;
      this.ruleForm.standardLibraryHospitalId = this.model.standardLibraryHospitalId;
      this.data = [
        {
          id: this.model.standardLibraryHospitalId,
          hospitalName: this.model.standardLibraryHospitalName
        }
      ]
    }
  },
	methods: {
    changeName(val) {
      console.log(val)
      console.log(this.ruleForm)
			// this.ruleForm.standardLibraryHospitalId = this.data.find(el => el.id == val).id;
			this.ruleForm.standardLibraryHospitalName = this.data.find(el => el.id == val).hospitalName;
		},
    async remoteName(query) {
			if (query !== "") {
				this.loading = true;
				const res = await this.api.getEsSearchPage({
          name: query,
          pageIndex: 1,
          pageSize: 50,

        })
				console.log(res, 'rrr')
				this.loading = false;
				this.data = res.data || [];
			} else {
				this.data = [];
				this.loading = false;
			}
		},
		submitForm (status) {
      if(status == 0) {
        let params = {
          id: this.model.id,
          status: status,
          standardLibraryHospitalId: this.model.standardLibraryHospitalId,
          standardLibraryHospitalName: this.model.standardLibraryHospitalName,
          userId: this.info.userId,
          username: this.info.userName,
        }
        console.log(params, 'dd')
        this.api.examineUserHospital(params).then(response => {
          console.log(response, 'rr');
          if(response.status === 200) {
            this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
          } else {
            this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
          }
          this.$emit('up-date')
          this.loading = false
        }).catch( () => {
          this.loading = false;
          this.PUBLIC_Methods.apiNotify('请求出错', 'warning')
          this.$emit('close')
        })
      } else {
        // this.$refs['ruleForm'].validate((valid) => {
        //   if (valid) {
            
        //   } else {
        //     console.log('error submit!!');
        //     return false;
        //   }
        // });
        this.loading = true;
        let params = {
          "id": this.model.id,
          "standardLibraryHospitalId": this.ruleForm.standardLibraryHospitalId,
          "standardLibraryHospitalName": this.ruleForm.standardLibraryHospitalName,
          "status": status,
          userId: this.info.userId,
          username: this.info.userName,
        }
        if(!this.ruleForm.standardLibraryHospitalId && status == 1) {
          this.$message({
            message: '请关联医院库',
            type: 'warning'
          });
          this.loading = false
          return false;
        }
        console.log(params, 'dd')
        this.api.examineUserHospital(params).then(response => {
          if(response.status === 200) {
            this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
          } else {
            this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
          }
          this.$emit('up-date')
          this.loading = false
        }).catch( () => {
          this.loading = false;
          this.$emit('close')
        })

      }
		}
	}
}
</script>

<style scoped>
.demo-ruleForm{
  padding: 0 0 30px 0;
}
</style>
