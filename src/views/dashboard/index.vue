<template>
  <div class="form-tab">
    <el-tabs v-model="activeName" @tab-click="tab_click">
      <template v-for="(item, index) in tabPaneList">
        <el-tab-pane :label="item.label" :name="item.name" :key="index"></el-tab-pane>
      </template>
    </el-tabs>
    <component :is="dashboardComponent"></component>
  </div>
</template>

<script>
import { mapGetters } from "vuex";

import MsInfoDashboard from './tool/ms-info-dashboard'
import MsUserDashboard from './tool/ms-user-dashboard'
import MsBehaviorDashboard from './tool/ms-behavior-dashboard'
export default {
  name: "ms-video",
  data () {
    return {
      activeName: 'article_report',
      dashboardComponent: 'MsInfoDashboard',
      tabPaneList: []
    }
  },
  components: {
    MsInfoDashboard,
    MsUserDashboard,
    MsBehaviorDashboard
  },
  computed: {
    ...mapGetters(["dashboardActive","info","permissionMenuId"])
  },
  watch: {
    permissionMenuId: function (val) {
      if (val) {
        this.getListPermission()
      }
    }
  },
  mounted () {
    this.getListPermission()
  },
  methods: {
    getListPermission () {
      let btnParams = {
        "projectId": this.info.projectId,
        "roleId": this.info.roleId,
        "menuId": this.permissionMenuId[this.$route.fullPath]
      }
      this.api.getRoleMenuPermissionsBtnList(btnParams).then(res => {
        if (Number(res.status) === 200) {
          let firstTabName = res.data[0].menuPers
          res.data.forEach(v => {
            this.tabPaneList.push({
              label: v.menuChineseName,
              name: v.menuPers
            })
          });

          this.$nextTick(() => {
            this.activeName = this.dashboardActive ? this.dashboardActive : firstTabName
            this.tab_click()
          })
        }
      })
    },
    tab_click() {
        switch(this.activeName) {
            case 'article_report':
                this.dashboardComponent = 'MsInfoDashboard';
                break;
            case 'user_report':
                this.dashboardComponent = 'MsUserDashboard';
                break;
            case 'behavior_report':
                this.dashboardComponent = 'MsBehaviorDashboard';
                break;
            default: break;
        }
        this.$store.dispatch('SetDashboardActive', this.activeName)
    }
  }
}
</script>
