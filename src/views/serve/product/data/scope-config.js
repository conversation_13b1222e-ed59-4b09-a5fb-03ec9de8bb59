import serveUrl from '@/store/data/serveUrl.js'

const scopeConfig = {
  show: {
    status: () => {
      return {
        type: 'status',
        rule: {
          0: { label: '待审核', background: '#A7ADBD' },
          1: { label: '审核通过', background: '#40A23F' }
        }
      }
    },
    recommend: () => {
      return {
        type: 'icon',
        icon: 'icon-tuijiansel',
        showVal: 1,
        color: '#EA6E72'
      }
    },
    title: () => {
      return {
        type: 'preview',
        config: {
          field: 'status',
          paramsVal: 'id',
          pageUrl: `${serveUrl['product']}`,
          previewName: 'MsPreviewDialog',
          title: '产品信息预览'
        }
      }
    },
  },
  headerShow: {
    status: () => {
      return {
        type: 'dropdown',
        icon: 'icon-funnel',
        options: [
          { label: '全部', value: null },
          { label: '待审核', value: 0 },
          { label: '审核通过', value: 1 }
        ],
        operation: 'query'
      }
    }
  }
}

export default scopeConfig;
