const domConfig = {
  listSearch: [
    {
      label: '标题',
      placeholder: '请输入',
      model: 'title',
      component: 'ms-input'
    },
    {
      label: '栏目',
      placeholder: '请选择',
      model: 'categoryId',
      component: 'ms-category-cascader',
      config: {
        moduleName: 'product'
      }
    },
    {
      label: '发布时间',
      placeholder: '请选择时间段',
      model: 'createTime',
      component: 'ms-picker',
      type: 'daterange',
      width: '240px'
    }
  ],
  tableHeader: [
    { label: '', property: 'recommend', width: '30' },
    { label: 'ID', property: 'id', sortable: true, width: 60  },
    { label: '标题', property: 'title', width: 200 },
    { label: '栏目', property: 'categoryName', width: 150 },
    { label: '排序', property: 'rank', sortable: true , width: 60},
    { label: '点击量', property: 'clickNumber', width: 60 },
    { label: '创建人', property: 'createdName',  width: '80' },
    { label: '发布时间', property: 'publishedTime', sortable: true,  width: '130' },
    { label: '状态', property: 'status'},
  ],
  tableButtons: [
    {
      label: '编辑',
      icon: '',
      role: '',
      operation: 'edit',
      component: 'product-operation',
      way: 'page',
      type: 'primary',
      path: 'product-operation',
      params: ['id']
    },
    {
      label: '',
      icon: '',
      role: '',
      operation: 'editStatus',
      component: 'msProductOperation',
      way: 'dialog',
      field: 'status',
      rule: {
        1: { label: '去审', type: '', operation: 'toreview' },
        0: { label: '审核', type: 'success', operation: 'approval' }
      }
    },
    {
      label: '更多',
      operation: 'more',
      type: 'info',
      children: [
        // {
        //   label: '',
        //   way: 'recommend',
        //   operation: '',
        //   component: '',
        //   field: 'recommend',
        //   rule: {
        //     1: { label: '取消推荐' },
        //     0: { label: '推荐' }
        //   }
        // },
        {
          title: '推荐产品',
          label: '推荐',
          way: 'dialog',
          operation: 'recommend',
          component: 'msProductRecommend',
        },
        {
          label: '删除',
          way: 'dialog',
          operation: 'delete',
          component: 'msProductOperation',
        },
      ]
    }
  ],
  soltButtons: [
    { 
      label: '手工添加', 
      type: 'primary', 
      icon: 'el-icon-plus',
      operation: 'created',
      component: 'product-operation',
      way: 'page',
      path: 'product-operation',
      params: ['id']
    },
    { 
      label: '批量审核', 
      type: 'primary',
      operation: 'approval',
      component: 'msProductOperation',
      way: 'batch'
    },
    { 
      label: '批量去审', 
      type: 'primary',
      operation: 'toreview',
      component: 'msProductOperation',
      way: 'batch'
    },
    { 
      label: '批量删除',
      type: 'info',
      icon: 'el-icon-close',
      operation: 'delete',
      component: 'msProductOperation',
      way: 'batch'
    }
  ]
}

export default domConfig;
