<template>
  <section v-loading="getLoading" class="form-tab">
    <!-- 表单内容 -->
    <el-form :model="submitData"
             ref="submitRef"
             class="rule-form info-form"
             :rules="rules"
             label-width="70px">
      <el-row :gutter="20"> 
        <el-col :span="18">
          <el-row>
            <el-col :span="24">
              <el-form-item label="标题" prop="title">
                <el-input v-model="submitData.title" style="width: 100%"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="内容" prop="content">
                <ms-editor v-model="submitData.content"></ms-editor>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="摘要" prop="contentConclusion">
                <el-input v-model="submitData.contentConclusion" type="textarea" :rows="2" @keyup.native="summaryKeyUp = true" maxlength="100" show-word-limit></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="6">
              <el-form-item prop="publishedTime" label="发布时间">
                <ms-picker :model.sync="submitData.publishedTime" type="datetime"></ms-picker>
              </el-form-item>
            </el-col>
            <el-col :span="4" v-show="dataId !== 0">
              <el-form-item label="编辑者：">
                <span class="font-12">{{submitData.editor}}</span>
              </el-form-item>
            </el-col>
            <el-col :span="6" v-show="dataId !== 0">
              <el-form-item label="创建时间：" label-width="80px">
                <span class="font-12">{{submitData.createdTime | parseTime}}</span>
              </el-form-item>
            </el-col>
          </el-row>
        </el-col>
        <el-col :span="6" class="info-form-right">
          <el-row>
            <el-col :span="24">
              <el-form-item label="栏目" prop="categoryId">
                <ms-category-cascader :model.sync="submitData.categoryId" :modelName.sync="submitData.categoryName" :config="{moduleName: 'product'}"></ms-category-cascader>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="排序" prop="rank">
                <el-input-number :min="1" controls-position="right" v-model="submitData.rank" style="width: 100%"></el-input-number>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="显示选项">
                <el-checkbox v-model="submitData.recommend" :true-label="1" :false-label="0" @change="endTimeCh(submitData.recommend,'推荐结束时间', 'recommend')">推荐</el-checkbox>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="封面图片" prop="image">
                <ms-single-image v-model="submitData.image" :upFileSize="0.5"></ms-single-image>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="附件" prop="image">
                <ms-file-upload v-model="submitData.attachmentList"></ms-file-upload>
              </el-form-item>
            </el-col>
          </el-row>
        </el-col>
      </el-row>
    </el-form>
    <!-- 弹出层 -->
    <el-dialog :visible.sync="dialog" 
               closeable 
               :show-close="false"
               :close-on-click-modal="false"
               width="50%"
               :title="dialogTitle">
      <component :is="dialogComponent" 
                   :model="dialogInfo" 
                   :operation="dialogOperation" 
                   :isNeedApi="false"
                   @close="closeDialog" 
                   @return-data="returnData"
                   @up-date="init" 
                   v-if="dialog"></component>
    </el-dialog>
    <!-- 提交按钮 -->
    <footer-tool-bar v-loading="buttonLoading">
      <template slot="tool-content">
        <el-button plain v-show="dataId" @click="info_operation('approval')">{{submitData.status === 1 ? '去审' : '审核'}}</el-button>
        <el-button type="primary" @click="info_operation('save')">保存</el-button>
        <el-button type="info" @click="info_operation('back')">返回</el-button>
      </template>
    </footer-tool-bar>
  </section>
</template>

<script>
import FooterToolBar from '@/components/ToolBar/footer-tool-bar'
import productMixin from "./product-mixin"
import MsEditor from '@/components/MsEditor'
import formConfig from './data/form-config' 
import { getEditContent } from '@/utils/index'
import { mapGetters } from "vuex";
export default {
  name: "company-operation",
  mixins: [productMixin],
	data () {
		return {
      buttonLoading: false,
      getLoading: false,
      dataId: this.$route.query.id ? this.$route.query.id : 0,
      formConfig: formConfig,
      submitData: { 
        title: "",
        categoryId: 0,
        categoryName: "",
        rank: 0,
        content: "",
        contentConclusion: "",
        image: "",
        attachmentList: [],
        status: 0,
        recommend: 0,
        recommendEndTime: ""
      },
      rules: {
        title: [
          { required: true, message: "请输入标题", trigger: 'blur' }
        ],
        categoryId: [
          { required: true, message: "请选择栏目", trigger: 'blur' }
        ],
        content: [
          { required: true, message: "请输入内容", trigger: 'blur' }
        ],
      },
      dialogComponent: '',
      dialog: false,
      dialogTitle: '',
      dialogOperation: '',
      dialogInfo: {},
      summaryKeyUp: false
		}
  },
  components: {
    FooterToolBar,
    MsEditor
  },
  computed: {
    ...mapGetters(["info"])
  },
  watch: {
    'submitData.content': function (val) {
      if (val) {
        if (!this.submitData.id && !this.summaryKeyUp) {
          this.submitData.summary = this.PUBLIC_Methods.unexcapeHtml(getEditContent(val))
        }
      }
    }
  },
  created() {
    this.init()
  },
  methods: {
    init() {
      this.dialog = false
      let id = this.dataId
      if(id !== 0) {
        this.getLoading = true;
        this.submitData.id = id;
        this.api.getProduct({id: id}).then( response => {
          this.getLoading = false;
          if(response.status === 200) {
            let res = response.data
            let file = []
            if (res.attachmentList && res.attachmentList.length > 0) {
              file = res.attachmentList.map(v => {
                return{
                  name: v.fileName || v.attachmentKey,
                  url: v.attachmentUrl,
                  fileKey: v.attachmentKey
                }
              });
            }
            this.submitData = {
              ...this.submitData,
              ...res,
              attachmentList: file,
              content: this.PUBLIC_Methods.unexcapeHtml(res.content)
            }
          } else {
            this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
          }
        }).catch(() => {
          this.getLoading = false;
        })
      } 
    },
    info_operation(val) {
      switch (val) {
        case 'save': 
          this.$refs.submitRef.validate( valid => {
            if(valid) {
              let fileList = []
              if (this.submitData.attachmentList.length > 0) {
                fileList = this.submitData.attachmentList.map(v => {
                  return {
                    attachmentKey: v.fileKey,
                    attachmentUrl: v.url,
                    fileName: v.name
                  }
                });
              }
              let params = {
                userId: this.info.userId,
                username: this.info.userName,
                ...this.submitData,
                attachmentList: fileList,
                content: this.PUBLIC_Methods.excapeHtml(this.submitData.content)
              }
              this.dataId ? this.updateOperation(params) : this.createOperration(params)
            }
          })
          break;
        case 'back':
          this.$router.back();
          break;
        case 'approval':
          var params = {
            way: 'dialog',
            operation: this.submitData.status === 1 ? 'toreview' : 'approval',
            component: 'msProductOperation',
            data: this.submitData
          }
          this.dialog = true;
          this.dialogInfo = params.data
          this.dialogOperation = params.operation;
          this.dialogComponent = params.component;
          this.dialogTitle = params.title;
          break;
        default: break;
      }
    },
    createOperration(params) {
      this.buttonLoading = true;
      this.api.insertProduct(params).then(response => {
        this.buttonLoading = false
        if(response.status === 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
          this.$router.back()
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
      }).catch(() => this.buttonLoading = false)
    },
    updateOperation(params) {
      this.buttonLoading = true;
      this.api.updateProduct(params).then(response => {
        this.buttonLoading = false
        if(response.status === 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
          this.$router.back()
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
      }).catch(() => this.buttonLoading = false)
    },
    endTimeCh(val, title, operation) {
      if(val) {
        this.dialog = true;
        this.dialogInfo = {recommendEndTime: this.submitData.recommendEndTime}
        this.dialogOperation = operation;
        this.dialogComponent = 'msProductRecommend';
        this.dialogTitle = title;
      }
    },
    returnData(val) {
      this.dialog = false;
      if (val.endTime) {
        this.submitData[`${val.operation}EndTime`] = val.endTime
      } else {
        this.submitData[val.operation] = 0
      }
    },
    closeDialog(val) {
      this.dialog = !this.dialog
      if (val) {
        if (val.operation === 'recommend') {
          this.submitData.recommend = 0
        } else if (val.operation === 'sticky') {
          this.submitData.sticky = 0
        }
      }
    }
  }
}
</script>
