<template>
  <ms-operation-dialog :title="title">
    <template slot="content">
      <el-tag v-for="(item, index) in tagArr" 
              :key="index" 
              type="info" 
              style="margin: 0 5px 5px 0">{{item}}</el-tag>
    </template>
    <template slot="footer">
      <el-button @click="submitForm"
                 :loading="loading"
                 size="mini"
                 type="primary">确 定</el-button>
      <el-button @click="$emit('close')"
                 size="mini">取 消</el-button>
    </template>
  </ms-operation-dialog>
</template>

<script>
export default {
	name: "ms-company-operation",
	data () {
		return {
      userInfo: {},
      type: null,
      tagArr: [],
      ids: [],
      title: '',
      loading: false
		}
	},
	props: [
		"model",
		"operation"
  ],
  created() {
    this.userInfo = this.$store.getters.info || {}
    this.init()
  },
	methods: {
    init() {
      let arr = []
      let ids = []
      let way = this.operation || this.$route.query.operation
      this.type = way === 'approval' ? 1 : way === 'toreview' ? 2 : way === 'delete' ? 3 : null
      this.title = `是否${this.type === 1 ? '审核' : this.type === 2 ? '去审' : this.type === 3 ? '删除' : ''}单页信息`
      if (this.PUBLIC_Methods.isArrayFn(this.model)) {
        this.model.forEach(item => {
          arr.push(item.title)
          ids.push(item.id)
        });
      } else {
        arr.push(this.model.title)
        ids.push(this.model.id)
      }
      this.tagArr = arr
      this.ids = ids
    },
		submitForm () {
      if(this.type === 3) {
        this.$confirm('此操作将永久删除单页信息，是否继续', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.apiOperationAudit()
        })
      } else {
        this.apiOperationAudit()
      }
    },
    apiOperationAudit() {
      let params = {
        userId: this.userInfo.userId,
        username: this.userInfo.userName,
        type: this.type,
        id: this.ids[0]
      }
      this.loading = true;
      this.api.dealSinglePage(params).then(response => {
        if(response.status === 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
        this.$emit('up-date')
        this.loading = false
      }).catch( () => {
        this.loading = false;
        this.$emit('close')
      })
    }
	}
}
</script>
