<template>
  <section v-loading="getLoading" class="form-tab">
    <el-tabs v-model="activeName">
      <el-tab-pane label="单页内容" name="content">
        <!-- 表单内容 -->
        <el-form ref="submitRef"
              class="rule-form"
              :model="submitData"
              :rules="formConfig.rule"
              label-width="70px">
          <el-row>
            <template v-for="(item, index) in formConfig.formField">
              <el-col :key="index"
                      :span="item.colSpan">
                <el-form-item :prop="item.prop"
                              :label="item.label">
                  <component :is="item.component"
                            :model.sync="submitData[item.prop]"
                            v-model="submitData[item.prop]"
                            :width="item.width || '100%'"
                            :disabled="item.disabled"
                            :options="item.options"
                            :type="item.type">
                  </component>
                </el-form-item>
              </el-col>
            </template>
          </el-row>
          <el-row>
            <el-col :span="6">
              <el-form-item prop="publishedTime" label="发布时间">
                <ms-picker :model.sync="submitData.publishedTime" type="datetime"></ms-picker>
              </el-form-item>
            </el-col>
            <el-col :span="4" v-show="dataId !== 0">
              <el-form-item label="编辑者：">
                <span class="font-12">{{submitData.createdName}}</span>
              </el-form-item>
            </el-col>
            <el-col :span="6" v-show="dataId !== 0">
              <el-form-item label="创建时间：" label-width="80px">
                <span class="font-12">{{submitData.createdTime | parseTime}}</span>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-tab-pane>
      <el-tab-pane label="显示栏目" name="categoryDtoList">
        <ms-info-setting :categoryModel.sync="submitData.categoryDtoList" :categoryConfigChild="{moduleName: 'singlePage'}" headerShow></ms-info-setting>
      </el-tab-pane>
    </el-tabs>
    <!-- 弹出层 -->
    <el-dialog :visible.sync="dialog" 
               closeable 
               :show-close="false"
               :close-on-click-modal="false"
               width="50%"
               :title="dialogTitle">
      <component :is="dialogComponent" 
                   :model="dialogInfo" 
                   :operation="dialogOperation" 
                   @close="dialog = !dialog" 
                   @up-date="init" 
                   v-if="dialog"></component>
    </el-dialog>
    <!-- 提交按钮 -->
    <footer-tool-bar v-loading="buttonLoading">
      <template slot="tool-content">
        <el-button plain v-show="dataId" @click="info_operation('approval')">{{submitData.status === 1 ? '去审' : '审核'}}</el-button>
        <el-button type="primary" @click="info_operation('save')">保存</el-button>
        <el-button type="info" @click="info_operation('back')">返回</el-button>
      </template>
    </footer-tool-bar>
  </section>
</template>

<script>
import FooterToolBar from '@/components/ToolBar/footer-tool-bar'
import msInfoSetting from '@/components/MsCommon/ms-info-setting'
import companyMixin from "./company-mixin"
import MsEditor from '@/components/MsEditor'
import formConfig from './data/form-config' 
import { mapGetters } from "vuex";
export default {
  name: "company-operation",
  mixins: [companyMixin],
	data () {
		return {
      buttonLoading: false,
      getLoading: false,
      dataId: this.$route.query.id ? this.$route.query.id : 0,
      formConfig: formConfig,
      submitData: { 
        title: "",
        type: "",
        content: "",
        status: 0,
        categoryDtoList: []
      },
      dialogComponent: '',
      dialog: false,
      dialogTitle: '',
      dialogOperation: '',
      dialogInfo: {},
      activeName: 'content'
		}
  },
  components: {
    FooterToolBar,
    msInfoSetting,
    MsEditor
  },
  computed: {
    ...mapGetters(["info"])
  },
  created() {
    this.init()
  },
  methods: {
    init() {
      this.dialog = false
      let id = this.dataId
      if(id !== 0) {
        this.getLoading = true;
        this.submitData.id = id;
        this.api.getSinglePageById({id: id}).then( response => {
          this.getLoading = false;
          if(response.status === 200) {
            let res = response.data
            this.submitData = {
              ...this.submitData,
              ...res,
              content: this.PUBLIC_Methods.unexcapeHtml(res.content),
              categoryDtoList: res.categoryDtoList ? res.categoryDtoList : []
            }
          } else {
            this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
          }
        }).catch(() => {
          this.getLoading = false;
        })
      } 
    },
    info_operation(val) {
      switch (val) {
        case 'save': 
          this.$refs.submitRef.validate( valid => {
            if(valid) {
              let params = {
                userId: this.info.userId,
                username: this.info.userName,
                ...this.submitData,
                content: this.PUBLIC_Methods.excapeHtml(this.submitData.content)
              }
              this.dataId ? this.updateOperation(params) : this.createOperration(params)
            }
          })
          break;
        case 'back':
          this.$router.back();
          break;
        case 'approval':
          var params = {
            way: 'dialog',
            operation: this.submitData.status === 1 ? 'toreview' : 'approval',
            component: 'msCompanyOperation',
            data: this.submitData
          }
          this.dialog = true;
          this.dialogInfo = params.data
          this.dialogOperation = params.operation;
          this.dialogComponent = params.component;
          this.dialogTitle = params.title;
          break;
        default: break;
      }
    },
    createOperration(params) {
      this.buttonLoading = true;
      this.api.insertSinglePage(params).then(response => {
        this.buttonLoading = false
        if(response.status === 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
          this.$router.back()
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
      }).catch(() => this.buttonLoading = false)
    },
    updateOperation(params) {
      this.buttonLoading = true;
      this.api.updateSinglePage(params).then(response => {
        this.buttonLoading = false
        if(response.status === 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
          this.$router.back()
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
      }).catch(() => this.buttonLoading = false)
    }
  }
}
</script>
<style scoped>
.form-tab /deep/ .ms-radio .el-radio {
  margin-right: 26px !important;
  margin-left: 0px !important;
}
</style>
