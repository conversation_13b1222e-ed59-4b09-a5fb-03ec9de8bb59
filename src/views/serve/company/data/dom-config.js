const domConfig = {
  listSearch: [
    {
      label: '标题',
      placeholder: '请输入',
      model: 'title',
      component: 'ms-input'
    },
    {
      label: '发布时间',
      placeholder: '请选择时间段',
      model: 'createTime',
      component: 'ms-picker',
      type: 'daterange',
      width: '240px'
    },
    {
      label: '单页类型',
      placeholder: '请选择单页类型',
      model: 'type',
      component: 'ms-select-local',
      clearable: 'clearable',
      options:[
        {label:'全部', value: 0},
        {label:'单页', value: 1},
        {label:'公告', value: 2},
        {label:'知情同意书', value: 3}
      ]
    },
  ],
  tableHeader: [
    { label: 'ID', property: 'id', sortable: true, width: '80' },
    { label: '标题', property: 'title', width: '120' },
    { label: '单页类型', property: 'type', width: '90' },
    { label: '点击量', property: 'clickNumber', width: '90' },
    { label: '创建人', property: 'createdName', width: '80' },
    { label: '更新人', property: 'updatedName', width: '80' },
    { label: '发布时间', property: 'publishedTime', sortable: true, width: '130' },
    { label: '更新时间', property: 'updatedTime', sortable: true, width: '130' },
    { label: '创建时间', property: 'createdTime', sortable: true, width: '130' },
    { label: '状态', property: 'status' }
  ],
  tableButtons: [
    {
      label: '编辑',
      icon: '',
      role: '',
      operation: 'edit',
      component: 'company-operation',
      way: 'page',
      type: 'primary',
      path: 'company-operation',
      params: ['id'],
      identify: 'edit'
    },
    {
      label: '',
      icon: '',
      role: '',
      operation: 'editStatus',
      component: 'msCompanyOperation',
      way: 'dialog',
      field: 'status',
      rule: {
        1: { label: '去审', type: '', operation: 'toreview' },
        0: { label: '审核', type: 'success', operation: 'approval' }
      },
      identify: 'status'
    },
    {
      label: '删除',
      operation: 'delete',
      type: 'danger',
      way: 'dialog',
      component: 'msCompanyOperation',
      identify: 'single_delete'
    }
  ],
  soltButtons: [
    {
      label: '手工添加',
      type: 'primary',
      icon: 'el-icon-plus',
      operation: 'created',
      component: 'company-operation',
      way: 'page',
      path: 'company-operation',
      params: ['id'],
      identify: 'created'
    },
    // {
    //   label: '批量审核',
    //   type: 'primary',
    //   operation: 'approval',
    //   component: 'msCompanyOperation',
    //   way: 'batch',
    //   identify: 'batch_approval'
    // },
    // {
    //   label: '批量去审',
    //   type: 'primary',
    //   operation: 'toreview',
    //   component: 'msCompanyOperation',
    //   way: 'batch',
    //   identify: 'batch_toreview'
    // },
    // {
    //   label: '批量删除',
    //   type: 'info',
    //   icon: 'el-icon-close',
    //   operation: 'delete',
    //   component: 'msCompanyOperation',
    //   way: 'batch',
    //   identify: 'batch_delete'
    // }
  ]
}

export default domConfig;
