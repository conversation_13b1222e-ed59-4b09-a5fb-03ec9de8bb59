const formConfig = {
  formField: [
    {
      label: '标题',
      prop: 'title',
      colSpan: 12,
      component: 'ms-input'
    },
    {
      label: '单页类型',
      prop: 'type',
      colSpan: 24,
      component: 'ms-radio',
      options: [
        {label: '单页', value: 1},
        {label: '公告', value: 2},
        {label: '知情同意书', value: 3},
      ]
    },
    {
      label: '内容',
      prop: 'content',
      colSpan: 24,
      component: 'ms-editor'
    }
  ],
  rule: {
    title: [
      { required: true, message: "请输入标题", trigger: 'blur' }
    ],
    type: [
      { required: true, message: "请选择单页类型", trigger: 'blur' }
    ],
    content: [
      { required: true, message: "请输入内容", trigger: 'blur' }
    ],
    publishedTime: [
      { required: true, message: "请选择发布时间", trigger: 'blur' }
    ]
  }
}

export default formConfig;
