<template>
  <ms-operation-dialog>
    <template slot="content">
      <el-form ref="submitRef"
               :model="submitData"
               label-width="75px"
               class="rule-form"
               v-loading="loading">
        <el-row>
          <el-col :span="18" :offset="3">
            <template v-for="(item, index) in formConfig.formField">
              <el-form-item :label="item.label" :key="index">
                <component :is="item.component"
                        :model.sync="submitData[item.prop]"
                        :width="item.width || '100%'"
                        :disabled="item.disabled" />
              </el-form-item>
            </template>
          </el-col>
        </el-row>
      </el-form>
    </template>
  </ms-operation-dialog>
</template>

<script> 
import formConfig from '../data/form-config' 
export default {
	name: "ms-certificate-look",
	data () {
		return {
      formConfig: formConfig,
      loading: false,
      submitData: {}
    }
  },
	props: {
		model: Object,
		operation: String
  },
  created() {
    this.init()
  },
	methods: {
    init() {
      let id = this.model && this.model.id || 0
      if (id !== 0) {
        this.loading = true;
        this.api.showCertificate(id).then( response => {
          this.loading = false;
          if(response.status === 200) {
            let res = response.data
            this.submitData = {
              ...res
            }
          } else {
            this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
          }
        }).catch(() => {
          this.loading = false;
        })
      } else {
        this.PUBLIC_Methods.apiNotify('请获取证书id', 'warning')
      }
    }
	}
}
</script>
