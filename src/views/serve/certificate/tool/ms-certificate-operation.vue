<template>
  <ms-operation-dialog>
    <template slot="content">
      <el-form ref="submitRef"
               :model="submitData"
               :rules="rules"
               label-width="85px"
               class="rule-form">
        <el-row>
          <el-col :span="18" :offset="3">
            <el-form-item label="论文标题" prop="title">
              <el-input v-model="submitData.title" placeholder="请输入"></el-input>
            </el-form-item>
            <el-form-item label="第一作者姓名" prop="firstAuthor">
              <el-input v-model="submitData.firstAuthor" placeholder="请输入"></el-input>
            </el-form-item>
            <el-form-item label="通讯作者姓名" prop="author">
              <el-input v-model="submitData.author" placeholder="请输入"></el-input>
            </el-form-item>
            <el-form-item label="合同编号" prop="contractNo">
              <el-input v-model="submitData.contractNo" placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </template>
    <template slot="footer">
      <el-button @click="submitForm"
                 :loading="loading"
                 size="mini"
                 type="primary">确 定</el-button>
      <el-button @click="$emit('close')"
                 size="mini">取 消</el-button>
    </template>
  </ms-operation-dialog>
</template>

<script> 
export default {
	name: "ms-certificate-operation",
	data () {
    // var validateAuthor = (rule, value, callback) => {
    //   if (this.submitData.firstAuthor === '' && this.submitData.author === '') {
    //     callback('请填写第一作者或者通讯作者')
    //   } else {
    //     callback()
    //   }
    // }
		return {
      loading: false,
      submitData: {
        title: "",
        firstAuthor: "",
        author: "",
        contractNo: ""
      },
      rules: {
        title: [
          { required: true, message: "请输入标题", trigger: 'blur' },
          { max: 1000, message: '请输入1000个字符以内的文本', trigger: 'blur' }
        ],
        firstAuthor: [
          { required: true, message: "请输入第一作者姓名", trigger: 'blur' },
          { max: 50, message: '请输入50个字符以内的文本', trigger: 'blur' }
        ],
        author: [
          { required: true, message: "请输入通讯作者姓名", trigger: 'blur' },
          { max: 50, message: '请输入50个字符以内的文本', trigger: 'blur' }
        ],
        contractNo: [
          { required: true, message: "请输入合同编号", trigger: 'blur' }
        ]
      },
    }
  },
	props: {
		model: Object,
		operation: String
  },
	methods: {
		submitForm() {
      this.$refs.submitRef.validate( valid => {
        if(valid) {
          this.loading = true;
          this.api.addCertificate(this.submitData).then( response => {
            if(response.status === 200) {
            this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
          } else {
            this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
          }
          this.$emit('up-date')
          this.loading = false
        }).catch( () => {
          this.loading = false;
          this.$emit('close')
        })
        }
      })
    }
	}
}
</script>
