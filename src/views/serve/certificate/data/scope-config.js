const scopeConfig = {
  show: {
    createdTime: () => {
      return {
        type: 'formatTime',
        cFormat: '{y}-{m}-{d} {h}:{i}'
      }
    }
  },
  headerShow: {
    type: () => {
      return {
        type: 'dropdown',
        icon: 'icon-funnel',
        options: [
          { label: '全部', value: null },
          { label: '一作', value: '一作' },
          { label: '通讯', value: '通讯' }
        ],
        operation: 'query'
      }
    }
  },
}

export default scopeConfig
