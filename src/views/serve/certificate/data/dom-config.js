const searchConfig = {
  listSearch: [
    {
      label: '证书编号',
      placeholder: '请输入证书编码',
      model: 'certificate',
      component: 'ms-input'
    },
    {
      label: '作者',
      placeholder: '请输入作者姓名',
      model: 'author',
      component: 'ms-input'
    },
    {
      label: '标题',
      placeholder: '请输入标题',
      model: 'title',
      component: 'ms-input'
    }
  ],
  tableHeader: [
    { label: '标题', property: 'title' },
    { label: '作者', property: 'author'},
    { label: 'Certificate Code', property: 'certificate'},
    { label: '合同编号', property: 'contractNo'},
    { label: '类型', property: 'type', width: 80  },
    { label: '创建时间', property: 'createdTime', sortable: true}
  ],
  tableButtons: [
    {
      label: '查看',
      icon: '',
      role: '',
      operation: '',
      component: 'ms-certificate-look',
      way: 'dialog',
      title: '证书查看',
      type: 'primary',
      width: '50%'
    },
    // {
    //   label: '下载',
    //   icon: '',
    //   role: '',
    //   operation: '',
    //   component: '',
    //   way: 'download',
    //   title: '下载',
    //   type: 'success'
    // }
  ],
  soltButtons: [
    { 
      label: '证书添加', 
      type: 'primary', 
      icon: 'el-icon-plus',
      operation: 'created',
      component: 'ms-certificate-operation',
      way: 'dialog',
      title: '证书添加',
      width: '50%'
    }
  ]
}


export default searchConfig;
