<template>
  <ms-operation-dialog >
    <template slot="content">
        <el-row>
            <p class="content-top">
                <span class="top-label">文章标题：</span><span class="top-content">{{submitForm.title}}</span>
            </p>
        </el-row>
		<el-row>
            <el-col :span="12">
                <p class="content-top">
                    <span class="top-label">创建人：</span><span class="top-content">{{submitForm.createdName}}</span>
                </p>
            </el-col>
            <el-col :span="12">
                <p class="content-top">
                    <span class="top-label">创建时间：</span><span class="top-content">{{submitForm.createdTime}}</span>
                </p>
            </el-col>
        </el-row>
        <el-row>
            <el-col :span="12">
                <p class="content-top">
                    <span class="top-label">文章来源：</span><span class="top-content">{{submitForm.articleFrom}}</span>
                </p>
            </el-col>
            <el-col :span="12">
                <p class="content-top">
                    <span class="top-label">原地址：</span><span class="top-content">
                        <el-link :href="model.articleOriginalUrl" target="_blank" type="primary">{{submitForm.articleOriginalUrl}}</el-link>
                    </span>
                </p>
            </el-col>
        </el-row>
        <el-row>
            <el-col :span="24">
                <p class="content-top">
                    <span class="top-label">导入状态：</span><span class="top-content">
                        <template v-if="submitForm.status === 0">
                            未导入
                        </template>
                        <template v-else-if="submitForm.status === 1">
                            导入成功<el-link type="primary" @click="goArticleDetail(submitForm.sourceId)">（查看文章资讯模块详情）</el-link>
                        </template>
                        <template v-else-if="submitForm.status === 2">
                            导入失败 <span style="color: #F56C6C;">（错误信息：{{submitForm.message}}）</span>
                        </template>
                    </span>
                </p>
            </el-col>
        </el-row>
		
		
        <div v-html="PUBLIC_Methods.unexcapeHtml(submitForm.content)" style="min-height: 300px" class="preview-content" v-loading="getLoading"></div>
    </template>
  </ms-operation-dialog>
</template>

<script>
export default {
	name: "ms-spider-preview",
	data () {
		return {
            getLoading: true,
            content: '',
            submitForm: {}
		}
	},
	props: {
		model: Object,
		operation: String
    },
    created() {
        this.init()
    },
    methods: {
        init() {
            this.getLoading = true
            this.api.getSpiderById({id: this.model.id}).then(response => {
                this.getLoading = false;
                if (response.status === 200) {
                    this.submitForm = response.data
                }
            })
        },
        goArticleDetail(id) {
            this.$router.push({
                path: 'article-operation',
                query: {
                    operation: 'edit',
                    component: 'article-operation',
                    id: id
                }
            })
        }
    }
}
</script>

<style>
    .preview-content li{
        list-style-type: disc !important;
    }
</style>

<style scoped>
	.content-top {
		margin-bottom: 10px;
		font-size: 14px;
		text-align: left;
	}
	.content-top .top-label {
		font-weight: bold;
		margin-right: 12px;
		display: inline-block;
        width: 80px;
        text-align-last: justify;
	}
</style>
