const scopeConfig = {
    show: {
        status: () => {
            return {
                type: 'status',
                rule: {
                    0: { label: '未处理', background: '#A7ADBD' },
                    1: { label: '处理成功', background: '#40A23F' },
                    2: { label: '处理失败', background: '#F56C6C' }
                }
            }
        },
        cover: () => {
            return {
                type: 'other',
                config: {
                    way: 'link',
                    pathKey: 'cover' 
                }
            }
        },
        articleOriginalUrl: () => {
            return {
                type: 'other',
                config: {
                    way: 'link',
                    pathKey: 'articleOriginalUrl' 
                }
            }
        },
    },
    headerShow: {}
}

export default scopeConfig;
