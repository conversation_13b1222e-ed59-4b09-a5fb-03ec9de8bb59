const searchConfig = {
    listSearch: [
        {
            label: '搜索关键字',
            placeholder: '请输入',
            model: 'value',
            component: 'ms-input'
        }
    ],
    tableHeader: [
        { label: '标题', property: 'title', width: 180 },
        { label: '封面', property: 'cover', width: 180 },
        { label: '原地址', property: 'articleOriginalUrl', width: 180 },
        { label: '文章状态', property: 'status', width: 80  },
        { label: '创建人', property: 'createdName' },
        { label: '创建时间', property: 'createdTime', sortable: true}
    ],
    tableButtons: [
        {
            label: '查看',
            icon: '',
            role: '',
            operation: '',
            component: 'ms-spider-preview',
            way: 'dialog',
            title: '文章预览',
            type: 'primary',
            width: '70%'
        },
        {
            label: '导入',
            operation: 'import',
            component: 'ms-spider-operation',
            way: 'dialog',
            type: 'primary',
            width: '50%',
            showCallback: (val) => {
                if (val.status === 0 || val.status === 2) {
                  return true
                } else {
                  return false
                }
            },
        },
        {
            label: '编辑',
            operation: 'edit',
            component: 'article-operation',
            way: 'page',
            path: 'article-operation',
            type: 'primary',
            params: [{
                keyName: 'id',
                valName: 'sourceId'
            }],
            showCallback: (val) => {
                if (val.status === 1) {
                  return true
                } else {
                  return false
                }
            },
        },
        {
            label: '删除',
            operation: 'delete',
            component: 'ms-spider-operation',
            way: 'dialog',
            type: 'danger',
            width: '50%',
        }
    ],
    soltButtons: [
        { 
            label: '批量导入', 
            type: 'primary', 
            icon: 'el-icon-plus',
            operation: 'import',
            component: 'ms-spider-operation',
            way: 'batch',
            width: '50%'
        },
        { 
            label: '批量删除', 
            type: 'info', 
            icon: 'el-icon-delete',
            operation: 'delete',
            component: 'ms-spider-operation',
            way: 'batch',
            width: '50%'
        }
    ]
}
  
  
export default searchConfig;
  