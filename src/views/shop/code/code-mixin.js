import domConfig from "./data/dom-config";
import scopeConfig from "./data/scope-config";
import msCodeOperation from "./tool/ms-code-operation";
import msCodeDetails from "./tool/ms-code-details";
import msCodeDelete from "./tool/ms-code-delete";
import msDescribeSearch from "@/components/MsCommon/ms-describe-search";

export default {
  data() {
    return {
      domConfig: domConfig, // => dom展示数据
      scopeConfig: scopeConfig, // => 显示信息配置 
    }
  },
  components: {
    msCodeOperation,
    msCodeDetails,
    msCodeDelete,
    msDescribeSearch
  }
}
