<template>
    <ms-operation-dialog>
        <template slot="content">
            <el-form ref="submitRef"
                    class="rule-form"
                    :model="submitData"
                    :rules="rules"
                    label-width="115px"
                    v-loading="getLoading">
                <el-row>
                  <el-col :span="24">
                        <el-form-item prop="usefulType" label="兑换码用途">
                           <ms-select-local :model.sync='submitData.usefulType' 
                              :options="[
                              { label: '2B项目', value: 1 },
                              { label: '运营项目', value: 2 },
                              { label: '会员兑换', value: 3 }]">
                            </ms-select-local>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item prop="usefulDescribe" label="用途描述">
                            <ms-input :model.sync="submitData.usefulDescribe" :maxlength='15' :showWordLimit='true'></ms-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24" v-if="submitData.usefulType != 3">
                        <el-form-item prop="integralValue" label="积分面值">
                            <ms-input-number :precision="0" :step="1" :stepStrictly="true" :max="5000" :model.sync="submitData.integralValue" width="100%"></ms-input-number>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24" v-if="submitData.usefulType == 3">
                        <el-form-item prop="cardId" label="会员类型">
                           <ms-select-local :model.sync='submitData.cardId' 
                              :options="memberList">
                            </ms-select-local>
                        </el-form-item>
                    </el-col><el-col :span="24" v-if="submitData.usefulType == 3">
                        <el-form-item prop="integralValue" label="会员时长">
                            <ms-input-number :precision="0" :step="1" :stepStrictly="true" :max="5000" :model.sync="submitData.integralValue" width="calc(100% - 30px)">
                            </ms-input-number>
                            <span style="position: absolute;right: 5px;top: 0;">天</span>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item prop="exchangeCodeSum" label="兑换码数量">
                            <ms-input-number :precision="0" :step="1" :stepStrictly="true" :max="5000" :model.sync="submitData.exchangeCodeSum" width="100%"></ms-input-number>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item prop="validityTime" label="有效时间">
                            <ms-picker type="datetimerange" :model.sync="submitData.validityTime" :config="pickerConfig" ></ms-picker>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item prop="exchangeLimit" label="每人可用" class="flex-row">
                            <ms-input-number :precision="0" :step="1" :stepStrictly="true" :max="5" :model.sync="submitData.exchangeLimit"></ms-input-number>
                            个码（注：每人可用≤5个）
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
        </template>
        <template slot="footer">
            <el-button @click="submitForm"
                        :loading="loading"
                        size="mini"
                        type="primary">确 定</el-button>
            <el-button @click="$emit('close')"
                        size="mini">取 消</el-button>
        </template>
    </ms-operation-dialog>
</template>

<script>
import { mapGetters } from "vuex";
export default {
	name: "ms-code-operation",
	data () {
        var validateValue = (rule, value, callback) => {
            console.log(this.submitData.usefulType, 'usefulType')
            if (!value) {
                return callback(new Error(this.submitData.usefulType == 3 ? '请输入会员时长' : '请输入积分面值'));
            } else {
                return callback()
            }
        }
		return {
            loading: false,
            getLoading: false,
            submitData: {
              "usefulType": 1,
              "usefulDescribe": "",
              "integralValue": 1,
              "exchangeCodeSum": 1,
              "validityStartTime": "",
              "validityEndTime": "",
              "exchangeLimit": 1,
              "validityTime": []
            },
            rules: {
                usefulType: [
                    { required: true, message: "请选择兑换码用途", trigger: 'blur' }
                ],
                usefulDescribe: [
                    { required: true, message: "请输入用途描述", trigger: 'blur' }
                ],
                cardId: [
                    { required: true, message: "请输入会员类型", trigger: 'blur' }
                ],
                integralValue: [
                    // { required: true, message: "请输入积分面值", trigger: 'blur' }
                    { required: true, validator: validateValue, trigger: 'blur' }
                ],
                exchangeCodeSum: [
                    { required: true, message: "请输入兑换码数量", trigger: 'change' }
                ],
                validityTime: [
                    { required: true, message: "请输入有效时间", trigger: 'blur' }
                ]
            },
            pickerConfig: {
              pickerOptions: {
                disabledDate: (time) => {
                  return time.getTime() < new Date().getTime() - 8.64e7;
                }
              }
            },
            memberList: []
		}
	},
	props: {
        model: Object,
        operation: String
    },
    computed: {
        ...mapGetters(["info"])
    },
    created() {
        this.init()
        this.initMemberList()
    },
	methods: {
        init() {
            let id = this.model.id
            if(id) {
                this.submitData = this.model
                this.submitData.validityTime = [this.model.validityStartTime,this.model.validityEndTime]
            } 
        },
        submitForm () {
            this.$refs["submitRef"].validate(valid => {
                if(valid) {
                    this.loading = true;
                    if (this.model.id) {
                        this.submitUpdate()
                    } else {
                        this.submitAdd()
                    }
                    
                }
            })
        },

        submitAdd() {
            this.submitData.validityStartTime = this.submitData.validityTime[0]
            this.submitData.validityEndTime = this.submitData.validityTime[1]
            let params = {
                createdBy: this.info.userId,
                createdName: this.info.userName,
                ...this.submitData
            }
            this.api.codeLibrarySave(params).then(response => {
                if(response.status === 200) {
                    this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
                } else {
                    this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
                }
                this.$emit('up-date')
                this.loading = false
            }).catch( () => {
                this.loading = false;
                this.$emit('close')
            })
        },

        submitUpdate() {
            this.submitData.validityStartTime = this.submitData.validityTime[0]
            this.submitData.validityEndTime = this.submitData.validityTime[1]
            let params = {
                createdBy: this.info.userId,
                createdName: this.info.userName,
                id: this.model.id,
                ...this.submitData
            }
            this.api.codeLibrarySave(params).then(response => {
                if(response.status === 200) {
                    this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
                } else {
                    this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
                }
                this.$emit('up-date')
                this.loading = false
            }).catch( () => {
                this.loading = false;
                this.$emit('close')
            })
        },
        initMemberList() {
            let searchParams = {
                pageIndex: 1,
                pageSize: 99,
                status: 1
            }
            this.api.memberCardQuery(searchParams).then(response => {
                if(response.data) {
                    response.data.forEach(element => {
                        this.memberList.push({
                            label: element.cardName,
                            value: element.id
                        })
                    });
                } else {
                    
                    this.memberList = []
                }
                if (response.status !== 200) {
                this.PUBLIC_Methods.apiNotify(
                    response.message || "请求出错",
                    "warning"
                )
                }
            })
            .catch((err) => (console.log(err)))
        },
	}
}
</script>
<style lang="scss" scoped>
.flex-row /deep/ .el-form-item__content {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
}
</style>
