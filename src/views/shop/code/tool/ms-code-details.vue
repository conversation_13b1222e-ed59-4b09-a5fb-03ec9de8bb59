<template>
   <div>
     <div class="code-tittle">
       <div>
        <span class="code-name">{{$route.query.name}}</span>
        <span>有效时间：{{$route.query.startTime}} 至 {{$route.query.endTime}}</span>
       </div>
      <el-button type="primary" @click="exportData" :disabled="loadingBtn" v-if="$route.query.usefulTypeValue != 3">导出</el-button>
     </div>
     <ms-table
          class="ms-table-box"
          :currentPage="searchParams.pageIndex"
          :loading="loading"
          :pageSize="searchParams.pageSize"
          :scopeConfig="scopeConfig.show"
          :tableData="list"
          :tableHeader="formConfig.tableHeader"
          :total="total"
          @current-change="current_change"
          @operation-change="operation_change"
          @size-change="size_change"
          >
      </ms-table>
      <!-- 提交按钮 -->
      <footer-tool-bar v-if="!loading">
          <template slot="tool-content">
              <el-button type="info" @click="$router.back()">返回</el-button>
          </template>
      </footer-tool-bar>
   </div>
</template>

<script>
import tableMixins from "../../../common/mixins/table"
import formConfig from "../data/form-config"
import scopeConfig from "../data/table-config"
import FooterToolBar from '@/components/ToolBar/footer-tool-bar'
import { mapGetters } from "vuex";
export default {
	name: "ms-code-details",
  mixins: [tableMixins],
	data () {
		return {
      loading: false,
      loadingBtn: false,
      getLoading: false,
      formConfig,
      scopeConfig
		}
	},
  components: {
    FooterToolBar,
  },
  props: {
    model: Object,
  },
  computed: {
      ...mapGetters(["info"])
  },
  created() {
      console.log(this.model)
  },
	methods: {
    apiInit (params) {
      let searchParams = {
        libraryId: this.$route.query.id,
        ...params
      }
      this.api.getExchageCodePage(searchParams).then(response => {
          this.loading = false
          this.total = response.totalSize || 0;
          this.list = response.data || []
          if (response.status !== 200) {
              this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
          }
      }).catch(() => this.loading = false)
    },
    exportData() {
      let data = {
        id: this.$route.query.id
      }
      this.loadingBtn = true
      this.api.codeLibraryExcelExport(data).then(response => {
          this.loadingBtn = false
          if (response.status !== 200) {
              this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
          }else {
            window.location.href = response.data
          }
      }).catch(() => this.loadingBtn = false)
    }
	}
}
</script>
<style lang="scss" scoped>
.code-tittle {
  text-align: left;
  margin-bottom: 10px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}
.code-name {
  font-size: 16px;
}
.code-tittle span {
  margin-right: 20px;
}
.ms-table-box {
  padding-bottom: 80px;
}
</style>
