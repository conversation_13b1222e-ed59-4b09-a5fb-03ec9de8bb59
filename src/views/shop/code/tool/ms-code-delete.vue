<template>
    <ms-operation-dialog :title="`是否要${dealType === 1 ? '删除' : dealType === 2 ? '恢复' : ''}以下兑换码`">
        <template slot="content">
            <el-tag v-for="(id, index) in strArr" 
                    :key="index" 
                    type="info" 
                    style="margin: 0 5px 5px 0">{{id}}</el-tag>
            </template>
            <template slot="footer">
            <el-button @click="submitForm"
                        :loading="loading"
                        size="mini"
                        type="primary">确 定</el-button>
            <el-button @click="$emit('close')"
                        size="mini">取 消</el-button>
        </template>
    </ms-operation-dialog>
</template>

<script>
export default {
	name: "ms-code-delete",
	data () {
		return {
            loading: false,
            userInfo: {},
            dealType: null,
            ids: '',
            strArr: []
		}
	},
	props: [
  "model",
  "operation"
  ],
  created() {
      this.userInfo = this.$store.getters.info || {}
      let showArr = []
      let operationLocal = this.operation || this.$route.query.operation
      this.dealType = operationLocal === 'delete' ? 1 : '';
      showArr.push(this.model.usefulDescribe)
      this.id = this.model.id
      this.strArr = showArr
  },
	methods: {
		submitForm () {
      this.loading = true;
      let params = {
          id: this.id
      }
      this.api.codeLibraryDeleteById(params).then(response => {
          if(response.status === 200) {
              this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
          } else {
              this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
          }
          this.$emit('up-date')
          this.loading = false
      }).catch( () => {
          this.loading = false;
          this.$emit('close')
      })
		}
	}
}
</script>
