const scopeConfig = {
    show: {
      usefulType: () => {
        return {
          type: 'code',
          rule: {
            '1': { label: '2B项目' },
            '2': { label: '运营活动' },
            '3': { label: '会员兑换' },
          }
        }
      },
      status: () => {
        return {
          type: 'code',
          rule: {
            0: { label: '未生效' },
            1: { label: '已生效' },
            2: { label: '已失效' },
          }
        }
      },
      fields: () => {
        return {
          type: 'fidd',
          fields: [
            {name: 'exchangeSum', way: 'text'},
            {name: 'exchangeCodeSum', way: 'text'},
          ]
        }
      },
    },
    headerShow: {
     
    }
  }
  
  export default scopeConfig;
  