const domConfig = {
    listSearch: [
      {
        label: '兑换码用途',
        model: 'usefulType',
        component: 'ms-select-local',
        options: [
          { label: '2B项目', value: 1 },
          { label: '运营活动', value: 2 },
          { label: '会员兑换', value: 3 },
        ]
      },
      {
        label: '用途描述',
        model: 'usefulDescribe',
        component: 'msDescribeSearch'
      },
      {
        label: '创建时间',
        placeholder: '请选择时间段',
        model: 'createTime',
        component: 'ms-picker',
        type: 'daterange',
        width: '240px'
      },
    ],
    tableHeader: [
      { label: 'ID', property: 'id', width: '60' },
      { label: '兑换码用途', property: 'usefulType', width: '100' },
      { label: '用途描述', property: 'usefulDescribe', width: '100' },
      { label: '兑换类型', property: 'cardName', width: '100' },
      { label: '兑换值', property: 'integralValue', sortable: true, width: '100' },
      { label: '状态', property: 'status' },
      { label: '有效期开始时间', property: 'validityStartTime', width: '130' },
      { label: '有效期结束时间', property: 'validityEndTime', width: '130' },
      { label: '兑换限制', property: 'exchangeLimit', width: '80' },
      { label: '兑换人数/数量', property: 'fields' },
      { label: '创建时间', property: 'createdTime', width: '130'  },
      { label: '创建人', property: 'createdName', width: '80'  }
    ],
    soltButtons: [
      { 
        label: '创建码库', 
        type: 'primary', 
        icon: 'el-icon-plus',
        operation: 'created',
        title: '创建码库',
        component: 'msCodeOperation',
        way: 'dialog',
      },
    ],
    tableButtons: [
      // {
      //   label: '编辑',
      //   way: 'dialog',
      //   type: 'primary',
      //   operation: 'update',
      //   component: 'msCodeOperation',
      //   title: '编辑码库',
      // },
      {
        label: '码库',
        type: 'primary', 
        way: 'page',
        path: 'code-details',
        params: [
          {keyName:'id',valName:'id'},
          {keyName:'name',valName:'usefulDescribe'},
          {keyName:'startTime',valName:'validityStartTime'},
          {keyName:'endTime',valName:'validityEndTime'},
          {keyName:'usefulTypeValue',valName:'usefulType'},
        ]
      },
      {
        label: '删除',
        way: 'dialog',
        type: 'danger',
        operation: 'delete',
        component: 'msCodeDelete',
      }
    ]
  }
  
  export default domConfig;
  