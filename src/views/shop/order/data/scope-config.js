const scopeConfig = {
    show: {
      status: () => {
        return {
          type: 'status',
          rule: {
            0: { label: '审核中', background: '#E6A23C' },
            1: { label: '已发货', background: '#40A23F' }
          }
        }
      },
      createdName: () => {
        return {
            type: 'webLink',
            config: {
              role: '',
              operation: 'edit',
              way: 'page',
              path: 'user-detail',
              params: [{
                keyName: 'id',
                valName: 'createdBy'
              }],
            }
        }
      },
      goodsTitle: (row) => {
        return {
          type: 'fidd',
          fields: [
            {name: row.goodsTitle ?'goodsTitle':null, way: 'text'},
          ]
        }
      },
      goodsCategoryName: (row) => {
        return {
          type: 'fidd',
          fields: [
            {name: row.goodsCategoryName ?'goodsCategoryName':null, way: 'text'},
          ]
        }
      },
      goodsAttributes: (row) => {
        return {
          type: 'fidd',
          fields: [
            {name: row.goodsAttributes ?'goodsAttributes':null, way: 'text'},
          ]
        }
      },
    },
    headerShow: {
      status: () => {
        return {
          type: 'dropdown',
          icon: 'icon-funnel',
          options: [
            { label: '全部', value: null },
            { label: '审核中', value: 0 },
            { label: '已发货', value: 1 }
          ],
          operation: 'query'
        }
      }
    }
  }
  
  export default scopeConfig;
  