const domConfig = {
    listSearch: [
      {
        label: '商品名称',
        model: 'title',
        component: 'ms-input'
      },
      {
        label: '商品分类',
        model: 'categoryId',
        component: 'ms-category-cascader',
        config: {
          moduleName: 'shop'
        }
      },
      {
        label: '兑换人姓名',
        model: 'uname',
        component: 'ms-input'
      },
      {
        label: '兑换人手机',
        model: 'mobile',
        component: 'ms-input'
      },
    ],
    tableHeader: [
      // { label: '订单ID', property: 'id' },
      { label: '商品名称', property: 'goodsTitle', width: '100' },
      { label: '商品类型', property: 'goodsCategoryName', width: '100' },
      { label: '商品属性', property: 'goodsAttributes'},
      { label: '兑换人', property: 'createdName', width: '100' },
      { label: '收货人', property: 'receiverName', width: '80' },
      { label: '手机号码', property: 'mobile', sortable: true },
      { label: '收货地址', property: 'expressAddress', width: '150' },
      { label: '订单状态', property: 'status'},
      { label: '创建时间', property: 'createdTime', sortable: true,  width: '130'  }
    ],
    tableButtons: [
      {
        label: '发货',
        way: 'dialog',
        type: 'primary',
        operation: 'update',
        component: 'msOrderDeliver',
        title: '实物商品发货',
        showCallback:(val) => {
          if(val.status === 0 && val.goodsAttributes === '实物') {
            return true
          } else {
            return false
          }
        }
      }
    ]
  }
  
  export default domConfig;
  