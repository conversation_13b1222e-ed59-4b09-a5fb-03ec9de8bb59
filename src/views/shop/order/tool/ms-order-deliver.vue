<template>
  <ms-operation-dialog>
    <template slot="content">
      <el-form ref="submitRef"
               :model="submitData"
               :rules="rules"
               label-width="85px"
               class="rule-form">
        <el-row>
          <el-col :span="18" :offset="3">
            <el-form-item label="发货信息" prop="expressId">
                <!-- <el-select v-model="submitData.expressType" style="width: 100%">
                    <el-option :value="1" label="顺丰"></el-option>
                    <el-option :value="2" label="中通"></el-option>
                    <el-option :value="3" label="圆通"></el-option>
                    <el-option :value="4" label="韵达"></el-option>
                    <el-option :value="5" label="其他"></el-option>
                </el-select> -->
                <el-input
                    type="textarea"
                    :autosize="{ minRows: 4, maxRows: 8}"
                    placeholder="请输入物流公司及快递单号"
                    v-model="submitData.expressId">
                </el-input>
            </el-form-item>
            <!-- <el-form-item label="运单号" prop="expressId">
              <el-input v-model="submitData.expressId" placeholder="请输入"></el-input>
            </el-form-item> -->
          </el-col>
        </el-row>
      </el-form>
    </template>
    <template slot="footer">
      <el-button @click="submitForm"
                 :loading="loading"
                 size="mini"
                 type="primary">提交</el-button>
      <el-button @click="$emit('close')"
                 size="mini">取 消</el-button>
    </template>
  </ms-operation-dialog>
</template>

<script> 
export default {
	name: "ms-order-deliver",
	data () {
		return {
            loading: false,
            submitData: {
                "userId": 0,
                "username": "",
                "id": 0,
                "expressId": ""
            },
            rules: {
                expressId: [
                    { required: true, message: "请输入发货信息", trigger: 'blur' }
                ]
            },
        }
    },
	props: {
		model: Object,
		operation: String
    },
    created() {
        this.submitData.expressId = this.model.express
    },
	methods: {
		submitForm() {
            this.$refs.submitRef.validate( valid => {
                if(valid) {
                    let params = {
                        ...this.submitData,
                        userId: this.$store.getters.info.userId,
                        username: this.$store.getters.info.userName,
                        id: this.model.id
                    }
                    this.loading = true;
                    this.api.orderUpdate(params).then( response => {
                        if(response.status === 200) {
                            this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
                        } else {
                            this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
                        }
                        this.$emit('up-date')
                        this.loading = false
                    }).catch( () => {
                        this.loading = false;
                        this.$emit('close')
                    })
                }
            })
        }
	}
}
</script>
