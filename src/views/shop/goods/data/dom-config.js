const domConfig = {
  listSearch: [
    {
      label: '商品名称',
      placeholder: '请输入',
      model: 'title',
      component: 'ms-input'
    },
    {
      label: '商品分类',
      model: 'categoryId',
      component: 'ms-category-cascader',
      config: {
        moduleName: 'shop'
      }
    },
    {
      label: '创建时间',
      placeholder: '请选择时间段',
      model: 'createTime',
      component: 'ms-picker',
      type: 'daterange',
      width: '240px'
    },
  ],
  tableHeader: [
    { label: '商品名称', property: 'title', width: '160' },
    { label: '商品类型', property: 'categoryName' },
    { label: '商品属性', property: 'goodsAttributes' },
    { label: '积分数量', property: 'paymentAmount', width: '80' },
    { label: '商品置顶', property: 'sticky',},
    { label: '审核状态', property: 'status'},
    { label: '库存数量', property: 'stock'},
    { label: '创建人', property: 'createdName'},
    { label: '创建时间', property: 'createdTime', sortable: true,  width: '130'  },
    { label: '下线时间', property: 'statue_time'},
  ],
  tableButtons: [
    {
      label: '编辑',
      icon: '',
      role: '',
      operation: 'edit',
      component: 'goods-operation',
      way: 'page',
      type: 'primary',
      path: 'goods-operation',
      params: ['id']
    },
    {
      label: '',
      icon: '',
      role: '',
      operation: 'editStatus',
      component: 'msGoodsAudit',
      way: 'dialog',
      field: 'status',
      rule: {
        1: { label: '去审', type: '', operation: 'toreview' },
        0: { label: '审核', type: 'success', operation: 'approval' }
      },
      identify: 'status'
    },
    {
      label: '删除',
      way: 'dialog',
      type: 'danger',
      operation: 'delete',
      component: 'msGoodsOperation',
    }
  ],
  soltButtons: [
    { 
      label: '创建商品', 
      type: 'primary', 
      icon: 'el-icon-plus',
      operation: 'created',
      component: 'goods-operation',
      way: 'page',
      path: 'goods-operation',
      params: ['id']
    },
    { 
      label: '批量删除',
      type: 'info',
      icon: 'el-icon-close',
      operation: 'delete',
      component: 'msGoodsOperation',
      way: 'batch',
    }
  ]
}

export default domConfig;
