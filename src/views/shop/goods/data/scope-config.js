const scopeConfig = {
  show: {
    paymentType: () => {
      return {
        type: 'status-button',
        rule: {
          1: { label: '积分', background: '#67C23A' },
          2: { label: '梅花', background: '#E6A23C' }
        }
      }
    },
    sticky: () => {
      return {
        type: 'switch',
        activeVal: 1,
        inactiveVal: 0,
        config: {
          way: 'update',
          keyName: 'sticky'
        }
      }
    },
    status: () => {
      return {
        type:'status',
        rule: {
          0: {label:'待审核',background:'rgba(170,170,170)'},
          1: {label:'审核成功',background:'#40A23F'}
        }
      }
    },
    goodsAttributes: (row) => {
      return {
        type: 'fidd',
        fields: [
          {name: row.goodsAttributes ?'goodsAttributes':null, way: 'text'},
        ]
      }
    },
    statue_time: (row) => {
      return {
        type: 'fidd',
        fields: [
          {name: row.statue_time ?'statue_time':null, way: 'text'},
        ]
      }
    }
  },
  headerShow: {
    paymentType: () => {
      return {
        type: 'dropdown',
        icon: 'icon-funnel',
        options: [
          { label: '全部', value: null },
          { label: '积分', value: 1 },
          { label: '梅花', value: 2 }
        ],
        operation: 'query'
      }
    },
    sticky: () => {
      return {
        type: 'dropdown',
        icon: 'icon-funnel',
        options: [
          { label: '全部', value: null },
          { label: '不置顶', value: 0 },
          { label: '置顶', value: 1 }
        ],
        operation: 'query'
      }
    },
    status: () => {
      return {
        type: 'dropdown',
        icon: 'icon-funnel',
        options: [
          { label: '全部', value: null },
          { label: '待审核', value: 0 },
          { label: '审核通过', value: 1 }
        ],
        operation: 'query'
      }
    }
    
  }
}

export default scopeConfig;
