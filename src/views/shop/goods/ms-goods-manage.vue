<template>
	<ms-table
		:currentPage="searchParams.pageIndex"
		:loading="loading"
		:operationButtons="domConfig.tableButtons"
		:pageSize="searchParams.pageSize"
		:scopeConfig="scopeConfig.show"
        :scopeHeader="scopeConfig.headerShow"
		:tableData="list"
		:tableHeader="domConfig.tableHeader"
		:total="total"
        :showSelection="true"
        :showIndex='showIndex'
		@current-change="current_change"
		@operation-change="operation_change"
		@size-change="size_change"
        @header-operation="header_operation"
        @handleSelectionChange="handleSelectionChange"
        class="table-svg"
	>
    <!-- 列表搜索去区域插槽 -->
		<template slot="ms-table-header">
			<div class="slot-search">
				<template v-for="(searchItem, key) in domConfig.listSearch">
					<component
						:index="searchItem.index || ''"
						:is="searchItem.component"
						:key="key"
                        :width="searchItem.width || '150px'"
                        :model.sync="searchParams[searchItem.model]"
                        :label="searchItem.label"
						:operation="searchItem.operation || ''"
						:options="searchItem.options || []"
						:placeholder="searchItem.placeholder || ''"
						:type="searchItem.type || ''"
                        :multiple="searchItem.multiple"
                        :disabled="searchItem.disabled"
                        :code="searchItem.code"
                        :config="searchItem.config" 
                        v-if="key < search_number"
					></component>
				</template>
                <div class="inlineBlock">
                    <el-button @click="handleClick('query')" type="primary" plain icon="el-icon-search">查询</el-button>
                    <el-button @click="handleClick('reset')">重置</el-button>
                    <!-- <el-button @click="search_number = search_number === 99 ? 4 : 99">{{search_number === 99 ? '收起' : '展开'}} <i :class="search_number === 99 ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i></el-button> -->
                </div>
        
			</div>
			<div class="slot-button-article clearfix">
				<template v-for="(item, index) in domConfig.soltButtons">
					<el-button :key="index" 
                     :type="item.type" 
                     size="mini"
                     :icon="item.icon"
                     @click="operation_change({operation: item})"
                     :disabled="item.roleDisabled"
                     plain>{{ item.label }}</el-button>
				</template>
			</div>
			<el-dialog :visible.sync="dialog" 
                 closeable 
                 show-close
                 :close-on-click-modal="false"
                 :width="dialogWidth"
                 :title="dialogTitle">
				<component :is="dialogComponent" 
                   :model="scopeInfo" 
                   :operation="dialogOperation" 
                   @close="dialog = !dialog" 
                   @up-date="init" 
                   v-if="dialog"></component>
			</el-dialog>
		</template>
	</ms-table>
</template>

<script>
import goodsMixin from "./goods-mixin"
import tableMixins from "../../common/mixins/table"
import msGoodsCategory from "@/components/MsCommon/ms-goods-category"
import msGoodsOperation from "./tool/ms-goods-operation"
import msGoodsAudit from './tool/ms-goods-audit'
export default {
    name: "ms-goods-manage",
    mixins: [tableMixins,goodsMixin],
    data () {
        return {
            search_number: 4,
            showIndex: true
        }
    },
    components: {
        msGoodsCategory,
        msGoodsOperation,
        msGoodsAudit
    },
    methods: {
        apiInit (params) {
            let searchParams = {...params}
            if (searchParams.createTime) {
                searchParams.publishedStartTime = searchParams.createTime[0] || ''
                searchParams.publishedEndTime = searchParams.createTime[1] || ''
            }
            this.api.goodsPageList(searchParams).then(response => {
                this.loading = false
                this.total = response.totalSize || 0;
                this.list = response.data || []
                if (response.status !== 200) {
                this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
                }
            }).catch(() => this.loading = false)
        },
        operation_change_module(val) {
            if (val.operation.way === 'update') {
                let params = {
                    userId: this.$store.getters.info.userId,
                    username: this.$store.getters.info.userName,
                    id: val.model.id
                }
                params[val.operation.keyName] = val.operation.valName

                this.api.goodsUpdate(params).then(response => {
                    if(response.status === 200) {
                        this.init()
                    } else {
                        this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
                    }
                }).catch()
            }
        }
    }
}
</script>
