<template>
  <section class="form-tab" v-loading="getLoading">
    <el-form :model="submitData"
             ref="submitRef"
             class="rule-form info-form"
             :rules="rules"
             label-width="110px">
      <el-tabs v-model="activeName">
        <el-tab-pane label="基本信息" name="message">
          <el-row :gutter="20">
            <el-col :span="18">
              <el-row>
                <el-form-item label="商品名称" prop="title">
                  <el-input v-model="submitData.title" placeholder="请输入100字以内的商品名称" :maxlength='100'></el-input>
                </el-form-item>
              </el-row>
              <el-row>
                <el-col :span="12">
                  <el-form-item label="商品类型" prop="categoryId">
                    <el-select v-model="submitData.categoryId" style="width:100%" @change='categoryChange'>
                      <el-option :value="item.categoryId" :label="item.titleCn" v-for="(item,index) in categoryData"
                                 :key="index"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="商品属性" prop="goodsAttributes">
                    <el-select v-model="submitData.goodsAttributes" style="width:100%">
                      <el-option :value="item.goodsAttributes" :label="item.goodsAttributes"
                                 v-for="(item ,index) in commodityProperty" :key="index"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row v-if="submitData.categoryName == '虚拟券码'">
                <el-col :span="12">
                  <el-form-item label="绑定券码" prop="categoryId">
                    <el-select v-model="submitData.ticketProject" filterable clearable style="width:49%;margin-right:2%" @change='ticketChange'>
                      <el-option :value="item.projectId" :label="item.name" v-for="(item,index) in projectData"
                                 :key="index"></el-option>
                    </el-select>
                    <el-select v-model="submitData.ticketId" clearable filterable style="width:49%">
                      <el-option :value="item.id" :label="`${item.title}(${item.remaining})`"
                                 v-for="(item ,index) in ticketList" :key="index"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="8">
                  <el-form-item label="支付方式" prop="paymentType">
                    <el-select v-model="submitData.paymentType" style="width:100%">
                      <el-option :value="1" label="积分" v-if="submitData.categoryName != '虚拟券码'"></el-option>
                      <el-option :value="2" label="梅花"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="支付金额" prop="paymentAmount">
                    <el-input-number :min="0" v-model="submitData.paymentAmount" style="width:100%"></el-input-number>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="兑换要求" prop="exchangeCondition">
                    <el-select v-model="submitData.exchangeCondition" style="width:100%">
                      <el-option :value="0" label="无条件"></el-option>
                      <el-option :value="1" label="完善信息"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="12">
                  <el-form-item label="兑换次数" prop="exchangeCount">
                    <el-input-number :min="0" v-model="submitData.exchangeCount" style="width:100%"></el-input-number>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-form-item label="商品详情" prop="description">
                  <ms-editor v-model="submitData.description"></ms-editor>
                </el-form-item>
              </el-row>
              <el-row>
                <el-form-item label="提取地址" prop="extractAddress" v-if="submitData.categoryName != '虚拟券码' && submitData.goodsAttributes === '电子书'">
                  <el-input v-model="submitData.extractAddress" placeholder="请输入电子书下载地址"></el-input>
                </el-form-item>
                <el-form-item label="视频课程" prop="seriesId" v-if="submitData.goodsAttributes === '视频课程'">
                  <el-select
                    v-model="submitData.seriesId"
                    filterable
                    remote
                    :remote-method="initVideoList"
                    clearable
                    ref='myselected'
                    placeholder="请输入课程标题并选择课程"
                    style="width:100%"
                    v-el-select-loadmore="loadmore"
                    @change="changeInitVideo"
                  >
                    <el-option v-for="(item,index) in videoList" :key="index" :label="item.title"
                               :value="item.id"></el-option>
                  </el-select>
                </el-form-item>
              </el-row>
            </el-col>
          </el-row>
        </el-tab-pane>
        <el-tab-pane label="商品设置" name="install">
          <el-row :gutter="20">
            <el-col :span="18">
              <el-row>
                <el-col :span="12">
                  <el-form-item label="市场价格" prop="marketPrice">
                    <el-input-number :min="0" v-model="submitData.marketPrice" style="width: 100%"></el-input-number>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="12">
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="12">
                  <el-form-item label="库存数量" prop="stock">
                    <el-input-number v-model="submitData.stock" v-if="submitData.categoryName == '虚拟券码'" :min="0" @change="changeCount" :max="count"   style="width: 100%"></el-input-number>
                    <el-input-number v-model="submitData.stock" v-else :min="0"    style="width: 100%"></el-input-number>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="12">
                  <el-form-item label="商品置顶" prop="stickyStartDate">
                    <el-date-picker
                      v-model="submitData.stickyStartDate"
                      type="daterange"
                      range-separator="至"
                      start-placeholder="开始日期"
                      end-placeholder="结束日期"
                      style="width:100%"
                      :picker-options="pickerOptions"
                      value-format="yyyy-MM-dd HH:mm:ss"
                    >
                    </el-date-picker>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="12">
                  <el-form-item label="显示选项">
                    <el-checkbox v-model="submitData.status" style="width: 33%" :true-label="1" :false-label="0">上架
                    </el-checkbox>
                    <el-checkbox v-model="submitData.sticky" style="width: 33%" :true-label="1" :false-label="0">置顶
                    </el-checkbox>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="12">
                  <el-form-item label="前台选项">
                    <el-checkbox v-model="submitData.showZhu" style="width: 33%" :true-label="1" :false-label="0">APP / H5官网
                    </el-checkbox>
                    <el-checkbox v-model="submitData.showApoc" style="width: 33%" :true-label="1" :false-label="0">项目
                    </el-checkbox>
                  </el-form-item>
                </el-col>
              </el-row>
              
              <el-form-item prop="detailImg">
                <div slot="label">
                  <div>商品小图</div>
                  <div style="color:#999;">尺寸608*344px</div>
                </div>
                <ms-single-image v-model="submitData.detailImg"></ms-single-image>
              </el-form-item>
              <el-form-item prop="bigImg">
                <div slot="label">
                  <div>商品大图</div>
                  <div style="color:#999;">尺寸1380*776px</div>
                </div>
                <ms-single-image v-model="submitData.bigImg"></ms-single-image>
              </el-form-item>
            </el-col>
          </el-row>
        </el-tab-pane>
        <el-tab-pane label="显示栏目" name="category">
            <ms-info-setting :categoryModel.sync="submitData.categorys"   :categoryConfigChild="{moduleName: 'shop'}" headerShow></ms-info-setting>
        </el-tab-pane>
      </el-tabs>
    </el-form>
    <footer-tool-bar v-loading="buttonLoading">
      <template slot="tool-content">
        <el-button type="primary" @click="info_operation('save')">保存</el-button>
        <el-button type="info" @click="info_operation('back')">返回</el-button>
      </template>
    </footer-tool-bar>
  </section>
</template>

<script>
import FooterToolBar from '@/components/ToolBar/footer-tool-bar'
import MsEditor from '@/components/MsEditor'
import msInfoSetting from '@/components/MsCommon/ms-info-setting'
import { mapGetters } from 'vuex'

export default {
  name: 'goods-operation',
  data () {
    return {
      buttonLoading: false,
      getLoading: false,
      dataId: this.$route.query.id ? this.$route.query.id : 0,
      submitData: {
        'title': '',
        'description': '',
        'categoryId': null,
        'categoryName': '',
        'bigImg': '',
        'detailImg': '',
        'stock': 0,
        'sticky': 0,
        'stickyEndTime': '',
        'stickyStartTime': '',
        'status': 0,
        'paymentType': null,
        'paymentAmount': 0,
        'exchangeCount': 0,
        'seriesId': null,
        'seriesName': '',
        'goodsAttributes': '',
        'extractAddress': '',
        'marketPrice': '',
        'stickyStartDate': [],
        'ticketProject':'',
        'ticketId':'',
        'categorys':[],
        'showZhu':0,
        'showApoc':0
      },
      rules: {
        title: [
          { required: true, message: '请输入商品名称', trigger: 'blur' }
        ],
        categoryId: [
          { required: true, message: '请选择商品类型', trigger: 'blur' }
        ],
        goodsAttributes: [
          { required: true, message: '请选择商品属性', trigger: 'blur' }
        ],
        paymentType: [
          { required: true, message: '请选择支付方式', trigger: 'blur' }
        ],
        paymentAmount: [
          { required: true, message: '请输入支付金额', trigger: 'blur' },
          {
            validator: (rule, value, callback) => {
              if (!value) {
                callback('请输入支付金额')
              } else {
                callback()
              }
            }, trigger: ' blur '
          }
        ],
        description: [
          { required: true, message: '请输入商品详情', trigger: 'blur' }
        ],
        extractAddress: [
          { required: true, message: '请输入提取地址', trigger: 'blur' }
        ],
        seriesId: [
          { required: true, message: '请输入视频课程', trigger: 'blur' }
        ]
      },
      activeName: 'message',
      categoryData: [],
      commodityProperty: [],
      projectData:[],
      ticketList:[],
      ticketData:{},
      pickerOptions: {
        disabledDate (time) {
          return time.getTime() < Date.now() - 8.64e7
        },
      },
      videoPageIndex: 1,
      videoList: [],
      normal: false,
      pitchOn: false,
      videoCourseId: null,
    }
  },
  components: {
    FooterToolBar,
    MsEditor,
    msInfoSetting
  },
  watch:{
    'submitData.ticketId': {
      handler() {
        this.ticketList.forEach(element => {
          if(this.submitData.ticketId==element.id){
            this.counts = element.remaining
          }
        });
      },
      // 如果需要在对象属性被添加或删除时也触发，可以使用 deep: true
      deep: true,
      immediate:true
    }
  },
  computed: {
    ...mapGetters(['info'])
  },
  created () {
    this.init()
    this.queryCategory()
  },
  directives: {
    'el-select-loadmore': {
      bind (el, binding) {
        const SELECTWRAP_DOM = el.querySelector(
          '.el-select-dropdown .el-select-dropdown__wrap'
        )
        SELECTWRAP_DOM.addEventListener('scroll', function () {
          const condition =
            this.scrollHeight - this.scrollTop <= this.clientHeight
          if (condition) {
            binding.value()
          }
        })
      }
    }
  },
  methods: {
    changeCount(){
      this.count = this.dataId? this.submitData.stockCopy+this.counts:this.counts
    },
    init () {
      let id = this.dataId
      if (id !== 0) {
        this.getLoading = true
        this.submitData.id = id
        this.api.goodsDetail({ id: id }).then(response => {
          this.getLoading = false
          if (response.status === 200) {
            let oldData = JSON.parse(JSON.stringify(response.data))
            response.data.ticketId = ''
            response.data.ticketProject = ''
            response.data.stockCopy = JSON.parse(JSON.stringify(response.data.stock))
            let res = response.data
            this.submitData.exchangeCondition = JSON.stringify(response.data.exchangeCondition)

            this.submitData = {
              ...this.submitData,
              ...res,
              description: this.PUBLIC_Methods.unexcapeHtml(res.description),
              stickyStartDate: res.stickyStartTime && res.stickyEndTime ? [res.stickyStartTime.toString(), res.stickyEndTime.toString()] : []
            }
            this.videoCourseId = this.submitData.seriesId
            this.submitData.seriesId = this.submitData.seriesName
            if(this.submitData.categoryName == '虚拟券码'){
              this.api.projectList().then(res => {
                if (res.status === 200) {
                    this.projectData = res.data.projectList
                    this.ticketData = res.data.voucherMap
                    this.submitData.ticketProject = oldData.ticketProject
                    if(JSON.stringify(this.ticketData)!='{}'){
                     this.ticketList = this.ticketData[this.submitData.ticketProject]
                    }
                    this.submitData.ticketId = Number(oldData.ticketId)
                }
              })
            }
            this.ticketList.forEach(element => {
          if(this.submitData.ticketId==element.id){
            this.count = element.remaining
          }
        });
          } else {
            this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
          }
        }).catch(() => {
          this.getLoading = false
        })
      }
    },
    info_operation (val) {
      switch (val) {
        case 'save':
          this.$refs['submitRef'].validate(valid => {
            if (valid) {
              this.submitData.description = this.PUBLIC_Methods.excapeHtml(this.submitData.description)
              this.submitData.stickyStartTime = this.submitData.stickyStartDate[0]
              this.submitData.stickyEndTime = this.submitData.stickyStartDate[1]
              this.dataId ? this.updateGoods() : this.createGoods()
            }
          })
          break
        case 'back':
          this.$router.back()
          break
        default:
          break
      }
    },
    createGoods () {
      this.buttonLoading = true
      let params = {
        userId: this.info.userId,
        username: this.info.userName,
        ...this.submitData
      }
      this.api.goodsCreate(params).then(response => {
        this.buttonLoading = false
        if (response.status === 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
          this.$router.back()
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
      }).catch(() => this.buttonLoading = false)
    },
    updateGoods () {
      this.buttonLoading = true
      let params = {
        userId: this.info.userId,
        username: this.info.userName,
        ...this.submitData
      }
      if (!this.pitchOn) {
        params.seriesId = this.videoCourseId
      }
      this.api.goodsUpdate(params).then(response => {
        this.buttonLoading = false
        if (response.status === 200) {
          this.PUBLIC_Methods.apiNotify(response.message || '请求成功', 'success')
          this.$router.back()
        } else {
          this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        }
      }).catch(() => this.buttonLoading = false)
    },
    ticketChange(val){
      this.ticketList = this.ticketData[val]
    },
    categoryChange (val) {
      this.commodityProperty = []
      this.submitData.goodsAttributes = ''
      let name = ''
        this.submitData.paymentType = null

      name = this.categoryData.find(item => {
        return item.categoryId === val
      })
      this.submitData.categoryName = name.titleCn
      if(this.submitData.categoryName == '虚拟券码'){
        this.api.projectList().then(response => {
          console.log(response)
          if (response.status === 200) {
            this.projectData = response.data.projectList
            this.ticketData = response.data.voucherMap
          }
        })
      }
      let params = {
        categoryName: name.titleCn,
        pageIndex: 1,
        pageSize: 2
      }
      this.api.getMallGoodsAttributes(params).then(response => {
        if (response.status === 200) {
          this.commodityProperty = response.data
        }
      })
    },
    queryCategory () {
      let params = {
        module: 'shop'
      }
      this.api.getProjectModuleAndCategoryList(params).then(response => {
        if (response.status === 200) {
          this.categoryData = response.data[0].children
        }
      })
    },
    initVideoList (str) {
      if (!str || this.normal) return
      this.submitData.seriesName = str
      let params = {
        pageIndex: this.videoPageIndex,
        pageSize: 20,
        status: 1,
        title: str
      }
      this.api.videoPageList(params).then(response => {
        if (response.status === 200) {
          this.videoPageIndex++
          this.videoList = [...this.videoList, ...response.data]
          if (this.videoList.length === response.totalSize) this.normal = true
        } else {
          this.PUBLIC_Methods.apiNotify(
            response.message || '请求出错',
            'warning'
          )
        }
      })
    },
    loadmore () {
      this.initVideoList(this.submitData.seriesName)
    },
    changeInitVideo (val) {
      this.pitchOn = true
      if (val) {
        let str = this.videoList.find(item => {
          return item.id === val
        })
        this.submitData.seriesName = str.title
      }
    }
  }
}
</script>
