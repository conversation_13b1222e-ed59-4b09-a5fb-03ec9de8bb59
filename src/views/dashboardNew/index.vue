<template>
  <div>
    <el-tabs v-model="activeName" @tab-click="tab_click" >
      <!-- <el-tab-pane label="资讯报表" name="article_report"></el-tab-pane> -->
      <el-tab-pane label="总看板" name="all_report"></el-tab-pane>
      <el-tab-pane label="用户报表" name="user_report"></el-tab-pane>
      <el-tab-pane label="付费用户" name="user_pay"></el-tab-pane>
      <el-tab-pane label="兼职报表" name="part_time"></el-tab-pane>
      <keep-alive>
        <component :is="dashboardComponent"></component>
      </keep-alive>
    </el-tabs>
  </div>
</template>

<script>
import { mapGetters } from "vuex";

import MsInfoDashboard from './tool/ms-info-dashboard'
import MsUserDashboard from './tool/ms-user-dashboard'
import MsUserPayboard from './tool/ms-user-payboard'
import MsAllBoard from './tool/ms-all-board'
import MsBehaviorDashboard from './tool/ms-behavior-dashboard'
import MsPartTimeBoard from './tool/ms-part-time-board'
export default {
  name: "ms-video",
  data () {
    return {
      activeName: 'all_report',
      // activeName: 'user_report',
      dashboardComponent: 'MsAllBoard',
      // dashboardComponent: 'MsUserDashboard',
      tabPaneList: []
    }
  },
  components: {
    MsInfoDashboard,
    MsUserDashboard,
    MsBehaviorDashboard,
    MsUserPayboard,
    MsAllBoard,
    MsPartTimeBoard
  },
  computed: {
    ...mapGetters(["dashboardActive","info","permissionMenuId"])
  },
  watch: {
  },
  mounted () {
    // this.getListPermission()
  },

  methods: {
    
    tab_click() {
        switch(this.activeName) {
            // case 'article_report':
            //     this.dashboardComponent = 'MsInfoDashboard';
            //     break;
            case 'user_report':
                this.dashboardComponent = 'MsUserDashboard';
                break;
            case 'user_pay':
                this.dashboardComponent = 'MsUserPayboard';
                break;
            case 'all_report':
                this.dashboardComponent = 'MsAllBoard';
                break;
            case 'part_time':
                this.dashboardComponent = 'MsPartTimeBoard';
                break;
            // case 'behavior_report':
            //     this.dashboardComponent = 'MsBehaviorDashboard';
            //     break;
            default: break;
        }
        // this.$store.dispatch('SetDashboardActive', this.activeName)
    }
  }
}
</script>
