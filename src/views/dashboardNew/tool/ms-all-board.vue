<template>
  <section v-if="isShow">
    <div class="date-picker">
      <el-date-picker
        unlink-panels
        v-model="value1"
        type="daterange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        value-format="yyyy-MM-dd"
      >
      </el-date-picker>
      <el-button type="primary" @click="hanleQuery" style="margin-left: 5%"
        >查询</el-button
      >
    </div>
    <div style="margin-bottom:30px;">
      <el-row :gutter="20" style="margin-bottom:23px;display:flex;justify-content:center">
      <el-col :span="3" v-if="resData1.totalPageView != 0">
        <div>
          <div class="title-type">总浏览量(PV)</div>
          <div class="num-detail">{{formatThousands(resData1.totalPageView)}}</div>
        </div>
      </el-col>
      <el-col :span="3" v-if="resData1.articlePageView != 0">
        <div>
          <div class="title-type">资讯(PV)</div>
          <div class="num-detail">{{formatThousands(resData1.articlePageView)}}</div>
        </div>
      </el-col>
      <el-col :span="3" v-if="resData1.journalPageView != 0">
        <div>
          <div class="title-type">期刊(PV)</div>
          <div class="num-detail">{{formatThousands(resData1.journalPageView)}}</div>
        </div>
      </el-col>
      <el-col :span="3" v-if="resData1.guiderPageView != 0">
        <div>
          <div class="title-type">指南(PV)</div>
          <div class="num-detail">{{formatThousands(resData1.guiderPageView)}}</div>
        </div>
      </el-col>
      <el-col :span="3" v-if="resData1.nsfcPageView != 0">
        <div>
          <div class="title-type">国自然(PV)</div>
          <div class="num-detail">{{formatThousands(resData1.nsfcPageView)}}</div>
        </div>
      </el-col>
      <el-col :span="3" v-if="resData1.scalePageView != 0">
        <div>
          <div class="title-type">医学公式(PV)</div>
          <div class="num-detail">{{formatThousands(resData1.scalePageView)}}</div>
        </div>
      </el-col>
      <el-col :span="3" v-if="resData1.videoSinglePageView != 0">
        <div>
          <div class="title-type">精品课(PV)</div>
          <div class="num-detail">{{formatThousands(resData1.videoSinglePageView)}}</div>
        </div>
      </el-col>
      <el-col :span="3" v-if="resData1.coursePageView != 0">
        <div>
          <div class="title-type">公开课(PV)</div>
          <div class="num-detail">{{formatThousands(resData1.coursePageView)}}</div>
        </div>
      </el-col>
    </el-row>

    <el-row :gutter="20" style="display:flex;justify-content:center">
      <el-col :span="3" v-if="resData2.totalUniqueVisitor != 0">
        <div>
          <div class="title-type">总访问用户(UV)</div>
          <div class="num-detail">{{formatThousands(resData2.totalUniqueVisitor)}}</div>
        </div>
      </el-col>
      <el-col :span="3" v-if="resData2.articleUniqueVisitor != 0">
        <div>
          <div class="title-type">资讯(UV)</div>
          <div class="num-detail">{{formatThousands(resData2.articleUniqueVisitor)}}</div>
        </div>
      </el-col>
      <el-col :span="3" v-if="resData2.journalUniqueVisitor != 0">
        <div>
          <div class="title-type">期刊(UV)</div>
          <div class="num-detail">{{formatThousands(resData2.journalUniqueVisitor)}}</div>
        </div>
      </el-col>
      <el-col :span="3" v-if="resData2.guiderUniqueVisitor != 0">
        <div>
          <div class="title-type">指南(UV)</div>
          <div class="num-detail">{{formatThousands(resData2.guiderUniqueVisitor)}}</div>
        </div>
      </el-col>
      <el-col :span="3" v-if="resData2.nsfcUniqueVisitor != 0">
        <div>
          <div class="title-type">国自然(UV)</div>
          <div class="num-detail">{{formatThousands(resData2.nsfcUniqueVisitor)}}</div>
        </div>
      </el-col>
      <el-col :span="3" v-if="resData2.scaleUniqueVisitor != 0">
        <div>
          <div class="title-type">医学公式(UV)</div>
          <div class="num-detail">{{formatThousands(resData2.scaleUniqueVisitor)}}</div>
        </div>
      </el-col>
      <el-col :span="3" v-if="resData2.videoSingleUniqueVisitor != 0">
        <div>
          <div class="title-type">精品课(UV)</div>
          <div class="num-detail">{{formatThousands(resData2.videoSingleUniqueVisitor)}}</div>
        </div>
      </el-col>
      <el-col :span="3" v-if="resData2.courseUniqueVisitor != 0">
        <div>
          <div class="title-type">公开课(UV)</div>
          <div class="num-detail">{{formatThousands(resData2.courseUniqueVisitor)}}</div>
        </div>
      </el-col>
    </el-row>
    </div>
   
    <my-pv
      :echartsDataList="list1"
      v-if="list1 && list1.length"
      style="position: relative"
    >
      <el-button
        @click="downloadExcel1({ startTime: value1[0], endTime: value1[1] })"
        style="position: absolute; top: -4px; right: 80px"
        type="primary"
        icon="el-icon-download"
      ></el-button>
    </my-pv>
    <my-uv
      :echartsDataList="list2"
      v-if="list2 && list2.length"
      style="position: relative"
    >
      <el-button
        @click="downloadExcel2({ startTime: value1[0], endTime: value1[1] })"
        style="position: absolute; top: -4px; right: 80px"
        type="primary"
        icon="el-icon-download"
      ></el-button>
    </my-uv>
    <div style="display:flex;justify-content: flex-start;">
      <el-date-picker
        v-model="valueMAU"
        @change="handleChange"
        format="yyyy 年 MM 月"
        type="month"
        placeholder="选择月"
        :clearable="false"
        style="width:150px;margin-right:10px"
      >
      </el-date-picker>
      <div style="display: flex;align-items: center;">
        <span>
        MAU：{{mauNumber}}
      </span>
      </div>
    </div>
    <div class="date-picker" style="margin-top:20px">
      <el-date-picker
      style="margin-right:50px"
        unlink-panels
        v-model="value2"
        type="daterange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        value-format="yyyy-MM-dd"
      >
      </el-date-picker>
      <el-select v-model="value" placeholder="请选择" style="margin-right:50px;width:140px">
        <el-option
          v-for="item in options"
          :key="item.value"
          :label="item.label"
          :value="item.value">
        </el-option>
      </el-select>
      <el-select v-model="valuePU" placeholder="请选择" style="width:70px">
        <el-option
          v-for="item in options2"
          :key="item.value"
          :label="item.label"
          :value="item.value">
        </el-option>
      </el-select>
      <el-button type="primary" @click="hanleQuery2" style="margin-left: 5%"
        >查询</el-button
      >
    </div>
    
    <div style="margin-bottom:200px;display: flex;justify-content: center;" >
      <el-row :gutter="20" style="margin-bottom:23px;display:flex;justify-content:center">
        <el-table :data="respData" size="medium">
          <el-table-column
            type="index"
            align="center"
            label="序号"
            width="90">
          </el-table-column>
          <el-table-column
            class="title-column"
            prop="title"
            align="left"
            width="550"
            label="标题">
          </el-table-column>
          <el-table-column
            prop="type"
            align="center"
            width="100"
            label="所属板块">
          </el-table-column>
          <el-table-column
          align="center"
            prop="number"
            width="100"
            :label="behaviorType">
          </el-table-column>
        </el-table>
      </el-row>
    </div>
  </section>
</template>
<script>
import myPv from "./component/my-pv.vue";
import myUv from "./component/my-uv.vue";
import API from "@/api/module/dashboardNew";
export default {
  name: "ms-all-board",
  components: {
    myPv,
    myUv
  },
  data() {
    return {
      mauNumber:'',
      valueMAU:'2024-01',
      behaviorType:'PV',
      type:'期刊',
      respData:[],
      options: [{
          value: 'tool_impact_factor',
          label: '期刊'
        }, {
          value: 'nsfc',
          label: '国自然'
        }, {
          value: 'guider',
          label: '指南'
        }, {
          value: 'scale',
          label: '医学公式'
        },{
          value: 'article',
          label: '资讯'
        },{
          value: 'live_info',
          label: '直播'
        }],
      options2:[
        {
          value: 'pv',
          label: 'PV'
        }, {
          value: 'uv',
          label: 'UV'
        }
      ],
      value: 'tool_impact_factor',
      valuePU: 'pv',
      value1: [],
      value2: [],
      isShow:false,
      dashboardUrl: "",
      activeName: "user_new",
      resData1:{},
      resData2:{},
      list1: [],
      list2: [],
      data_1: [],
      data_2: [],
    };
  },
  async mounted() {
    this.value1 = [this.getDateString, this.yesterday];
    this.value2 = [this.getDateString, this.yesterday];
    await this.echarts1({ startTime: this.value1[0], endTime: this.value1[1] });
    this.isShow = true
    this.getPvTopData();
    await API.totalMonthUv({startTime:'2024-01'}).then((res) => {
        this.valueMAU = '2024-01'
        if(res.data){
          this.mauNumber = ''
          this.mauNumber = res.data.number
        }else{
          this.mauNumber = '暂无数据'
        }
      })
  },
  computed: {
    yesterday() {
      const date = new Date();
      date.setDate(date.getDate() - 1);
      const yyyy = date.getFullYear();
      let mm = date.getMonth() + 1;
      let dd = date.getDate();
      mm = mm.toString().padStart(2, "0");
      dd = dd.toString().padStart(2, "0");
      return yyyy + "-" + mm + "-" + dd;
    },
    getDateString() {
      const date = new Date();
      const year = date.getFullYear();
      return `${year}-01-01`;
    },
  },
  methods: {
    formatDate(date) {
      const year = date.getFullYear()
      const month = date.getMonth() + 1 
      return `${year}-${month < 10 ? '0' + month : month}` 
    },
    async handleChange(newVal){
      this.valueMAU = ''
      this.valueMAU =  this.formatDate(newVal)
      let params = {startTime: this.valueMAU}
      await API.totalMonthUv(params).then((res) => {
        // this.valueMAU = res.data.date
        if(res.data){
          this.mauNumber = ''
          this.mauNumber = res.data.number
        }else{
          this.mauNumber = ''
          this.mauNumber = '暂无数据'
        }
      })
    },
    // 处理下载文件
    downloadStream(url, filename) {
      const link = document.createElement('a')
      link.href = url
      link.download = filename
      link.style.display = 'none'
      document.body.append(link)
      link.click()
      link.remove()
    },
    downloadExcel1(params) {
      API.totalPageViewDownload(params).then((response) => {
        this.downloadStream(response.data.url, 'PV.xlsx');
      });
    },
    downloadExcel2(params) {
      API.totalUniqueVisitorDownload(params).then((response) => {
        this.downloadStream(response.data.url, 'UV.xlsx');
      });
    },
    // 初始化
    init(time) {
      this.echarts1(time);
    },
    // 图表
    async echarts1(timeValue) {
      this.clearData();
      await Promise.allSettled([
        API.totalPageView(timeValue),
        API.totalUniqueVisitor(timeValue),
      ]).then((res) => {
          if (
            res[0].status == "fulfilled" &&
            res[0].value.data.totalPageViewPerDayList.length != 0
          ) {
            this.resData1 = res[0].value.data
          }
          if (
            res[1].status == "fulfilled" &&
            res[1].value.data.totalUniqueVisitorPerDayList.length != 0
          ) {
            this.resData2 = res[1].value.data
          }

        // 第一张图
        this.resData1 = res[0].value.data
        this.data_1 = res[0].value.data.totalPageViewPerDayList;
        const totalPageView = res[0].value.data.totalPageView
        const date1Arr = [];
        const value1Arr = [];
        const module1Arr = [];
        this.data_1.forEach((item) => {
          date1Arr.push(item.date);
          value1Arr.push(item.number);
          module1Arr.push(item.module);
        });
        this.list1 = []
        this.list1.push(date1Arr, value1Arr, totalPageView, module1Arr);
        
        // 第二张图
        this.resData2 = res[1].value.data
        this.data_2 = res[1].value.data.totalUniqueVisitorPerDayList;
        const totalUniqueVisitor = res[1].value.data.totalUniqueVisitor
        const date2Arr = [];
        const value2Arr = [];
        const module2Arr = [];
        this.data_2.forEach((item) => {
          date2Arr.push(item.date);
          value2Arr.push(item.number);
          module2Arr.push(item.module);
        });
        this.list2 = []
        this.list2.push(date2Arr, value2Arr, totalUniqueVisitor, module2Arr);
      });
    },
    hanleQuery() {
      if(this.value1 && this.value1[0] && this.value1[1]){
        this.init({ startTime: this.value1[0], endTime: this.value1[1] });
      }else{
        this.$message.warning('请输入起始日期')
      }
    },
    hanleQuery2() {
      this.options.forEach(item=>{
        if(item.value == this.value){
          this.type = item.label
        }
      })
      if(this.value2 && this.value2[0] && this.value2[1]){
        if(this.valuePU == 'pv'){
          this.getPvTopData()
        }
        if(this.valuePU == 'uv'){
          this.getUvTopData()
        }
      }else{
        this.$message.warning('请输入起始日期')
      }
    },
    async getPvTopData(){
      let params = {
          startTime: this.value2[0], 
          endTime: this.value2[1],
          module:this.value,
          behaviorType:'pv'
        }
      await API.totalPvLeaderboard(params).then((res) => {
        this.behaviorType = 'PV'
        this.respData = res.data
        this.respData.forEach(item=>{
          item.module = this.value
          item.behaviorType = this.valuePU
          item.type = this.type
        })
      })
    },
    async getUvTopData(){
      let params = {
          startTime: this.value2[0], 
          endTime: this.value2[1],
          module:this.value,
          behaviorType:'uv'
        }
      await API.totalUvLeaderboard(params).then((res) => {
        this.behaviorType = 'UV'
        this.respData = res.data
        this.respData.forEach(item=>{
          item.module = this.value
          item.behaviorType = this.valuePU
          item.type = this.type
        })
      })
    },
    clearData() {
      this.data_1 = [];
      this.data_2 = [];
    },
    formatThousands(num) {
      if(num && num != 0){
        return num.toString().replace(/(\d)(?=(\d{3})+$)/g, "$1,");
      }else{
        return num
      }
    },
  },
};
</script>

<style scoped>
.num-detail{
  font-size: 20px;
}
#chart {
  margin: 0 auto;
}

.date-picker {
  display: flex;
  justify-content: flex-start;
  margin: 0 20px 45px 0;
}
.titlePart{
  font-size: 16px;
}
</style>
