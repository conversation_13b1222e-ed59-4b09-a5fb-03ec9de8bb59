<template>
  <section>
    <div class="search">
      <el-select v-model="moduleValue" placeholder="请选择模块" style="width: 20%; margin-right: 20px">
        <el-option
          v-for="item in options"
          :key="item.value"
          clearable
          :label="item.label"
          :value="item.value">
        </el-option>
      </el-select>
      <el-date-picker
        style="margin-right: 20px;"
        v-model="pyear"
        type="year"
        format="yyyy" 
        value-format="yyyy"
        placeholder="选择年">
      </el-date-picker>
      <el-date-picker
        v-model="pmonth"
        type="month"
        format="M"
        value-format="M"
        placeholder="选择月">
      </el-date-picker>
      <el-input clearable v-model="username" placeholder="请输入用户" style="width: 20%;margin-left: 20px;"></el-input>
      <el-button type="primary" @click="hanleQuery" style="margin-left: 5%"
        >查询</el-button
      >
      <div style="margin-bottom:200px;width: 100%;" >
      <el-row style="margin-bottom:23px;">
        <el-table :data="respData" size="medium">
          <el-table-column
            prop="username"
            align="center"
            label="姓名">
          </el-table-column>
          <el-table-column
            prop="num"
            align="center"
            label="篇数">
          </el-table-column>
          <el-table-column
            align="center"
            prop="module"
            label="模块">
          </el-table-column>
          <el-table-column
            align="center"
            prop="appHits"
            label="app点击数">
          </el-table-column>
          <el-table-column
            align="center"
            prop="pcHits"
            label="pc点击数">
          </el-table-column>
          <el-table-column
            align="center"
            prop="pyearPmonth"
            label="年月">
          </el-table-column>
        </el-table>
      </el-row>
    </div>
    </div>
  </section>
</template>
<script>
import API from "@/api/module/dashboardNew";
export default{
  data(){
    return{
      moduleValue:'article',
      pyear:'2025',
      pmonth:'1',
      username:'',
      respData:[],
      options:[
        {
          value: 'article',
          label: '资讯'
        },
        {
          value: 'guider',
          label: '指南'
        }
      ]
    }
  },
  async mounted() {
    this.hanleQuery()
  },
  methods:{
    hanleQuery(){
      let params = {
        "module": this.moduleValue,
        "pyear": this.pyear,
        "pmonth": this.pmonth,
        "username": this.username
      }
      API.partTimeReport(params).then((res) => {
        if(res.status == 200){
          this.respData = res.data
        }else{
          this.respData = []
        }
      })
  }
}
}
</script>
<style scoped>
.search{
  text-align: left!important;
}
</style>