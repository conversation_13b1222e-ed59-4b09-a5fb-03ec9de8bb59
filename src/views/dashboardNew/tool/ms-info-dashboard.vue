<template>
  <section>
        <iframe :src="dashboardUrl" class="iframe-style"  frameborder=0></iframe>
  </section>
</template>
<script>
// import 'vizjs'
export default {
    name: "ms-info-dashboard",
    data () {
        return {
            dashboardUrl: ''
        }
    },
    mounted() {
        let  ajax = new XMLHttpRequest()
        let that = this
        ajax.open("get", "https://bi-tableau.medsci.cn/get_tickit?server_ip=tableau.medsci.cn&report_name=medsci_dashboard_online/medsci_dashboard_online&system_flag=online")

        ajax.send()

        ajax.onreadystatechange = function(){

            if (ajax.readyState === 4 && ajax.status === 200) {
                let tmp = JSON.parse(ajax.response)
                that.dashboardUrl = tmp[0].url
            }
        }
    }
}
</script>

<style scoped>
    .iframe-style {
        width: 100%;
        height: calc(100vh - 180px); 
    }
</style>
