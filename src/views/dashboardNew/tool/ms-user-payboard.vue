<template>
  <section>
    <div class="date-picker">
      <el-date-picker
        unlink-panels
        v-model="value1"
        type="daterange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        value-format="yyyy-MM-dd"
      >
      </el-date-picker>
      <el-button type="primary" @click="hanleQuery" style="margin-left: 5%"
        >查询</el-button
      >
    </div>
    <my-line-orders
      :echartsDataList="list1"
      v-if="list1 && list1.length"
    ></my-line-orders>
    <my-line-transaction
      :echartsDataList="list2"
      v-if="list2 && list2.length"
    ></my-line-transaction>
    <my-line-paynum
      :echartsDataList="list3"
      v-if="list3 && list3.length"
    ></my-line-paynum>
    <div style="display: flex; justify-content: center">
      <my-channel-pay
        v-if="list4 && list4.length"
        :echartsDataList="list4"
      ></my-channel-pay>
    </div>
  </section>
</template>
<script>
import myLineOrders from "./component/my-line-orders.vue";
import myLinePaynum from "./component/my-line-paynum.vue";
import myLineTransaction from "./component/my-line-transaction.vue";
import myChannelPay from "./component/my-channel-pay.vue";
import API from "@/api/module/dashboardNew";
export default {
  name: "ms-user-payboard",
  components: {
    myLineTransaction,
    myLinePaynum,
    myLineOrders,
    myChannelPay,
  },

  data() {
    return {
      value1: [],
      dashboardUrl: "",
      activeName: "user_new",
      resData:{},
      resData2:{},
      list1: [],
      list2: [],
      list3: [],
      list4: [],

      data_1: [],
      data_2: [],
      data_3: [],
      data_4: {}
    };
  },
  async mounted() {
    this.value1 = [this.getDateString, this.yesterday];
    await this.echarts1({ startTime: this.value1[0], endTime: this.value1[1] });
  },
  // async mounted(){
  //     await this.echarts1({ startTime: this.value1[0], endTime: this.value1[1] });
  //     // this.echarts1({ startTime: this.value1[0], endTime: this.value1[1]});
  // },
  computed: {
    yesterday() {
      const date = new Date();
      date.setDate(date.getDate() - 1);
      const yyyy = date.getFullYear();
      let mm = date.getMonth() + 1;
      let dd = date.getDate();
      mm = mm.toString().padStart(2, "0");
      dd = dd.toString().padStart(2, "0");
      return yyyy + "-" + mm + "-" + dd;
    },
    getDateString() {
      const date = new Date();
      const year = date.getFullYear();
      return `${year}-01-01`;
    },
  },
  methods: {
    // 初始化
    init(time) {
      this.echarts1(time);
    },
    // 图表
    async echarts1(timeValue) {
      this.clearData();
      await Promise.allSettled([
        API.totalOrderFind(timeValue),
        API.totalOrderProportionFind(timeValue),
      ]).then((res) => {
          if (
            res[0].status == "fulfilled" &&
            res[0].value.data.totalOrderNumberPerDays.length != 0 &&
            res[0].value.data.totalOrderPayAmountPerDays.length != 0 &&
            res[0].value.data.totalOrderPayNumberPerDays.length != 0
          ) {
            this.data_1 = res[0].value.data.totalOrderNumberPerDays;
            this.data_2 = res[0].value.data.totalOrderPayAmountPerDays;
            this.data_3 = res[0].value.data.totalOrderPayNumberPerDays;
          }
          if (
            res[1].status == "fulfilled" &&
            res[1].value.data.proportionDetail
          ) {
            this.data_4 = res[1].value.data.proportionDetail;
          }
        // 第一张图
        const totalOrderNumber = res[0].value.data.totalOrderNumber
        // this.data_1 = this.resData.totalOrderNumberPerDays
        // const totalOrderNumber = this.resData.totalOrderNumber
        const date1Arr = [];
        const value1Arr = [];
        this.data_1.forEach((item) => {
          date1Arr.push(item.date);
          value1Arr.push(item.orderNumber);
        });
        this.list1 = []
        this.list1.push(date1Arr, value1Arr, totalOrderNumber);
        // 第二张图
        const totalOrderPayAmount = res[0].value.data.totalOrderPayAmount
        // this.data_2 = this.resData.totalOrderPayAmountPerDays
        // const totalOrderPayAmount = this.resData.totalOrderPayAmount
        const date2Arr = [];
        const value2Arr = [];
        this.data_2.forEach((item) => {
          date2Arr.push(item.date);
          value2Arr.push(item.payAmount);
        });
        this.list2 = []
        this.list2.push(date2Arr, value2Arr, totalOrderPayAmount);

        // 第三张图
        // this.data_3 = this.resData.totalOrderPayNumberPerDays
        const totalOrderPayNumber = res[0].value.data.totalOrderPayNumber
        // const totalOrderPayNumber = this.resData.totalOrderPayNumber
        const date3Arr = [];
        const value3Arr = [];
        this.data_3.forEach((item) => {
          date3Arr.push(item.date);
          value3Arr.push(item.payNumber);
        });
        this.list3 = []
        this.list3.push(date3Arr, value3Arr, totalOrderPayNumber);


        //第四张图给子组件的数据
        // this.data_4 = this.resData2.proportionDetail
        this.list4 = Object.values(this.data_4);
      });
    },
    hanleQuery() {
      // console.log(this.value1,'value1');
      if(this.value1 && this.value1[0] && this.value1[1]){
        this.init({ startTime: this.value1[0], endTime: this.value1[1] });
      }else{
        this.$message.warning('请输入起始日期')
      }
    },
    clearData() {
      this.data_1 = [];
      this.data_2 = [];
      this.data_3 = [];
      this.data_4 = [];
    },
    formatThousands(num) {
      return num.toString().replace(/(\d)(?=(\d{3})+$)/g, "$1,");
    },
  },
};
</script>

<style scoped>
#chart {
  margin: 0 auto;
}

.date-picker {
  display: flex;
  justify-content: flex-start;
  margin: 0 20px 45px 0;
}
</style>
