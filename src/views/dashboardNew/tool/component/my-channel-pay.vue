<template>
  <div class="chart-container">
    <div ref="chart1" class="chart1"></div>
  </div>
</template>

<script>
import * as echarts from "echarts";

export default {
  props: ["echartsDataList"],
  mounted() {
    // this.$nextTick(()=>{
      this.renderChart();
    // })
  },
  watch: {
      echartsDataList: {
          handler() {
              this.renderChart()
          },
          deep: true
      }
  },
  methods: {
    renderChart() {
      const chart = echarts.init(this.$refs.chart1);
      const channels = [
        ...new Set(this.echartsDataList.map((item) => item.propertyName)),
      ];
      const provinceData = channels.map((channel) => {
        const totalNumber = this.echartsDataList
          .filter((item) => item.propertyName === channel)
          .reduce((acc, item) => acc + item.number, 0);
        const proportion = this.echartsDataList
          .filter((item) => item.propertyName === channel)
          .reduce((acc, item) => item.proportion, 0);
        const payAmount = this.echartsDataList
          .filter((item) => item.propertyName === channel)
          .reduce((acc, item) => item.payAmount, 0);
        return {
          name: channel,
          totalNumber: totalNumber,
          value: proportion,
          payAmount:payAmount
        };
      });
      chart.clear(); 
      const option = {
        title: {
          text: "成交占比",
        },
        // toolbox: {
        //             feature: {
        //                 // dataView: { show: true, readOnly: false },
        //                 // magicType: { show: true, type: ['line', 'bar'] },
        //                 // restore: { show: true },
        //                 saveAsImage: { show: true }
        //             }
        //         },
        tooltip: {
          trigger: "item",
          formatter: (res) => {
            return `<div style="padding:10px 30px">
            <span style='width:250px;display:inline-block;text-align:left'>成交类型：${res.data.name}</span><br/>
            <span style='width:250px;display:inline-block;text-align:left'>成交数量：${res.data.totalNumber}</span><br/>
            <span style='width:250px;display:inline-block;text-align:left'>成交数量占比：${res.data.value}%</span><br/>
            <span style='width:250px;display:inline-block;text-align:left'>成交金额：${res.data.payAmount}</span>
            </div>`;
          },
        },
        legend: {
          orient: "vertical",
          left: "right",
        },
        label: {
          show: true,
          formatter: (res) => {
            return res.data.value + "%";
          }, // 显示数值和百分比
        },
        series: [
          {
            name: "注册来源",
            type: "pie",
            radius: "55%",
            center: ["50%", "60%"],
            data: provinceData,
            emphasis: {
              label: {
                show: true,
                fontSize: "14",
                fontWeight: "bold",
              },
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: "rgba(0, 0, 0, 0.5)",
              },
            },
          },
        ],
      };

      chart.setOption(option);
    },
  },
  computed: {
    totalCount() {
      return this.echartsDataList.reduce((acc, item) => acc + item.count, 0);
    },
  },
};
</script>

<style lang="scss" scoped>
.chart-container {
  width: 500px;
  height: 400px;
  h2 {
    text-align: left;
  }
}
.chart1 {
  width: 500px;
  height: 400px;

  /* 调整y轴滚动条宽度 */
  .echarts-slider-horizontal {
    width: 10px; /* 设置为较小的宽度 */
  }
}
</style>