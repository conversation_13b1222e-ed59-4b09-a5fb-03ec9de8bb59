<template>
    <div>
        <!-- <div class="desc-title">
            <div>每日注册用户总数</div>
            <div>累计注册用户量:328,216</div>
        </div> -->

        <div ref="chart" class="echarts"></div>
    </div>
</template>
<script>
import * as echarts from 'echarts'
export default {
    data() {
        return {

        }
    },
    props: ['echartsDataList'],
    watch: {
        echartsDataList: {
            handler() {
                this.init()
            },
            deep: true,
        }
    },
    mounted() {
        this.init()
    },
    methods: {
        formatThousands(num) {
            return num.toString().replace(/(\d)(?=(\d{3})+$)/g, '$1,');
        },
        init() {
            let newValue = this.echartsDataList
            //     console.log('newValue', newValue)
            //     const data = [["2000-06-05", 116], ["2000-06-06", 129], ["2000-06-07", 135], ["2000-06-08", 86], ["2000-06-09", 73], ["2000-06-10", 85], ["2000-06-11", 73], ["2000-06-12", 68], ["2000-06-13", 92], ["2000-06-14", 130], ["2000-06-15", 245], ["2000-06-16", 139], ["2000-06-17", 115], ["2000-06-18", 111], ["2000-06-19", 309], ["2000-06-20", 206], ["2000-06-21", 137], ["2000-06-22", 128], ["2000-06-23", 85], ["2000-06-24", 94], ["2000-06-25", 71], ["2000-06-26", 106], ["2000-06-27", 84], ["2000-06-28", 93], ["2000-06-29", 85], ["2000-06-30", 73], ["2000-07-01", 83], ["2000-07-02", 125], ["2000-07-03", 107], ["2000-07-04", 82], ["2000-07-05", 44], ["2000-07-06", 72], ["2000-07-07", 106], ["2000-07-08", 107], ["2000-07-09", 66], ["2000-07-10", 91], ["2000-07-11", 92], ["2000-07-12", 113], ["2000-07-13", 107], ["2000-07-14", 131], ["2000-07-15", 111], ["2000-07-16", 64], ["2000-07-17", 69], ["2000-07-18", 88], ["2000-07-19", 77], ["2000-07-20", 83], ["2000-07-21", 111], ["2000-07-22", 57], ["2000-07-23", 55], ["2000-07-24", 60]];
            // const dateList = data.map(function (item) {
            //     return item[0];
            // });
            // const valueList = data.map(function (item) {
            //     return item[1];
            // });

            // 获取容器元素
            // const container = this.$el.querySelector('#chart')
            const container = this.$refs.chart;
            // 初始化echarts实例
            const chart = echarts.init(container)
            chart.clear();
            // 折线图的数据
            const option = {
                visualMap: [
                    {
                        show: false,
                        type: 'continuous',
                        seriesIndex: 0,
                        min: 0,
                        max: 400
                    },
                    {
                        show: false,
                        type: 'continuous',
                        seriesIndex: 1,
                        dimension: 0,
                        min: 0,
                        max: newValue[0].length - 1
                    }
                ],
                // title: {
                //     text: `每日注册用户总数                                                                                        (累计注册用户量:${this.formatThousands(newValue[2])})`
                // },
                title: [{
                    text: "成交金额",
                    left: 0,
                    textStyle: {
                        fontSize: 18,
                    },

                }, {
                    text: `成交金额:${this.formatThousands(newValue[2])}`,
                    left: 700,
                    textStyle: {
                        fontSize: 14
                    }
                }],

                // toolbox: {
                //     feature: {
                //         // dataView: { show: true, readOnly: false },
                //         // magicType: { show: true, type: ['line', 'bar'] },
                //         // restore: { show: true },
                //         saveAsImage: { show: true }
                //     }
                // },
                tooltip: {
                    trigger: 'axis',
                    formatter: (params) => {
                        let date = new Date(params[0].axisValue.substring(0, 4), params[0].axisValue.substring(4, 6) - 1, params[0].axisValue.substring(6));
                        // let date = new Date(params[0].axisValue);
                        let dateStr = `${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日`;
                        // console.log(params);
                        return `
                    <div 
                       style='display: flex;
                       align-items: flex-start;
                       flex-direction: column;'>
                        <div>日期:${dateStr}</div>
                        <div>成交金额:${this.formatThousands(params[0].value)}</div>
                    </div>
                        `
                    }
                },
                xAxis: {
                    // data: dataValue.xAxis
                    data: newValue[0],
                    axisLabel: {
                        formatter: function (value) {
                            // value为原始日期字符串"20230101"  
                            let date = new Date(value.substring(0, 4), value.substring(4, 6) - 1, value.substring(6));
                            let formatDate = `${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日`;
                            return formatDate;
                        }
                    }
                },
                yAxis: {
                    // type:'value',
                    // name: '成交金额',
                    //     axisLabel: {
                    //     formatter: "{value}k",
                    //  },
                    // axisLabel: {
                    //     formatter: function (value) {
                    //         if (value >= 1000) {
                    //             value = (value / 1000).toFixed(0) + 'k'
                    //         }
                    //         return value
                    //     }
                    // }
                },
                series: [
                    {
                        name: '成交金额',
                        type: 'line',
                        // data: dataValue.series
                        data: newValue[1],
                        showSymbol: false,
                        lineStyle: {
                            color: '#4e79a7', // 设置线的颜色为红色
                            width: 3
                        }
                    }
                ]
            }
            // 渲染图表
            chart.setOption(option)
        }
    },
    created() {
    }
}
</script>
<style lang="scss" scoped>
.echarts {
    width: 1000px;
    height: 400px;
    margin: 0 auto;
}

.desc-title {
    width: 500px;
    display: flex;
    justify-content: space-between;
    margin: 0 auto;
}
</style>