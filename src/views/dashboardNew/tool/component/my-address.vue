<template>
  <div class="chart-container">
    <div ref="chart" class="chart"></div>
  </div>
</template>

<script>
import * as echarts from "echarts";

export default {
  props: ["echartsDataList"],
  mounted() {
    // this.$nextTick(()=>{
    this.renderChart();
    // })
  },
  watch: {
    echartsDataList: {
      handler() {
        this.renderChart()
      },
      deep: true,
    }
  },
  methods: {
    renderChart() {
      // console.log(this.echartsDataList,'list');
      const chart = echarts.init(this.$refs.chart);

      const provinces = [
        ...new Set(this.echartsDataList.map((item) => item.provincial)),
      ];

      const provinceData = provinces.map((provincial) => {
        const totalNumber = this.echartsDataList
          .filter((item) => item.provincial === provincial)
          .reduce((acc, item) => acc + item.totalNumber, 0);
        const proportion = this.echartsDataList
          .filter((item) => item.provincial === provincial)
          .reduce((acc, item) => item.proportion, 0);
        return {
          provincial: provincial,
          totalNumber: totalNumber,
          proportion: proportion,
        };
      });
      chart.clear();

      const option = {
        title: {
          text: "注册用户省市层级分布",
        },
        // toolbox: {
        //             feature: {
        //                 // dataView: { show: true, readOnly: false },
        //                 // magicType: { show: true, type: ['line', 'bar'] },
        //                 // restore: { show: true },
        //                 saveAsImage: { show: true }
        //             }
        //         },
        tooltip: {
          trigger: "axis",
          formatter: function (params) {
            const dataIndex = params[0].dataIndex;
            const data = provinceData[dataIndex];
            return `<div style="padding:10px 30px">
            <span style='width:80px;display:inline-block;text-align:left'>省份：${data.provincial}</span><br/>
            <span style='width:80px;display:inline-block;text-align:left'>占比：${data.proportion}%</span><br/>
            <span style='width:80px;display:inline-block;text-align:left'>注册人数：${data.totalNumber}</span>
            </div>`;
          },
        },
        dataZoom: [
          {
            type: "slider",
            yAxisIndex: 0,
            start: 100,
            end: 130,
            width: 5,
            brushSelect: false,
            showDetail: false,
          },
          {
            // 没有下面这块的话，只能拖动滚动条，
            // 鼠标滚轮在区域内不能控制外部滚动条
            type: "inside",
            yAxisIndex: [0, 1],
            zoomOnMouseWheel: false,
            moveOnMouseMove: true,
            moveOnMouseWheel: true,
          },
        ],
        grid: {
          left: "3%",
          right: "12%",
          bottom: "15%",
          containLabel: true,
        },
        xAxis: {
          type: "value",
          name: "注册人数",
          nameLocation: "middle",
          nameGap: 30,
          max: this.totalCount,
          // axisLabel: {
          //   formatter: "{value}k",
          // },
        },
        yAxis: {
          type: "category",
          data: provinces,
          name: "省份",
          axisTick: {
            show: false,
          },
          axisLabel: {
            interval: 0,
          },
        },

        series: [
          {
            type: "bar",
            data: provinceData.map((item) => ({
              value: item.totalNumber,
              provincial: item.provincial,
              proportion: item.proportion,
            })),
            itemStyle: {
              color: '#4e79a7',
            },
            label: {
              show: true,
              position: "right",
              formatter: function (params) {
                const { value, proportion } = params.data;
                return `${value} | ${proportion}%`;
              },
            },
          },
        ],
      };

      chart.setOption(option);
    },
  },
  computed: {
    totalCount() {
      return this.echartsDataList.reduce((acc, item) => acc + item.totalNumber, 0);
    },
  },
};
</script>

<style lang="scss" scoped>
.chart-container {
  width: 1000px;
  height: 400px;
  margin: 0 auto;

  & h2 {
    text-align: left;
  }
}

.chart {
  width: 1000px;
  height: 400px;

  .echarts-slider-horizontal {
    width: 10px;
  }
}
</style>