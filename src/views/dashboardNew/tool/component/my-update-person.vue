<template>
    <div style="position: relative;width:1000px;margin:0 auto;">
        <div ref="chart" class="echarts"></div>
        <slot></slot>
    </div>
</template>
<script>
import * as echarts from 'echarts'
export default {
    data() {
        return {

        }
    },
    props: ['echartsDataList'],
    watch: {
        echartsDataList: {
            handler() {
                this.init()
            },
            deep: true,
        }
    },
    mounted() {
        this.init()
    },

    methods: {
        formatThousands(num) {
            if(this.echartsDataList[5]){
                return num.toString().replace(/(\d)(?=(\d{3})+$)/g, '$1,');
            }else{
                return 0
            }
        },
        init() {
            let newValue = this.echartsDataList
            // console.log(newValue, '======');
            // 获取容器元素
            const container = this.$refs.chart;
            // 初始化echarts实例
            const chart = echarts.init(container)
            chart.clear();
            // 折线图的数据
            const option = {
                // title: {
                //     text: '每日更新个人信息人数'
                // },
                // toolbox: {
                //     feature: {
                //         saveAsImage: {}
                //     }
                // },
                title: [{
                    text: "每日更新个人信息人数",
                    left: 0,
                    textStyle: {
                        fontSize: 18,
                    },

                }, {
                    text: `更新总数: ${this.formatThousands(newValue[6])}               ${this.formatThousands(newValue[5])}               ${this.formatThousands(newValue[7])} `,
                    left: 323,
                    top: 20,
                    textStyle: {
                        fontSize: 12
                    }
                }],
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'cross',
                        crossStyle: {
                            color: '#999'
                        },
                        label: {
                            formatter: function (params) {
                                if (params.seriesData.length === 0) {
                                    window.mouseCurValue = params.value;
                                }
                            }
                        }
                    },

                    formatter: (params) => {
                        let date = new Date(params[0].axisValue.substring(0, 4), params[0].axisValue.substring(4, 6) - 1, params[0].axisValue.substring(6));
                        // let date = new Date(params[0].axisValue);
                        let dateStr = `${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日`;
                        // console.log(params, 'newValue');
                        let res = "", sum = 0;
                        let index;
                        // 找到第一个对象的axisValue
                        let axisValue = params[0].axisValue;
                        // 循环newValue数组
                        for (let i = 0; i < newValue[4].length; i++) {
                            // 如果对象的date值等于axisValue
                            if (newValue[4][i].newUser.date === axisValue) {
                                // 记录索引
                                index = i;
                                break;
                            }
                        }
                        // index就是新值数组对象所在位置的索引
                        // console.log(index, 'index');
                        for (let i = 0; i < params.length; i++) {
                            let series = params[i];
                            sum += Number(series.data);
                            if (sum >= window.mouseCurValue) {
                                // res = series.axisValue + "<br/>" + series.seriesName + "<br/>";
                                if (series.componentIndex == 0) {
                                    res = `
                                    <div 
                                    style='display: flex;
                                    align-items: flex-start;
                                    flex-direction: column;'>
                                        <div>用户类型:${series.seriesName}</div>
                                        <div>日期:${dateStr}</div>
                                        <div>总人数:${this.formatThousands(series.value)}</div>
                                        <div>网站更新人数:${this.formatThousands(newValue[4][index].newUser.webNumber)}</div>
                                        <div>APP更新人数:${this.formatThousands(newValue[4][index].newUser.appNumber)}</div>
                                        <div>其他平台更新人数:${this.formatThousands(newValue[4][index].newUser.otherNumber)}</div>
                                    </div>
                                        `;
                                    // webNUm = newValue[4][index].newUser.webNumber
                                } else if (series.componentIndex == 1) {
                                    res = `
                                    <div 
                                    style='display: flex;
                                    align-items: flex-start;
                                    flex-direction: column;'>
                                        <div>用户类型:${series.seriesName}</div>
                                        <div>日期:${dateStr}</div>
                                        <div>总人数:${this.formatThousands(series.value)}</div>
                                        <div>网站更新人数:${this.formatThousands(newValue[4][index].oldUser.webNumber)}</div>
                                        <div>APP更新人数:${this.formatThousands(newValue[4][index].oldUser.appNumber)}</div>
                                        <div>其他平台更新人数:${this.formatThousands(newValue[4][index].oldUser.otherNumber)}</div>
                                    </div>
                                        `;
                                } else if (series.componentIndex == 2) {
                                    res = `
                                    <div 
                                    style='display: flex;
                                    align-items: flex-start;
                                    flex-direction: column;'>
                                        <div>用户类型:${series.seriesName}</div>
                                        <div>日期:${dateStr}</div>
                                        <div>总人数:${this.formatThousands(series.value)}</div>
                                        <div>网站更新人数:${this.formatThousands(newValue[4][index].totalUser.webNumber)}</div>
                                        <div>APP更新人数:${this.formatThousands(newValue[4][index].totalUser.appNumber)}</div>
                                        <div>其他平台更新人数:${this.formatThousands(newValue[4][index].totalUser.otherNumber)}</div>
                                    </div>
                                        `;
                                }
                                break;
                            }
                        }
                        return res;
                    },
                },
                // toolbox: {
                //     feature: {
                //         // dataView: { show: true, readOnly: false },
                //         // magicType: { show: true, type: ['line', 'bar'] },
                //         // restore: { show: true },
                //         saveAsImage: { show: true }
                //     }
                // },

                legend: {
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: [
                    {
                        type: 'category',
                        data: newValue[0],
                        axisLabel: {
                            formatter: function (value) {
                                // value为原始日期字符串"20230101"  
                                let date = new Date(value.substring(0, 4), value.substring(4, 6) - 1, value.substring(6));
                                let formatDate = `${date.getMonth() + 1}月${date.getDate()}日`;
                                return formatDate;
                            }
                        }
                        // data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun','Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun','Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun','Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
                    }
                ],
                yAxis: [
                    {
                        type: 'value',
                        name: '总人数',
                    }
                ],
                series: [
                    {
                        name: `新用户`,
                        type: 'bar',
                        stack: 'Ad',
                        emphasis: {
                            focus: 'series'
                        },
                        data: newValue[1],
                        itemStyle: {
                            color: '#598baa',
                        },
                    },
                    {
                        name: `老用户`,
                        type: 'bar',
                        stack: 'Ad',
                        emphasis: {
                            focus: 'series'
                        },
                        data: newValue[2],
                        itemStyle: {
                            color: '#97cdd4',
                        }
                    },
                    {
                        name: '更新总数',
                        legendHoverLink: true,
                        type: 'line',
                        emphasis: {
                            focus: 'series'
                        },
                        showSymbol: false,
                        data: newValue[3],
                        lineStyle: {
                            color: '##a0a0a0',
                            width: 3
                        }
                    },


                ]
            }
            // 渲染图表
            chart.setOption(option)
        }
    },
    created() {
    }
}
</script>
<style lang="scss" scoped>
.echarts {
    width: 1000px;
    height: 400px;
    margin: 0 auto;
}

.desc-title {
    width: 500px;
    display: flex;
    justify-content: space-between;
    margin: 0 auto;
}
</style>