
<template>
  <div class="chart-container">
    <div ref="chart" class="chart"></div>
  </div>
</template>

<script>
import * as echarts from "echarts";

export default {
  props: ["echartsDataList"],
  mounted() {
    // this.$nextTick(()=>{
    this.renderChart();
    // })
  },
  watch: {
    echartsDataList: {
      handler() {
        this.renderChart()
      },
      deep: true
    }
  },
  methods: {
    renderChart() {
      const chart = echarts.init(this.$refs.chart);

      const provinces = [
        ...new Set(this.echartsDataList.map((item) => item.hospitalLevel)),
      ];

      const provinceData = provinces.map((hospitalLevel) => {
        const totalNumber = this.echartsDataList
          .filter((item) => item.hospitalLevel === hospitalLevel)
          .reduce((acc, item) => acc + item.totalNumber, 0);
        const proportion = this.echartsDataList
          .filter((item) => item.hospitalLevel === hospitalLevel)
          .reduce((acc, item) => item.proportion, 0);
        return {
          hospitalLevel: hospitalLevel,
          totalNumber: totalNumber,
          proportion: proportion,
        };
      });
      chart.clear();
      const option = {
        title: {
          text: "注册用户医院等级分布",
        },
        // toolbox: {
        //             feature: {
        //                 // dataView: { show: true, readOnly: false },
        //                 // magicType: { show: true, type: ['line', 'bar'] },
        //                 // restore: { show: true },
        //                 saveAsImage: { show: true }
        //             }
        //         },
        tooltip: {
          trigger: "axis",
          formatter: function (params) {
            const dataIndex = params[0].dataIndex;
            const data = provinceData[dataIndex];
            return `<div style="padding:10px 30px">
            <span style='width:80px;display:inline-block;text-align:left'>医院等级：${data.hospitalLevel}</span><br/>
            <span style='width:80px;display:inline-block;text-align:left'>占比：${data.proportion}%</span><br/>
            <span style='width:80px;display:inline-block;text-align:left'>注册人数：${data.totalNumber}</span>
            </div>`;
          },
        },
        dataZoom: [
          {
            type: "slider",
            yAxisIndex: 0,
            start: 100,
            end: 200,
            width: 5,
            brushSelect: false,
            showDetail: false,
          },
          {
            // 没有下面这块的话，只能拖动滚动条，
            // 鼠标滚轮在区域内不能控制外部滚动条
            type: "inside",
            yAxisIndex: [0, 1],
            zoomOnMouseWheel: false,
            moveOnMouseMove: true,
            moveOnMouseWheel: true,
          },
        ],
        grid: {
          left: "3%",
          right: "12%",
          bottom: "15%",
          containLabel: true,
        },
        xAxis: {
          type: "value",
          name: "注册人数",
          nameLocation: "middle",
          nameGap: 30,
          max: this.totalCount,
          // axisLabel: {
          //   formatter: "{value}k",
          // },
        },
        yAxis: {
          type: "category",
          data: provinces,
          name: "医院等级",
          axisTick: {
            show: false,
          },
          axisLabel: {
            interval: 0,
          },
        },

        series: [
          {
            type: "bar",
            data: provinceData.map((item) => ({
              value: item.totalNumber,
              hospitalLevel: item.hospitalLevel,
              proportion: item.proportion,
            })),
            itemStyle: {
              color: '#4e79a7',
            },
            label: {
              show: true,
              position: "right",
              formatter: function (params) {
                const { value, proportion } = params.data;
                return `${value} | ${proportion}%`;
              },
            },
          },
        ],
      };

      chart.setOption(option);
    },
    totalSum() {
      return this.echartsDataList.reduce((acc, item) => {
        return acc + item.totalNumber
      }, 0)
    },
  },
  computed: {
    totalCount() {
      // return this.incrementSum() 
      // return this.echartsDataList.reduce((acc, item) => acc + item.totalNumber, 0);
      // let sum = this.echartsDataList.reduce((acc, item) => acc + item.totalNumber, 0);
      // let maxDigit = Math.max(...String(sum).split('').map(Number));
      // let result = (maxDigit + 1) * 10 ** (String(sum).length - 1);
      // return result;
      let totalSum = this.totalSum();
      let numStr = totalSum.toString();
      let digit = numStr.length;
      let highestNum = numStr[0];
      let incremented = Number(highestNum) + 2;
      let result = '';
      for (let i = 0; i < digit; i++) {
        if (i == 0) {
          result += incremented;
        } else {
          result += '0';
        }
      }
      return result;
    },
  },
};
</script>
<style lang="scss" scoped>
.chart-container {
  width: 500px;
  height: 400px;
  h2 {
    text-align: left;
  }
}
.chart {
  width: 500px;
  height: 400px;

  /* 调整y轴滚动条宽度 */
  .echarts-slider-horizontal {
    width: 10px; /* 设置为较小的宽度 */
  }
}
</style>