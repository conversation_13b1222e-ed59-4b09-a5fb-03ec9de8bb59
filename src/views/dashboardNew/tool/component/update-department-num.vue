<template>
    <div style="position: relative;width:1000px;margin:0 auto;">
        <div ref="chart" class="echarts"></div>
        <slot></slot>
    </div>
</template>
<script>
import * as echarts from 'echarts'
export default {
    data() {
        return {

        }
    },
    props: ['echartsDataList'],
    watch: {
        echartsDataList: {
            handler() {
                this.init()
            },
            deep: true,
        }
    },
    mounted() {
        this.init()
    },
    methods: {
        formatThousands(num) {
            return num.toString().replace(/(\d)(?=(\d{3})+$)/g, '$1,');
        },
        init() {
            let newValue = this.echartsDataList
            // 获取容器元素
            const container =  this.$refs.chart;
            // 初始化echarts实例
            const chart = echarts.init(container)
            chart.clear();
            // 折线图的数据
            const option = {
                title: {
                    text: '更新个人信息用户科室分布'
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'cross',
                        crossStyle: {
                            color: '#999'
                        },
                        label: {
                            formatter: function (params) {
                                if (params.seriesData.length === 0) {
                                    window.mouseCurValue = params.value;
                                }
                            }
                        }
                    },
                    formatter: (params) => {
                        // console.log(params,'mmmm');
                        // console.log(params, 'newValue');
                        let res = "", sum = 0;
                        let index;
                        // 找到第一个对象的axisValue
                        let axisValue = params[0].axisValue;
                        // 循环newValue数组
                        for (let i = 0; i < newValue[3].length; i++) {
                            // 如果对象的值等于axisValue
                            if (newValue[3][i].newUser.department === axisValue) {
                                // 记录索引
                                index = i;
                                break;
                            }
                        }
                        // index就是新值数组对象所在位置的索引
                        // console.log(index, 'index');
                        for (let i = 0; i < params.length; i++) {
                            let series = params[i];
                            sum += Number(series.data);
                            if (sum >= window.mouseCurValue) {
                                // res = series.axisValue + "<br/>" + series.seriesName + "<br/>";
                                if (series.componentIndex == 0) {
                                    res = `
                                    <div 
                                    style='display: flex;
                                    align-items: flex-start;
                                    flex-direction: column;'>
                                        <div>用户科室:${series.axisValue}</div>
                                        <div>用户类型:${series.seriesName}</div>
                                        <div>总人数:${this.formatThousands(series.value)}</div>
                                        <div>网站更新人数:${this.formatThousands(newValue[3][index].oldUser.webNumber)}</div>
                                        <div>APP更新人数:${this.formatThousands(newValue[3][index].oldUser.appNumber)}</div>
                                        <div>其他平台更新人数:${this.formatThousands(newValue[3][index].oldUser.otherNumber)}</div>
                                    </div>
                                        `;
                                    // webNUm = newValue[4][index].newUser.webNumber
                                } else if (series.componentIndex == 1) {
                                    res = `
                                    <div 
                                    style='display: flex;
                                    align-items: flex-start;
                                    flex-direction: column;'>
                                        <div>用户科室:${series.axisValue}</div>
                                        <div>用户类型:${series.seriesName}</div>
                                        <div>总人数:${this.formatThousands(series.value)}</div>
                                        <div>网站更新人数:${this.formatThousands(newValue[3][index].newUser.webNumber)}</div>
                                        <div>APP更新人数:${this.formatThousands(newValue[3][index].newUser.appNumber)}</div>
                                        <div>其他平台更新人数:${this.formatThousands(newValue[3][index].newUser.otherNumber)}</div>
                                    </div>
                                        `;
                                }
                                break;
                            }
                        }
                        return res;
                    },
                },
                dataZoom: [
                    {
                        type: "slider", // 使用滑动条型式的数据区域选择组件
                        yAxisIndex: 0, // 指定对应的 x 轴索引
                        start: 100,
                        end: 110,
                        width: 5,
                        brushSelect: false,
                        showDetail: false,
                    },
                    {
                        // 没有下面这块的话，只能拖动滚动条，
                        // 鼠标滚轮在区域内不能控制外部滚动条
                        type: "inside",
                        yAxisIndex: [0, 1],
                        zoomOnMouseWheel: false,
                        moveOnMouseMove: true,
                        moveOnMouseWheel: true,
                    },
                ],
                // toolbox: {
                //     feature: {
                //         // dataView: { show: true, readOnly: false },
                //         // magicType: { show: true, type: ['line', 'bar'] },
                //         // restore: { show: true },
                //         saveAsImage: { show: true }
                //     }
                // },

                legend: {},
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'value',
                    max: newValue[4]
                },
                yAxis: {
                    name: '用户科室',
                    type: 'category',
                    data:newValue[0]
                    // data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
                },
                series: [
                    {
                        name: '老用户',
                        type: 'bar',
                        stack: 'total',
                        label: {
                            show: true,
                            formatter: function(params) {
                            if (params.value > 10000) {
                                return params.value;  
                            } else {
                                return '';
                            }
                        }
                        },
                        emphasis: {
                            focus: 'series'
                        },
                        data: newValue[2],
                        itemStyle: {
                            color: '#97cdd4',
                        }
                    },

                    {
                        name: '新用户',
                        type: 'bar',
                        stack: 'total',
                        label: {
                            show: true,
                            formatter: function(params) {
                            if (params.value > 10000) {
                                return params.value;  
                            } else {
                                return '';
                            }
                        }
                        },
                        emphasis: {
                            focus: 'series'
                        },
                        data: newValue[1],
                        itemStyle: {
                            color: '#598baa',
                        }
                    },

                ]
            }
            // 渲染图表
            chart.setOption(option)
        }
    },
    created() {
    }
}
</script>
<style lang="scss" scoped>
.echarts {
    width: 1000px;
    height: 400px;
    margin: 0 auto;
}

.desc-title {
    width: 500px;
    display: flex;
    justify-content: space-between;
    margin: 0 auto;
}
</style>