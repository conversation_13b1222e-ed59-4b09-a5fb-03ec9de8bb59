<template>
  <section>
    <!-- <div class="button-container" v-for="item in btnList" :key="item.id"> -->
    <!-- <el-button active size="medium" type="primary" plain>{{ item.name }}</el-button> -->
    <!-- <div id="chart" style="width: 600px;height:400px;" v-if="item.id == 1"></div> -->
    <!-- </div> -->
    <div class="date-picker">
      <el-date-picker
        unlink-panels
        v-model="value1"
        type="daterange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        value-format="yyyy-MM-dd"
      >
      </el-date-picker>
      <el-button type="primary" @click="hanleQuery" style="margin-left: 5%"
        >查询</el-button
      >
    </div>
    <my-line-deshboard
      :echartsDataList="list1"
      v-if="list1 && list1.length"
      style="position: relative"
    >
      <el-button
        @click="downloadExcel1({ startTime: value1[0], endTime: value1[1] , responseType: 'blob'})"
        style="position: absolute; top: -4px; right: 130px"
        type="primary"
        icon="el-icon-download"
      ></el-button>
    </my-line-deshboard>
    <my-update-person
      :echartsDataList="list2"
      v-if="list2 && list2.length"
      style="position: relative"
    >
      <el-button
        @click="downloadExcel2({ startTime: value1[0], endTime: value1[1] })"
        style="position: absolute; top: -4px; right: 130px"
        type="primary"
        icon="el-icon-download"
      ></el-button>
    </my-update-person>
    <update-department-num
      :echartsDataList="list3"
      v-if="list3 && list3.length"
      style="position: relative"
    >
      <el-button
        @click="downloadExcel3({ startTime: value1[0], endTime: value1[1] })"
        style="position: absolute; top: -4px; right: 130px"
        type="primary"
        icon="el-icon-download"
      ></el-button>
    </update-department-num>
    <my-register-department-all
      :echartsDataList="list5"
      v-if="list5 && list5.length"
      style="position: relative"
    >
      <el-button
        @click="downloadExcel4({ startTime: value1[0], endTime: value1[1] })"
        style="position: absolute; top: -4px; right: 130px"
        type="primary"
        icon="el-icon-download"
      ></el-button>
    </my-register-department-all>
    <my-register-department
      :echartsDataList="list4"
      v-if="list4 && list4.length"
    ></my-register-department>
    <my-address
      :echartsDataList="list6"
      v-if="list6 && list6.length"
    ></my-address>
    <div style="display: flex; justify-content: center">
      <my-hospital-all
        :echartsDataList="list8"
        v-if="list8 && list8.length"
      ></my-hospital-all>
      <my-Hospital
        :echartsDataList="list7"
        v-if="list7 && list7.length"
      ></my-Hospital>
    </div>
    <div style="display: flex; justify-content: center">
      <my-title
        :echartsDataList="list9"
        v-if="list9 && list9.length"
      ></my-title>
      <my-channel
        v-if="list10 && list10.length"
        :echartsDataList="list10"
      ></my-channel>
    </div>
  </section>
</template>
<script>
// import 'vizjs'
import myLineDeshboard from "./component/my-line-deshboard.vue";
import myUpdatePerson from "./component/my-update-person.vue";
import myAddress from "./component/my-address.vue";
import myHospital from "./component/my-hospital.vue";
import myHospitalAll from "./component/my-hospital-all.vue";
import myTitle from "./component/my-title.vue";
import myChannel from "./component/my-channel.vue";
import updateDepartmentNum from "./component/update-department-num.vue";
import myRegisterDepartment from "./component/my-register-department.vue";
import myRegisterDepartmentAll from "./component/my-register-department-all.vue";
import API from "@/api/module/dashboardNew";
export default {
  name: "ms-user-dashboard",
  components: {
    myLineDeshboard,
    myUpdatePerson,
    myAddress,
    myHospital,
    myHospitalAll,
    myTitle,
    myChannel,
    updateDepartmentNum,
    myRegisterDepartment,
    myRegisterDepartmentAll,
  },

  data() {
    return {
      value1: [],
      dashboardUrl: "",
      activeName: "user_new",
      // btnList: [
      //     {
      //         name: '用户新增',
      //         id: 1
      //     },
      //     // {
      //     //     name: '用户行为_UV',
      //     //     id: 2
      //     // },
      //     // {
      //     //     name: '用户行为_PV',
      //     //     id: 3
      //     // },
      //     // {
      //     //     name: '用户转化',
      //     //     id: 4
      //     // },
      //     // {
      //     //     name: '直播',
      //     //     id: 5
      //     // },
      // ],
      // 折线图
      // echartsDataList: {},
      list1: [],
      list2: [],
      list3: [],
      list4: [],
      list5: [],
      list6: [],
      list7: [],
      list8: [],
      list9: [],
      list10: [],

      data_1: [],
      data_2: {},
      data_3: {},
      data_4: [],
      data_5: [],
      data_6: [],
      data_7: [],
      data_8: [],
      data_9: [],
      data_10: [],
      // data_1: [
      //     {
      //         "registeredNumber": "100",
      //         "date": "20230101"
      //     },
      //     {
      //         "registeredNumber": "300",
      //         "date": "20230102"
      //     },
      //     {
      //         "registeredNumber": "200",
      //         "date": "20230103"
      //     },
      //     {
      //         "registeredNumber": "500",
      //         "date": "20230104"
      //     },
      //     {
      //         "registeredNumber": "600",
      //         "date": "20230105"
      //     },
      //     {
      //         "registeredNumber": "500",
      //         "date": "20230106"
      //     },
      //     {
      //         "registeredNumber": "700",
      //         "date": "20230107"
      //     },
      //     {
      //         "registeredNumber": "1010",
      //         "date": "20230108"
      //     },
      //     {
      //         "registeredNumber": "100",
      //         "date": "20230109"
      //     },
      //     {
      //         "registeredNumber": "300",
      //         "date": "20230110"
      //     },
      //     {
      //         "registeredNumber": "200",
      //         "date": "20230111"
      //     },
      //     {
      //         "registeredNumber": "500",
      //         "date": "20230112"
      //     },
      //     {
      //         "registeredNumber": "600",
      //         "date": "20230113"
      //     },
      //     {
      //         "registeredNumber": "500",
      //         "date": "20230114"
      //     },
      //     {
      //         "registeredNumber": "700",
      //         "date": "20230115"
      //     },
      //     {
      //         "registeredNumber": "1010",
      //         "date": "20230116"
      //     },
      //     {
      //         "registeredNumber": "100",
      //         "date": "20230117"
      //     },
      //     {
      //         "registeredNumber": "300",
      //         "date": "20230118"
      //     },
      //     {
      //         "registeredNumber": "200",
      //         "date": "20230119"
      //     },
      //     {
      //         "registeredNumber": "500",
      //         "date": "20230120"
      //     },
      //     {
      //         "registeredNumber": "600",
      //         "date": "20230121"
      //     },
      //     {
      //         "registeredNumber": "500",
      //         "date": "20230122"
      //     },
      //     {
      //         "registeredNumber": "700",
      //         "date": "20230123"
      //     },
      //     {
      //         "registeredNumber": "1010",
      //         "date": "20230124"
      //     },
      //     {
      //         "registeredNumber": "100",
      //         "date": "20230101"
      //     },
      //     {
      //         "registeredNumber": "300",
      //         "date": "20230102"
      //     },
      //     {
      //         "registeredNumber": "200",
      //         "date": "20230103"
      //     },
      //     {
      //         "registeredNumber": "500",
      //         "date": "20230104"
      //     },
      //     {
      //         "registeredNumber": "600",
      //         "date": "20230105"
      //     },
      //     {
      //         "registeredNumber": "500",
      //         "date": "20230106"
      //     },
      //     {
      //         "registeredNumber": "700",
      //         "date": "20230107"
      //     },
      //     {
      //         "registeredNumber": "1010",
      //         "date": "20230108"
      //     },
      //     {
      //         "registeredNumber": "1000",
      //         "date": "20230101"
      //     },
      //     {
      //         "registeredNumber": "3000",
      //         "date": "20230102"
      //     },
      //     {
      //         "registeredNumber": "200",
      //         "date": "20230103"
      //     },
      //     {
      //         "registeredNumber": "500",
      //         "date": "20230104"
      //     },
      //     {
      //         "registeredNumber": "6000",
      //         "date": "20230105"
      //     },
      //     {
      //         "registeredNumber": "500",
      //         "date": "20230106"
      //     },
      //     {
      //         "registeredNumber": "700",
      //         "date": "20230107"
      //     },
      //     {
      //         "registeredNumber": "10100",
      //         "date": "20230108"
      //     },
      //     {
      //         "registeredNumber": "100",
      //         "date": "20230101"
      //     },
      //     {
      //         "registeredNumber": "300",
      //         "date": "20230102"
      //     },
      //     {
      //         "registeredNumber": "2000",
      //         "date": "20230103"
      //     },
      //     {
      //         "registeredNumber": "500",
      //         "date": "20230104"
      //     },
      //     {
      //         "registeredNumber": "600",
      //         "date": "20230105"
      //     },
      //     {
      //         "registeredNumber": "500",
      //         "date": "20230106"
      //     },
      //     {
      //         "registeredNumber": "700",
      //         "date": "20230107"
      //     },
      //     {
      //         "registeredNumber": "1010",
      //         "date": "20230108"
      //     },
      //     {
      //         "registeredNumber": "100",
      //         "date": "20230101"
      //     },
      //     {
      //         "registeredNumber": "3000",
      //         "date": "20230102"
      //     },
      //     {
      //         "registeredNumber": "200",
      //         "date": "20230103"
      //     },
      //     {
      //         "registeredNumber": "500",
      //         "date": "20230104"
      //     },
      //     {
      //         "registeredNumber": "600",
      //         "date": "20230105"
      //     },
      //     {
      //         "registeredNumber": "500",
      //         "date": "20230106"
      //     },
      //     {
      //         "registeredNumber": "700",
      //         "date": "20230107"
      //     },
      //     {
      //         "registeredNumber": "1010",
      //         "date": "20230108"
      //     },
      // ],
      // data_2: {
      //     "updatedPersonalInformationPerDayList": [{
      //         "newUser": {
      //             "totalNumber": 50,
      //             "date": "20230101",
      //             "webNumber": 23,
      //             "appNumber": 5,
      //             "otherNumber": 22
      //         },
      //         "oldUser": {
      //             "totalNumber": 100,
      //             "date": "20230101",
      //             "webNumber": 80,
      //             "appNumber": 10,
      //             "otherNumber": 10
      //         },
      //         "totalNumber": 150
      //     },
      //     {
      //         "newUser": {
      //             "totalNumber": 50,
      //             "date": "20230102",
      //             "webNumber": 23,
      //             "appNumber": 5,
      //             "otherNumber": 22
      //         },
      //         "oldUser": {
      //             "totalNumber": 100,
      //             "date": "20230102",
      //             "webNumber": 80,
      //             "appNumber": 10,
      //             "otherNumber": 10
      //         },
      //         "totalNumber": 150
      //     },
      //     {
      //         "newUser": {
      //             "totalNumber": 50,
      //             "date": "20230103",
      //             "webNumber": 23,
      //             "appNumber": 5,
      //             "otherNumber": 22
      //         },
      //         "oldUser": {
      //             "totalNumber": 100,
      //             "date": "20230103",
      //             "webNumber": 80,
      //             "appNumber": 10,
      //             "otherNumber": 10
      //         },
      //         "totalNumber": 150
      //     },
      //     {
      //         "newUser": {
      //             "totalNumber": 50,
      //             "date": "20230104",
      //             "webNumber": 23,
      //             "appNumber": 5,
      //             "otherNumber": 22
      //         },
      //         "oldUser": {
      //             "totalNumber": 100,
      //             "date": "20230104",
      //             "webNumber": 80,
      //             "appNumber": 10,
      //             "otherNumber": 10
      //         },
      //         "totalNumber": 150
      //     },
      //     {
      //         "newUser": {
      //             "totalNumber": 550,
      //             "date": "20230105",
      //             "webNumber": 423,
      //             "appNumber": 5,
      //             "otherNumber": 122
      //         },
      //         "oldUser": {
      //             "totalNumber": 100,
      //             "date": "20230105",
      //             "webNumber": 80,
      //             "appNumber": 10,
      //             "otherNumber": 10
      //         },
      //         "totalNumber": 650
      //     },
      //     {
      //         "newUser": {
      //             "totalNumber": 500,
      //             "date": "20230106",
      //             "webNumber": 230,
      //             "appNumber": 150,
      //             "otherNumber": 120
      //         },
      //         "oldUser": {
      //             "totalNumber": 100,
      //             "date": "20230106",
      //             "webNumber": 80,
      //             "appNumber": 10,
      //             "otherNumber": 10
      //         },
      //         "totalNumber": 600
      //     },
      //     {
      //         "newUser": {
      //             "totalNumber": 500,
      //             "date": "20230107",
      //             "webNumber": 230,
      //             "appNumber": 150,
      //             "otherNumber": 120
      //         },
      //         "oldUser": {
      //             "totalNumber": 100,
      //             "date": "20230107",
      //             "webNumber": 80,
      //             "appNumber": 10,
      //             "otherNumber": 10
      //         },
      //         "totalNumber": 600
      //     },
      //     {
      //         "newUser": {
      //             "totalNumber": 200,
      //             "date": "20230108",
      //             "webNumber": 130,
      //             "appNumber": 50,
      //             "otherNumber": 20
      //         },
      //         "oldUser": {
      //             "totalNumber": 900,
      //             "date": "20230108",
      //             "webNumber": 80,
      //             "appNumber": 800,
      //             "otherNumber": 20
      //         },
      //         "totalNumber": 1100
      //     },
      //     ],
      //     "newTotalNumber": 1600,
      //     "oldTotalNumber": 1950
      // },
      // data_3: {
      //     updatedPersonalDepartmentPerDayList: [],
      //     "newTotalNumber": 2000,
      //     "oldTotalNumber": 30000
      // },
      // data_3: {
      //     updatedPersonalDepartmentPerDayList: [
      //         {
      //             "newUser": {
      //                 "department": "心血管",
      //                 "totalNumber": 500,
      //                 "date": "20230101",
      //                 "webNumber": 230,
      //                 "appNumber": 50,
      //                 "otherNumber": 220
      //             },
      //             "oldUser": {
      //                 "department": "心血管",
      //                 "totalNumber": 100,
      //                 "date": "20230101",
      //                 "webNumber": 80,
      //                 "appNumber": 10,
      //                 "otherNumber": 10
      //             },
      //             "totalNumber": 600
      //         },
      //         {
      //             "newUser": {
      //                 "department": "肾内科",
      //                 "totalNumber": 50,
      //                 "date": "20230101",
      //                 "webNumber": 23,
      //                 "appNumber": 5,
      //                 "otherNumber": 22
      //             },
      //             "oldUser": {
      //                 "department": "肾内科",
      //                 "totalNumber": 100,
      //                 "date": "20230101",
      //                 "webNumber": 80,
      //                 "appNumber": 10,
      //                 "otherNumber": 10
      //             },
      //             "totalNumber": 150
      //         },
      //         {
      //             "newUser": {
      //                 "department": "精神心理科",
      //                 "totalNumber": 50,
      //                 "date": "20230101",
      //                 "webNumber": 23,
      //                 "appNumber": 5,
      //                 "otherNumber": 22
      //             },
      //             "oldUser": {
      //                 "department": "精神心理科",
      //                 "totalNumber": 100,
      //                 "date": "20230101",
      //                 "webNumber": 80,
      //                 "appNumber": 10,
      //                 "otherNumber": 10
      //             },
      //             "totalNumber": 150
      //         },
      //         {
      //             "newUser": {
      //                 "department": "呼吸科",
      //                 "totalNumber": 50,
      //                 "date": "20230101",
      //                 "webNumber": 23,
      //                 "appNumber": 5,
      //                 "otherNumber": 22
      //             },
      //             "oldUser": {
      //                 "department": "呼吸科",
      //                 "totalNumber": 100,
      //                 "date": "20230101",
      //                 "webNumber": 80,
      //                 "appNumber": 10,
      //                 "otherNumber": 10
      //             },
      //             "totalNumber": 150
      //         },
      //         {
      //             "newUser": {
      //                 "department": "妇产科",
      //                 "totalNumber": 50,
      //                 "date": "20230101",
      //                 "webNumber": 23,
      //                 "appNumber": 5,
      //                 "otherNumber": 22
      //             },
      //             "oldUser": {
      //                 "department": "妇产科",
      //                 "totalNumber": 100,
      //                 "date": "20230101",
      //                 "webNumber": 80,
      //                 "appNumber": 10,
      //                 "otherNumber": 10
      //             },
      //             "totalNumber": 150
      //         },
      //         {
      //             "newUser": {
      //                 "department": "皮肤科1",
      //                 "totalNumber": 50,
      //                 "date": "20230101",
      //                 "webNumber": 23,
      //                 "appNumber": 5,
      //                 "otherNumber": 22
      //             },
      //             "oldUser": {
      //                 "department": "皮肤科1",
      //                 "totalNumber": 100,
      //                 "date": "20230101",
      //                 "webNumber": 80,
      //                 "appNumber": 10,
      //                 "otherNumber": 10
      //             },
      //             "totalNumber": 150
      //         },
      //         {
      //             "newUser": {
      //                 "department": "肿瘤科",
      //                 "totalNumber": 500,
      //                 "date": "20230102",
      //                 "webNumber": 230,
      //                 "appNumber": 150,
      //                 "otherNumber": 120
      //             },
      //             "oldUser": {
      //                 "department": "肿瘤科",
      //                 "totalNumber": 100,
      //                 "date": "20230102",
      //                 "webNumber": 80,
      //                 "appNumber": 10,
      //                 "otherNumber": 10
      //             },
      //             "totalNumber": 600
      //         },
      //         {
      //             "newUser": {
      //                 "department": "内科",
      //                 "totalNumber": 500,
      //                 "date": "20230103",
      //                 "webNumber": 230,
      //                 "appNumber": 150,
      //                 "otherNumber": 120
      //             },
      //             "oldUser": {
      //                 "department": "内科",
      //                 "totalNumber": 100,
      //                 "date": "20230103",
      //                 "webNumber": 80,
      //                 "appNumber": 10,
      //                 "otherNumber": 10
      //             },
      //             "totalNumber": 600
      //         },
      //         {
      //             "newUser": {
      //                 "department": "外科",
      //                 "totalNumber": 200,
      //                 "date": "20230104",
      //                 "webNumber": 130,
      //                 "appNumber": 50,
      //                 "otherNumber": 20
      //             },
      //             "oldUser": {
      //                 "department": "外科",
      //                 "totalNumber": 800,
      //                 "date": "20230104",
      //                 "webNumber": 80,
      //                 "appNumber": 700,
      //                 "otherNumber": 20
      //             },
      //             "totalNumber": 1000
      //         },
      //         {
      //             "newUser": {
      //                 "department": "外1科",
      //                 "totalNumber": 200,
      //                 "date": "20230104",
      //                 "webNumber": 130,
      //                 "appNumber": 50,
      //                 "otherNumber": 20
      //             },
      //             "oldUser": {
      //                 "department": "外1科",
      //                 "totalNumber": 200,
      //                 "date": "20230104",
      //                 "webNumber": 80,
      //                 "appNumber": 700,
      //                 "otherNumber": 20
      //             },
      //             "totalNumber": 1000
      //         },
      //         {
      //             "newUser": {
      //                 "department": "外2科",
      //                 "totalNumber": 200,
      //                 "date": "20230104",
      //                 "webNumber": 130,
      //                 "appNumber": 50,
      //                 "otherNumber": 20
      //             },
      //             "oldUser": {
      //                 "department": "外2科",
      //                 "totalNumber": 300,
      //                 "date": "20230104",
      //                 "webNumber": 80,
      //                 "appNumber": 700,
      //                 "otherNumber": 20
      //             },
      //             "totalNumber": 1000
      //         },
      //     ],
      //     "newTotalNumber": 2000,
      //     "oldTotalNumber": 30000
      // },
      // data_4: [
      //     {
      //         department: "心血管",
      //         proportion: 5,
      //         date: "yyyyMMdd",
      //         totalNumber: 100,
      //     },
      //     {
      //         department: "内分泌",
      //         proportion: 5,
      //         date: "yyyyMMdd",
      //         totalNumber: 80,
      //     },
      //     {
      //         department: "肿瘤科",
      //         proportion: 5,
      //         date: "yyyyMMdd",
      //         totalNumber: 60,
      //     },
      //     {
      //         department: "外科",
      //         proportion: 5,
      //         date: "yyyyMMdd",
      //         totalNumber: 40,
      //     },
      //     {
      //         department: "内科",
      //         proportion: 5,
      //         date: "yyyyMMdd",
      //         totalNumber: 20,
      //     },
      //     {
      //         department: "肿瘤科",
      //         proportion: 5,
      //         date: "yyyyMMdd",
      //         totalNumber: 15,
      //     },
      //     {
      //         department: "肿瘤科",
      //         proportion: 5,
      //         date: "yyyyMMdd",
      //         totalNumber: 10,
      //     },
      //     { department: "肿瘤科科", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { department: "肿瘤d科", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { department: "肿瘤d科", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { department: "肿瘤科d", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { department: "肿瘤科d", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { department: "肿瘤科d", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { department: "肿瘤dq科", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { department: "肿瘤q科", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { department: "肿瘤b科", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     {
      //         department: "肿瘤科",
      //         proportion: 5,
      //         date: "yyyyMMdd",
      //         totalNumber: 5,
      //     },
      //     { department: "肿瘤科1", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { department: "肿瘤科2", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { department: "肿瘤科f", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { department: "肿瘤科f", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { department: "肿瘤科f", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     {
      //         department: "肿瘤科古d",
      //         proportion: 5,
      //         date: "yyyyMMdd",
      //         totalNumber: 5,
      //     },
      //     { department: "内科1", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { department: "内科4", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { department: "内科3", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { department: "内科2", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { department: "内科5", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { department: "内科", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { department: "内科6", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { department: "内科", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { department: "内科7", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { department: "内科", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { department: "内科8", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { department: "内科9", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { department: "内科10", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      // ],
      // data_5: [
      //     {
      //         department: "心血管",
      //         proportion: 5,
      //         date: "yyyyMMdd",
      //         totalNumber: 100,
      //     },
      //     {
      //         department: "内分泌",
      //         proportion: 5,
      //         date: "yyyyMMdd",
      //         totalNumber: 80,
      //     },
      //     {
      //         department: "肿瘤科",
      //         proportion: 5,
      //         date: "yyyyMMdd",
      //         totalNumber: 60,
      //     },
      //     {
      //         department: "外科",
      //         proportion: 5,
      //         date: "yyyyMMdd",
      //         totalNumber: 40,
      //     },
      //     {
      //         department: "内科",
      //         proportion: 5,
      //         date: "yyyyMMdd",
      //         totalNumber: 20,
      //     },
      //     {
      //         department: "肿瘤科",
      //         proportion: 5,
      //         date: "yyyyMMdd",
      //         totalNumber: 15,
      //     },
      //     {
      //         department: "肿瘤科",
      //         proportion: 5,
      //         date: "yyyyMMdd",
      //         totalNumber: 10,
      //     },
      //     { department: "肿瘤科科", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { department: "肿瘤d科", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { department: "肿瘤d科", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { department: "肿瘤科d", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { department: "肿瘤科d", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { department: "肿瘤科d", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { department: "肿瘤dq科", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { department: "肿瘤q科", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { department: "肿瘤b科", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     {
      //         department: "肿瘤科",
      //         proportion: 5,
      //         date: "yyyyMMdd",
      //         totalNumber: 5,
      //     },
      //     { department: "肿瘤科1", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { department: "肿瘤科2", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { department: "肿瘤科f", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { department: "肿瘤科f", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { department: "肿瘤科f", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     {
      //         department: "肿瘤科古d",
      //         proportion: 5,
      //         date: "yyyyMMdd",
      //         totalNumber: 5,
      //     },
      //     { department: "内科1", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { department: "内科4", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { department: "内科3", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { department: "内科2", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { department: "内科5", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { department: "内科", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { department: "内科6", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { department: "内科", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { department: "内科7", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { department: "内科", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { department: "内科8", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { department: "内科9", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { department: "内科10", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      // ],
      // data_6: [
      //     {
      //         hospitalLevel: "北京",
      //         proportion: 5,
      //         date: "yyyyMMdd",
      //         totalNumber: 100,
      //     },
      //     {
      //         provincial: "上海",
      //         proportion: 5,
      //         date: "yyyyMMdd",
      //         totalNumber: 80,
      //     },
      //     {
      //         provincial: "广东",
      //         proportion: 5,
      //         date: "yyyyMMdd",
      //         totalNumber: 60,
      //     },
      //     {
      //         provincial: "广东",
      //         proportion: 5,
      //         date: "yyyyMMdd",
      //         totalNumber: 40,
      //     },
      //     {
      //         provincial: "江苏",
      //         proportion: 5,
      //         date: "yyyyMMdd",
      //         totalNumber: 20,
      //     },
      //     {
      //         provincial: "江苏",
      //         proportion: 5,
      //         date: "yyyyMMdd",
      //         totalNumber: 15,
      //     },
      //     {
      //         provincial: "浙江",
      //         proportion: 5,
      //         date: "yyyyMMdd",
      //         totalNumber: 10,
      //     },
      //     { provincial: "浙江", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { provincial: "安徽", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { provincial: "福建", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { provincial: "甘肃", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { provincial: "广西", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { provincial: "贵州", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { provincial: "海南", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { provincial: "河北", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { provincial: "河南", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     {
      //         provincial: "黑龙江",
      //         proportion: 5,
      //         date: "yyyyMMdd",
      //         totalNumber: 5,
      //     },
      //     { provincial: "湖北", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { provincial: "湖南", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { provincial: "吉林", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { provincial: "江西", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { provincial: "辽宁", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     {
      //         provincial: "内蒙古",
      //         proportion: 5,
      //         date: "yyyyMMdd",
      //         totalNumber: 5,
      //     },
      //     { provincial: "宁夏", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { provincial: "青海", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { provincial: "山东", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { provincial: "山西", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { provincial: "陕西", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { provincial: "四川", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { provincial: "台湾", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { provincial: "西藏", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { provincial: "香港", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { provincial: "新疆", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { provincial: "云南", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { provincial: "浙江", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { provincial: "重庆", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      // ],
      // data_7: [
      //     {
      //         hospitalLevel: "北京",
      //         proportion: 5,
      //         date: "yyyyMMdd",
      //         totalNumber: 100,
      //     },
      //     {
      //         hospitalLevel: "上海",
      //         proportion: 5,
      //         date: "yyyyMMdd",
      //         totalNumber: 80,
      //     },
      //     {
      //         hospitalLevel: "广东",
      //         proportion: 5,
      //         date: "yyyyMMdd",
      //         totalNumber: 60,
      //     },
      //     {
      //         hospitalLevel: "广东",
      //         proportion: 5,
      //         date: "yyyyMMdd",
      //         totalNumber: 40,
      //     },
      //     {
      //         hospitalLevel: "江苏",
      //         proportion: 5,
      //         date: "yyyyMMdd",
      //         totalNumber: 20,
      //     },
      //     {
      //         hospitalLevel: "江苏",
      //         proportion: 5,
      //         date: "yyyyMMdd",
      //         totalNumber: 15,
      //     },
      //     {
      //         hospitalLevel: "浙江",
      //         proportion: 5,
      //         date: "yyyyMMdd",
      //         totalNumber: 10,
      //     },
      //     { hospitalLevel: "浙江", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { hospitalLevel: "安徽", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { hospitalLevel: "福建", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { hospitalLevel: "甘肃", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { hospitalLevel: "广西", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { hospitalLevel: "贵州", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { hospitalLevel: "海南", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { hospitalLevel: "河北", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { hospitalLevel: "河南", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     {
      //         hospitalLevel: "黑龙江",
      //         proportion: 5,
      //         date: "yyyyMMdd",
      //         totalNumber: 5,
      //     },
      //     { hospitalLevel: "湖北", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { hospitalLevel: "湖南", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { hospitalLevel: "吉林", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { hospitalLevel: "江西", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { hospitalLevel: "辽宁", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     {
      //         hospitalLevel: "内蒙古",
      //         proportion: 5,
      //         date: "yyyyMMdd",
      //         totalNumber: 5,
      //     },
      //     { hospitalLevel: "宁夏", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { hospitalLevel: "青海", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { hospitalLevel: "山东", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { hospitalLevel: "山西", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { hospitalLevel: "陕西", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { hospitalLevel: "四川", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { hospitalLevel: "台湾", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { hospitalLevel: "西藏", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { hospitalLevel: "香港", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { hospitalLevel: "新疆", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { hospitalLevel: "云南", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { hospitalLevel: "浙江", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { hospitalLevel: "重庆", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      // ],
      // data_8: [
      //     {
      //         hospitalLevel: "北京",
      //         proportion: 5,
      //         date: "yyyyMMdd",
      //         totalNumber: 100,
      //     },
      //     {
      //         hospitalLevel: "上海",
      //         proportion: 5,
      //         date: "yyyyMMdd",
      //         totalNumber: 80,
      //     },
      //     {
      //         hospitalLevel: "广东",
      //         proportion: 5,
      //         date: "yyyyMMdd",
      //         totalNumber: 60,
      //     },
      //     {
      //         hospitalLevel: "广东",
      //         proportion: 5,
      //         date: "yyyyMMdd",
      //         totalNumber: 40,
      //     },
      //     {
      //         hospitalLevel: "江苏",
      //         proportion: 5,
      //         date: "yyyyMMdd",
      //         totalNumber: 20,
      //     },
      //     {
      //         hospitalLevel: "江苏",
      //         proportion: 5,
      //         date: "yyyyMMdd",
      //         totalNumber: 15,
      //     },
      //     {
      //         hospitalLevel: "浙江",
      //         proportion: 5,
      //         date: "yyyyMMdd",
      //         totalNumber: 10,
      //     },
      //     { hospitalLevel: "浙江", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { hospitalLevel: "安徽", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { hospitalLevel: "福建", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { hospitalLevel: "甘肃", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { hospitalLevel: "广西", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { hospitalLevel: "贵州", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { hospitalLevel: "海南", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { hospitalLevel: "河北", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { hospitalLevel: "河南", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     {
      //         hospitalLevel: "黑龙江",
      //         proportion: 5,
      //         date: "yyyyMMdd",
      //         totalNumber: 5,
      //     },
      //     { hospitalLevel: "湖北", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { hospitalLevel: "湖南", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { hospitalLevel: "吉林", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { hospitalLevel: "江西", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { hospitalLevel: "辽宁", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     {
      //         hospitalLevel: "内蒙古",
      //         proportion: 5,
      //         date: "yyyyMMdd",
      //         totalNumber: 5,
      //     },
      //     { hospitalLevel: "宁夏", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { hospitalLevel: "青海", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { hospitalLevel: "山东", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { hospitalLevel: "山西", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { hospitalLevel: "陕西", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { hospitalLevel: "四川", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { hospitalLevel: "台湾", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { hospitalLevel: "西藏", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { hospitalLevel: "香港", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { hospitalLevel: "新疆", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { hospitalLevel: "云南", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { hospitalLevel: "浙江", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { hospitalLevel: "重庆", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      // ],
      // data_9: [
      //     {
      //         professional: "北京",
      //         proportion: 5,
      //         date: "yyyyMMdd",
      //         totalNumber: 100,
      //     },
      //     {
      //         professional: "上海",
      //         proportion: 5,
      //         date: "yyyyMMdd",
      //         totalNumber: 80,
      //     },
      //     {
      //         professional: "广东",
      //         proportion: 5,
      //         date: "yyyyMMdd",
      //         totalNumber: 60,
      //     },
      //     {
      //         professional: "广东",
      //         proportion: 5,
      //         date: "yyyyMMdd",
      //         totalNumber: 40,
      //     },
      //     {
      //         professional: "江苏",
      //         proportion: 5,
      //         date: "yyyyMMdd",
      //         totalNumber: 20,
      //     },
      //     {
      //         professional: "江苏",
      //         proportion: 5,
      //         date: "yyyyMMdd",
      //         totalNumber: 15,
      //     },
      //     {
      //         professional: "浙江",
      //         proportion: 5,
      //         date: "yyyyMMdd",
      //         totalNumber: 10,
      //     },
      //     { professional: "浙江", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { professional: "安徽", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { professional: "福建", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { professional: "甘肃", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { professional: "广西", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { professional: "贵州", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { professional: "海南", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { professional: "河北", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { professional: "河南", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     {
      //         professional: "黑龙江",
      //         proportion: 5,
      //         date: "yyyyMMdd",
      //         totalNumber: 5,
      //     },
      //     { professional: "湖北", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { professional: "湖南", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { professional: "吉林", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { professional: "江西", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { professional: "辽宁", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     {
      //         professional: "内蒙古",
      //         proportion: 5,
      //         date: "yyyyMMdd",
      //         totalNumber: 5,
      //     },
      //     { professional: "宁夏", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { professional: "青海", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { professional: "山东", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { professional: "山西", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { professional: "陕西", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { professional: "四川", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { professional: "台湾", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { professional: "西藏", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { professional: "香港", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { professional: "新疆", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { professional: "云南", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { professional: "浙江", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      //     { professional: "重庆", proportion: 5, date: "yyyyMMdd", totalNumber: 5 },
      // ],
      // data_10: [
      //     {
      //         channel: "北京",
      //         proportion: 25,
      //         date: "yyyyMMdd",
      //         totalNumber: 100,
      //     },
      //     {
      //         channel: "上海",
      //         proportion: 25,
      //         date: "yyyyMMdd",
      //         totalNumber: 80,
      //     },
      //     {
      //         channel: "广东",
      //         proportion: 50,
      //         date: "yyyyMMdd",
      //         totalNumber: 60,
      //     },
      // ],
    };
  },
  async mounted() {
    this.value1 = [this.getDateString, this.yesterday];
    await this.echarts1({ startTime: this.value1[0], endTime: this.value1[1] });
  },
  // async mounted(){
  //     await this.echarts1({ startTime: this.value1[0], endTime: this.value1[1] });
  //     // this.echarts1({ startTime: this.value1[0], endTime: this.value1[1]});
  // },
  computed: {
    yesterday() {
      const date = new Date();
      date.setDate(date.getDate() - 1);
      const yyyy = date.getFullYear();
      let mm = date.getMonth() + 1;
      let dd = date.getDate();
      mm = mm.toString().padStart(2, "0");
      dd = dd.toString().padStart(2, "0");
      return yyyy + "-" + mm + "-" + dd;
    },
    getDateString() {
      const date = new Date();
      const year = date.getFullYear();
      return `${year}-01-01`;
    },
  },
  methods: {
    // 处理下载文件
    downloadStream(url, filename) {
      const link = document.createElement('a')
      link.href = url
      link.download = filename
      link.style.display = 'none'
      document.body.append(link)
      link.click()
      link.remove()
    },
    downloadExcel1(params) {
      API.totalRegisteredUsersPerDayDownload(params).then((response) => {
        this.downloadStream(response.data.url, '每日注册用户总数.xlsx');
      });
    },
    downloadExcel2(params) {
      API.updatedPersonalInformationPerDayDownload(params).then((response) => {
        this.downloadStream(response.data.url, '每日更新个人信息人数.xlsx');
      });
    },
    downloadExcel3(params) {
      API.updatedPersonalDepartmentPerDayDownload(params).then((response) => {
        this.downloadStream(response.data.url, '更新个人信息用户科室分布.xlsx');
      });
    },
    downloadExcel4(params) {
      API.registeredUsersDepartmentDistributionAllDownload(params).then(
        (response) => {
            this.downloadStream(response.data.url, '注册用户科室分布.xlsx');
        }
      );
    },
    // 初始化
    init(time) {
      this.echarts1(time);
    },
    // 图表
    async echarts1(timeValue) {
      this.clearData();
      // let res = await API.updatedPersonalInformationPerDay(timeValue)
      await Promise.allSettled([
        API.totalRegisteredUsersPerDay(timeValue),
        API.updatedPersonalInformationPerDay(timeValue),
        API.updatedPersonalDepartmentPerDay(timeValue),
        API.registeredUsersDepartmentDistribution(timeValue),
        API.registeredUsersDepartmentDistributionAll(timeValue),
        API.registeredUsersProvincialDistribution(timeValue),
        API.registeredUsersHospitalLevelsDistribution(timeValue),
        API.registeredUsersHospitalLevelsDistributionAll(timeValue),
        API.registeredUsersProfessionalDistribution(timeValue),
        API.registeredUsersChannelsDistribution(timeValue),
      ]).then((res) => {
        //    this.$nextTick(()=>{
        // console.log(res,'res');
        if (
          res[0].status == "fulfilled" &&
          res[0].value.data.totalRegisteredUsersPerDayList.length != 0
        ) {
          this.data_1 = res[0].value.data.totalRegisteredUsersPerDayList;
        }
        if (
          res[1].status == "fulfilled" &&
          res[1].value.data.updatedPersonalInformationPerDayList.length != 0
        ) {
          this.data_2 = res[1].value.data;
        }
        if (
          res[2].status == "fulfilled" &&
          res[2].value.data.updatedPersonalDepartmentPerDayList.length != 0
        ) {
          this.data_3 = res[2].value.data;
        }
        if (
          res[3].status == "fulfilled" &&
          res[3].value.data.registeredUsersDepartmentDistributionList.length !=
            0
        ) {
          this.data_4 =
            res[3].value.data.registeredUsersDepartmentDistributionList;
        }
        if (
          res[4].status == "fulfilled" &&
          res[4].value.data.registeredUsersDepartmentDistributionAllList
            .length != 0
        ) {
          this.data_5 =
            res[4].value.data.registeredUsersDepartmentDistributionAllList;
        }
        if (
          res[5].status == "fulfilled" &&
          res[5].value.data.registeredUsersProvincialDistributions.length != 0
        ) {
          this.data_6 =
            res[5].value.data.registeredUsersProvincialDistributions;
        }
        if (
          res[6].status == "fulfilled" &&
          res[6].value.data.registeredUsersHospitalLevelsDistributions.length !=
            0
        ) {
          this.data_7 =
            res[6].value.data.registeredUsersHospitalLevelsDistributions;
        }
        if (
          res[7].status == "fulfilled" &&
          res[7].value.data.registeredUsersHospitalLevelsDistributionAlls
            .length != 0
        ) {
          this.data_8 =
            res[7].value.data.registeredUsersHospitalLevelsDistributionAlls;
        }
        if (
          res[8].status == "fulfilled" &&
          res[8].value.data.registeredUsersProfessionalDistributions.length != 0
        ) {
          this.data_9 =
            res[8].value.data.registeredUsersProfessionalDistributions;
        }
        if (
          res[9].status == "fulfilled" &&
          res[9].value.data.registeredUsersChannelsDistributions.length != 0
        ) {
          this.data_10 = res[9].value.data.registeredUsersChannelsDistributions;
        }
        //    })
        // 第一张图
        // const this.data_1 = res && res[0] && res[0]?.value?.data?.totalRegisteredUsersPerDayList || [];
        // const this.data_1 = res && res[0].value.data.totalRegisteredUsersPerDayList || []
        const totalRegister = res[0].value.data.totalRegister;
        const date1Arr = [];
        const value1Arr = [];
        this.data_1.forEach((item) => {
          date1Arr.push(item.date);
          value1Arr.push(item.registeredNumber);
        });
        this.list1 = [];
        this.list1.push(date1Arr, value1Arr, totalRegister);
        // 第2张图
        // const oldTotalNumber = res && res[1].value.data.oldTotalNumber || 30000
        // const newTotalNumber = res && res[1].value.data.newTotalNumber || 30000
        // console.log(this.data_2.updatedPersonalInformationPerDayList,'list22');
        if (
          this.data_2.updatedPersonalInformationPerDayList &&
          this.data_2.updatedPersonalInformationPerDayList.length != 0
        ) {
          this.data_2.updatedPersonalInformationPerDayList.forEach((item) => {
            // console.log(item);
            delete item.totalNumber;
            if (item.oldUser && item.newUser) {
              item.totalUser = {
                date: item.newUser.date,
                totalNumber:
                  item.newUser.totalNumber + item.oldUser.totalNumber,
                webNumber: item.newUser.webNumber + item.oldUser.webNumber,
                appNumber: item.newUser.appNumber + item.oldUser.appNumber,
                otherNumber:
                  item.newUser.otherNumber + item.oldUser.otherNumber,
              };
            }
            if (item.oldUser == null && item.newUser) {
              item.totalUser = {
                date: item.newUser.date,
                totalNumber: item.newUser.totalNumber + 0,
                webNumber: item.newUser.webNumber + 0,
                appNumber: item.newUser.appNumber + 0,
                otherNumber: item.newUser.otherNumber + 0,
              };
            }
          });
          const data_2 = this.data_2.updatedPersonalInformationPerDayList;
          // console.log(this.data_2,'111111');
          const date2Arr = data_2.map((obj) => obj.newUser.date);
          const value2Arr_new = data_2.map((obj) => obj.newUser.totalNumber);
          let value2Arr_old;
          if (data_2[0].oldUser != null) {
            value2Arr_old = data_2.map((obj) => obj.oldUser.totalNumber);
          }

          const total2_old = this.formatThousands(this.data_2.oldTotalNumber);
          const total2_new = this.formatThousands(this.data_2.newTotalNumber);
          const value2Arr_All = data_2.map((obj) => obj.totalUser.totalNumber);
          const total_all = this.formatThousands(
            Number(this.data_2.oldTotalNumber) +
              Number(this.data_2.newTotalNumber)
          );
          //第二张图传给子组件的数据
          this.list2 = [];
          this.list2.push(
            date2Arr,
            value2Arr_new,
            value2Arr_old,
            value2Arr_All,
            data_2,
            total2_old,
            total2_new,
            total_all
          );
        } else {
          this.list2 = [];
          this.list2.push([]);
        }

        //第三张图
        let maxTotalNumber = 0;
        if (
          this.data_3.updatedPersonalDepartmentPerDayList &&
          this.data_3.updatedPersonalDepartmentPerDayList.length != 0
        ) {
          this.data_3.updatedPersonalDepartmentPerDayList.forEach((item) => {
            delete item.totalNumber;
            item.totalUser = {
              date: item.newUser.date,
              department: item.newUser.department,
              totalNumber: item.newUser.totalNumber + item.oldUser.totalNumber,
              webNumber: item.newUser.webNumber + item.oldUser.webNumber,
              appNumber: item.newUser.appNumber + item.oldUser.appNumber,
              otherNumber: item.newUser.otherNumber + item.oldUser.otherNumber,
            };
            // 取出总用户数
            const totalNumber = item.totalUser.totalNumber;
            // 比较最大值
            if (totalNumber > maxTotalNumber) {
              maxTotalNumber = totalNumber;
            }
          });
          const data_3 = this.data_3.updatedPersonalDepartmentPerDayList;
          const departmentArr = data_3.map((obj) => obj.newUser.department);
          const depArr_new = data_3.map((obj) => obj.newUser.totalNumber);
          const depArr_old = data_3.map((obj) => obj.oldUser.totalNumber);

          //第三张图传给子组件的数据
          this.list3.push(
            departmentArr,
            depArr_new,
            depArr_old,
            data_3,
            maxTotalNumber
          );
        } else {
          this.list3 = [];
        }

        // console.log(this.list3,'data333');
        //第四张图传给子组件的数据
        this.list4 = this.data_4;
        //第五张图传给子组件的数据
        this.list5 = this.data_5;
        //第六张图给子组件的数据
        this.list6 = this.data_6;
        //第七张图给子组件的数据
        this.list7 = this.data_7;
        //第八张图给子组件的数据
        this.list8 = this.data_8;
        //第九张图给子组件的数据
        this.list9 = this.data_9;
        //第十张图给子组件的数据
        this.list10 = this.data_10;
      });
    },
    hanleQuery() {
      // console.log(this.value1,'value1');
      if(this.value1 && this.value1[0] && this.value1[1]){
        this.init({ startTime: this.value1[0], endTime: this.value1[1] });
      }else{
        this.$message.warning('请输入起始日期')
      }
    },
    clearData() {
      this.data_1 = [];
      this.data_2 = [];
      this.data_3 = [];
      this.data_4 = [];
      this.data_5 = [];
      this.data_6 = [];
      this.data_7 = [];
      this.data_8 = [];
      this.data_9 = [];
      this.data_10 = [];
    },
    formatThousands(num) {
      return num.toString().replace(/(\d)(?=(\d{3})+$)/g, "$1,");
    },
  },
};
</script>

<style scoped>
#chart {
  margin: 0 auto;
}

.date-picker {
  display: flex;
  justify-content: flex-start;
  margin: 0 20px 45px 0;
}
</style>
