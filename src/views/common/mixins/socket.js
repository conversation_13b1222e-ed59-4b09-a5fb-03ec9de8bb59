export default {
    data () {
        return {
            msWebSocket: null,
            excelId: 0
        }
    },
    destroyed() {
        this.msWebSocket.close()
    },
    methods: {
        // 初始化websocket
        initWebSocket(excelId) {
            this.excelId = excelId
            let wsuri = "ws://10.0.0.85:10013/imserver/" + excelId
            console.log(wsuri)
            this.msWebSocket = new WebSocket(wsuri)
            this.msWebSocket.onopen = this.websocketonopen;
            this.msWebSocket.onerror = this.websocketonerror;
            this.msWebSocket.onclose = this.websocketclose;
            this.msWebSocket.onmessage = this.websocketonmessage;
        },
        // 连接建立执行send方法
        websocketonopen() {
            this.websocketsend()
        },
        // 连接建立失败（重新建立连接）
        websocketonerror() {
            this.initWebSocket(this.excelId)
        },
        // 连接发送方法
        websocketsend(data) {
            this.msWebSocket.send(data)
        },
        // 数据接收
        websocketonmessage(e){
            console.log(e)
        },
        // 连接关闭
        websocketclose(e){  
            console.log('断开连接',e);
        },
    }
}
