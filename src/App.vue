<template>
  <div id="app">
    <router-view></router-view>
  </div>
</template>

<script>
import {mapGetters} from 'vuex';
// eslint-disable-next-line
import api from "./api/index"
export default {
  name: "app",
  data () {
      return {
        oldTime: new Date().getTime()
      }
  },
  computed: {
    ...mapGetters(["info"])
  },
  mounted() {
    window.addEventListener("click", this.clickOther);
  },
  watch: {
    $route: {
      handler: function(value) {
        this.$nextTick(() => {
          let userInfo = this.info
          // eslint-disable-next-line
          let params = {
            "operateType": '',
            "routeAddress": value.path,
            "userId": userInfo.userId,
            "userName": userInfo.userName
          }
          // this.record(params)
        })
      },
      deep: true
    }
  },
  methods: {
      clickOther (event) {
        let routePath = this.$route.path
        let btnText = ''
        if(event.target.getAttribute('type') == 'button' || event.target.parentNode.getAttribute('type') == 'button') {
          if(event.target.getAttribute('type')) {
            btnText = event.target.firstElementChild.innerText
          }
          if(event.target.parentNode.getAttribute('type')) {
            btnText = event.target.innerText;
          }
          let date = new Date()
          let userInfo = this.info
          // let now = `${date.getFullYear()}-${date.getMonth()}-${date.getDate()} ${date.getHours()}:${date.getMinutes()}:${date.getSeconds()}`
          if(date.getTime() > this.oldTime) {
            // eslint-disable-next-line
            let params = {
              "operateType": btnText,
              "routeAddress": routePath,
              "userId": userInfo.userId,
              "userName": userInfo.userName
            }
            // this.record(params)
            this.oldTime = date.getTime();
          }
        }
        
        // let params = {}
        // this.api.getBackstageTaskPage(params).then(response => {
        //   console.log(response, 'rrr')
        //   if (response.status !== 200) {
        //       this.PUBLIC_Methods.apiNotify(response.message || '请求出错', 'warning')
        //   }
        // }).catch(() => this.loading = false)
      },
      // eslint-disable-next-line
      record(params) {
        // api.insertSystemLog(params).then(() => {
        // })
      }
  }
};
</script>

<style>
#app {
  font-family: "Microsoft YaHei", "微软雅黑", Arial, "Helvetica Neue", Helvetica,
    "PingFang SC", "Hiragino Sans GB", sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #2c3e50;
  height: 100%;
  font-size: 12px;
  font-weight: normal;
  min-width: 1000px;
  position: relative;
}
</style>
