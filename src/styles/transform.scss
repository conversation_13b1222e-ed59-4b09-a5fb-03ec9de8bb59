/**
 * 路由页面左侧滑出
 */
 .fold-left-enter-active {
  animation-name: fold-left-in;
  animation-duration: .3s;
}
.fold-left-leave-active {
  animation-name: fold-left-out;
  animation-duration: .3s;
}
@keyframes fold-left-in {
  0% {
    transform: translate3d(100%, 0, 0);
  }
  100% {
    transform: translate3d(0, 0, 0);
  }
}
@keyframes fold-left-out {
  0% {
    transform: translate3d(0, 0, 0);
  }
  100% {
    transform: translate3d(-100%, 0, 0);
  }
}

/**
 * 主页面跳转动画
 */
.fade-transform-leave-active,
.fade-transform-enter-active {
  transition: all .3s;
}

.fade-transform-enter {
  opacity: 0;
  transform: translateX(-30px);
}

.fade-transform-leave-to {
  opacity: 0;
  transform: translateX(30px);
}
