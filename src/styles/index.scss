body {
  height: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif;
}
label {
  font-weight: 700;
}
html {
  height: 100%;
  box-sizing: border-box;
}
#app {
  height: 100%;
}
*,
*:before,
*:after {
  box-sizing: inherit;
}
a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  outline: none;
  text-decoration: none;
}
div:focus {
  outline: none;
}
.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
  }
}
p {padding: 0;margin: 0;}

.tac {text-align: center;}
.tal {text-align: left;}
.tar {text-align: right;}
.w100 {width: 100% !important;}
.fr {float: right;}
.fl {float: left;}
ul, li {list-style-type: none;}
.under-line {text-decoration: underline}
.inlineBlock { display: inline-block !important;}

.flex {
  display: flex;
}

.word-break {
  word-break:break-all;
  word-wrap:break-word;
}


.font-12 {
  font-size: 12px
}
.editor-info {
  text-align: left;
  margin-bottom: 5px;
  .info-li {
    display: inline-block;
    margin-right:55px;
    margin-bottom: 10px;
    .label {
      text-align: right;
      display: inline-block;
    }
  }
}

// 超出宽度省略号
.hidden-dot {
  overflow:hidden;//隐藏文字
   text-overflow:ellipsis;//显示 ...
   white-space:nowrap;//不换行
}

.no-data-page {
  text-align: center;
  img {
    max-width: 100%;
    width: 400px;
  }
  p {
    font-size: 16px;
    color: grey;
    margin: 20px 0;
    text-align: center;
  }
}

.category-label {
  width: 70px;
  font-size: 12px;
  // color: #4B74FF;
  color: #409EFF;
  text-align: right;
  margin-right: 15px;
  position: relative;
  top: 3px;
  font-weight: 600;
}

// 模块样式
@import './element.scss';
@import './whole.scss';

// 路由跳转动画
@import './transform.scss';

// 公共表单样式
@import './modules/input.scss';

// 公共表格样式
@import './modules/table.scss';

// 菜单样式
@import './modules/side.scss';
