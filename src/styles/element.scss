.form-tab {
  padding-bottom: 30px;
  // .el-tabs__nav-scroll {
    // padding: 0 40px !important;
  // }
  // .el-tabs__item:nth-child(2) {
  //   padding: 0 40px 0 20px !important;
  // }
  // .el-tabs__item {
  //   padding: 0 40px 0 40px;
  // }
  // .el-tabs__item:last-child {
  //   padding: 0 20px 0 40px !important;
  // }
}

.rule-form {
  .el-form-item__label {
    font-size: 12px !important;
    font-weight: 500 !important;
  }
  .el-form-item__content {
    text-align: left !important;
  }
}

.ms-radio {
  .el-radio {
    margin-right: 6px !important;
    margin-left: 20px !important;
    margin-bottom: 4px !important;  
  }
  .el-radio__label {
    padding-left: 2px !important;
  }
}

.el-checkbox__label {
  font-size: 12px !important;
  font-weight: 500 !important;
}

.el-radio__label {
  font-size: 12px !important;
}

.el-tabs__item {
  height: 30px !important;
  line-height: 25px !important;
}

.el-dialog__title {
  font-size: 16px !important;
  position: relative !important;
  top: -3px !important;
}

.el-dialog__header {
  text-align: left !important;
}

.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
}

.el-textarea.is-disabled .el-textarea__inner {
  color: rgba(96,98,102,.7) !important;
}
.el-input.is-disabled .el-input__inner {
  color: rgba(96,98,102,.7) !important;
}

.el-image-viewer__close {
  color: #ffffff;
}

.progress {
  padding-left: 100px;
  position: relative;
  width: 450px;
  margin-top: 10px;
  span {
      position: absolute;
      left: 0;
      top: 0px;
      color: #606266;
      display: inline-block;
      font-size: 14px;
  }
  .el-progress__text {
      font-size: 16px !important;
  }
}
.small-table-td .table-scope{
  padding: 7px 0;
}
