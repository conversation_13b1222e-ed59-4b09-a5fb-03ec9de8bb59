.table-style{
  &.el-table {
    border-left: 1px solid #EBEEF5;
    border-right: 1px solid #EBEEF5;
  }
  .el-table__fixed-right {
    height: 100% !important;
  }
  .el-table__expanded-cell {
    padding: 0px 0px !important;
  }
  .el-table__expand-icon {
    // top: -2px !important;
  }
  td {
    padding: 2px 0 !important;
  }
  .cell {
    // text-overflow: clip !important;
    display: flex;
    padding: 0 !important;
    padding-left: 10px !important;
  }
}
.high-row-none td{
  background-color: rgba(0,0,0,0) !important;
}
.notify-info {
  left: 200px !important;
  padding-top: 4px !important;
  padding-bottom: 4px !important;
}
.table-scope {
  text-align: center;
  display: inline-block;
  text-align: left;
  width: 100%;
  .status-label {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 6px;
    display: inline-block;
    position: relative;
    top: 2px;
  }
}

.table-operation {
  display: block;
  .scope-btn {
    margin: 8px 6px;
    padding: 4px 8px !important;
    font-size: 12px !important;
  }
}

.slot-search {
  float: left;
}
.slot-button {
  float: right;
  margin-bottom: 10px;
  text-align: left;
}

.slot-button-article {
  width: 100%;
  float: left;
  padding-top: 5px;
  .article-total {
    width: 227px !important;
    float: right;
  }
  .total-svg {
    width: 12px;
    height: 12px;
    color: #7695FF;
    position: relative;
    top: -4px;
    margin-left: 4px;
  }
}

.table-svg {
  .el-tooltip {
    min-width: 0 !important;
  }
}