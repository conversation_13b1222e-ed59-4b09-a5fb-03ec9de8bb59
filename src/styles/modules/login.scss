// @import '../variable.scss';

.login {
  width: 500px;
  padding: 50px;
  position: absolute;
  border-radius: 5px;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -60%);
  &-header {
    &-title {
      margin: 0 auto 30px;
      color: rgba(32, 39, 61, 1);
      font-size: 28px;
      letter-spacing: 2px;
      font-family: SourceHanSansSC-regular;
    }
    &-nav {
      font-size: 14px;
      text-align: left;
      margin-bottom: 25px;
      span {
        margin-right: 4px;
      }
    }
  }
  &-footer {
    font-size: 14px;
    nav {
      text-align: right;
    }
  }
  &-btn {
    margin-top: 50px;
    width: 100%;
  }
  a {
    // color: #4B74FF;
    color: #409EFF;
  }
}