//内页标题
.ms {
  &-title {
    font-weight: 600;
    font-size: 16px;
    line-height: 34px;
  }
}
//详情页样式
.show {
  text-align: left;
  &-title {
    font-size: 13px;
    font-weight: 600;
    line-height: 30px;
    padding-left: 5px;
    border-radius: 5px;
  }
  &-title:hover {
    background: #d3dce6;
  }
  //两行
  &-content {
    li {
      display: inline-block;
      float: left;
      width: 50%;
      line-height: 26px;
      border-bottom: 1px solid #e4e4e4;
      span {
        padding-left: 5px;
        display: inline-block;
        width: 100px;
        margin-right: 5px;
      }
    }
    //单行
    .one-line {
      width: 100%;
    }
  }
}
//菜单样式
// .ms-menu .el-tree-node__content {
//   height: 36px;
// }
// .operation-details {
//   min-height: 100px;
//   position: relative;
//   &-back {
//     position: fixed;
//     top: 70px;
//     right: 40px;
//     cursor: pointer;
//     z-index: 100;
//     font-size: 24px;
//   }
//   &-back:hover {
//     font-size: 26px;
//   }
// }
.single-line {
  max-width: 500px;
}

#app .el-dialog__body {
  padding: 0 20px 20px;
}
.show-dialog {
  text-align: left;
  &-title {
    font-size: 13px;
    line-height: 26px;
    margin-bottom: 5px;
    font-weight: bold;
  }
  &-content {
    padding: 0;
    margin: 0;
    li {
      line-height: 24px;
      border-bottom: 1px solid #e4e4e4;
      span:first-child {
        display: inline-block;
        width: 100px;
      }
    }
  }
}
