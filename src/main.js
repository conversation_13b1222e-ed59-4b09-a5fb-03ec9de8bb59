// The Vue build version to load with the `import` command
// (runtime-only or standalone) has been set in webpack.base.conf with an alias.
import * as filters from "./filters"
import * as directives from "./directive"
import Vue from "vue"
import App from "./App"
import router from "./router"
import store from "./store"
import api from "./api/index"
import validate from "./utils/validate"
import publicMethods from "./utils/public"

import "./components/index.js"
import "./assets/css/normalize.css"
import "./styles/index.scss"
import "./router/permission"
import "./plugins/element.js"
import "./icons"

Object.keys(filters).forEach((key) => {
    Vue.filter(key, filters[key])
})
Object.keys(directives.default).forEach((key) => {
    Vue.directive(key, directives.default[key])
})

Vue.config.productionTip = false
Vue.prototype.api = api
Vue.prototype.validate = validate
Vue.prototype.PUBLIC_Methods = publicMethods
Vue.prototype.cosUrl = 'img.medsci.cn'



/* eslint-disable no-new */
window.vm = new Vue({
    el: "#app",
    router,
    store,
    template: "<app/>",
    components: { App },
})
