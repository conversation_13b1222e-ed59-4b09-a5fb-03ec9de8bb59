import Vue from 'vue'
import Router from 'vue-router'
import resource from './module/resource'
import user from './module/user'
import info from './module/info'
import serve from './module/serve'
import dashboard from './module/dashboard'
import dashboardNew from './module/dashboardNew'
import recruitment from './module/recruitment'
import shop from './module/shop'
import task from './module/task'
import order from './module/order'
import operate from './module/operate'
import live from './module/live'
import video from './module/video'
import member from './module/member'
import operateActive from './module/operateActive'

Vue.use(Router)
const routeChildren = [...dashboardNew, ...resource, ...user, ...info, ...recruitment, ...serve, ...dashboard, ...shop, ...task, ...order, ...operate, ...live, ...video, ...member, ...operateActive]

export const constantRouterMap = [
  {
    path: '/login',
    component: () => import('../views/login/index'),
  },
  {
    path: '/404',
    component: () => import('../views/404/index')
  },
  {
    path: '/modifyPasswordEmail',
    component: () => import('../views/login/modify-password-email')
  },
  {
    path: '/modifyPasswordPhone',
    component: () => import('../views/login/modify-password-phone')
  },
  {
    path: '/resetPassword',
    component: () => import('../views/login/reset-password')
  },
  {
    path: '/',
    component: () => import('../views/home/<USER>'),
    redirect: '/homePage',
    children: routeChildren
  },
  { path: '*', redirect: '/404' },
  // ai对话内容
  {
   path: '/aiCon',
   name: 'aiCon',
   component: () => import('../views/info/imsl/imslList/aiCon')
 },
]

export default new Router({
  // mode: 'history',
  scrollBehavior: () => ({ y: 0 }),
  routes: constantRouterMap
})

