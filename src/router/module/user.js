const user = [
  {
    path: 'user',
    meta: { title: '用户管理' },
    component: () => import('../../views/user/user-manage/ms-user-manage')
  },
  {
    path: 'user-detail',
    meta: { title: '用户管理-用户详情' },
    component: () => import('../../views/user/user-manage/ms-user-detail')
  },
  // 生物谷
  {
    path: 'userBioon',
    meta: { title: '用户管理' },
    component: () => import('../../views/user/user-manage/ms-user-manage')
  },
  {
    path: 'userBioon-detail',
    meta: { title: '用户管理-用户详情' },
    component: () => import('../../views/user/user-manage/ms-user-detail')
  },
  {
    path: 'legalize',
    meta: { title: '用户认证' },
    component: () => import('../../views/user/user-legalize/ms-user-legalize')
  },
  {
    path: 'legalize-info',
    meta: { title: '用户认证-认证信息' },
    component: () => import('../../views/user/user-legalize/user-legalize-info')
  },
  {
    path: 'userLabel',
    meta: { title: '用户标签' },
    component: () => import('../../views/user/user-label/ms-user-label')
  },
  {
    path: 'userpassword',
    meta: { title: '修改密码' },
    component: () => import('../../views/user/ms-change-password')
  },
  {
    path: 'integral',
    meta: { title: '积分流水' },
    component: () => import('../../views/integral/ms-integral-manage')
  },
  {
    path: 'infoReview',
    meta: { title: '信息审核' },
    component: () => import('../../views/user/info-review/ms-info-review')
  },
  {
    path: 'loginLog',
    meta: { title: '登录日志' },
    component: () => import('../../views/user/login-log/ms-login-log')
  },
  {
    path: 'systemLog',
    meta: { title: '操作日志' },
    component: () => import('../../views/user/system-log/ms-system-log')
  },
]

export default user
