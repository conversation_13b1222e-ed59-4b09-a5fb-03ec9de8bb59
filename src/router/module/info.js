const info = [
  {
    path: 'workbenchArticle',
    meta: { title: '个人资讯' },
    component: () => import('../../views/info/article/ms-article-workbench')
  },
  {
    path: 'workbenchArticle-operation',
    meta: { title: '个人资讯-资讯操作' },
    component: () => import('../../views/info/article/workbenchArticle-operation')
  },
  // 生物谷个人咨询 bioon
  {
    path: 'workbenchArticleBioon',
    meta: { title: '个人资讯-生物谷' },
    component: () => import('../../views/info/article/ms-article-workbench')
  },
  {
    path: 'workbenchArticleBioon-operation',
    meta: { title: '个人资讯-生物谷-资讯操作' },
    component: () => import('../../views/info/article/workbenchArticle-operation')
  },
  {
    path: 'workbenchGuider',
    meta: { title: '个人指南' },
    component: () => import('../../views/info/guider/ms-guider-workbench')
  },
  {
    path: 'workbenchGuider-operation',
    meta: { title: '个人指南-指南操作' },
    component: () => import('../../views/info/guider/workbenchGuider-operation')
  },
  {
    path: 'article',
    meta: { title: '资讯' },
    component: () => import('../../views/info/article/ms-article-manage')
  },
  {
    path: 'article-operation',
    meta: { title: '资讯-资讯操作' },
    component: () => import('../../views/info/article/article-operation')
  },
  // 生物谷咨询 bioon
  {
    path: 'articleBioon',
    meta: { title: '资讯-生物谷' },
    component: () => import('../../views/info/article/ms-article-manage')
  },
  {
    path: 'articleBioon-operation',
    meta: { title: '资讯-生物谷-资讯操作' },
    component: () => import('../../views/info/article/article-operation')
  },
  {
    path: 'category',
    meta: { title: '分类管理' },
    component: () => import('../../views/info/category/ms-category-manage')
  },
  {
    path: 'guider',
    meta: { title: '指南' },
    component: () => import('../../views/info/guider/ms-guider-manage')
  },
  {
    path: 'guider-operation',
    meta: { title: '指南-指南操作' },
    component: () => import('../../views/info/guider/guider-operation')
  },
  {
    path: 'agree',
    meta: { title: '协议' },
    component: () => import('../../views/info/agree/ms-agree-manage')
  },
  {
    path: 'agree-operation',
    meta: { title: '协议-协议操作' },
    component: () => import('../../views/info/agree/agree-operation')
  },
  {
    path: 'journal',
    meta: { title: '期刊' },
    component: () => import('../../views/info/journal/ms-journal-manage')
  },
  {
    path: 'journal-operation',
    meta: { title: '期刊-期刊操作' },
    component: () => import('../../views/info/journal/journal-operation')
  },
  {
    path: 'calendar',
    meta: { title: '运营日历' },
    component: () => import('../../views/info/calendar/ms-calendar-manage')
  },
  {
    path: 'calendar-operation',
    meta: { title: '运营日历-笔记' },
    component: () => import('../../views/info/calendar/calendar-operation')
  },
  {
    path: 'message',
    meta: { title: '在线留言' },
    component: () => import('../../views/info/message/ms-message-manage')
  },
  {
    path: 'message-operation',
    meta: { title: '在线留言-查看留言' },
    component: () => import('../../views/info/message/message-operation')
  },
  {
    path: 'comments',
    meta: { title: '评论' },
    component: () => import('../../views/info/comments/ms-comments-manage')
  },
  {
    path: 'scale',
    meta: { title: '医学公式' },
    component: () => import('../../views/info/scale/ms-scale-manage')
  },
  {
    path: 'scale-operation',
    meta: { title: '医学公式-医学公式操作' },
    component: () => import('../../views/info/scale/scale-operation')
  },
  {
    path: 'associate',
    meta: { title: '协会组织' },
    component: () => import('../../views/info/associate/ms-associate-manage')
  },
  {
    path: 'pagekey',
    meta: { title: '页内关键词' },
    component: () => import('../../views/info/pagekey/ms-pagekey-manage')
  },
  {
    path: 'contentTag',
    meta: { title: '内容标签' },
    component: () => import('../../views/info/contentTag/index')
  },
  {
    path: 'contentTagRecord',
    meta: { title: '操作日志' },
    component: () => import('../../views/info/contentTag/record')
  },
  {
    path: 'topickey',
    meta: { title: '话题关键词' },
    component: () => import('../../views/info/topickey/ms-topickey-manage')
  },
  {
    path: 'keywordManagement',
    meta: { title: '关键词管理' },
    component: () => import('../../views/info/keywordManagement/index')
  },
  {
    path: 'video',
    meta: { title: '视频' },
    component: () => import('../../views/info/video')
  },
  {
    path: 'video-manage-single',
    meta: { title: '视频-单节课程' },
    component: () => import('../../views/info/video/single/video-single-operation')
  },
  {
    path: 'video-manage-series',
    meta: { title: '视频-系列课程' },
    component: () => import('../../views/info/video/series/video-series-operation')
  },
  {
    path: 'video-manage-series-course',
    meta: { title: '视频-系列课程管理' },
    component: () => import('../../views/info/video/series/video-series-course')
  },
  {
    path: 'video-manage-special',
    meta: { title: '视频-专题课程' },
    component: () => import('../../views/info/video/special/video-special-operation')
  },
  {
    path: 'video-manage-special-course',
    meta: { title: '视频-专题课程管理' },
    component: () => import('../../views/info/video/special/video-special-course')
  },
  {
    path: 'video-manage-failarmy',
    meta: { title: '视频-合集课程' },
    component: () => import('../../views/info/video/failarmy/video-failarmy-operation')
  },
  {
    path: 'video-manage-failarmy-course',
    meta: { title: '视频-合集课程管理' },
    component: () => import('../../views/info/video/failarmy/video-failarmy-course')
  },
  {
    path: 'video-manage-media',
    meta: { title: '视频-视频课程' },
    component: () => import('../../views/info/video/media/video-media-operation')
  },
  {
    path: 'live',
    meta: { title: '直播' },
    component: () => import('../../views/info/live/ms-live-manage')
  },
  {
    path: 'live-manage-apply',
    meta: { title: '直播-直播报名' },
    component: () => import('../../views/info/live/live-apply')
  },
  {
    path: 'live-manage-operation',
    meta: { title: '直播-直播管理' },
    component: () => import('../../views/info/live/live-operation')
  },
  {
    path: 'sci',
    meta: { title: 'SCI' },
    component: () => import('../../views/info/sci/ms-sci-manage')
  },
  {
    path: 'sci-manage-operation',
    meta: { title: 'SCI-SCI论文写作宝典' },
    component: () => import('../../views/info/sci/sci-operation')
  },
  {
    path: 'meeting',
    meta: { title: '会议' },
    component: () => import('../../views/info/meeting/ms-meeting-manage')
  },
  {
    path: 'meeting-operation',
    meta: { title: '会议-会议操作' },
    component: () => import('../../views/info/meeting/meeting-operation')
  },
  {
    path: 'advertplace',
    meta: { title: '广告位' },
    component: () => import('../../views/advert/advert-place/ms-advert-place')
  },
  {
    path: 'advert',
    meta: { title: '广告管理' },
    component: () => import('../../views/advert/advert-manage/ms-advert-manage')
  },
  {
    path: 'advert-operation',
    meta: { title: '广告管理-广告操作' },
    component: () => import('../../views/advert/advert-manage/advert-operation')
  },
  {
    path: 'sensitive',
    meta: { title: '敏感词库' },
    component: () => import('../../views/info/sensitive/ms-sensitive')
  },
  {
    path: 'appSystemMessage',
    meta: { title: 'app系统消息' },
    component: () => import('../../views/info/systemMessage/ms-system-message')
  },
  {
    path: 'pcSystemMessage',
    meta: { title: '系统消息' },
    component: () => import('../../views/info/systemMessagePc/ms-system-message')
  },
  // app系统消息
  {
    path: 'pcSystemMessage-operation',
    meta: { title: '系统消息-系统通知' },
    component: () => import('../../views/info/systemMessagePc/systemMessage-operation')
  },
  {
    path: 'chat',
    meta: { title: '私信管理' },
    component: () => import('../../views/info/chat/ms-chat-manage')
  },
  {
    path: 'chat-operation',
    meta: { title: '私信管理-私信操作' },
    component: () => import('../../views/info/chat/chat-operation')
  },
  {
    path: 'nsfc',
    meta: { title: '基金管理' },
    component: () => import('../../views/info/nsfc/ms-nsfc-manage')
  },
  {
    path: 'nsfc-edit',
    meta: { title: '基金管理-编辑基金' },
    component: () => import('../../views/info/nsfc/nsfc-edit')
  },
  {
    path: 'material',
    meta: { title: '素材管理' },
    component: () => import('../../views/info/cms/material/ms-material-manage')
  },
  {
    path: 'material-operation-video',
    meta: { title: '素材管理-视频管理' },
    component: () => import('../../views/info/cms/material/video-operation')
  },
  {
    path: 'medsciUser',
    meta: { title: '专家库——用户信息' },
    component: () => import('../../views/info/info-manage/ms-user-manage.vue')
  },
  {
    path: 'medsciUser-detail',
    meta: { title: '专家库——用户信息-用户详情' },
    component: () => import('../../views/info/info-manage/ms-user-detail.vue')
  },
  {
    path: 'form',
    meta: { title: '调研管理' },
    component: () => import('../../views/form/ms-form-manage')
  },
  {
    path: 'form-operation',
    meta: { title: '调研管理-调研操作' },
    component: () => import('../../views/form/form-operation')
  },
  {
    path: 'form-statistics',
    meta: { title: '调研管理-统计' },
    component: () => import('../../views/form/form-statistics')
  },
  {
    path: 'form-details',
    meta: { title: '调研管理-调研详情' },
    component: () => import('../../views/form/ms-form-details')
  },
  {
    path: 'material-operation-form',
    meta: { title: '素材管理-表单管理' },
    component: () => import('../../views/info/cms/material/form-operation')
  },
  {
    path: 'question',
    meta: { title: '题库管理' },
    component: () => import('../../views/info/question/ms-question-manage')
  },
  {
    path: 'question-operation',
    meta: { title: '题库管理-题库操作' },
    component: () => import('../../views/info/question/question-operation')
  },
  // 试题集
  {
    path: 'questionGather',
    meta: { title: '试题集管理' },
    component: () => import('../../views/info/questionGather/ms-questionGather-manage')
  },
  {
    path: 'questionGather-operation',
    meta: { title: '试题集管理-试题集操作' },
    component: () => import('../../views/info/questionGather/questionGather-operation')
  },
  // 试卷
  {
    path: 'paper',
    meta: { title: '试卷管理' },
    component: () => import('../../views/info/paper/ms-paper-manage')
  },
  {
    path: 'paper-operation',
    meta: { title: '试卷管理-试卷操作' },
    component: () => import('../../views/info/paper/paper-operation')
  },
  // 图标管理
  {
    path: 'icon',
    meta: { title: '图标管理' },
    component: () => import('../../views/info/icon/ms-icon-manage')
  },
  {
    path: 'icon-operation',
    meta: { title: '图标管理-图标操作' },
    component: () => import('../../views/info/icon/icon-operation')
  },
  // 处方列表
  {
    path: 'prescription',
    meta: { title: '药品处方' },
    component: () => import('../../views/info/prescription/ms-prescription-manage')
  },
  {
    path: 'prescription-operation',
    meta: { title: '药品处方-处方操作' },
    component: () => import('../../views/info/prescription/prescription-operation')
  },
  // 医讯达列表
  {
    path: 'yxd',
    meta: { title: '医讯达列表' },
    component: () => import('../../views/info/yxd/ms-yxd-manage')
  },
  {
    path: 'yxd-operation',
    meta: { title: '医讯达-医讯达操作' },
    component: () => import('../../views/info/yxd/yxd-operation')
  },
  {
    path: 'yxd-details',
    meta: { title: '医讯达-医讯达详情' },
    component: () => import('../../views/info/yxd/ms-yxd-details')
  },
  // 帖子管理
  {
    path: 'post',
    meta: { title: '帖子管理' },
    component: () => import('../../views/info/post/ms-post-manage')
  },
  // iMSL管理
  {
    path: 'imsl',
    meta: { title: 'iMSL管理' },
    component: () => import('../../views/info/imsl/imsl')
  },
  {
    path: 'mcpToken',
    meta: { title: 'mcpToken管理' },
    component: () => import('../../views/info/mcpToken/ms-mcpToken-manage')
  },
  {
    path: 'archive',
    meta: { title: '数据评估' },
    component: () => import('../../views/info/archive/ms-archive-manage')
  },
];

export default info;
