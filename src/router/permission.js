import router from './index'
import { getToken } from '@/utils/auth'
import store from './../store';
// import { Notification } from "element-ui";

const whiteList = ['/login', '/modifyPasswordEmail', '/modifyPasswordPhone', '/resetPassword']

router.beforeEach((to, from, next) => {
  if (getToken()) {
    if (to.path === '/login') {
      store.dispatch('LogOut').then(() => next('/login'))
    }
    else {
      // let permissionMenu = [...store.getters.permissionMenu, '/404', '/', '/homePage','/workbenchArticle','/workbenchArticle-operation',...whiteList]
      // if (permissionMenu.indexOf(to.path) !== -1) {
        next()
      // } else {
      //   next('/404?step=-2')
        // console.log(4040)
      // }
      // next()
    }
  }else {
    if (whiteList.indexOf(to.path) !== -1) {
      next()
    }
    else {
      store.dispatch('LogOut').then(() => next('/login'))
    }
  }
})

router.afterEach(() => {
})
