const getters = {

  update: state => state.user.update,
  menu: state => state.app.menu,
  submitData: state => state.setting.submitData,
  tableDropId: state => state.setting.tableDropVal,

  /**
   * 主侧边栏控制
   */
  topActive: state => state.app.topActive,
  collapse: state => state.app.collapse,
  side: state => state.app.side,
  open: state => state.app.open,
  active: state => state.app.active,

  /**
   * 全局编码设置
   */
  addressCode: state => state.code.addressCode, // 地址编码（省、市）
  code: state => state.code.code,
  localCode: state => state.code.localCode,

  /**
   * 登录信息
   */
  project: state => state.login.project, // 当前选中项目信息
  token: state => state.login.token, // 登录密钥
  ticket: state => state.login.ticket, // 登录ticket
  info: state => state.login.info,  // 账户信息
  projectId: state => state.login.info.projectId, // 当前项目id
  permissions: state => state.login.permissions,

  /**
   * 全局配置
   */
  pageSize: state => state.app.pageSize, // 列表每页显示条数
  listSearchParams: state => state.app.searchParams, // 列表查询传参
  serverInfo: state => state.app.serverInfo, // 访问服务URL
  sideMenu: state => state.app.sideMenu, // 权限-左侧展示菜单数组
  permissionMenu: state => state.app.permissionMenu, // 权限-路由菜单: route跳转使用
  permissionMenuId: state => state.app.permissionMenuId, // 权限-路由id: 获取下级按钮权限传参
  permissionBtnName: state => state.app.permissionBtnName, // 权限-按钮权限

  /**
   * 组件配置
   */
  editorImgArr: state => state.setting.editorImgArr, // 富文本内容图片url数组
  videoActive: state => state.setting.videoActive,  // 视频模块切换tab栏
  dashboardActive: state => state.setting.dashboardActive,  // 图表模块切换tab栏
  libraryImg: state => state.setting.libraryImg,  // 图片库图片数据
  infoListTip: state => state.setting.infoListTip, // 资讯、指南列表提示
  pageParams: state => state.setting.pageParams, // 页面内部跳转传参
  journalImportData: state => state.setting.journalImportData, // 期刊导入
  questionImportData: state => state.setting.questionImportData, // 题库导入
  districtId: state => state.setting.districtId, // 地区选中id (关联医院查询)
  chapterId: state => state.setting.chapterId, // 视频选中章节Id
  createByList: state => state.setting.createByList, // 后台创建人列表
  speakerList: state => state.setting.speakerList, // 讲师列表
  gatherId: state => state.setting.gatherId, // 试卷编辑中试题集id

  /**
   * 项目管理
   */
  userImportData: state => state.setting.userImportData,
  step: state => state.setting.userImportData.activeStep,
  userList: state => state.setting.userImportData.userList,
  roleList: state => state.setting.userImportData.roleList,
}
export default getters
