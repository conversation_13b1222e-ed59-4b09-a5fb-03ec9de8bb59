import codeData from "../data/code";
// import { getSession, setSession } from "../../utils/auth";
const setting = {
  state: {
    code: {}, // => 编码code
    localCode: codeData,
    addressCode: []
  },
  mutations: {
    SET_CODE: (state, data) => {
      state.code = {...state.code, ...data}
    },
    CLEAR_CODE_FILED: (state, keys) => {
      state.code[keys] = []
    },
    SET_ADDRESS_CODE: (state, data) => {
      state.addressCode = data
    }
  },
  actions: {
    // 设置用户code
    SetCode({commit}, data) {
      commit('SET_CODE', data)
    },
    // 清空对应字段code
    ClearCodeFiled({commit}, keys) {
      commit('CLEAR_CODE_FILED', keys)
    },
    // 设置地址code
    SetAddressCode({commit}, data) {
      commit('SET_ADDRESS_CODE', data)
    }
  }
}
export default setting
