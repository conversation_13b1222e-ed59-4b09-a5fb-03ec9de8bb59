import userApi from '../../api/module/userService'

import {
  getToken,
  setToken,
  removeToken,
  setSession,
  getSession,
  removeSession,
  setTicket,
  getTicket,
  removeTicket
} from '../../utils/auth'
import { Notification } from 'element-ui'

const loginInfo = {
  state: {
    token: getToken(),
    ticket: getTicket(),
    permissions: [],
    info: {},
    role: [],
    project: [],
    NoImgCaptchaAppld:'195962529',
    ImgCaptchaAppld:'198306945',
  },

  mutations: {
    SET_TOKEN: (state, token) => {
      state.token = token
      setToken(token)
    },
    SET_TICKET: (state, ticket) => {
      state.ticket = ticket
      setTicket(ticket)
    },
    SET_INFO: (state, info) => {
      // 设置角色级别
      let roleInfo = {
        ...info,
        roleLevel: info.roleEnglishName === 'inter article pt' || info.roleEnglishName === 'junior article pt' ? 3 : 1
      }
      setSession('user_info', JSON.stringify(roleInfo))
      state.info = roleInfo
    },
    SET_PERMISSIONS: (state, permissions) => {
      setSession('user_permissions', JSON.stringify(permissions))
      state.permissions = permissions
    },
    SET_ROLE: (state, role) => {
      setSession('user_role', JSON.stringify(role))
      state.role = role
    },
    SET_PROJECT: (state, project) => {
      setSession('user_project', JSON.stringify(project))
      state.project = project
    }
  },

  actions: {
    // 登录
    Login ({ commit }, loginForm) {
      return new Promise((resolve, reject) => {
        userApi.userLogin(loginForm).then(response => {
          console.log(loginForm, 'loginForm');
          // if (response.status === 200 && response.data.ticket) {
            if (response.status === 200) {
            let data = response.data.getLoginProjectResponses && response.data.getLoginProjectResponses[0] || []
            let role = data.getRoleLoginResponses && data.getRoleLoginResponses[0] || {}

            const userProject = response.data.getLoginProjectResponses || []
            const token = response.data && response.data.token && response.data.token.accessToken
            const userInfo = { ...data, ...role, id: data.userId }
            const ticket = response.data.ticket

            commit('SET_TOKEN', token)
            commit('SET_TICKET', ticket)
            commit('SET_INFO', userInfo)
            commit('SET_PROJECT', userProject)

            resolve('/')
          } else {
            Notification({
              title: response.status,
              message: response.message || '登录失败',
              type: 'warning',
              position: 'bottom-right'
            })
            reject(response)
          }
        }).catch(error => {
          reject(error)
        })
      })
    },
    // 获取用户信息 | 用户角色 | 用户项目
    GetInfo ({ commit }) {
      return new Promise((resolve, reject) => {
        const userInfo = JSON.parse(getSession('user_info'))
        const userRole = JSON.parse(getSession('user_role'))
        const userProject = JSON.parse(getSession('user_project'))
        if (userInfo) {
          commit('SET_INFO', userInfo)
          commit('SET_ROLE', userRole)
          commit('SET_PROJECT', userProject)
          resolve(userInfo)
        } else {
          reject('获取用户信息失败')
        }
      })
    },
    // 切换项目
    TabProject ({ commit }, data) {
      return new Promise((resolve) => {
        let roleInfo = data.getRoleLoginResponses && data.getRoleLoginResponses[0] || {}
        let tabInfo = {
          ...data,
          ...roleInfo,
          id: data.userId
        }
        commit('SET_INFO', tabInfo)
        resolve('/')
      })
    },
    // 切换角色
    TabRole ({ state, commit }, data) {
      let newUserInfo = { ...state.info, ...data }
      commit('SET_INFO', newUserInfo)
    },
    // 获取用户权限
    // GetPermissions ({ commit }) {
    //   return new Promise((resolve, reject) => {
    //     const permissionInfo = JSON.parse(getSession('user_permissions'))
    //     if (permissionInfo) {
    //       commit('SET_PERMISSIONS', permissionInfo)
    //       resolve(permissionInfo)
    //     } else {
    //       reject('获取用户权限失败')
    //     }
    //   })
    // },
    // 登出
    LogOut ({ commit }) {
      return new Promise((resolve) => {
        commit('SET_TOKEN', '')
        commit('SET_TICKET', '')
        commit('SET_INFO', {})
        commit('SET_PERMISSIONS', [])
        commit('SET_ROLE', [])
        commit('SET_PROJECT', [])

        removeSession()
        removeToken()
        removeTicket()
        resolve('/login')
      })
    }
  }
}

export default loginInfo
