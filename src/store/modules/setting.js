const setting = {
  state: {
    submitData: {},
    videoActive: '',
    dashboardActive: '',
    chapterId: 0, // => 选中的视频系列章节id
    tableDropVal: null, // => 列表下拉选中值
    editorImgArr: [],
    libraryImg: [],  // => 图片库图片
    infoListTip: {
      guiderTip: true,
      articleTip: true,
      liveTip: true
    },
    pageParams: {},
    journalImportData: {
      excelId: 0
    },
    questionImportData: {
      uuid: 0
    },
    districtId: 0, // 地区选中id，公司关联查询
    createByList: [], // 创建人列表
    userImportData: {
      activeStep: 0,
      userList: [],
      roleList: []
    },
    speakerList: [],
    gatherId: 0,
  },
  mutations: {
    SET_SUBMIT_DATA: (state, data) => {
      state.submitData = { ...data }
    },
    SET_VIDEO_ACTIVE: (state, data) => {
      state.videoActive = data
    },
    SET_DASHBOARD_ACTIVE: (state, data) => {
      state.dashboardActive = data
    },
    SET_CHAPTER_ID: (state, data) => {
      state.chapterId = data
    },
    SET_DROP_ID: (state, data) => {
      state.tableDropVal = data
    },
    SET_EDITOR_IMG_ARR: (state, data) => {
      state.editorImgArr = data
    },
    SET_LIBRARY_IMG: (state, data) => {
      state.libraryImg = data
    },
    SET_SPEAKER_LIST: (state, data) => {
      state.speakerList = data
    },
    SET_INFO_TIP: (state, data) => {
      state.infoListTip[data] = false
    },
    SET_PAGE_PARAMS: (state, data) => {
      state.pageParams = data
    },
    SET_JOURNAL_IMPORT: (state, data) => {
      state.journalImportData = { ...data }
    },
    SET_QUESTION_IMPORT: (state, data) => {
      state.questionImportData = { ...data }
    },
    SET_DISTRICT_ID: (state, data) => {
      state.districtId = data
    },
    SET_CREATEBY_LIST: (state, data) => {
      state.createByList = data
    },
    SET_IMPORT_STEP: (state, step) => {
      state.userImportData.activeStep = step
    },
    SET_IMPORT_USERLIST: (state, data) => {
      state.userImportData.userList = data || []
    },
    SET_IMPORT_ROLELIST: (state, data) => {
      state.userImportData.roleList = data || []
    },
    SET_GATHER_ID: (state, data) => {
      state.gatherId = data
    },
  },
  actions: {
    // 设置表单数据信息
    SetSubmitData({ commit }, data) {
      return new Promise(resolve => {
        commit('SET_SUBMIT_DATA', data)
        resolve()
      })
    },
    // 清空表单数据信息
    ClearSubmitData({ commit }, data) {
      return new Promise(resolve => {
        commit('SET_SUBMIT_DATA', data || {})
        resolve()
      })
    },
    // 设置视频列表选中值
    SetVideoActive({ commit }, data) {
      commit('SET_VIDEO_ACTIVE', data)
    },
    // 设置视频系列章节id
    SetChapterId({ commit }, data) {
      commit('SET_CHAPTER_ID', data)
    },
    // 设置图表tab选中值
    SetDashboardActive({ commit }, data) {
      commit('SET_DASHBOARD_ACTIVE', data)
    },
    // 重置列表下拉值
    SetDropId({ commit }, data) {
      commit('SET_DROP_ID', data)
    },
    // 设置富文本内容图片
    SetEditorImg({ commit }, data) {
      let editImgArr = null
      if (data && data.length > 0) {
        editImgArr = data.map(v => {
          // if (v.substr(v.lastIndexOf(".") + 1) !== 'webp') {
            return v
          // }
        })
      } else {
        editImgArr = []
      }
      commit('SET_EDITOR_IMG_ARR', editImgArr)
    },
    // 设置讲师库信息列表
    SetSpeakerList({ commit }, data) {
      commit('SET_SPEAKER_LIST', data)
    },
    // 设置图片库图片
    SetLibraryImg({ commit }, data) {
      commit('SET_LIBRARY_IMG', data)
    },
    // 设置资讯列表提示
    SetInfoTip({ commit }, data) {
      commit('SET_INFO_TIP', data)
    },
    // 设置页面跳转传参
    SetPageParams({ commit }, data) {
      commit('SET_PAGE_PARAMS', data)
    },
    // 设置期刊导入临时信息
    SetJournalImport({ commit }, data) {
      commit('SET_JOURNAL_IMPORT', data)
    },
    // 设置题库导入临时信息
    SetQuestionImport({ commit }, data) {
      commit('SET_QUESTION_IMPORT', data)
    },
    // 设置地区id
    SetDistrictId({ commit }, data) {
      commit('SET_DISTRICT_ID', data)
    },
    // 设置创建人信息
    SetCreatebyList({ commit }, data) {
      commit('SET_CREATEBY_LIST', data)
    },
    // 设置导入数据步骤数
    SetImportStep({ commit }, step) {
      commit('SET_IMPORT_STEP', step)
    },
    // 设置导入成员列表
    SetImportUser({ commit }, data) {
      commit('SET_IMPORT_USERLIST', data)
    },
    // 设置导入数据角色列表
    SetImportRole({ commit }, data) {
      commit('SET_IMPORT_ROLELIST', data)
    },
    // 设置试题集
    SetGatherId({ commit }, data) {
      commit('SET_GATHER_ID', data)
    },
  }
}
export default setting
