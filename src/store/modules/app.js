import { getSession, setSession } from "../../utils/auth";
// import validate from '../../utils/validate'
// import menuData from "../data/menu";

const app = {

  state: {
    side: getSession('side') ? getSession('side') : '',
    collapse: getSession('collapse') ? !!+getSession('collapse') : false,
    open: getSession('open') ? getSession('open') : [],
    active: getSession('active') ? getSession('active') : '',
    topActive: getSession('topActive') ? getSession('topActive') : '',
    menu: [],
    sideMenu: [],
    permissionMenu: getSession('permissionMenu') ? JSON.parse(getSession('permissionMenu')) : [],
    permissionMenuId: {},
    permissionBtnName: getSession('permissionBtnName') ? JSON.parse(getSession('permissionBtnName')) : null,
    pageSize: 20,
    searchParams: {},
    serverInfo: { // 服务请求信息 （跳转链接、图片请求接口）
    }
  },

  mutations: {
    SET_SIDE: (state, index) => {
      state.side = index
      setSession('side', index)
    },
    SET_COLLAPSE: (state) => {
      state.collapse = !state.collapse
      if(state.collapse) {
        setSession('collapse', 1)
      } else {
        setSession('collapse', 0)
      }
    },
    SET_OPEN: (state, index) => {
      state.open = index
      setSession('open', index)
    },
    SET_ACTIVE: (state, index) => {
      state.active = index
      setSession('active', index)
    },
    SET_MENU: (state, menu) => {
      state.menu = menu
    },
    SET_SIDEMENU: (state, menu) => {
      state.sideMenu = menu
      console.log(state.sideMenu,'kkkka');
    },
    SET_PAGESIZE: (state, size)=> {
      state.pageSize = size
    },
    SET_PERRMISSION_MENU: (state, data)=> {
      state.permissionMenu = data
      setSession('permissionMenu', JSON.stringify(data))
    },
    SET_PERRMISSION_MENU_ID: (state, data)=> {
      state.permissionMenuId = data
    },
    SET_PERMISSION_BTN_NAME: (state, data)=> {
      setSession('permissionBtnName',JSON.stringify(data))
      state.permissionBtnName = data
    },
    SET_SEARCH_PARAM: (state, data)=> {
      state.searchParams = data
    }
  },

  actions: {
    // 得到菜单
    GetMenu ({ commit }, menu) {
      // return new Promise((resolve) => {
        commit('SET_SIDEMENU', menu)
        // resolve(menuData)
      // })
    },
    // 设置权限菜单
    SetPermissionMenu ({ commit }, data) {
      commit('SET_PERRMISSION_MENU', data)
    },
    // 设置权限菜单id (获取列表按钮权限使用)
    SetPermissionMenuId ({ commit }, data) {
      commit('SET_PERRMISSION_MENU_ID', data)
    },
    // 设置按钮权限
    SetPermissionBtnName ({ state,commit }, data) {
      commit('SET_PERMISSION_BTN_NAME', {...state.permissionBtnName, ...data})
    },
    // 清空按钮权限
    clearPermissionBtn ({commit}) {
      commit('SET_PERMISSION_BTN_NAME', null)
    },
    SetTop ({ commit, state }, params) {
      return new Promise((resolve) => {
        state.menu.forEach(i => {
          if (i.name === params && i.children && i.children.length) {
            commit('SET_SIDEMENU', i.children)
            setSession('topActive', params)
            resolve()
          }
        })
      })
    },
    // 点击菜单
    SetSide ({ commit }, params) {
      return new Promise((resolve) => {
        commit('SET_SIDE', params)
        resolve(params.index)
      })
    },
    // 折叠菜单
    SetCollapse ({ commit }) {
      commit('SET_COLLAPSE')
    },
    // 选择菜单
    SetActive ({ commit }, params) {
      return new Promise((resolve) => {
        commit('SET_ACTIVE', params)
        resolve(params)
      })
    },
    // 打开菜单
    SetOpen ({ commit }, collapse) {
      return new Promise((resolve) => {
        commit('SET_OPEN', collapse)
        resolve()
      })
    },
    // 设置pagesize
    SetPagesize ({commit}, size) {
      commit('SET_PAGESIZE', size)
    },
    // 设置列表页面搜索指
    SetSearchParams ({commit}, data) {
      commit('SET_SEARCH_PARAM', data)  
    }
  }
}

export default app
