import Vue from 'vue'
import Vuex from 'vuex'
import app from './modules/app'
import login from './modules/login'
import user from './modules/user'
import setting from './modules/setting'
import code from './modules/code'
import getters from './getters'

Vue.use(Vuex)

const store = new Vuex.Store({
  modules: {
    app,
    login,
    user,
    setting,
    code
  },
  getters
})

export default store
