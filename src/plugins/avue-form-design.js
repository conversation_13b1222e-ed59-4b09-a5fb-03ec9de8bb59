import Vue from "vue"
import Avue from '@smallwei/avue';
import '@smallwei/avue/lib/index.css';
import AvueFormDesign from 'medsci-avue-form-design'
Vue.use(AvueFormDesign)
Vue.use(Avue, {
  ali: {
    region: 'oss-cn-shanghai',
    // endpoint: 'oss-cn-shanghai.aliyuncs.com',
    stsToken: '',
    // stsToken: 'CAIS9wF1q6Ft5B2yfSjIr5bXO/HRrr1L/ba5RBGCs1oWdrdGjYOfhjz2IH9JeXJvCOoXtvoxlG5R7/YSlqV2RpNeTk2Bc8p845MPLpJ6nDOG6aKP9rUhpMCPOwr6UmzWvqL7Z+H+U6muGJOEYEzFkSle2KbzcS7YMXWuLZyOj+wMDL1VJH7aCwBLH9BLPABvhdYHPH/KT5aXPwXtn3DbATgD2GM+qxsmsPnmnJzAskqD3QGqkLBJnemrfMj4NfsLFYxkTtK40NZxcqf8yyNK43BIjvwp0fEfpmad74nDXQkPukzWY/Cw99BjNw5yI7Y7F6NDvGISdXLKPAG5GoABa6ASTF4oMBA4wH1pw3JC4+zO8d2r68LdcxmHMtcYTgTM6E9Bu6z09xL8/eNEl+xKd9fKE9DB3HKeKjVv+09xqhN/JR53fumWIrRpdlmTh2LHm8CPkALHvlL96PgarZe6qj97HFR2uVXjvFU+s9vTlWKy2O0yKOk1T/ClN87+7LE=',
    accessKeyId: '',
    accessKeySecret: '',
    bucket: 'medsci-open-files',
  }
})

// Vue.use(Avue);
