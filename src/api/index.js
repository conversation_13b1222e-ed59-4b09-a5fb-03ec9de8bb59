// import login from './module/login'
import userService from './module/userService'
import tagService from './module/tagService'
import infoService from './module/infoService'
import searchService from './module/searchService'
import mgrService from './module/mgrService'
import dashboardService from './module/dashboardService'
import liveService from './module/liveService'
import cmsService from './module/cmsService'
import examService from './module/examService'
import formService from './module/formService'
import operateActive from './module/operateActive'
import medHuiBaoTaskService from './module/medHuiBaoTaskService'

const api = {}

Object.keys(userService).forEach(key => {
  api[key] = userService[key]
})

Object.keys(tagService).forEach(key => {
  api[key] = tagService[key]
})

Object.keys(infoService).forEach(key => {
  api[key] = infoService[key]
})

Object.keys(searchService).forEach(key => {
  api[key] = searchService[key]
})

Object.keys(mgrService).forEach(key => {
  api[key] = mgrService[key]
})

Object.keys(dashboardService).forEach(key => {
  api[key] = dashboardService[key]
})

Object.keys(liveService).forEach(key => {
  api[key] = liveService[key]
})

Object.keys(cmsService).forEach(key => {
  api[key] = cmsService[key]
})

Object.keys(examService).forEach(key => {
  api[key] = examService[key]
})

Object.keys(formService).forEach(key => {
  api[key] = formService[key]
})

Object.keys(operateActive).forEach(key => {
  api[key] = operateActive[key]
})

Object.keys(medHuiBaoTaskService).forEach(key => {
  api[key] = medHuiBaoTaskService[key]
})

export default api
