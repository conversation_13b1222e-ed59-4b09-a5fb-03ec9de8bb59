import request from '../../utils/request'

export default {
 
  // 页面PV排行查询
  totalPvLeaderboard: params => {
    return request.post(`/report-service/totalPvLeaderboard`, params)
  },
  // 页面UV排行查询
  totalUvLeaderboard: params => {
    return request.post(`/report-service/totalUvLeaderboard`, params)
  },
  // uv查询
  totalUniqueVisitor: params => {
    return request.post(`/report-service/totalUniqueVisitor`, params)
  },
  // uv下载
  totalUniqueVisitorDownload: params => {
    return request.post(`/report-service/totalUniqueVisitorDownload`, params)
  },
  // pv查询
  totalPageView: params => {
    return request.post(`/report-service/totalPageView`, params)
  },
  // pv下载
  totalPageViewDownload: params => {
    return request.post(`/report-service/totalPageViewDownload`, params)
  },
  // 月活查询
  totalMonthUv: params => {
    return request.post(`/report-service/totalMonthUv`, params)
  },
  // 订单统计查询
  totalOrderFind: params => {
    return request.post(`/report-service/totalOrder`, params)
  },
  // 订单成交占比查询
  totalOrderProportionFind: params => {
    return request.post(`/report-service/totalOrderProportion`, params)
  },

  // 每日更新个人信息人数下载
  updatedPersonalInformationPerDayDownload: params => {
    return request.post(`/report-service/updatedPersonalInformationPerDayDownload`, params)
  },
  // 每日更新个人信息人数
  updatedPersonalInformationPerDay: params => {
    return request.post(`/report-service/updatedPersonalInformationPerDay`, params)
  },
  // 更新个人信息用户科室分布
  updatedPersonalDepartmentPerDay: params => {
    return request.post(`/report-service/updatedPersonalDepartmentPerDay`, params)
  },
  // 更新个人信息用户科室分布下载
  updatedPersonalDepartmentPerDayDownload: params => {
    return request.post(`/report-service/updatedPersonalDepartmentPerDayDownload`, params)
  },
  // 每日注册用户总数
  totalRegisteredUsersPerDay: params => {
    return request.post(`/report-service/totalRegisteredUsersPerDay`, params)
  },
  // 每日注册用户总数下载
  totalRegisteredUsersPerDayDownload: params => {
    return request.post(`/report-service/totalRegisteredUsersPerDayDownload`, params)
  },
  // 注册用户省市层级分布
  registeredUsersProvincialDistribution: params => {
    return request.post(`/report-service/registeredUsersProvincialDistribution`, params)
  },
  // 注册用户职称分布
  registeredUsersProfessionalDistribution: params => {
    return request.post(`/report-service/registeredUsersProfessionalDistribution`, params)
  },
  // 注册用户医院等级分布（除去企业、学生两类用户）
  registeredUsersHospitalLevelsDistribution: params => {
    return request.post(`/report-service/registeredUsersHospitalLevelsDistribution`, params)
  },
  // 注册用户医院等级分布
  registeredUsersHospitalLevelsDistributionAll: params => {
    return request.post(`/report-service/registeredUsersHospitalLevelsDistributionAll`, params)
  },
  // 注册用户科室分布(除去研究员、教学、学生、企业四类用户)
  registeredUsersDepartmentDistribution: params => {
    return request.post(`/report-service/registeredUsersDepartmentDistribution`, params)
  },
  // 注册用户科室分布
  registeredUsersDepartmentDistributionAll: params => {
    return request.post(`/report-service/registeredUsersDepartmentDistributionAll`, params)
  },
  // 注册用户科室分布下载
  registeredUsersDepartmentDistributionAllDownload: params => {
    return request.post(`/report-service/registeredUsersDepartmentDistributionAllDownload`, params)
  },
  // 新增用户新增渠道占比
  registeredUsersChannelsDistribution: params => {
    return request.post(`/report-service/registeredUsersChannelsDistribution`, params)
  },
  // 兼职报表
  partTimeReport: params => {
    return request.post(`/report-service/showStatistic`, params)
  }
}