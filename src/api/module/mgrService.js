import request from '../../utils/request'
import store from '../../store/index'

export default {
  /**
   * 项目管理
   */
  // 修改应用
  addMedsciProject: params => {
    return request.post(`/paas-mgr-center-service/medsciProject/addMedsciProject`,
      { ...params, userId: store.getters.info && store.getters.info.userId, username: store.getters.info && store.getters.info.userName }
    )
  },
  // 新增子级项目
  addChildrenProject: params => {
    return request.post(`/paas-mgr-center-service/medsciProject/addChildrenProject`,
      { ...params, userId: store.getters.info && store.getters.info.userId, username: store.getters.info && store.getters.info.userName }
    )
  },
  // 查询项目子项目列表
  getChildProjectList: params => {
    return request.post(`/paas-mgr-center-service/medsciProject/getChildProjectList`, params)
  },
  // 删除项目
  deleteMedsciProjectById: params => {
    return request.post(`/paas-mgr-center-service/medsciProject/deleteMedsciProjectById`, params)
  },
  // 查询公司分页
  getMedsciCompanyNamePage: params => {
    return request.post(`/paas-mgr-center-service/medsciProject/getMedsciCompanyNamePage`, params)
  },
  // 查询项目详情
  getMedsciProjectDetail: params => {
    return request.post(`/paas-mgr-center-service/medsciProject/getMedsciProjectDetail`, params)
  },
  // 查询项目分页
  getMedsciProjectPage: params => {
    return request.post(`/paas-mgr-center-service/medsciProject/getMedsciProjectPage`, params)
  },
  // 修改项目
  updateMedsciProject: params => {
    return request.post(`/paas-mgr-center-service/medsciProject/updateMedsciProject`,
      { ...params, userId: store.getters.info && store.getters.info.userId, username: store.getters.info && store.getters.info.userName }
    )
  },
  // 2021/2/23新增 查询站点/项目分页
  getProjectPage: params => {
    return request.post(`/paas-mgr-center-service/medsciProject/getProjectPage`, params)
  },

  /**
   * 项目菜单查询
   */
  // 查询导入项目菜单关系树形列表
  getImportProjectMenuTreeList: params => {
    return request.post(`/paas-mgr-center-service/medsciProjectMenu/getImportProjectMenuTreeList`, params)
  },
  // 查询项目菜单关系树形列表
  getProjectMenuTreeList: params => {
    return request.post(`/paas-mgr-center-service/medsciProjectMenu/getProjectMenuTreeList`, params)
  },
  // 保存项目菜单关系列表
  saveProjectMenuList: params => {
    return request.post(`/paas-mgr-center-service/medsciProjectMenu/saveProjectMenuList`, params)
  },
  // 启用/禁用项目菜单关系
  updateProjectMenuStatus: params => {
    return request.post(`/paas-mgr-center-service/medsciProjectMenu/updateProjectMenuStatus`, params)
  },
  // 删除项目菜单关系
  // getImportProjectMenuTreeList: params => {
  //   return request.post(`/paas-mgr-center-service/medsciProjectMenu/getImportProjectMenuTreeList`, params)
  // },
  // 批量删除项目菜单关系
  deleteBatchProjectMenu: params => {
    return request.post(`/paas-mgr-center-service/medsciProjectMenu/deleteBatchProjectMenu`, params)
  },

  /**
   * 项目角色配置
   */
  // 新增项目角色
  addMedsciRole: params => {
    return request.post(`/paas-mgr-center-service/medsciRole/addMedsciRole`,
      { ...params, userId: store.getters.info && store.getters.info.userId, username: store.getters.info && store.getters.info.userName }
    )
  },
  // 删除角色
  deleteMedsciRole: params => {
    return request.post(`/paas-mgr-center-service/medsciRole/deleteMedsciRole`, params)
  },
  // 查询项目角色详情
  getMedsciRoleDetail: params => {
    return request.post(`/paas-mgr-center-service/medsciRole/getMedsciRoleDetail`, params)
  },
  // 查询项目下角色字典列表
  getMedsciRoleDicList: params => {
    return request.post(`/paas-mgr-center-service/medsciRole/getMedsciRoleDicList`, params)
  },
  // 查询项目角色分页
  getMedsciRolePage: params => {
    return request.post(`/paas-mgr-center-service/medsciRole/getMedsciRolePage`, params)
  },
  // 修改项目角色
  updateMedsciRole: params => {
    return request.post(`/paas-mgr-center-service/medsciRole/updateMedsciRole`,
      { ...params, userId: store.getters.info && store.getters.info.userId, username: store.getters.info && store.getters.info.userName }
    )
  },
  // 查询角色用户列表
  getMedsciUserRolePage: params => {
    return request.post(`/paas-mgr-center-service/medsciUserRole/getMedsciUserRolePage`, params)
  },

  // 查询角色用户列表
  getMedsciUserRolePageV2: params => {
    return request.post(`/paas-mgr-center-service/medsciUserRole/getMedsciUserRolePageV2`, params)
  },


  /**
  * 项目菜单配置
  */
  // 新增子菜单/权限
  addChildMedsciMenu: params => {
    return request.post(`/paas-mgr-center-service/medsciMenu/addChildMedsciMenu`,
      { ...params, userId: store.getters.info && store.getters.info.userId, username: store.getters.info && store.getters.info.userName }
    )
  },
  // 新增一级菜单
  addFirstMedsciMenu: params => {
    return request.post(`/paas-mgr-center-service/medsciMenu/addFirstMedsciMenu`,
      { ...params, userId: store.getters.info && store.getters.info.userId, username: store.getters.info && store.getters.info.userName }
    )
  },
  // 批量删除
  deleteBatchMedsciMenuByIds: params => {
    return request.post(`/paas-mgr-center-service/medsciMenu/deleteBatchMedsciMenuByIds`, params)
  },
  // 查询菜单详情
  getMedsciMenuDetail: params => {
    return request.post(`/paas-mgr-center-service/medsciMenu/getMedsciMenuDetail`, params)
  },
  // 查询菜单列表
  getMedsciMenuList: params => {
    return request.post(`/paas-mgr-center-service/medsciMenu/getMedsciMenuList`, params)
  },
  // 启用/禁用菜单
  qjMedsciMenu: params => {
    return request.post(`/paas-mgr-center-service/medsciMenu/qjMedsciMenu`,
      { ...params, userId: store.getters.info && store.getters.info.userId, username: store.getters.info && store.getters.info.userName }
    )
  },
  // 更新菜单
  updateMedsciMenu: params => {
    return request.post(`/paas-mgr-center-service/medsciMenu/updateMedsciMenu`,
      { ...params, userId: store.getters.info && store.getters.info.userId, username: store.getters.info && store.getters.info.userName }
    )
  },
  // 查询平台菜单列表
  getMedsciPlatformMenuList: params => {
    return request.post(`/paas-mgr-center-service/medsciMenu/getMedsciPlatformMenuList`, params)
  },

  /**
   * 项目成员配置
   */
  // 项目成员角色添加
  addMedsciUserRole: params => {
    return request.post(`/paas-mgr-center-service/medsciUserRole/addMedsciUserRole`,
      { ...params, userId: store.getters.info && store.getters.info.userId, username: store.getters.info && store.getters.info.userName }
    )
  },
  // 用户和角色关系添加
  addUserAndRole: params => {
    return request.post(`/paas-mgr-center-service/medsciUserRole/addUserAndRole`, params)
  },
  // 查询该项目成员下的角色信息
  getProjectUserAndRoleDicList: params => {
    return request.post(`/paas-mgr-center-service/medsciUserRole/getProjectUserAndRoleDicList`, params)
  },
  // 项目成员角色编辑
  updateMedsciUserRole: params => {
    return request.post(`/paas-mgr-center-service/medsciUserRole/updateMedsciUserRole`,
      { ...params, userId: store.getters.info && store.getters.info.userId, username: store.getters.info && store.getters.info.userName }
    )
  },
  // 用户和角色关系编辑
  updateUserAndRole: params => {
    return request.post(`/paas-mgr-center-service/medsciUserRole/updateUserAndRole`, params)
  },

  /**
   * 项目成员
   */
  // 平台库导入-批量添加项目成员及角色
  addBatchOtherProjectUserAndRole: params => {
    return request.post(`/paas-mgr-center-service/medsciProjectUser/addBatchOtherProjectUserAndRole`,
      { ...params, userId: store.getters.info && store.getters.info.userId, username: store.getters.info && store.getters.info.userName }
    )
  },
  // 批量删除项目成员
  deleteBatchMedsciProjectUser: params => {
    return request.post(`/paas-mgr-center-service/medsciProjectUser/deleteBatchMedsciProjectUser`, params)
  },
  // 删除项目成员
  deleteMedsciProjectUser: params => {
    return request.post(`/paas-mgr-center-service/medsciProjectUser/deleteMedsciProjectUser`, params)
  },
  // 查询项目成员关系详情
  getMedsciProjectUserDetail: params => {
    return request.post(`/paas-mgr-center-service/medsciProjectUser/getMedsciProjectUserDetail`, params)
  },
  // 查询项目成员分页
  getMedsciProjectUserPage: params => {
    return request.post(`/paas-mgr-center-service/medsciProjectUser/getMedsciProjectUserPage`, params)
  },
  // 平台库导入-选择成员分页查询
  getPtkProjectUserPage: params => {
    return request.post(`/paas-mgr-center-service/medsciProjectUser/getPtkProjectUserPage`, params)
  },
  //启用/禁用项目成员关系
  updateProjectUserStatus: params => {
    return request.post(`/paas-mgr-center-service/medsciProjectUser/updateProjectUserStatus`, params)
  },

  // 测试服务
  getMedsciRoleAndMenuList: params => {
    return request.post(`/paas-mgr-center-service/medsciRoleMenu/getMedsciRoleAndMenuList`, params)
  },
  saveMedsciRoleAndMenu: params => {
    return request.post(`/paas-mgr-center-service/medsciRoleMenu/saveMedsciRoleAndMenu`,
      { ...params, userId: store.getters.info && store.getters.info.userId, username: store.getters.info && store.getters.info.userName }
    )
  },



  // 查询招聘岗位的详情
  getRecruitmentJob: params => {
    return request.post(`/medsci-mgr-center-service/recruitmentJob/getRecruitmentJob`, params)
  },
  // 查询招聘岗位列表及模糊查询
  getRecruitmentJobPage: params => {
    return request.post(`/medsci-mgr-center-service/recruitmentJob/getRecruitmentJobPage`, params)
  },
  // 岗位搜索
  getRecruitmentJobTitle: params => {
    return request.post(`/medsci-mgr-center-service/recruitmentJob/getRecruitmentJobTitle`, params)
  },
  // 新建招聘岗位
  insertRecruitmentJob: params => {
    return request.post(`/medsci-mgr-center-service/recruitmentJob/insertRecruitmentJob`, params)
  },
  // 修改招聘岗位
  updateRecruitmentJob: params => {
    return request.post(`/medsci-mgr-center-service/recruitmentJob/updateRecruitmentJob`, params)
  },
  // 批量去审，批量审核，批量删除
  updateRecruitmentJobBatch: params => {
    return request.post(`/medsci-mgr-center-service/recruitmentJob/updateRecruitmentJobBatch`, params)
  },
  // 删除人才应聘信息
  delRecruitmentUser: params => {
    return request.post(`/medsci-mgr-center-service/recruitmentUser/delRecruitmentUser`, params)
  },
  // 查询应聘人才列表及模糊查询
  getRecruitmentUserPage: params => {
    return request.post(`/medsci-mgr-center-service/recruitmentUser/getRecruitmentUserPage`, params)
  },

  /**
   * 会议
   */
  // 相关会议
  RelatedMeetings: params => {
    return request.post(`/medsci-mgr-center-service/medsciMeeting/RelatedMeetings`, params)
  },
  // 批量审核，去审，删除会议
  dealMedsciMeetingBatchIds: params => {
    return request.post(`/medsci-mgr-center-service/medsciMeeting/dealMedsciMeetingBatchIds`, params)
  },
  // 根据id查会议详情
  getMedsciMeetingById: params => {
    return request.post(`/medsci-mgr-center-service/medsciMeeting/getMedsciMeetingById`, params)
  },
  // 后台会议列表查询及分页
  getMedsciMeetingPage: params => {
    return request.post(`/medsci-mgr-center-service/medsciMeeting/getMedsciMeetingPage`, params)
  },
  // 添加会议
  saveMedsciMeetingAdministrator: params => {
    return request.post(`/medsci-mgr-center-service/medsciMeeting/saveMedsciMeetingAdministrator`, params)
  },
  // 根据id修改会议
  updateMedsciMeetingById: params => {
    return request.post(`/medsci-mgr-center-service/medsciMeeting/updateMedsciMeetingById`, params)
  },
  // 资讯显示会议栏目
  accordingToSection: params => {
    return request.post(`/medsci-mgr-center-service/medsciMeeting/accordingToSection`, params)
  },
  // 批量设置会议分类
  batchSettingMeetingCategory: params => {
    return request.post(`/medsci-mgr-center-service/medsciMeeting/batchSettingMeetingCategory`, params)
  },

  /**
   * 广告位
   */
  // 删除广告位
  delAdvertisingSpaceById: params => {
    return request.post(`/medsci-mgr-center-service/advertisementSpace/delAdvertisingSpaceById`, params)
  },
  // 根据id查询广告位详情
  getAdvertisementSpaceById: params => {
    return request.post(`/medsci-mgr-center-service/advertisementSpace/getAdvertisementSpaceById`, params)
  },
  // 根据广告位名模糊匹配广告位列表
  getAdvertisementSpaceListByName: params => {
    return request.post(`/medsci-mgr-center-service/advertisementSpace/getAdvertisementSpaceListByName`, params)
  },
  // 查询广告位分页
  getAdvertisementSpacePage: params => {
    return request.post(`/medsci-mgr-center-service/advertisementSpace/getAdvertisementSpacePage`, params)
  },
  // 新增广告位
  saveAdvertisementSpace: params => {
    return request.post(`/medsci-mgr-center-service/advertisementSpace/saveAdvertisementSpace`, params)
  },
  // 修改广告位
  updateAdvertisementSpaceById: params => {
    return request.post(`/medsci-mgr-center-service/advertisementSpace/updateAdvertisementSpaceById`, params)
  },

  /**
   * 广告
   */
  // 批量删除广告信息
  AdvertisementByBatchId: params => {
    return request.post(`/medsci-mgr-center-service/advertisement/AdvertisementByBatchId`, params)
  },
  // 批量处理 审核去审
  dealAdvertisementByBatchId: params => {
    return request.post(`/medsci-mgr-center-service/advertisement/dealAdvertisementByBatchId`, params)
  },
  // 根据ID查询广告详情
  getAdvertisementById: params => {
    return request.post(`/medsci-mgr-center-service/advertisement/getAdvertisementById`, params)
  },
  // 查询广告列表
  getAdvertisementList: params => {
    return request.post(`/medsci-mgr-center-service/advertisement/getAdvertisementList`, params)
  },
  // 添加广告信息
  saveAdvertisement: params => {
    return request.post(`/medsci-mgr-center-service/advertisement/saveAdvertisement`, params)
  },
  // 根据ID修改广告信息
  updateAdvertisementById: params => {
    return request.post(`/medsci-mgr-center-service/advertisement/updateAdvertisementById`, params)
  },
  // 第二版广告分页
  getAdvertisementPage: params => {
    return request.post(`/medsci-mgr-center-service/advertisement/getAdvertisementPage`, params)
  },

  /**
   * 运营日历
   */
  // 根据ID删除运营事件
  delOperaCalen: params => {
    return request.post(`/medsci-mgr-center-service/operatingCalendar/delOperaCalen`, params)
  },
  // 根据ID查询事件的详情数据
  getOperaCalen: params => {
    return request.post(`/medsci-mgr-center-service/operatingCalendar/getOperaCalen`, params)
  },
  // 查询运营日历列表及模糊查询
  getOperaCalenPage: params => {
    return request.post(`/medsci-mgr-center-service/operatingCalendar/getOperaCalenPage`, params)
  },
  // 添加事件
  insertOperaCalen: params => {
    return request.post(`/medsci-mgr-center-service/operatingCalendar/insertOperaCalen`, params)
  },
  // 根据ID修改运营事件
  updateOperaCalen: params => {
    return request.post(`/medsci-mgr-center-service/operatingCalendar/updateOperaCalen`, params)
  },
  // 近期事件列表
  getOperatingCalendarRecent: params => {
    return request.post(`/medsci-mgr-center-service/operatingCalendar/getOperatingCalendarRecent`, params)
  },
  // 近期事件统计
  getOperatingCalendarRecentCount: params => {
    return request.post(`/medsci-mgr-center-service/operatingCalendar/getOperatingCalendarRecentCount`, params)
  },

  /**
   * 留言内容
   */
  // 删除在线留言
  delLeaveMessage: params => {
    return request.post(`/medsci-mgr-center-service/leaveMessage/delLeaveMessage`, params)
  },
  // 查询在线留言的详情
  getLeaveMessage: params => {
    return request.post(`/medsci-mgr-center-service/leaveMessage/getLeaveMessage`, params)
  },
  // 在线留言记录列表和模糊查询
  getLeaveMessagePage: params => {
    return request.post(`/medsci-mgr-center-service/leaveMessage/getLeaveMessagePage`, params)
  },
  // 多项目-在线留言记录列表和模糊查询
  getMultiLeaveMessagePage: params => {
    return request.post(`/medsci-mgr-center-service/leaveMessage/getPage`, params)
  },
  // 在线留言回复
  updateReplyLeaveMessage: params => {
    return request.post(`/medsci-mgr-center-service/leaveMessage/updateReplyLeaveMessage`, params)
  },

  /**
   * 量表管理 
   */
  // 量表删除，批量删除
  delToolsBatch: params => {
    return request.post(`/medsci-mgr-center-service/medicalformula/delMedicalFormulaBatch`, params)
  },
  // 查询量表的详情
  getTools: params => {
    return request.post(`/medsci-mgr-center-service/medicalformula/getMedicalFormula`, params)
  },
  // 查询量表列表及模糊查询
  getToolsPage: params => {
    return request.post(`/medsci-mgr-center-service/medicalformula/getMedicalFormulaPage`, params)
  },
  // 新增量表
  insertTools: params => {
    return request.post(`/medsci-mgr-center-service/medicalformula/insertMedicalFormula`, params)
  },
  // 修改量表
  updateTools: params => {
    return request.post(`/medsci-mgr-center-service/medicalformula/updateMedicalFormula`, params)
  },
  // 量表批量去审，批量审核
  updateToolsBatch: params => {
    return request.post(`/medsci-mgr-center-service/medicalformula/updateMedicalFormulaBatch`, params)
  },

  /**
   * 论文验证 
   */
  // 添加证书
  addCertificate: params => {
    return request.post(`/medsci-mgr-center-service/thesis-certificate/create`, params)
  },
  // 分页列表
  getCertificatePage: params => {
    return request.post(`/medsci-mgr-center-service/thesis-certificate/getPage`, params)
  },
  // 查看证书
  showCertificate: id => {
    return request.get(`/medsci-mgr-center-service/thesis-certificate/show/${id}`)
  },

  /**
   * 公司管理
   */
  delCompanysBatch: params => {
    return request.post(`/medsci-mgr-center-service/companys/delCompanysBatch`, params)
  },
  // 查询公司信息的详情
  getCompanys: params => {
    return request.post(`/medsci-mgr-center-service/companys/getCompanys`, params)
  },
  // 查询公司信息列表及模糊查询
  getCompanysPage: params => {
    return request.post(`/medsci-mgr-center-service/companys/getCompanysPage`, params)
  },
  // 新建公司信息
  insertCompanys: params => {
    return request.post(`/medsci-mgr-center-service/companys/insertCompanys`, params)
  },
  // 修改公司信息
  updateCompanys: params => {
    return request.post(`/medsci-mgr-center-service/companys/updateCompanys`, params)
  },
  // 去审，审核
  updateCompanysStatus: params => {
    return request.post(`/medsci-mgr-center-service/companys/updateCompanysStatus`, params)
  },

  /**
   * 产品服务
   */
  // 删除，批量删除产品服务信息
  delProductBatch: params => {
    return request.post(`/medsci-mgr-center-service/product/delProductBatch`, params)
  },
  // 查询产品服务信息列表及模糊查询
  getProducPage: params => {
    return request.post(`/medsci-mgr-center-service/product/getProducPage`, params)
  },
  // 查询产品服务信息的详情
  getProduct: params => {
    return request.post(`/medsci-mgr-center-service/product/getProduct`, params)
  },
  // 新建产品服务信息
  insertProduct: params => {
    return request.post(`/medsci-mgr-center-service/product/insertProduct`, params)
  },
  // 修改产品服务信息
  updateProduct: params => {
    return request.post(`/medsci-mgr-center-service/product/updateProduct`, params)
  },
  // 去审，审核，推荐，取消推荐
  updateProductBatch: params => {
    return request.post(`/medsci-mgr-center-service/product/updateProductBatch`, params)
  },

  /**
   * 页内关键字 
   */
  // 删除页内关键字，批量删除页内关键字
  delPageKeyWords: params => {
    return request.post(`/medsci-mgr-center-service/pageKeywords/delPageKeyWords`, params)
  },
  // 查询页内关键字的详情
  getPageKeyWords: params => {
    return request.post(`/medsci-mgr-center-service/pageKeywords/getPageKeyWords`, params)
  },
  // 查询页内关键字列表及模糊查询
  getPageKeyWordsPage: params => {
    return request.post(`/medsci-mgr-center-service/pageKeywords/getPageKeyWordsPage`, params)
  },
  // 新建页内关键字
  insertPageKeyWords: params => {
    return request.post(`/medsci-mgr-center-service/pageKeywords/insertPageKeyWords`, params)
  },
  // 修改页内关键字
  updatePageKeyWords: params => {
    return request.post(`/medsci-mgr-center-service/pageKeywords/updatePageKeyWords`, params)
  },
  // 禁用，启用
  updatePageKeyWordsStatus: params => {
    return request.post(`/medsci-mgr-center-service/pageKeywords/updatePageKeyWordsStatus`, params)
  },

  /**
   * 话题关键字
   */
  // 删除,批量删除话题关键字
  delTopicKeyWords: params => {
    return request.post(`/medsci-mgr-center-service/medsciTopic/delete`, params)
  },
  // 查询话题关键字的详情
  getTopicKeyWords: params => {
    return request.post(`/medsci-mgr-center-service/medsciTopic/show`, params)
  },
  // 查询话题关键字列表及模糊查询
  getTopicKeywordsPage: params => {
    return request.post(`/medsci-mgr-center-service/medsciTopic/getTopicPage`, params)
  },
  // 新建话题关键字
  insertTopicKeyWords: params => {
    return request.post(`/medsci-mgr-center-service/medsciTopic/saveMedsciTopic`, params)
  },
  // 修改话题关键字
  updateTopicKeyWords: params => {
    return request.post(`/medsci-mgr-center-service/medsciTopic/updateTopic`, params)
  },
  // 话题关键字启用与禁用
  updateTopicKeyWordsStatus: params => {
    return request.post(`/medsci-mgr-center-service/medsciTopic/updateTopicStatus`, params)
  },
  // 批量设置话题科室
  batchUpdateTopicCategory: params => {
    return request.post(`/medsci-mgr-center-service/medsciTopic/batchUpdateTopicCategory`, params)
  },
  // 修改话题公开状态
  updateTopicSecret: params => {
    return request.post(`/medsci-mgr-center-service/medsciTopic/updateTopicSecret`, params)
  },

  /**
   * 视频单节
   */
  // 删除单节课程
  delVideoSingle: params => {
    return request.post(`/medsci-mgr-center-service/videoSingle/delVideoSingle`, params)
  },
  // 查询单节课程的详情
  getVideoSingle: params => {
    return request.post(`/medsci-mgr-center-service/videoSingle/getVideoSingle`, params)
  },
  // 查询单节课程及模糊查询
  getVideoSinglePage: params => {
    return request.post(`/medsci-mgr-center-service/videoSingle/getVideoSinglePage`, params)
  },
  // 添加单节课程
  saveVideoSingle: params => {
    return request.post(`/medsci-mgr-center-service/videoSingle/saveVideoSingle`, params)
  },
  // 修改单节课程
  updateVideoSingle: params => {
    return request.post(`/medsci-mgr-center-service/videoSingle/updateVideoSingle`, params)
  },
  // 批量去审，审核
  updateVideoSingleBatch: params => {
    return request.post(`/medsci-mgr-center-service/videoSingle/updateVideoSingleBatch`, params)
  },
  // 单节课程的显示设置
  updateVideoSingleDisplay: params => {
    return request.post(`/medsci-mgr-center-service/videoSingle/updateVideoSingleDisplay`, params)
  },
  // 单节课程批量设置分类
  videoSingleBatchModifyCategory: params => {
    return request.post(`/medsci-mgr-center-service/videoSingle/batchModifyCategory`, params)
  },

  /**
   * 视频系列、合集
   */
  // 删除视频合集
  delVideoCollection: params => {
    return request.post(`/medsci-mgr-center-service/videos/delVideoCollection`, params)
  },
  // 删除视频系列
  delVideoSeries: params => {
    return request.post(`/medsci-mgr-center-service/videos/delVideoSeries`, params)
  },
  // 查询系列，合集的详情
  getVideoSingleSeriesList: params => {
    return request.post(`/medsci-mgr-center-service/videos/getVideoSingleSeriesList`, params)
  },
  // 单节课程的显示设置
  getVideos: params => {
    return request.post(`/medsci-mgr-center-service/videos/getVideos`, params)
  },
  // 查询系列，合集及模糊查询
  getVideosPage: params => {
    return request.post(`/medsci-mgr-center-service/videos/getVideosPage`, params)
  },
  // 添加视频系列，集合
  saveVideos: params => {
    return request.post(`/medsci-mgr-center-service/videos/saveVideos`, params)
  },
  // 更新视频系列，集合
  updateVideos: params => {
    return request.post(`/medsci-mgr-center-service/videos/updateVideos`, params)
  },
  // 系列，合集 审核去审
  updateVideosBatch: params => {
    return request.post(`/medsci-mgr-center-service/videos/updateVideosBatch`, params)
  },
  // 视频系列，集合的显示设置
  updateVideosDisplay: params => {
    return request.post(`/medsci-mgr-center-service/videos/updateVideosDisplay`, params)
  },
  // 合集课程列表
  getVideoSingleSeriesListDisplay: params => {
    return request.post(`/medsci-mgr-center-service/videos/getVideoSingleSeriesListDisplay`, params)
  },
  // 系列课程批量设置分类
  videosBatchModifyCategory: params => {
    return request.post(`/medsci-mgr-center-service/videos/batchModifyCategory`, params)
  },

  /**
   * 视频课程伍达辉
   */
  // 视频课程列表
  videoPageList: params => {
    return request.post(`/video/medsciCourse/pageList`, params)
  },
  // 新增视频课程
  videoSaveCourse: params => {
    return request.post(`/video/medsciCourse/saveCourse`, params)
  },
  // 视频课程详情
  videoMedsciCourseDetails: params => {
    return request.get(`/video/medsciCourse/details/${params.id}`, params)
  },
  // 编辑视频课程
  videoMedsciCourseEditCourse: params => {
    return request.post(`/video/medsciCourse/editCourse`, params)
  },
  // 视频课程审核去审
  videoMedsciCourseOperateCourse: params => {
    return request.post(`/video/medsciCourse/operateCourse`, params)
  },
  // 获取章节视频
  videoMedsciCourseTreeList: params => {
    return request.get(`/video/medsciCourseInfo/treeList/${params.id}`, params)
  },
  // 编辑章节视频
  videoMedsciCourseEditCourseInfo: params => {
    return request.post(`/video/medsciCourseInfo/editCourseInfo`, params)
  },
  // 删除章节视频
  videoMedsciCourseDeletedCourse: params => {
    return request.post(`/video/medsciCourseInfo/deletedCourse`, params)
  },
  
  // 视频列表-会员卡列表
  // memberCardQuery: params => {
  //   return request.post(`/member/card/query`, params)
  // },
  // 视频列表-课程列表
  getCoureByTitle: params => {
    return request.post(`/video/medsciCourse/getCoureByTitle`, params)
  },

  


  /**
   * 视频章节
   */
  // 删除章节课程
  delVideoAddition: params => {
    return request.post(`/medsci-mgr-center-service/videoSeriesChapter/delVideoAddition`, params)
  },
  // 删除合集课程
  delVideoCollectionAddition: params => {
    return request.post(`/medsci-mgr-center-service/videoSeriesChapter/delVideoCollectionAddition`, params)
  },
  // 删除章节
  delVideoSeriesChapter: params => {
    return request.post(`/medsci-mgr-center-service/videoSeriesChapter/delVideoSeriesChapter`, params)
  },
  // 章节课程列表
  getVideoAdditionList: params => {
    return request.post(`/medsci-mgr-center-service/videoSeriesChapter/getVideoAdditionList`, params)
  },
  // 章节列表
  getVideoSeriesChapterList: params => {
    return request.post(`/medsci-mgr-center-service/videoSeriesChapter/getVideoSeriesChapterList`, params)
  },
  // 添加章节课程
  saveVideoAddition: params => {
    return request.post(`/medsci-mgr-center-service/videoSeriesChapter/saveVideoAddition`, params)
  },
  // 添加合集课程
  saveVideoCollectionAddition: params => {
    return request.post(`/medsci-mgr-center-service/videoSeriesChapter/saveVideoCollectionAddition`, params)
  },
  // 添加章节
  saveVideoSeriesChapter: params => {
    return request.post(`/medsci-mgr-center-service/videoSeriesChapter/saveVideoSeriesChapter`, params)
  },
  // 章节课程排序
  updateVideoAdditionRank: params => {
    return request.post(`/medsci-mgr-center-service/videoSeriesChapter/updateVideoAdditionRank`, params)
  },
  // 合集课程排序
  updateVideoCollectionAdditionRank: params => {
    return request.post(`/medsci-mgr-center-service/videoSeriesChapter/updateVideoCollectionAdditionRank`, params)
  },
  // 更新章节
  updateVideoSeriesChapter: params => {
    return request.post(`/medsci-mgr-center-service/videoSeriesChapter/updateVideoSeriesChapter`, params)
  },
  // 章节课程非试看，试看
  updateVideoAdditionHaveLook: params => {
    return request.post(`/medsci-mgr-center-service/videoSeriesChapter/updateVideoAdditionHaveLook`, params)
  },

  // 获取视频系列列表
  getVideoSeriesList: id => {
    return request.get(`/medsci-mgr-center-service/videoChapterRelation/queryCourses/${id}?date=${new Date().getTime()}`)
  },

  // 视频系列操作
  editVideoSeries: params => {
    return request.post(`/medsci-mgr-center-service/videoChapterRelation/editCoursesToSeries`, params)
  },

  // 视频系列数据排序
  sortChapterRelation: params => {
    return request.post(`/medsci-mgr-center-service/videoChapterRelation/sort`, params)
  },

  /**
   * paas 视频内容查询
   */
  getVideoListLikeTitle: params => {
    return request.post(`/paas-mgr-center-service/video/getVideoListLikeTitle`, params)
  },

  /**
   * 项目信息接口
   */
  getProjectIdAndRoleListDicList: params => {
    return request.post(`/paas-mgr-center-service/medsciRole/getProjectIdAndRoleListDicList`, params)
  },


  /**
   * 查询角色-菜单下权限按钮列表
   */
  getRoleMenuPermissionsBtnList: params => {
    return request.post(`/paas-mgr-center-service/medsciMenu/getRoleMenuPermissionsBtnList`, params)
  },

  /**
   * 查询公司字典
   */
  getMedsciCompanyDicList: params => {
    return request.post(`/paas-mgr-center-service/medsciCompany/getMedsciCompanyDicList`, params)
  },

  // 查询erp公司字典
  getErpCompanyDiclist: params => {
    return request.post(`/paas-mgr-center-service/medsciProject/getMedsciCompanyNamePage`, params)
  },

  /**
   * 查询项目字典
   */
  getProjectDicList: params => {
    return request.post(`/paas-mgr-center-service/medsciProject/getProjectDicList`, params)
  },

  /**
   * 评论
   */
  // 查询评论的详情
  getComments: params => {
    return request.post(`/paas-mgr-center-service/mesdciComments/getComments`, params)
  },
  // 项目模块的评论列表分页
  getCommentsPageByProject: params => {
    return request.post(`/paas-mgr-center-service/mesdciComments/getCommentsPage`, params)
  },
  // 评论批量去审核，批量审核,批量删除
  updateCommentsBatch: params => {
    return request.post(`/paas-mgr-center-service/mesdciComments/batchDeal`, params)
  },
  // 更新状态（精彩评论/举报）
  updateStatus: params => {
    return request.post(`/paas-mgr-center-service/mesdciComments/updateStatus`, params)
  },

  /**
   * 直播
   */
  // 删除直播信息
  deleteLive: params => {
    return request.post(`/medsci-mgr-center-service/live/deleteLive`, params)
  },
  // 删除直播流程信息
  deleteLiveProcedure: params => {
    return request.post(`/medsci-mgr-center-service/live/deleteLiveProcedure`, params)
  },
  // 删除相关推荐信息
  deleteLiveRecommendedVideo: params => {
    return request.post(`/medsci-mgr-center-service/live/deleteLiveRecommendedVideo`, params)
  },
  // 新增直播初始信息(返回初始直播对象)
  addInitialLive: params => {
    return request.post(`/medsci-mgr-center-service/live/addInitialLive`, params)
  },
  // 新增赞助企业
  addLiveSponsorshipEnterprises: params => {
    return request.post(`/medsci-mgr-center-service/live/addLiveSponsorshipEnterprises`, params)
  },
  // 生成直播地址二维码
  createLiveQRCode: params => {
    return request.post(`/medsci-mgr-center-service/live/createLiveQRCode`, params)
  },
  // 删除赞助企业信息
  deleteLiveSponsorshipEnterprises: params => {
    return request.post(`/medsci-mgr-center-service/live/deleteLiveSponsorshipEnterprises`, params)
  },
  // 审核直播信息
  examineLive: params => {
    return request.post(`/medsci-mgr-center-service/live/examineLive`, params)
  },
  // 导出报名数据excel
  exportRegistrationExcel: params => {
    return request.post(`/medsci-mgr-center-service/live/exportRegistrationExcel`, params)
  },
  // 查询直播详情信息
  getLiveDetails: params => {
    return request.post(`/medsci-mgr-center-service/live/getLiveDetails`, params)
  },
  // 查询直播信息分页
  getLivePage: params => {
    return request.post(`/medsci-mgr-center-service/live/getLivePage`, params)
  },
  // 查询直播流程列表信息
  getLiveProcedureList: params => {
    return request.post(`/medsci-mgr-center-service/live/getLiveProcedureList`, params)
  },
  // 查询相关推荐关联视频信息
  getLiveRecommendedVideoGlList: params => {
    return request.post(`/medsci-mgr-center-service/live/getLiveRecommendedVideoGlList`, params)
  },
  // 查询相关推荐信息
  getLiveRecommendedVideoList: params => {
    return request.post(`/medsci-mgr-center-service/live/getLiveRecommendedVideoList`, params)
  },
  // 查询相关推荐信息
  getLiveRegistrationPage: params => {
    return request.post(`/medsci-mgr-center-service/live/getLiveRegistrationPage`, params)
  },
  // 查询赞助企业列表信息
  getLiveSponsorshipEnterprisesList: params => {
    return request.post(`/medsci-mgr-center-service/live/getLiveSponsorshipEnterprisesList`, params)
  },
  // 保存直播信息
  saveLive: params => {
    return request.post(`/medsci-mgr-center-service/live/saveLive`, params)
  },
  // 保存直播流程设置
  saveLiveProcedure: params => {
    return request.post(`/medsci-mgr-center-service/live/saveLiveProcedure`, params)
  },
  // 保存相关推荐设置
  saveLiveRecommendedVideo: params => {
    return request.post(`/medsci-mgr-center-service/live/saveLiveRecommendedVideo`, params)
  },
  // 保存直播报名信息
  saveLiveRegistration: params => {
    return request.post(`/medsci-mgr-center-service/live/saveLiveRegistration`, params)
  },
  // 保存赞助企业详情
  saveLiveSponsorshipEnterprisesDetails: params => {
    return request.post(`/medsci-mgr-center-service/live/saveLiveSponsorshipEnterprisesDetails`, params)
  },
  // 保存报名量表设置
  saveRegistrationScale: params => {
    return request.post(`/medsci-mgr-center-service/live/saveRegistrationScale`, params)
  },
  deleteLiveSponsorshipEnterprisesDetail: params => {
    return request.post(`/medsci-mgr-center-service/live/deleteLiveSponsorshipEnterprisesDetail`, params)
  },

  /**
   * 字典库搜索
   */
  getValuesByType: params => {
    return request.post(`/paas-mgr-center-service/dictionary/getValuesByType`, params)
  },

  // 修改字典
  updateValuesByType: params => {
    return request.post('/paas-mgr-center-service/dictionary/updateDictionary', params)
  },

  /**
   * 经典句子管理
   */
  // 新增句子
  addClassicSentences: params => {
    return request.post(`/medsci-mgr-center-service/classicSentences/addClassicSentences`, params)
  },
  // 批量删除句子
  deleteBatchClassicSentences: params => {
    return request.post(`/medsci-mgr-center-service/classicSentences/deleteBatchClassicSentences`, params)
  },
  // 删除句子
  deleteClassicSentences: params => {
    return request.post(`/medsci-mgr-center-service/classicSentences/deleteClassicSentences`, params)
  },
  // 根据ID查询句子详情
  getClassicSentencesById: params => {
    return request.post(`/medsci-mgr-center-service/classicSentences/getClassicSentencesById`, params)
  },
  // 查询句子分页
  getClassicSentencesPage: params => {
    return request.post(`/medsci-mgr-center-service/classicSentences/getClassicSentencesPage`, params)
  },
  // 修改句子
  updateClassicSentences: params => {
    return request.post(`/medsci-mgr-center-service/classicSentences/updateClassicSentences`, params)
  },
  // 句子审核/去审
  batchDealClassicSentencesById: params => {
    return request.post(`/medsci-mgr-center-service/classicSentences/batchDealClassicSentencesById`, params)
  },
  // sci分类查询
  getClassicSentencesSubject: () => {
    return request.get(`/medsci-mgr-center-service/classicSentences/getClassicSentencesSubject`)
  },

  /**
   * 敏感词库
   */
  // 删除, 批量删除敏感词
  delBadWordsBatch: params => {
    return request.post(`/paas-mgr-center-service/badWords/delBadWordsBatch`, params)
  },

  // 查询敏感词详情
  getBadWords: params => {
    return request.post(`/paas-mgr-center-service/badWords/getBadWords`, params)
  },

  // 查询敏感词列表及模糊查询
  getBadWordsPage: params => {
    return request.post(`/paas-mgr-center-service/badWords/getBadWordsPage`, params)
  },

  // 根据id修改敏感词
  updateBadWords: params => {
    return request.post(`/paas-mgr-center-service/badWords/updateBadWords`, params)
  },

  // 新增敏感词
  insertBadWords: params => {
    return request.post(`/paas-mgr-center-service/badWords/insertBadWords`, params)
  },

  /**
   * 系统消息
   */
  // 审核系统消息/去审
  systemAuditor: params => {
    return request.post(`/paas-mgr-center-service/medsciMessageSystem/auditor`, params)
  },
  // 获取系统通知列表
  getMessageSystemPageList: params => {
    return request.post(`/paas-mgr-center-service/medsciMessageSystem/getMessageSystemPageList`, params)
  },
  // 发布系统消息
  releaseSystemMessage: params => {
    return request.post(`/paas-mgr-center-service/medsciMessageSystem/releaseSystemMessage`, params)
  },
  // 获取pc系统通知列表
  queryMessagePush: params => {
    return request.post(`/paas-mgr-center-service/medsciMessagePush/queryMessagePush`, params)
  },
  // 审核pc系统通知/去审
  dealMessagePush: params => {
    return request.post(`/paas-mgr-center-service/medsciMessagePush/dealMessagePush`, params)
  },
  // pc系统通知删除
  dealMessageDelete: params => {
    return request.post(`/paas-mgr-center-service/medsciMessagePush/batch/delete`, params)
  },
  // pc查询梅斯消息推送
  getMessageId: id => {
   return request.get(`/paas-mgr-center-service/medsciMessagePush/getById/${id}`, )
  },
  // pc消息获取满足发送对象总用户数
  getSendObjectNum: params => {
    return request.post(`/paas-mgr-center-service/medsciMessagePush/getSendObjectNum`, params)
  },
  /**
   * 私信管理
   */
  // 删除聊天
  deleteChat: params => {
    return request.post(`/medsci-mgr-center-service/medsciChat/deleteChat`, params)
  },
  // 获取私信列表
  getMedsciChatListPage: params => {
    return request.post(`/medsci-mgr-center-service/medsciChat/getMedsciChatListPage`, params)
  },

  // 查询全局会话列表
  getGlobalConversations: params => {
    return request.post(`/medsci-mgr-center-service/medsciChat/getGlobalConversations`, params)
  },

  // 获取项目名称下拉
  medsciChatGetProjectName: params => {
    return request.post(`/medsci-mgr-center-service/medsciChat/getProjectName`, params)
  },

  // 获取业务线下拉
  medsciChatGetServiceLineName: params => {
    return request.post(`/medsci-mgr-center-service/medsciChat/getServiceLineName`, params)
  },

  // 获取单聊私信详情
  medsciChatGetHistoryMessages: params => {
    return request.post(`/medsci-mgr-center-service/medsciChat/getHistoryMessages`, params)
  },

  // 获取群聊私信详情
  medsciChatGetMsg: params => {
    return request.post(`/medsci-mgr-center-service/medsciChat/getMsg`, params)
  },

  // 删除私信消息
  medsciChatRemoveMsg: params => {
    return request.post(`/medsci-mgr-center-service/medsciChat/removeMsg`, params)
  },

  // 批量删除私信消息
  medsciChatRemoveMsgBatch: params => {
    return request.post(`/medsci-mgr-center-service/medsciChat/removeMsgBatch`, params)
  },



  /**
   * 兑换商品管理
   */
  // 新增兑换商品
  goodsCreate: params => {
    return request.post(`/medsci-mgr-center-service/medsciShopIntegralMallGoods/addGoods`, params)
  },
    // 查看项目列表和券码列表
    projectList: params => {
      return request.post(`/medsci-mgr-center-service/medsciShopIntegralMallGoods/project/list`, params)
    },
  // 批量删除兑换商品
  goodsBatchDelete: params => {
    return request.post(`/medsci-mgr-center-service/medsciShopIntegralMallGoods/batchDeleteGoods`, params)
  },
  // 查看单个兑换商品
  goodsDetail: params => {
    return request.post(`/medsci-mgr-center-service/medsciShopIntegralMallGoods/getOneGoods`, params)
  },
  // 兑换商品分页查询
  goodsPageList: params => {
    return request.post(`/medsci-mgr-center-service/medsciShopIntegralMallGoods/getPageGoodsList`, params)
  },
  // 修改兑换商品
  goodsUpdate: params => {
    return request.post(`/medsci-mgr-center-service/medsciShopIntegralMallGoods/updateGoods`, params)
  },
  //商品审核去审
  goodsAudit: params => {
    return request.post(`/medsci-mgr-center-service/medsciShopIntegralMallGoods/audit`, params)
  },
  
  //商品属性
  getMallGoodsAttributes:params => {
      return request.post(`/medsci-mgr-center-service/medsciMallGoodsAttributes/getMallGoodsAttributes`, params)
    },

  /**
   * 
   */
  // 订单状态修改 (发货)
  orderUpdate: params => {
    return request.post(`/medsci-mgr-center-service/medsciShopLog/deliverGoods`, params)
  },
  // 获取订单列表
  orderPageList: params => {
    return request.post(`/medsci-mgr-center-service/medsciShopLog/getIntegralShoPayLogByProjectId`, params)
  },

  /**
   * nsfc
   */
  // 新增基金
  nsfcCreate: params => {
    return request.post(`/medsci-mgr-center-service/medsciNsfc/addNsfc`, params)
  },
  // 删除基金
  nsfcDelete: params => {
    return request.post(`/medsci-mgr-center-service/medsciNsfc/deleteBatchNsfc`, params)
  },
  // 编辑基金
  nsfcEdit: params => {
    return request.post(`/medsci-mgr-center-service/medsciNsfc/editNsfc`, params)
  },
  // 获取基金列表
  nsfcGetDetail: params => {
    return request.post(`/medsci-mgr-center-service/medsciNsfc/getNsfcDetail`, params)
  },
  // 获取基金列表
  nsfcPageList: params => {
    return request.post(`/medsci-mgr-center-service/medsciNsfc/getNsfcPage`, params)
  },

  /**
   * 用户标签管理
   */
  // 获取标签列表
  getProjectUserLabelPageList: params => {
    return request.post(`/medsci-mgr-center-service/medsciUserLabel/getProjectUserLabelPageList`, params)
  },
  // 操作标签
  operationLabel: params => {
    return request.post(`/medsci-mgr-center-service/medsciUserLabel/operationLabel`, params)
  },

  /**
   * 任务管理
   */
  // 任务批量处理
  batchDealTask: params => {
    return request.post(`/paas-mgr-center-service/task/batchDealTask`, params)
  },
  // 查询任务分页
  getBackstageTaskPage: params => {
    return request.post(`/paas-mgr-center-service/task/getBackstageTaskPage`, params)
  },
  // 获取任务详情
  getTaskById: params => {
    return request.post(`/paas-mgr-center-service/task/getTaskById`, params)
  },
  // 新增任务
  saveTask: params => {
    return request.post(`/paas-mgr-center-service/task/saveTask`, params)
  },
  // 修改任务
  updateTaskById: params => {
    return request.post(`/paas-mgr-center-service/task/updateTaskById`, params)
  },
  // 统计任务列表
  taskStatistics: params => {
    return request.post(`/paas-mgr-center-service/medsciTaskRecord/taskStatistics`, params)
  },
  // 导出统计任务列表
  excelExport: params => {
    return request.post(`/paas-mgr-center-service/medsciTaskRecord/excelExport`, params)
  },

  /**
   * 运营配置
   */
  // 配置列表
  aConfigList: params => {
    return request.post(`/medsci-mgr-center-service/medsciOperationConfig/configList`, params)
  },
  // 修改配置
  aUpdateConfig: params => {
    return request.post(`/medsci-mgr-center-service/medsciOperationConfig/updateConfig`, params)
  },

  // 运营热榜列表
  hConfigList: params => {
    return request.post(`/medsci-mgr-center-service/operationtop/list`, params)
  },

  // 保存运营热榜
  hUpdateConfig: params => {
    return request.post(`/medsci-mgr-center-service/operationtop/save`, params)
  },

  // 查询热点话题列表
  getHotTopicList: params => {
    return request.post(`/medsci-mgr-center-service/hotTopic/getHotTopicList`, params)
  },

  // 保存热门话题
  saveHotTopic: params => {
    return request.post(`/medsci-mgr-center-service/hotTopic/saveHotTopic`, params)
  },

  // 话题查询
  searchHotTopic: params => {
    return request.post(`/medsci-mgr-center-service/hotTopic/searchHotTopic`, params)
  },

  // 后台查询直播运营位列表
  getMedsciOperationLiveList: params => {
    return request.post(`/medsci-mgr-center-service/medsciOperationLive/getMedsciOperationLiveList`, params)
  },

  // 更新直播运营位
  updateMedsciOperationLive: params => {
    return request.post(`/medsci-mgr-center-service/medsciOperationLive/updateMedsciOperationLive`, params)
  },

  /** 
   * 讲师库
   */
  // 获取讲师详情 /getMedsciSpeakeDetail
  getMedsciSpeakeDetail: params => {
    return request.post(`/paas-mgr-center-service/medsciSpeaker/getMedsciSpeakeDetail`, params)
  },

  // 删除讲师/deleteMedsciSpeake
  deleteMedsciSpeake: params => {
    return request.post(`/paas-mgr-center-service/medsciSpeaker/deleteMedsciSpeake`, params)
  },

  // 讲师分页查询
  getMedsciSpeakePage: params => {
    return request.post(`/paas-mgr-center-service/medsciSpeaker/getMedsciSpeakePage`, params)
  },

  // 直播讲师分页查询
  getAssociatedUserLecturer: params => {
    return request.post(`/paas-mgr-center-service/medsciSpeaker/getAssociatedUserLecturer`, params)
  },
  // 直播讲师分页查询
  getListExpertUsersV1: params => {
    return request.post(`/paas-mgr-center-service/medsciUser/listExpertUsersV1`, params)
  },
 // 直播讲师分页查询
  getBiProfessorInfo: params => {
    return request.post(`/paas-mgr-center-service/medsciBi/getBiProfessorInfo`, params)
  },
  // 启用讲师分页查询
  getMedsciSpeakeLike: params => {
    return request.post(`/paas-mgr-center-service/medsciSpeaker/getMedsciSpeakeLike`, params)
  },

  // 新增讲师saveMedsciSpeake
  saveMedsciSpeake: params => {
    return request.post(`/paas-mgr-center-service/medsciSpeaker/saveMedsciSpeake`, params)
  },

  // 编辑讲师updateMedsciSpeake
  updateMedsciSpeake: params => {
    return request.post(`/paas-mgr-center-service/medsciSpeaker/updateMedsciSpeake`, params)
  },

  // 启用禁用讲师
  isStatus: params => {
    return request.post(`/paas-mgr-center-service/medsciSpeaker/isStatus`, params)
  },

  // 讲师批量设置分类
  medsciSpeakerBatchModifyCategory: params => {
    return request.post(`/paas-mgr-center-service/medsciSpeaker/batchModifyCategory`, params)
  },


  // 查询讲师字典列表 /getMedsciLecturerDicList
  getMedsciLecturerDicList: params => {
    return request.post(`/mg-user-service/medsci-user/getMedsciLecturerDicList`, params)
  },

  // 更新讲师引用次数 /updateReferenceCount
  updateReferenceCount: params => {
    return request.post(`/mg-user-service/medsci-user/updateReferenceCount`, params)
  },

  // 查看用户列表 listMedsciUsers
  listMedsciUsers: params => {
    return request.post(`/mg-user-service/medsci-user/listMedsciUsers`, params)
  },

  /** 
   * 专题课程
   */
  // 视频专题列表
  pageListSpecial: params => {
    return request.post(`/medsci-mgr-center-service/videosSpecial/pageList`, params)
  },

  // 新增视频专题
  addVideosSpecial: params => {
    return request.post(`/medsci-mgr-center-service/videosSpecial/addVideosSpecial`, params)
  },

  // 编辑视频专题
  editorVideosSpecial: params => {
    return request.post(`/medsci-mgr-center-service/videosSpecial/editorVideosSpecial`, params)
  },

  // 操作视频专题
  operateVideosSpecial: params => {
    return request.post(`/medsci-mgr-center-service/videosSpecial/operateVideosSpecial`, params)
  },

  // 视频专题详情
  specialDetails: params => {
    return request.post(`/medsci-mgr-center-service/videosSpecial/specialDetails`, params)
  },

  // 专题课程批量设置分类
  videosSpecialBatchModifyCategory: params => {
    return request.post(`/medsci-mgr-center-service/videosSpecial/batchModifyCategory`, params)
  },

  // 删除专题系列关系
  specialDeleteRelation: params => {
    return request.post(`/medsci-mgr-center-service/videosSpecialRelation/deleteRelation`, params)
  },

  // 专题下新增系列课程
  specialRelatedSeries: params => {
    return request.post(`/medsci-mgr-center-service/videosSpecialRelation/relatedSeries`, params)
  },

  // 专题下系列列表
  specialSeriesList: params => {
    return request.post(`/medsci-mgr-center-service/videosSpecialRelation/seriesList`, params)
  },

  // 专题数据排序
  sortSpecialRelation: params => {
    return request.post(`/medsci-mgr-center-service/videosSpecialRelation/sort`, params)
  },

  /** 
   * 系列试题关系维护
   */

  //删除系列试题关系
  exercisesRelationDeleted: params => {
    return request.post(`/medsci-mgr-center-service/exercisesRelation/deleted`, params)
  },

  //系列试题列表
  exercisesRelationQuestionList: params => {
    return request.post(`/medsci-mgr-center-service/exercisesRelation/questionList`, params)
  },

  //系列课程关联试题
  relatedQuestions: params => {
    return request.post(`/medsci-mgr-center-service/exercisesRelation/relatedQuestions`, params)
  },

  //试题数据排序
  sortExercisesRelation: params => {
    return request.post(`/medsci-mgr-center-service/exercisesRelation/sort`, params)
  },

  /** 
   * 题库
   */

  // 新增考题
  addExercises: params => {
    return request.post(`/medsci-mgr-center-service/videosExercises/addExercises`, params)
  },

  // 编辑考题
  editorExercises: params => {
    return request.post(`/medsci-mgr-center-service/videosExercises/editorExercises`, params)
  },

  // 考题详情
  exercisesDetails: params => {
    return request.post(`/medsci-mgr-center-service/videosExercises/exercisesDetails`, params)
  },

  // 操作考题
  operateExercises: params => {
    return request.post(`/medsci-mgr-center-service/videosExercises/operateExercises`, params)
  },

  // 考题分页列表
  pageListExercises: params => {
    return request.post(`/medsci-mgr-center-service/videosExercises/pageList`, params)
  },

  // 批量设置分类
  videosExercisesBatchModifyCategory: params => {
    return request.post(`/medsci-mgr-center-service/videosExercises/batchModifyCategory`, params)
  },

  /**
   * 评论设置
   */
  // 内容黑名单
  settingBlacklist: params => {
    return request.post(`/paas-mgr-center-service/commentsSetting/settingBlacklist`, params)
  },
  // 删除内容黑名单
  deleteBlacklist: params => {
    return request.post(`/paas-mgr-center-service/commentsSetting/deleteBlacklist`, params)
  },
  // 添加内容黑名单
  saveBlacklist: params => {
    return request.post(`/paas-mgr-center-service/commentsSetting/saveBlacklist`, params)
  },
  // 配置列表
  settingList: params => {
    return request.post(`/paas-mgr-center-service/commentsSetting/settingList`, params)
  },
  // 修改配置
  updateSetting: params => {
    return request.post(`/paas-mgr-center-service/commentsSetting/updateSetting`, params)
  },
  // 配置详情id
  settingDetail: params => {
    return request.post(`/paas-mgr-center-service/commentsSetting/settingDetail`, params)
  },

  // 订单管理-伍达辉
  // 订单列表
  orderList: params => {
    return request.post(`/medsciOrder/orderList`, params)
  },
  // 批量导入人工订单
  batchOrderByExcel: params => {
    return request.post(`/medsciOrder/batchOrderByExcel`, params)
  },
  // 配置详情id
  getOrderById: params => {
    return request.post(`/medsciOrder/getOrderById`, params)
  },
  // 配置详情id
  deletedById: params => {
    return request.post(`/medsciOrder/deletedById`, params)
  },

  // 图标管理
  // 后台新增
  addToolApplication: params => {
    return request.post(`/medsci-mgr-center-service/applicationTool/addTool`, params)
  },
  // 后台批量处理-审核/去审/删除
  batchDealToolsApplication: params => {
    return request.post(`/medsci-mgr-center-service/applicationTool/batchDealTools`, params)
  },
  // 后台详情
  detailToolApplication: params => {
    return request.post(`/medsci-mgr-center-service/applicationTool/detailTool`, params)
  },
  // 后台分页
  getToolPageApplication: params => {
    return request.post(`/medsci-mgr-center-service/applicationTool/getToolPage`, params)
  },
  // 后台修改
  updateToolApplication: params => {
    return request.post(`/medsci-mgr-center-service/applicationTool/updateTool`, params)
  },

  // 单页配置
  // 单页-处理上下线删除操作
  dealSinglePage: params => {
    return request.post(`/medsci-mgr-center-service/singlePage/dealSinglePage`, params)
  },
  // 单页-分页查询
  getPageForSinglePage: params => {
    return request.post(`/medsci-mgr-center-service/singlePage/getPageForSinglePage`, params)
  },
  // 单页-详情查询
  getSinglePageById: params => {
    return request.post(`/medsci-mgr-center-service/singlePage/getSinglePageById`, params)
  },
  // 单页-添加
  insertSinglePage: params => {
    return request.post(`/medsci-mgr-center-service/singlePage/insertSinglePage`, params)
  },
  // 单页-修改
  updateSinglePage: params => {
    return request.post(`/medsci-mgr-center-service/singlePage/updateSinglePage`, params)
  },

  // 发放类型
  // 发放类型列表
  queryProperty: params => {
    return request.post(`/medsciOrder/property/queryProperty`, params)
  },
  // 创建类型
  createProperty: params => {
    return request.post(`/medsciOrder/property/createProperty`, params)
  },
  // 类型启用/禁用
  modifyPropertyStatus: params => {
    return request.post(`/medsciOrder/property/modifyPropertyStatus`, params)
  },
  // 发放类型列表
  updateProperty: params => {
    return request.post(`/medsciOrder/property/updateProperty`, params)
  },

  // 创建人工订单
  createArtificialOrder: params => {
    return request.post(`/medsciOrder/createArtificialOrder`, params)
  },
  // 发放权限(仅用于人工订单)
  grantPermissions: params => {
    return request.post(`/medsciOrder/grantPermissions`, params)
  },
  // erp订单
  erpOrderList: params => {
    return request.post(`/medsciOrder/erp/orderList`, params)
  },

  // 批量导出订单
  exportOrdersExcel: params => {
    return request.post(`/medsciOrder/exportOrdersExcel`, params)
  },

  // 直播设置商品查询
  selectLiveItemList: params => {
    return request.post(`/video/medsciLiveItem/selectLiveItemList`, params)
  },

  // 直播设置商品新增编辑
  updateLiveItemList: params => {
    return request.post(`/video/medsciLiveItem/updateLiveItemList`, params)
  },


  // 会员属性
  // 会员属性列表
  memberCardProperty: params => {
    return request.post(`/member/card-property`, params)
  },

  // 会员属性启用/禁用
  memberModifyStatus: params => {
    return request.post(`/member/card-property/modifyStatus`, params)
  },

  // 修改会员卡属性
  memberUpdateProperty: params => {
    return request.post(`/member/card-property/updateProperty`, params)
  },

  // 会员信息

  // 付费会员列表-伍达辉
  memberCardQuery: params => {
    return request.post(`/member/card/query`, params)
  },
  // 付费会员时长列表
  getCardByCardDurationList: params => {
    return request.post(`/member/card-duration/getCardByCardDurationList`, params)
  },
  // 添加付费会员
  memberCardSaveInfo: params => {
    return request.post(`/member/card/saveInfo`, params)
  },
  // 修改付费会员
  memberCardModifyInfo: params => {
    return request.post(`/member/card/modifyInfo`, params)
  },
  // 付费会员启用禁用
  memberCardModifyStatus: params => {
    return request.post(`/member/card/modifyStatus`, params)
  },
  // 付费会员详情
  memberCardDetail: params => {
    return request.post(`member/card/detail`, params)
  },
  // 付费会员删除
  memberCardRemove: params => {
    return request.post(`member/card/remove`, params)
  },
  // 会员卡绑定关系(非增量绑定模块与会员卡关系)
  memberCardBundleRight: params => {
    return request.post(`/member/card/bundleRight`, params)
  },
  // 获取会员卡时长列表
  getCardDuration: params => {
    return request.post(`/member/card-duration/getCardDuration`, params)
  },
  // 添加会员卡时长列表
  addCardDuration: params => {
      return request.post(`/member/card-duration/insertCardDuration`, params)
  },
  // 修改会员卡时长列表
  editCardDuration: params => {
    return request.post(`/member/card-duration/updateCardDuration`, params)
 },
// 根据会员卡时长id获取详情
  getCardDurationDetails: id => {
    return request.get(`/member/card-duration/getCardDurationDetails/?id=${id}`, )
 },
//  会员卡时长批量处理 启用-禁用-删除
  batchHandleMemberCardDuration: params => {
    return request.post(`/member/card-duration/batchHandleMemberCardDuration`, params)
  },
  // 帖子管理 
  // 帖子列表
  medsciPostPage: params => {
    return request.post(`/info-center/medsciPosts/getPostsPage`, params)
  },
  // 帖子批量审核 去审 删除
  medsciBanchPosts: params => {
    return request.post(`/info-center/medsciPosts/banchDealPosts`, params)
  },
  // 帖子管理推荐,固顶接口
  recommendOrStickyPosts: params => {
    return request.post(`/info-center/medsciPosts/recommendOrStickyPosts`, params)
  },
  // 获取帖子项目的接口
  getPostProjectName: () => {
    return request.get(`/info-center/medsciPosts/ProjectList`)
  },
 /**
   * 基金管理
   */
  //查询梅斯基金学科信息
  getNsfcSubjectRelationInfo: params => {
    return request.post(`/medsci-mgr-center-service/medsciNsfcSubjectRelationInfo/getNsfcSubjectRelationInfo`, params)
  },
  //分页查询基金项目类别信息
  getNsfcProjectCategoryPage: params => {
    return request.post(`/medsci-mgr-center-service/medsciNsfcProjectCategory/getNsfcProjectCategoryPage`, params)
  },
  // 查询用户列表分页信息
  getListExpertUsers: params => {
    return request.post(`/paas-mgr-center-service/medsciUser/listExpertUsers`,params)
  },
  // 根据分类列表批量获取项目详情
  getProjectDetailByCategoryIds: params => {
    return request.post(`/paas-mgr-center-service/medsciProject/getProjectDetailByCategoryIds`,params)
  },
  //获取群聊天记录
  getMedsciChat: params => {
  return request.post(`/medsci-mgr-center-service/medsciChat/msg`, params)
  // return request.post(`/medsciSJ-mgr-center-service/medsciChat/msg`, params)
  },
  // 直播批量操作
  DealMedsciChat: params => {
  return request.post(`/medsci-mgr-center-service/medsciChat/removeMsgBatch`, params)
  // return request.post(`/medsciSJ-mgr-center-service/medsciChat/removeMsgBatch`, params)
  },
  /**
   * iMSL管理
   */
  //后台查询梅斯AI小助手话题日志
  getQueryAiTopicLog: params => {
    return request.post(`/paas-mgr-center-service/medsciAiTopicLog/queryAiTopicLog`, params)
  },
  // //获取梅斯AI小助手内容日志
  getMedsciAiLog: params => {
    return request.post(`/paas-mgr-center-service/medsciAiLog`, params)
  },
   //获取梅斯AI小助手话题日志
  getMedsciAiTopicLog: params => {
    return request.post(`/paas-mgr-center-service/medsciAiTopicLog`, params)
  },
  // 梅斯AI模板批量处理
  dealBatchAiTemplate: params => {
    return request.post(`/paas-mgr-center-service/medsciAiTemplate/batchDealAiTemplate`, params)
  },
  // 查询梅斯AI模板
  getAiTemplate: params => {
    return request.post(`/paas-mgr-center-service/medsciAiTemplate/queryAiTemplate`, params)
  },
  //添加梅斯AI模板
  addAiTemplate: params => {
    return request.post(`/paas-mgr-center-service/medsciAiTemplate/saveAiTemplate`, params)
  },
  //更新梅斯AI模板
  updateAiTemplate: params => {
    return request.post(`/paas-mgr-center-service/medsciAiTemplate/updateAiTemplate`, params)
  },
  //根据ID查询梅斯AI模板
  getAiTemplateById: id => {
    return request.get(`/paas-mgr-center-service/medsciAiTemplate/getById/${id}`)
  },
  //文件导入梅斯白名单用户记录
  userWhiteMessageRecord: params => {
    return request.post(`/paas-mgr-center-service/medsciUserWhiteMessageRecord/leadingIn`, params)
  },
  // 添加消息推送
  saveMessagePush: params => {
    return request.post(`/paas-mgr-center-service/medsciMessagePush/saveMessagePush`, params)
  },
  // 更新梅斯消息推送
  updateMessagePush: params => {
    return request.post(`/paas-mgr-center-service/medsciMessagePush/updateMessagePush`, params)
  },

  // ES医院库
  getEsMedsciCommonHospitals: params => {
    return request.post(`/paas-mgr-center-service/medsciCommonHospital/getEsMedsciCommonHospitals`, params)
  },

  /**
   * 品牌栏目管理
   */
  // 获取品牌栏目列表
  getBrandBuildingList: params => {
    return request.get(`/medsci-mgr-center-service/brandBuilding/list`, { params })
  },
  // 保存或更新品牌栏目
  saveBrandBuilding: params => {
    return request.post(`/medsci-mgr-center-service/brandBuilding/saveOrUpdate`, params)
  },
}