import request from '../../utils/request'

export default {
    // 直播分类查询
    getLiveCategoryPage: params => {
        return request.post(`/live-center/liveCategory/getLiveCategoryPage`, params)
    },

    // 添加直播分类
    addLiveCategory: params => {
        return request.post(`/live-center/liveCategory/addLiveCategory`, params)
    },

    // 编辑直播分类
    editLiveCategory: params => {
        return request.post(`/live-center/liveCategory/editLiveCategory`, params)
    },

    // 直播分类字典表查询
    getLiveCategoryDicList: () => {
        return request.post(`/live-center/liveCategory/getLiveCategoryDicList`)
    },

    // 获取直播列表
    medsciLiveList: params => {
        return request.post(`/live-center/manage/live/list`, params)
    },
    // 导入直播水军
    liveWater: params => {
      return request.post(`/live-center/manage/live/water`, params)
  },

    // 直播添加
    medsciLiveSave: params => {
        return request.post(`/live-center/manage/live/save`, params)
    },
    // 直播回放地址
    medsciLivePlayback: params => {
        return request.post(`/live-center/manage/live/playback`, params)
    },

    // 直播更新
    medsciLiveUpdate: params => {
        return request.post(`/live-center/manage/live/update`, params)
    },

    // 获取直播详情
    medsciLiveById: id => {
        return request.get(`/live-center/manage/live/${id}?date=${new Date().getTime()}`)
    },

    // 直播禁流
    liveForbid: params => {
        return request.post(`/live-center/manage/live/forbid`, params)
    },

    // 直播地址
    GenerateChatRoom: params => {
        return request.post(`/live-center/generateChatRoom`, params)
    },
    
    // 直播批量操作
    batchDealLive: params => {
      return request.post(`/live-center/manage/live/batchDealLive`, params)
    },

    // 回收站列表
    liveRecycleList: params => {
      return request.post(`/live-center/manage/live/recycleList`, params)
    },

    // 暂停直播，恢复直播
    stopAndRecoveryLive: params => {
      return request.post(`/live-center/manage/live/stopAndRecoveryLive`, params)
    },

    // 获取直播详情
    exportLiveData: id => {
      return request.get(`/live-center/export/userInfo?liveId=${id}`)
    },

    // 直播uv列表
    liveUvList: params => {
      return request.post(`/live-center/live/uv/list`, params)
    },

    // 添加直播调研
    insertSurvey: params => {
      return request.post(`/live-center/manage/live/insertSurvey`, params)
    },

    // 异步直播导出
    exportUserInfoSync: id => {
      return request.get(`/live-center/export/userInfoSync?liveId=${id}`)
    },

    // 异步直播导出查询
    userInfoSyncQuery: id => {
      return request.get(`/live-center/export/userInfoSyncQuery?key=${id}`)
    },
}