import request from '../../utils/request'
import store from '../../store/index'

export default {
  // 获取新版医院列表
  getNewHospitalList: params => {
    return request.post(`/mg-user-service/medsci-common/getNewHospitalList`, params)
  },
  // 添加活动模板
  insertActivityTemplate: params => {
    return request.post(`/mg-user-service/medsciActivityTemplate/insertActivityTemplate`,
      { ...params, userId: store.getters.info && store.getters.info.userId, username: store.getters.info && store.getters.info.userName }
    )
  },
  // 查询活动列表
  getActivityTemplatePage: params => {
    return request.post(`/mg-user-service/medsciActivityTemplate/getActivityTemplatePage`, params)
  },
  // 活动模板详情 
  getActivityTemplateById: id => {
    return request.get(`/mg-user-service/medsciActivityTemplate/getActivityTemplateById/${id}`)
  },
  // 修改活动模板 
  updateActivityTemplate: params => {
    return request.post(`/mg-user-service/medsciActivityTemplate/updateActivityTemplate`,
    { ...params, userId: store.getters.info && store.getters.info.userId, username: store.getters.info && store.getters.info.userName }
    )
  },
  // 批量审核活动模板
  batchDealActivityTemplate: params => {
    return request.post(`/mg-user-service/medsciActivityTemplate/batchDealActivityTemplate`,
      {...params, userId: store.getters.info && store.getters.info.userId, username: store.getters.info && store.getters.info.userName }
    )
  },
  // 查询用户列表
  getParticipatePage: params => {
    return request.post(`/mg-user-service/medsciActivityParticipate/getParticipatePage`, params )
  },
  // 用户数据导出 
  participateExportExcel: params => {
    return request.post(`/mg-user-service/medsciActivityParticipate/exportExcel`, params)
  },
  // 选择用户数据导出
  // 邀请用户列表 
  getRecommenderParticipatePage: params => {
    return request.post(`/mg-user-service/medsciActivityParticipate/getRecommenderParticipatePage`, params)
  }
}