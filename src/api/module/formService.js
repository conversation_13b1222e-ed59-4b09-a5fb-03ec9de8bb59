import request from '../../utils/request-form'

export default {
  // 表单分页查询
  formService_getFormList: params => {
      return request.get(`/form-service/form/form`, {params:params})
  },

  // 创建表单
  formService_saveForm: params => {
    return request.post(`/form-service/form/form`, params)
  },

  // 修改表单
  formService_updateForm: params => {
    return request.put(`/form-service/form/form`, params)
  },

  // 批量删除表单
  formService_deleteForm: params => {
    return request.delete(`/form-service/form/form?ids=${params.ids}`)
  },

  // 批量审核/去审表单
  formService_operateForm: params => {
    return request.put(`/form-service/form/form/audit/${params.status}?ids=${params.ids}`)
  },
   
  // 表单详情
  formService_formDetails: params => {
    return request.get(`/form-service/form/form/${params}`)
  },

  // 导出表单
  formService_exportForm: params => {
    return request.get(`/form-service/form/form-data/export/${params}`)
  },

}