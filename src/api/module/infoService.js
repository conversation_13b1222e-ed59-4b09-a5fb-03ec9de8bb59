import request from '../../utils/request'

export default {
  /**
   * 资讯文章
   */
  // 获取文章计数
  articleViewsCount: params => {
    return request.post(`/info-center/article/articleViewsCount`, params)
  },

  // 文章批量处理
  batchDealArticle: params => {
    return request.post(`/info-center/article/batchDealArticle`, params)
  },

  // 删除文章
  deleteArticleById: params => {
    return request.post(`/info-center/article/deleteArticleById`, params)
  },

  // 新增文章
  saveArticle: params => {
    const submitData = JSON.parse(JSON.stringify(params))
    const newAttachmentList = submitData.attachmentList.map(file => {
      return {
        attachmentKey: file.fileKey,
        attachmentUrl: file.url,
        fileName: file.name
      }
    })
    submitData.attachmentList = newAttachmentList
    return request.post(`/info-center/article/saveArticle`, submitData)
  },

  // 更新文章
  updateArticleById: params => {
    const submitData = JSON.parse(JSON.stringify(params))
    const newAttachmentList = submitData.attachmentList.map(file => {
      return {
        attachmentKey: file.fileKey,
        attachmentUrl: file.url,
        fileName: file.name
      }
    })
    submitData.attachmentList = newAttachmentList
    return request.post(`/info-center/article/updateArticleById`, submitData)
  },

  // 根据文章id获取文章详情
  getArticleById: params => {
    return request.post(`/info-center/article/getArticleById`, params)
  },

  // 查询文章列表分页
  // getArticlePage: params => {
  //   return request.post(`/info-center/article/getArticlePage`, params)
  // },

  // // 查询文章列表分页（新）
  getArticlePage: params => {
    return request.post(`/info-center/article/getArticlePageNew`, params)
  },

  // 查询拓展阅读
  getExpandReadArticle: params => {
    return request.post(`/info-center/article/getExpandReadArticle`, params)
  },

  // 查询文章相关资讯
  getRelationArticle: params => {
    return request.post(`/info-center/article/getRelationArticle`, params)
  },

  // 猜你感兴趣
  guessInterested: params => {
    return request.post(`/info-center/article/guessInterested`, params)
  },

  // 置顶或推荐
  recommendOrSticky: params => {
    return request.post(`/info-center/article/recommendOrSticky`, params)
  },

  // 查看回收站文章
  getArticleInTrash: params => {
    return request.post(`/info-center/article/getArticleInTrash`, params)
  },

  // 回收站文章恢复
  recoverArticleInTrashById: params => {
    return request.post(`/info-center/article/recoverArticleInTrashById`, params)
  },

  // 回收站文章彻底删除
  removeArticleInTrashById: params => {
    return request.post(`/info-center/article/removeArticleInTrashById`, params)
  },

  // 推至其他项目
  pushToOtherProjects: params => {
    return request.post(`/info-center/article/pushToOtherProjects`, params)
  },

  // 根据资讯标题模糊查询相关资讯
  getArticleListLikeByTitle: params => {
    return request.post(`/info-center/article/getArticleListLikeByTitle`, params)
  },

  // 获取文章内容
  getArticleContentById: params => {
    return request.post(`/info-center/article/getArticleContentById`, params)
  },

  // 清除资讯/指南缓存
  clearCache: params => {
    return request.post(`/info-center/article/clearCache`, params)
  },
  pushQuickNews: params => {
    return request.post(`/info-center/article/pushQuickNews`, params)
  },
  // 文章批量修改分类
  batchModifyCategory: params => {
    return request.post(`/info-center/article/batchModifyCategory`, params)
  },

  /**
   * 指南
   */
  // 指南批量处理
  batchDealToolGuider: params => {
    return request.post(`/info-center/guider/batchDealToolGuider`, params)
  },
  // 指南批量修改分类
  guiderBatchModifyCategory: params => {
    return request.post(`/info-center/guider/batchModifyCategory`, params)
  },
  // 删除指南
  deleteToolGuiderById: params => {
    return request.post(`/info-center/guider/deleteToolGuiderById`, params)
  },
  // 查询指南拓展阅读
  getExpandReadToolGuider: params => {
    return request.post(`/info-center/guider/getExpandReadToolGuider`, params)
  },
  // 查询相关指南
  getRelationToolGuider: params => {
    return request.post(`/info-center/guider/getRelationToolGuider`, params)
  },
  // 根据指南id获取详情
  getToolGuiderById: params => {
    return request.post(`/info-center/guider/getToolGuiderById`, params)
  },
  // 查看指南回收站
  getToolGuiderInTrash: params => {
    return request.post(`/info-center/guider/getToolGuiderInTrash`, params)
  },
  // 查询指南分页
  getToolGuiderPage: params => {
    return request.post(`/info-center/guider/getToolGuiderPage`, params)
  },
  // 查询指南猜你感兴趣
  guessGuiderInterested: params => {
    return request.post(`/info-center/guider/guessGuiderInterested`, params)
  },
  // 指南置顶或推荐
  guiderRecommendOrSticky: params => {
    return request.post(`/info-center/guider/guiderRecommendOrSticky`, params)
  },
  // 回收站指南恢复
  recoverToolGuiderInTrashById: params => {
    return request.post(`/info-center/guider/recoverToolGuiderInTrashById`, params)
  },
  // 回收站指南彻底删除
  removeToolGuiderInTrashById: params => {
    return request.post(`/info-center/guider/removeToolGuiderInTrashById`, params)
  },
  // 添加指南
  saveToolGuider: params => {
    const submitData = JSON.parse(JSON.stringify(params))
    const newAttachmentList = submitData.attachmentList.map(file => {
      return {
        attachmentKey: file.fileKey,
        attachmentUrl: file.url,
        fileName: file.name
      }
    })
    submitData.attachmentList = newAttachmentList
    return request.post(`/info-center/guider/saveToolGuider`, submitData)
  },
  // 指南计数
  toolGuiderCount: params => {
    return request.post(`/info-center/guider/toolGuiderCount`, params)
  },
  // 修改指南
  updateToolGuiderById: params => {
    const submitData = JSON.parse(JSON.stringify(params))
    const newAttachmentList = submitData.attachmentList.map(file => {
      return {
        attachmentKey: file.fileKey,
        attachmentUrl: file.url,
        fileName: file.name
      }
    })
    submitData.attachmentList = newAttachmentList
    return request.post(`/info-center/guider/updateToolGuiderById`, submitData)
  },
  // 获取指南内容
  getToolGuiderContentById: params => {
    return request.post(`/info-center/guider/getToolGuiderContentById`, params)
  },

  /**
   * 期刊
   */
  // 期刊批量修改分类
  batchModifyJournal: params => {
    return request.post(`/info-center/toolImpactFactor/batchModifyCategory`, params)
  },
  // 按名称搜索期刊
  searchJournal: params => {
    return request.post(`/info-center/toolImpactFactor/search`, params)
  },
  // 添加期刊
  createJournal: params => {
    return request.post(`/info-center/toolImpactFactor/create`, params)
  },
  // 删除期刊
  deleteJournal: id => {
    return request.delete(`/info-center/toolImpactFactor/delete/${id}`)
  },
  // 期刊列表
  getImpactFactorPage: params => {
    return request.post(`/info-center/toolImpactFactor/getImpactFactorPage`, params)
  },
  // 查看期刊详情-所属期刊中文
  showJournalCn: params => {
    return request.post(`/info-center/toolImpactFactor/getJournalInfo`, params)
  },
  // 查看期刊详情
  showJournal: params => {
    return request.post(`/info-center/toolImpactFactor/show`, params)
  },
  // 编辑期刊
  updateJournal: params => {
    return request.post(`/info-center/toolImpactFactor/update`, params)
  },
  // 删除贡献期刊
  deleteContribution: params => {
    return request.post(`/info-center/toolJournalContribution/delete`, params)
  },
  // 贡献期刊列表
  getContributionPage: params => {
    return request.post(`/info-center/toolJournalContribution/getContributionPage`, params)
  },
  // 审核贡献期刊
  auditContribution: params => {
    return request.post(`/info-center/toolJournalContribution/audit`, params)
  },

  /**
   * 协会组织
   */
  // 删除协会
  deleteAssociationById: params => {
    return request.post(`/info-center/association/deleteAssociationById`, params)
  },
  // 查询协会详情
  getAssociationById: params => {
    return request.post(`/info-center/association/getAssociationById`, params)
  },
  // 查询协会选择列表
  getAssociationList: params => {
    return request.post(`/info-center/association/getAssociationList`, params)
  },
  // 查询协会分页
  getAssociationPage: params => {
    return request.post(`/info-center/association/getAssociationPage`, params)
  },
  // 添加协会
  saveAssociation: params => {
    return request.post(`/info-center/association/saveAssociation`, params)
  },
  // 修改协会
  updateAssociationById: params => {
    return request.post(`/info-center/association/updateAssociationById`, params)
  },

  /**
   * 期刊上传
   */
  // 获取期刊上传历史列表
  getJournalExcelPage: params => {
    return request.post(`/info-center/toolJournalExcel/getExcelPage`, params)
  },
  // 期刊临时文件查询
  getJournalPageByQuery: params => {
    return request.post(`/info-center/toolJournalExcel/getPageByQuery`, params)
  },
  // 根据id查询excel执行结果
  getJournalExcelById: params => {
    return request.post(`/info-center/toolJournalExcel/getExcelById`, params)
  },
  // 期刊文件上传 （第一步）
  uploadJournal: params => {
    return request.post(`/info-center/toolJournalExcel/addFile`, params)
  },
  // 期刊匹配 （第二步）
  matchJournal: params => {
    return request.post(`/info-center/toolJournalExcel/matchJournal`, params)
  },
  // 期刊导入 （第三步）
  executeJournal: params => {
    return request.post(`/info-center/toolJournalExcel/executeJournal`, params)
  },
  // 查看执行状态
  getProcessState: params => {
    return request.post(`/info-center/toolJournalExcel/getProcessState`, params)
  },

  /**
   * 协议管理
   */
  // 协议批量处理
  batchDealAgreement: params => {
    return request.post(`/info-center/agreement/batchDealAgreement`, params)
  },
  // 获取协议内容
  getAgreementContentById: params => {
    return request.post(`/info-center/agreement/getAgreementContentById`, params)
  },
  // 根据协议id获取详情
  getAgreementById: params => {
    return request.post(`/info-center/agreement/getAgreementById`, params)
  },
  // 查看回收站协议
  getAgreementInTrash: params => {
    return request.post(`/info-center/agreement/getAgreementInTrash`, params)
  },
  // 查询协议分页
  getAgreementPage: params => {
    return request.post(`/info-center/agreement/getAgreementPage`, params)
  },
  // 回收站协议恢复
  recoverAgreementInTrashById: params => {
    return request.post(`/info-center/agreement/recoverAgreementInTrashById`, params)
  },
  // 回收站协议彻底删除
  removeAgreementInTrashById: params => {
    return request.post(`/info-center/agreement/removeAgreementInTrashById`, params)
  },
  // 新增协议
  saveAgreement: params => {
    return request.post(`/info-center/agreement/saveAgreement`, params)
  },
  // 修改协议
  updateAgreementById: params => {
    return request.post(`/info-center/agreement/updateAgreementById`, params)
  },

  /**
   * 爬虫数据管理
   */
  // 爬虫数据导入
  batchSpiderDeal: params => {
    return request.post(`/info-center/articleTmp/batchDeal`, params)
  },
  // 获取爬虫数据详情
  getSpiderById: params => {
    return request.post(`/info-center/articleTmp/getById`, params)
  },
  // 获取爬虫数据分页
  getSpiderPage: params => {
    return request.post(`/info-center/articleTmp/getPage`, params)
  },

  /**
   * 文件上传
   */
  // 获取上传Token
  getToken: params => {
    return request.post(`/info-center/ossFile/getToken`, params)
  },
  // 获取公有空间的随机图列表
  getRandomImageList: params => {
    return request.get(`/info-center/ossFile/getRandomImageList`, params)
  },
  // 处理第三方图片并转存
  transferPictureUrl: params => {
    return request.post(`/info-center/ossFile/transferPictureUrl`, params)
  },

  // 医讯达-处方
  // 获取处方详情
  getPrescriptionById: params => {
    return request.post(`/info-center/medsciPrescription/GetPrescriptionById`, params)
  },
  // 处方分页查询
  getPrescriptionPage: params => {
    return request.post(`/info-center/medsciPrescription/getPrescriptionPage`, params)
  },
  // 新增处方
  savePrescription: params => {
    return request.post(`/info-center/medsciPrescription/savePrescription`, params)
  },
  // 修改处方
  updatePrescription: params => {
    return request.post(`/info-center/medsciPrescription/updatePrescription`, params)
  },
  // 启用-禁用-处方
  verifyPrescription: params => {
    return request.post(`/info-center/medsciPrescription/verifyPrescription`, params)
  },

  // 医讯达-列表
  // 批量操作 type 1,批量删除,2,批量恢复删除(回收站)3.批量审核,4批量去审,5批量彻底删除
  batchDealMedsciEdaArticle: params => {
    return request.post(`/info-center/medsciEdaArticle/batchDealMedsciEdaArticle`, params)
  },
  // 获取eda详情
  getMedsciEdaArticleById: params => {
    return request.post(`/info-center/medsciEdaArticle/getMedsciEdaArticleById`, params)
  },
  // eda分页查询
  getPageMedsciEdaArticle: params => {
    return request.post(`/info-center/medsciEdaArticle/getPageMedsciEdaArticle`, params)
  },
  // 回收站列表
  recycleList: params => {
    return request.post(`/info-center/medsciEdaArticle/recycleList`, params)
  },
  // 新增eda
  saveMedsciEdaArticle: params => {
    return request.post(`/info-center/medsciEdaArticle/saveMedsciEdaArticle`, params)
  },
  // 修改eda
  updateMedsciEdaArticle: params => {
    return request.post(`/info-center/medsciEdaArticle/updateMedsciEdaArticle`, params)
  },

  // 医讯达明细分页查询
  getMedsciEdaRecordPage: params => {
    return request.post(`/info-center/medsciEdaRecord/getMedsciEdaRecordPage`, params)
  },

  // 专题管理
  // 列表分页查询
  getPageSpecialTopicByTitle: params => {
    return request.post(`/info-center/medsciSpecialTopic/getPageSpecialTopicByTitle`, params)
  },

  // 专题批量操作
  specialBatchDeal: params => {
    return request.post(`/info-center/medsciSpecialTopic/batchDeal`, params)
  },

  // 专题回收站彻底删除
  specialCompleteRemoveById: params => {
    return request.post(`/info-center/medsciSpecialTopic/completeRemoveById`, params)
  },

  // 专题详情
  getMedsciSpecialTopicById: params => {
    return request.post(`/info-center/medsciSpecialTopic/getMedsciSpecialTopicById`, params)
  },

  // 专题分享分页
  getPageShareRecord: params => {
    return request.post(`/info-center/medsciSpecialTopic/getPageShareRecord`, params)
  },

  // 专题回收站列表
  specialGetRecycleList: params => {
    return request.post(`/info-center/medsciSpecialTopic/getRecycleList`, params)
  },

  // 专题回收站恢复
  recoverSpecialTopicById: params => {
    return request.post(`/info-center/medsciSpecialTopic/recoverSpecialTopicById`, params)
  },

  // 创建专题
  saveMedsciSpecialTopic: params => {
    return request.post(`/info-center/medsciSpecialTopic/saveMedsciSpecialTopic`, params)
  },

  // 选择内容及标题关键字检索
  searchContentTitle: params => {
    return request.post(`/info-center/medsciSpecialTopic/searchContentTitle`, params)
  },

  // 编辑专题
  updateMedsciSpecialTopic: params => {
    return request.post(`/info-center/medsciSpecialTopic/updateMedsciSpecialTopic`, params)
  },

  // 保存期刊音频
  saveToolImpactFactorAttachment: params => {
    return request.post(`/info-center/toolImpactFactor/saveToolImpactFactorAttachment`, params)
  },

  // 内容标签

  // insertSystemLog: params => {
  //   // return request.post(`/medsci-mgr-center-service/medsciSystemLog/insertSystemLog`, params)
  // },
  tagLabelGetChildClaim: params => {
    return request.post(`/tag-center/tagLabel/getChildClaim`, params)
  },
  tagLabelGetChild: params => {
    return request.post(`/tag-center/tagLabel/getChild`, params)
  },
  tagLabelUpdate: params => {
    return request.post(`/tag-center/tagLabel/update`, params)
  },
  tagLabelOperate: params => {
    return request.post(`/tag-center/tagLabel/operate`, params)
  },
  tagLabelList: params => {
    return request.post(`/tag-center/tagLabel/list`, params)
  },
  tagLabelDetails: params => {
    return request.get(`/tag-center/tagLabel/details/${params.id}`)
  },
  tagLabelAdd: params => {
    return request.post(`/tag-center/tagLabel/add`, params)
  },
  tagLabelSearch: params => {
    return request.post(`/tag-center/tagLabel/search`, params)
  },
  tagSearch: params => {
    return request.post(`/tag-center/tag/search`, params)
  },
  labelList: params => {
    return request.post(`/tag-center/label/list`, params)
  },
  labelAdd: params => {
    return request.post(`/tag-center/label/add`, params)
  },
  labelOperate: params => {
    return request.post(`/tag-center/label/operate`, params)
  },
  labelSearch: params => {
    return request.post(`/tag-center/label/search`, params)
  },
  labelDetails: params => {
    return request.get(`/tag-center/label/details/` + params.id)
  },
  labelUpdate: params => {
    return request.post(`/tag-center/label/update`, params)
  },
  labelLogList: params => {
    return request.post(`/tag-center/labelLog/pageList`, params)
  },
  labelChildList: params => {
    return request.post(`/tag-center/label/child/list`, params)
  },
   /**
  * 关键词管理
  */
 //热搜推荐
 getHotSearchRecommendPage: params => {
  return request.post(`/medsci-mgr-center-service/medsciHotSearchRecommend/getHotSearchRecommendPage`, params)
},
//关键词管理
getSearchDailyStatisticsPage: params => {
  return request.post(`/medsci-mgr-center-service/medsciSearchDailyStatistics/getSearchDailyStatisticsPage`, params)
},
//删除热搜推荐信息
removeHotSearchRecommend: params => {
  return request.post(`/medsci-mgr-center-service/medsciHotSearchRecommend/removeHotSearchRecommend`, params)
},
//添加关键词
saveHotSearchRecommend: params => {
  return request.post(`/medsci-mgr-center-service/medsciHotSearchRecommend/saveHotSearchRecommend`, params)
},
//修改热搜推荐信息
updateHotSearchRecommend: params => {
  return request.post(`/medsci-mgr-center-service/medsciHotSearchRecommend/updateHotSearchRecommend`, params)
},

/**
 * 数据评估
 */
// 获取数据评估记录分页列表
// 参数: { title, type, pageIndex, pageSize, roleId }
getEvaluationRecordPage: params => {
  return request.post(`/medsci-mgr-center-service/evaluationRecord/getEvaluationRecordPage`, params)
},

// 上传评估报告
// 参数: { id, editorId, editorName, reportFilePath }
uploadEvaluationReportFile: params => {
  return request.post(`/medsci-mgr-center-service/evaluationRecord/uploadReportFile`, params)
},

}
