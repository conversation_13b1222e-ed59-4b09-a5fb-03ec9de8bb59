import request from '../../utils/request'

export default {
    // 直播分类查询
    getLiveCategoryPage: params => {
        return request.post(`/live-center/liveCategory/getLiveCategoryPage`, params)
    },

    /**
     * 视频素材
     */
    // 获取素材列表
    getVideoMaterialList: params => {
        return request.post(`/material/video/list`, params)
    },
    // 操作视频素材
    videoMaterialOpera: params => {
        return request.post(`/material/video/operate`, params)
    },
    // 新增视频素材
    saveVideoMaterial: params => {
        return request.post(`/material/video/save`, params)
    },
    // 修改视频素材
    updateVideoMaterial: params => {
        return request.post(`/material/video/update`, params)
    },
    // 视频素材详情
    getVideoMaterialById: id => {
        return request.get(`/material/video/${id}?date=${new Date().getTime()}`)
    },
    // 视频素材回收站
    getVideoMaterialRecycle: params => {
        return request.post(`/material/video/recycleBin/list`, params)
    },

    /**
     * 图片素材
     */
    // 删除图片素材
    deleteImageMaterial: params => {
        return request.post(`/material/image/batch/operate`, params)
    },
    // 修改图片素材
    updateImageMaterial: params => {
        return request.post(`/material/image/update`, params)
    },
    // 保存图片素材
    saveImageMaterial: params => {
        return request.post(`/material/image/save`, params)
    },
    // 图片素材列表 - 项目
    getImageMaterialListByProject: params => {
        return request.post(`/material/image/list`, params)
    },
    // 图片素材列表 - 公共
    getImageMaterialList: params => {
        return request.post(`/material/image/sharelist`, params)
    },
    // 图片素材回收站
    getImageMaterialRecycle: params => {
        return request.post(`/material/image/recycleBin`, params)
    },
    // 音频

    // 保存音频素材库
    saveAudioMaterial: params => {
        return request.post(`/material/audio/saveMaterialAudio`, params)
    },
    // 修改音频素材
    updateAudioMaterial: params => {
        return request.post(`/material/audio/updateMaterialAudio`, params)
    },
    // 音频素材列表
    getAudioMaterialListByProject: params => {
        return request.post(`/material/audio/materialAudioList`, params)
    },
    // 删除音频素材
    deleteAudioMaterial: params => {
        return request.post(`/material/audio/batch/operate`, params)
    },
    // 回收站音频素材列表
    getAudioMaterialRecycle: params => {
        return request.post(`/material/audio/recycleMaterialAudioList`, params)
    },
    // 恢复回收站音频素材
    recoverAudioMaterial: params => {
        return request.post(`/material/audio/recoverMaterialAudio`, params)
    },
    // 彻底删除回收站音频素材
    delAudioMaterial: params => {
        return request.post(`/material/audio/deleteMaterialAudio`, params)
    },
    /**
     * 表单素材管理
     */
    // 表单分页列表
    getFormMaterialList: params => {
        return request.post(`/material/customForm/pageList`, params)
    },
    // 表单保存
    saveFormMaterial: params => {
        return request.post(`/material/customForm/save`, params)
    },
    // 表单更新
    updateFormMaterial: params => {
        return request.post(`/material/customForm/updateById`, params)
    },
    // 根据id获取表单详情
    getCustomFormById: params => {
        return request.post(`/material/customForm/getCustomFormById`, params)
    },
    // 通过模板id 获取模板
    // getFormTemplateId: id => {
    //     return request.get(`/mid-service/api/form-build/erp-apodaily/erp-form/form/template/${id}?date=${new Date().getTime()}`)
    // },
    // 表单批量处理
    formMaterialDeal: params => {
        return request.post(`/material/customForm/batchDealCustomForm`, params)
    },
    // 表单置顶或推荐
    formMaterialRecommendOrSticky: params => {
        return request.post(`/material/customForm/customFormRecommendOrSticky`, params)
    },
    // 查看表单回收站列表
    formMaterialInTrash: params => {
        return request.post(`/material/customForm/getCustomFormInTrash`, params)
    },
    // 回收站表单恢复
    formMaterialRecover: params => {
        return request.post(`/material/customForm/recoverCustomFormInTrashById`, params)
    },
    // 回收站表单彻底删除
    formMaterialRemove: params => {
        return request.post(`/material/customForm/removeCustomFormById`, params)
    },
    // 获取导出链接
    getFormExport: params => {
        return request.post(`/material/customForm/excelExport`, params)
    },
    // 获取省份
    getMedsciAddress: params => {
        return request.post(`/material/customForm/getMedsciAddress`, params)
    },
    // 调研uv列表
    getSurveyUvRecordPage: params => {
      return request.post(`/material/surveyUvRecord/getSurveyUvRecordPage`, params)
    },
    // 调研已提交列表
    getSurveyRecordPage: params => {
      return request.post(`/material/medsciSurveyRecord/getSurveyRecordPage`, params)
    },
    // 校验添加规则
    makeRuleCheck: params => {
        return request.post(`/material/customForm/rule/check`, params)
    },

    // 查询统计
    statisticsDetail: params => {
        return request.post(`/material/medsciSurvey/statisticsDetail`, params)
    },
// 统计主观题详情
    subjectiveList:params=>{
        return request.post(`/material/medsciSurvey/subjectiveList`, params)
    },
    // 导出excel
    subjectiveListExport:params=>{
        return request.post(`material/medsciSurvey/subjectiveList/export`, params)
    },
    // 异步获取导出链接
    excelExportSync: params => {
      return request.post(`/material/customForm/excelExportSync`, params)
    },
    // 查询异步获取导出链接
    excelExportSyncQuery: id => {
      return request.get(`/material/customForm/excelExportSyncQuery?key=${id}`)
    },

}