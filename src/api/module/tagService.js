import request from '../../utils/request'

export default {
  // 查看分类
  getCategory: params => {
    return request.post(`/tag-center/category/getCategoryPage`, params)
  },

  // 查看子分类
  getCategoryChild: categoryId => {
    return request.get(`/tag-center/category/getChild/`+categoryId)
  },

  // 查看分类详情
  getCategoryDetail: categoryId => {
    return request.get(`/tag-center/category/show/`+categoryId)
  },

  // 添加分类
  crateCategory: params => {
    return request.post(`/tag-center/category/createCategory`, params)
  },

  // 更新分类
  updateCategory: params => {
    return request.put(`/tag-center/category/updateCategory`, params)
  },
  
  // 禁用启用分类
  deleteCategory: params => {
    return request.post(`/tag-center/category/deleteCategory`, params)
  },

  // 查看横向分类
  getTransverseCategory: () => {
    return request.get(`/tag-center/category/getTransverseCategory`)
  },

  // 根据模块名称获取树型分类
  getCategoryTreeByModule: params => {
    return request.post(`/tag-center/category/getCategoryTreeByModule`, params)
  },

  /**
   * 项目分类 medsciProjectCategory/deleteBatchProjectCategory
   */
  // 批量删除项目分类关系
  deleteBatchProjectCategory: params => {
    return request.post(`/tag-center/medsciProjectCategory/deleteBatchProjectCategory`, params)
  },
  // 删除项目分类关系
  deleteProjectCategory: params => {
    return request.post(`/tag-center/medsciProjectCategory/deleteProjectCategory`, params)
  },
  // 查询所有分类树形列表和当前项目分类list
  getImportProjectCategotyTreeList: params => {
    return request.post(`/tag-center/medsciProjectCategory/getImportProjectCategotyTreeList`, params)
  },
  // 项目分类树形列表条件查询
  getProjectModuleAndCategoryList: params => {
    return request.post(`/tag-center/medsciProjectCategory/getProjectModuleAndCategoryList`, params)
  },
  // 查询项目分类模块
  getProjectCategoryModuleList: params => {
    return request.post(`/tag-center/medsciProjectCategory/getProjectCategoryModuleList`, params)
  },
  // 保存项目分类关系列表
  saveProjectCategoryList: params => {
    return request.post(`/tag-center/medsciProjectCategory/saveProjectCategoryList`, params)
  },
  // 项目分类修改
  UpdateProjectCategory: params => {
    return request.post(`/tag-center/medsciProjectCategory/UpdateProjectCategory`, params)
  },
  // 项目分类详情查询
  getProjectCategoryDetail: params => {
    return request.post(`/tag-center/medsciProjectCategory/getProjectCategoryDetail`, params)
  },
  // 启用禁用模块分类
  updateProjectCategoryModule: params => {
    return request.post(`/tag-center/medsciProjectCategory/updateProjectCategoryModule`, params)
  },
  // 启用/禁用项目分类关系
  updateProjectCategoryStatus: params => {
    return request.post(`/tag-center/category/deleteCategory`, params)
  },


  /**
   * 标签
   */
  // 按名称搜索标签
  searchTag: params => {
    return request.post(`/tag-center/tag/search`, params)
  }
}