import request from '../../utils/request'

export default {
  // 用户登录
  userLogin: params => {
    return request.post(`/mg-user-service/user-auth/userLogin`, params)
  },

  // 用户token校验
  Validate: params => {
    return request.get(`/mg-user-service/user-auth/Validate`, params)
  },

  // 滑块token校验
  sliderValidate: params => {
    return request.post(`/mg-user-service/user-auth/checkSliderValidation`, params)
  },

  // 修改用户密码
  changeUserPwd: params => {
    return request.post(`/mg-user-service/user-auth/changeUserPwd`, params)
  },

  //获取用户列表
  getUserList: params => {
    return request.post(`/mg-user-service/medsci-user/listMedsciUsersV2`, params)
  },

  //用户列表查重信息
  checkUserList: params => {
    return request.post(`/mg-user-service/medsci-user/getUserList`, params)
  },

  // 查看用户详情
  getUserEeatil: params => {
    return request.post(`/mg-user-service/medsci-user/getSingleUserV2`, params)
  },

  // 修改用户状态
  changeUserStatus: params => {
    return request.post(`/mg-user-service/medsci-user/changeUserStatusV2`, params)
  },

  // 新增用户信息/扩展信息
  addMedsciUserAndExt: params => {
    return request.post(`/mg-user-service/medsci-user/addMedsciUserAndExt`, params)
  },

  // 编辑用户信息/扩展信息
  updateMedsciUserAndExt: params => {
    return request.post(`/mg-user-service/medsci-user/updateMedsciUserAndExtV2`, params)
  },

  // 个人你修改用户密码
  changePersonalUserPassword: params => {
    return request.post(`/mg-user-service/medsci-user/changePersonalUserPassword`, params)
  },

  // 修改用户积分
  updateIntegral: params => {
    return request.post(`/mg-user-service/medsci-user/updateIntegralV2`, params)
  },

  // 码表api
  // 获取城市列表
  getDistrictList: params => {
    return request.post(`/mg-user-service/medsci-common/getDistrictList`, params)
  },

  // 获取地区树形信息
  getMedsciTreeAddress: params => {
    return request.post(`/mg-user-service/medsci-common/getMedsciTreeAddress`, params)
  },

  getMedsciAddress: params => {
    return request.post(`/mg-user-service/medsci-common/getMedsciAddress`, params)
  },

  // 根据省市区查询医院列表
  getHospitalListByAddress: params => {
    return request.post(`/mg-user-service/medsci-common/getHospitalListByAddress`, params)
  },

  // 获取医院科室列表
  getHospitalDepartmentList: params => {
    return request.post(`/mg-user-service/medsci-common/getHospitalDepartmentList`, params)
  },

  // 获取医院列表
  getHospitalList: params => {
    return request.post(`/mg-user-service/medsci-common/getHospitalList`, params)
  },

  // 获取职称列表
  getProfessionalList: params => {
    return request.post(`/mg-user-service/medsci-common/getProfessionalList`, params)
  },

  // 查询树形职称信息
  getTitleList: params => {
    return request.post(`/mg-user-service/medsci-common/getTitleList`, params)
  },

  // 查询用户积分流水信息分页
  getUserIntegralTransactionPage: params => {
    return request.post(`/mg-user-service/userIntegralTransaction/getUserIntegralTransactionPage`, params)
  },

  // 讲师字典查询
  getMedsciLecturerDicList: params => {
    return request.post(`/mg-user-service/medsci-user/getMedsciLecturerDicList`, params)
  },

  // 直播执行（项目管理员）查询
  getUserByAdminRoleDicList: params => {
    return request.post(`/mg-user-service/medsci-user/getUserByAdminRoleDicList`, params)
  },

  // 查询用户认证信息分页
  getUserCertificationPage: params => {
    return request.post(`/mg-user-service/medsciUserCertification/getUserCertificationPageV2`, params)
  },

  // 查询用户认证详情
  getCertificationProjectDetail: params => {
    return request.post(`/mg-user-service/medsciUserCertification/getCertificationProjectDetailV2`, params)
  },

  // 用户认证信息审核
  examineUserCertification: params => {
    return request.post(`/mg-user-service/medsciUserCertification/examineUserCertificationV2`, params)
  },

  // 批量审核用户认证信息
  batchExamineUserCertification: params => {
    return request.post(`/mg-user-service/medsciUserCertification/BatchExamineUserCertificationV2`, params)
  },


  // 删除码库
  codeLibraryDeleteById: params => {
    return request.post(`/mg-user-service/medsciExchangeCodeLibrary/deleteById`, params)
  },
  // 导出
  codeLibraryExcelExport: params => {
    return request.post(`/mg-user-service/medsciExchangeCodeLibrary/excelExport`, params)
  },
  // 兑换码兑换
  codeLibraryExchangeIntegral: params => {
    return request.post(`/mg-user-service/medsciExchangeCodeLibrary/exchangeIntegral`, params)
  },
  // 码库分页查询
  getExchageCodeLibraryPage: params => {
    return request.post(`/mg-user-service/medsciExchangeCodeLibrary/getExchageCodeLibraryPage`, params)
  },
  // 兑换库分页查询
  getExchageCodePage: params => {
    return request.post(`/mg-user-service/medsciExchangeCodeLibrary/getExchageCodePage`, params)
  },
  // 新增码库
  codeLibrarySave: params => {
    return request.post(`/mg-user-service/medsciExchangeCodeLibrary/save`, params)
  },
  // 用途模糊匹配
  codeLibrarySelectUseFul: params => {
    return request.post(`/mg-user-service/medsciExchangeCodeLibrary/selectUseFul`, params)
  },

  // 手动创建订单校验用户
  verifyUser: params => {
    return request.get(`/mg-user-service/medsci-user/verifyUser`, params)
  },

  // 直播用户权限列表查询
  getLiveUserAuthList: params => {
    return request.post(`/mg-user-service/medsciLiveUserAuth/getLiveUserAuthList`, params)
  },

  // 新增直播用户权限
  insertLiveUserAuth: params => {
    return request.post(`/mg-user-service/medsciLiveUserAuth/insertLiveUserAuth`, params)
  },

  // 移除直播用户权限
  remonveLiveUserAuth: params => {
    return request.post(`/mg-user-service/medsciLiveUserAuth/remonveLiveUserAuth`, params)
  },


  //获取活动列表
  getActivityList: params => {
    return request.post(`/mg-user-service/medsciActivityRecommends/getList`, params)
  },
  //导出活动列表
  exportActivityExcel: params => {
    return request.get(`/mg-user-service/medsciActivityRecommends/exportExcel`, params)
  },
  //活动统计
  getActivityStatistics: params => {
    return request.get(`/mg-user-service/medsciActivity/getStatistics`, params)
  },
  // 信息审核
  getUserHospitalPage: params => {
    return request.post(`/mg-user-service/medsci-user/getUserHospitalPage`, params)
  },
  examineUserHospital: params => {
    return request.post(`/mg-user-service/medsci-user/examineUserHospital`, params)
  },
  // 医院库
  getEsSearchPage: params => {
    return request.post(`/mg-user-service/medsci-common/getNewHospitalList`, params)
  },
  getUserCount () {
    return request.get(`/mg-user-service/medsci-user/getMedsciUserCount`)
  },
  /**
   * 日志
   */
  // 登录日志
  getLoginLog: params => {
    return request.post(`/mg-user-service/medsciUserLoginLog/getMedsciUserLoginLogList`, params)
  },
  // 登录异常日志
  getLoginErrorLog: params => {
    return request.post(`/mg-user-service/medsciUserLoginLog/getMedsciUserLoginErrorLogList`, params)
  },
  // 操作日志
  getSystemLog: params => {
    return request.post(`/medsci-mgr-center-service/medsciSystemLog/getSystemLogList`, params)
  },

  // 登录双重验证-短信验证码
  backStagesSendSms: params => {
    return request.post(`/mg-user-service/user-auth/backStagesSendSms`, params)
  },
}
