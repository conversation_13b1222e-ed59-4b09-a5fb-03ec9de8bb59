import request from '../../utils/request'

export default {
  // 获取医汇宝任务分页列表
  getMedHuiBaoTaskPage: params => {
    return request.post(`/medsci-mgr-center-service/medHuiBaoTask/getMedHuiBaoTaskPage`, params)
  },

  // 根据ID获取医汇宝任务详情
  getMedHuiBaoTaskById: params => {
    return request.post(`/medsci-mgr-center-service/medHuiBaoTask/getMedHuiBaoTaskById`, params)
  },

  // 保存医汇宝任务
  saveMedHuiBaoTask: params => {
    return request.post(`/medsci-mgr-center-service/medHuiBaoTask/saveMedHuiBaoTask`, params)
  },

  // 更新医汇宝任务
  updateMedHuiBaoTaskById: params => {
    return request.post(`/medsci-mgr-center-service/medHuiBaoTask/updateMedHuiBaoTaskById`, params)
  },

  // 删除医汇宝任务
  deleteMedHuiBaoTaskById: params => {
    return request.post(`/medsci-mgr-center-service/medHuiBaoTask/deleteMedHuiBaoTaskById`, params)
  },

  // 搜索调研内容 - 模糊搜索
  searchSurveyContent: params => {
    return request.post(`/medsci-mgr-center-service/medHuiBaoTask/searchSurveyContent`, params)
  },

  // 搜索直播内容 - 模糊搜索
  searchLiveContent: params => {
    return request.post(`/medsci-mgr-center-service/medHuiBaoTask/searchLiveContent`, params)
  },

  // 搜索阅读内容 - 模糊搜索
  searchReadingContent: params => {
    return request.post(`/medsci-mgr-center-service/medHuiBaoTask/searchReadingContent`, params)
  },

  // 通用内容搜索接口 - 根据类型和标题模糊搜索
  searchContentByTypeAndTitle: params => {
    return request.post(`/medsci-mgr-center-service/medHuiBaoTask/searchContentByTypeAndTitle`, params)
  },

  // 获取调研内容列表 - 使用与/form页面相同的API
  getFormMaterialList: params => {
    return request.post(`/material/customForm/pageList`, params)
  },

  // 获取直播内容列表 - 使用与/medsciLive页面相同的API
  medsciLiveList: params => {
    return request.post(`/live-center/manage/live/list`, params)
  },

  // 获取文章内容列表 - 使用与/article页面相同的API
  getArticlePage: params => {
    return request.post(`/info-center/article/getArticlePageNew`, params)
  }
}
