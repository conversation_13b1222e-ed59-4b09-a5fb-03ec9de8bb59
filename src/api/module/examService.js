import request from '../../utils/request'

export default {
    // 试题集服务
    // 添加试题集
    addGather: params => {
        return request.post(`/exam-service/medsciQuestionGather/addGather`, params)
    },

    // 批量审核/去审
    batchAudit: params => {
        return request.post(`/exam-service/medsciQuestionGather/batchAudit`, params)
    },

    // 批量删除
    batchDelete: params => {
        return request.post(`/exam-service/medsciQuestionGather/batchDelete`, params)
    },

    // 查询试题集详情
    getQuestionGatherDetail: params => {
        return request.post(`/exam-service/medsciQuestionGather/getQuestionGatherDetail`, params)
    },

    // 试题集分页查询
    getQuestionGatherPage: params => {
        return request.post(`/exam-service/medsciQuestionGather/getQuestionGatherPage`, params)
    },

    // 更新试题集
    updateGather: params => {
        return request.post(`/exam-service/medsciQuestionGather/updateGather`, params)
    },


    // 试题集题目关系服务
    // 批量添加试题集题目列表
    batchAddQuestionGatherList: params => {
        return request.post(`/exam-service/medsciQuestionGatherShip/batchAddQuestionGatherList`, params)
    },

    // 批量删除试题集题目关系
    batchDelQuestionGatherShip: params => {
        return request.post(`/exam-service/medsciQuestionGatherShip/batchDelQuestionGatherShip`, params)
    },

    // 试题集详情的试题集题目关系分页查询
    getQuestionGatherShipPage: params => {
        return request.post(`/exam-service/medsciQuestionGatherShip/getQuestionGatherShipPage`, params)
    },


    // 习题题目服务
    // 试题集题目选择分页查询
    getQuestionByGatherPage: params => {
        return request.post(`/exam-service/videosExercises/getQuestionByGatherPage`, params)
    },

    // 题目标签分页查询
    getTagByQuestionPage: params => {
        return request.post(`/exam-service/videosExercises/getTagByQuestionPage`, params)
    },

    // 文件和导入
    // excel-下载题目模板
    downQuestionTem: params => {
        return request.post(`/exam-service/file/downQuestionTem`, params)
    },

    // excel-批量导入题目
    batchAddQuestionsByExcel: params => {
        return request.post(`/exam-service/file/batchAddQuestionsByExcel`, params)
    },

    // excel-异步批量导入题目
    questionFileGetStatus: params => {
        return request.post(`/exam-service/file/get/status`, params)
    },


    // 试卷服务
    // 试卷服务分页查询
    examPaperPage: params => {
        return request.post(`/exam-service/exam-paper`, params)
    },

    // 新增编辑试卷
    examPaperSave: params => {
        return request.post(`/exam-service/exam-paper/save`, params)
    },

    // 试卷详情
    examPaperDetails: params => {
        return request.post(`/exam-service/exam-paper/info`, params)
    },

    // 获取当前试题集包含的关键词列表
    getExamPaperTagsById: params => {
        return request.post(`/exam-service/exam-paper/tags`, params)
    },

    // 试卷批量审核/去审/删除
    examPaperStatus: params => {
        return request.post(`/exam-service/exam-paper/status`, params)
    },

    // 切换试题来源
    examSwitchoverGather: params => {
        return request.post(`/exam-service/exam-paper/switchover-gather`, params)
    },


    // 试卷题目关联
    // 编辑试卷试题绑定关系
    paperShipEdit: params => {
        return request.post(`/exam-service/paper-question-ship/edit`, params)
    },

    // 获取当前试卷关联的题目信息
    paperShipDetails: params => {
        return request.post(`/exam-service/paper-question-ship`, params)
    },


    // 考试
    // 关闭考试
    examClose: params => {
        return request.post(`/exam-service/exam/close`, params)
    },

    // 新增编辑考试
    examSave: params => {
        return request.post(`/exam-service/exam/save`, params)
    },

    // 考试详情
    examDetails: params => {
        return request.post(`/exam-service/exam/info`, params)
    },

    // 发布考试
    examRelease: params => {
        return request.post(`/exam-service/exam/release`, params)
    },
}