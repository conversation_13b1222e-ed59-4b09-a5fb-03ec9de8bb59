const TokenKey = 'Token'

export function getToken() {
  return sessionStorage.getItem(TokenKey)
}

export function setToken(token) {
  return sessionStorage.setItem(TokenKey, token)
}
export function removeToken() {
  return sessionStorage.removeItem(TokenKey)
}


export function getTicket() {
  return sessionStorage.getItem('ticket')
}

export function setTicket(ticket) {
  return sessionStorage.setItem('ticket', ticket)
}
export function removeTicket() {
  return sessionStorage.removeItem('ticket')
}





export function getSession(key) {
  return sessionStorage.getItem(key)
}

export function setSession(key, value) {
  return sessionStorage.setItem(key, value)
}

export function removeSession(key) {
  if (key) {
    return sessionStorage.remove(key)
  } else {
    return sessionStorage.clear()
  }
}

