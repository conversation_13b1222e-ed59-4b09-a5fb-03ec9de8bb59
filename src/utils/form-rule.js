  /**
   * 表单校验规则 async -validator
   * @param {rule} sourceObject 
   * @param {value} corresponding 
   * @param {callback} corresponding 
   */
  // 大于当前日期
  export function ms_rule_date(rule, value, callback) {
    if (value && new Date(value).valueOf() < new Date().valueOf()) {
      callback("不能小于当前日期")
    } else {
      callback()
    }
  }
  // 手机号验证
  export function ms_rule_phone(rule, value, callback) {
    if (value === '') {
      callback("请输入手机号")
    } else if (!(/^1[3456789]\d{9}$/.test(value))) {
      callback("手机号格式错误")
    } else {
      callback()
    }
  }
  // 邮箱验证
  export function ms_rule_mail(rule, value, callback) {
    if (value === '') {
      callback("请输入邮箱")
    } else {
      callback()
    }
  }

  // URL校验
  export function ms_rule_url(rule, value, callback) {
    // eslint-disable-next-line no-useless-escape
    // if (value && !(/^(?:http(s)?:\/\/)?[\w.-]+(?:\.[\w\.-]+)+[\w\-\._~:/?#[\]@!\$&'\*\+,;=.]+$/.test(value))) {
      if (value && !(/^(http|https):\/\/[\S]+$/.test(value))) {
      callback("请输入正确的网站域名")
    } else {
      callback()
    }
  }

  // URL校验-https
  export function ms_rule_url_http(rule, value, callback) {
    // eslint-disable-next-line no-useless-escape
    // if (value && !(/(http|https):\/\/([\w.-]+(?:\.[\w\.-]+))+[\w\-\._~:/?#[\]@!\$&'\*\+,;=.]+$/.test(value))) {
      if (value && !(/^(http|https):\/\/[\S]+$/.test(value))) {
      callback("请输入带有协议前缀的正确网站域名")
    } else {
      callback()
    }
  }
  // URL校验
  export function ms_not_zero(rule, value, callback) {
    if (value || localStorage.getItem('ms_editorId')) {
      callback()
    } else {
      callback("请选择发布者")
    }
  }
  // 校验上传链接
  export function ms_rule_upload_url(rule, value, callback) {
    // eslint-disable-next-line no-useless-escape
    if(!value) {
      callback("请输入指南资源地址")
    // } else if (value && !(/^(?:http(s)?:\/\/)?[\w.-]+(?:\.[\w\.-]+)+[\w\-\._~:/?#[\]@!\$&'\*\+,;=.]+$/.test(value)))
    } else if (value && !(/^(http|https):\/\/[\S]+$/.test(value)))
    {
      callback("请输入正确的网站域名")
    } else {
      callback()
    }
  }
  // 校验指南上传链接
  export function ms_rule_upload_url_article(rule, value, callback) {
    // eslint-disable-next-line no-useless-escape
    // if (value && !(/^(?:http(s)?:\/\/)?[\w.-]+(?:\.[\w\.-]+)+[\w\-\._~:/?#[\]@!\$&'\*\+,;=.]+$/.test(value))) {
      if (value && !(/^(http|https):\/\/[\S]+$/.test(value))) {
      callback("请输入正确的网站域名")
    } else {
      callback()
    }
  }
  
