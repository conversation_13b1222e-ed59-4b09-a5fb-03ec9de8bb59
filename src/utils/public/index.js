/*
 * 操作公共函数
 * 在入口已引入
 * this.PUBLIC_Methods.【方法名】 => 调用
 */
import { Notification } from "element-ui";
const escapeMap = {
  '&': '&amp;',
  '<': '&lt;',
  '>': '&gt;',
  '"': '&quot;',
  "'": '&#x27;',
  '`': '&#x60;'
}
const unescapeMap = {
  '&amp;': '&',
  '&lt;': '<',
  '&gt;': '>',
  '&quot;': '"',
  "&#x27;": "'",
  '&#x60;': '`',
  '&ldquo;': '“',
  '&rdquo;': '”',
  '&lsquo;': '‘',
  '&rsquo;': '’',
  '&middot;': '·',
  '&nbsp;': ' ',
  '&hellip;': '…'
}
const publicMethods = {
  /**
   * @name重置列表搜索
   * @param dataObj -> 重置数据对象
   * @param filterArr -> 不需要重置字段数组
   * @return {boolean}
   */
  resetParams(dataObj, filterArr) {
    let resetObj = {}
    for (let k in dataObj) {
      if(filterArr.indexOf(k) === -1) {
        if(this.isArrayFn(dataObj[k])) {
          resetObj[k] = []
        } else {
          if (typeof(dataObj[k]) === 'number' || dataObj[k] === null) {
            resetObj[k] = null
          } else {
            resetObj[k] = ''
          }
        }
      } else {
        resetObj[k] = dataObj[k]
      }
    }
    return resetObj
  },
  isArrayFn(val) {
    if (typeof Array.isArray === "function") {
      return Array.isArray(val)
    } else {
      return Object.prototype.toString.call(val) === "[object Array]"
    }
  },
  /**
   * @name 判断是否为url
   * @param {*} val 
   * @return {boolean}
   */
  isUrlLink(val) {
    // eslint-disable-next-line no-useless-escape
    let urlReg = /^([hH][tT]{2}[pP]:\/\/|[hH][tT]{2}[pP][sS]:\/\/)(([A-Za-z0-9-~]+).)+([A-Za-z0-9-~\/])+$/;
    if (val && urlReg.test(val)) {
      return true
    } else {
      return false
    }
  },
  apiNotify(message, type, title) {
    Notification({
      title: title,
      message: message || '请求错误',
      type: type || 'warning',
      position: 'bottom-right',
      duration: 1500
    })
  },
  excapeHtml(content) {
    var escaper = function(match) {
      return escapeMap[match];
    };
    // Regexes for identifying a key that needs to be escaped
    var source = '(?:' + Object.keys(escapeMap).join('|') + ')';
    var testRegexp = RegExp(source);
    var replaceRegexp = RegExp(source, 'g');
    var string = content == null ? '' : '' + content;
    return testRegexp.test(string) ? string.replace(replaceRegexp, escaper) : string;
  },
  unexcapeHtml(content) {
    var escaper = function(match) {
      return unescapeMap[match];
    };
    // Regexes for identifying a key that needs to be escaped
    var source = '(?:' + Object.keys(unescapeMap).join('|') + ')';
    var testRegexp = RegExp(source);
    var replaceRegexp = RegExp(source, 'g');
    var string = content == null ? '' : '' + content;
    return testRegexp.test(string) ? string.replace(replaceRegexp, escaper) : string;
  },
  dealCosUrl(videoUrl){
    const baseUrl = videoUrl.split('/')[0]
    const snapshotDate = videoUrl.split('/')[2]
    const snapshotUrl = videoUrl
    .replace(/\.mp4$/, '.jpg')
    .replace(snapshotDate, 'snapshot')
    const m3u8Url = videoUrl
    // .replace(/\.mp4$/, '.m3u8')
    // .replace(date, 'transcode')
    .replace(baseUrl, '')
    const urls = {
        snapshotUrl,
        m3u8Url
    }
    return urls
  }
};

export default publicMethods
