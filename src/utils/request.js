// axios 实例
import axios from 'axios'
import store from '../store/index'
import publicMethods from './public'

// 设定访问根地址
let baseUrl = window.location.origin;

const service = axios.create({
  baseURL: baseUrl,
  timeout: 180000
})

// request拦截器
service.interceptors.request.use(config => {
  config.headers['Cache-Control'] = 'no-cache'
  if (config.formData) {
    config.headers['content-type'] = 'application/x-www-form-urlencoded'
  } else {
    if (store.getters.token) {
      // config.headers['Authorization'] = store.getters.token //不带权限
      config.headers['Authorization'] = store.getters.ticket //测试环境带权限

      // config.headers['Authorization'] = "*****************************************************************************************************************************************************************************************************************************************************************************************************************************************"
    }
    if (!publicMethods.isArrayFn(config.data) && store.getters.info && store.getters.projectId) {
      // 新增projectIds字段，子项目id合集
      let projectIds = []
      let projectId = store.getters.info.projectId
      projectIds.push(projectId)
      let childProject = store.getters.info.getChildProjectResponses
      if (childProject && childProject.length > 0) {
        childProject.forEach(item => projectIds.push(item.id))
      }
      // 菜单角色等接口白名单 与生物谷变量隔离
      let whiteList = ['/paas-mgr-center-service/medsciMenu/getMedsciPlatformMenuList', '/paas-mgr-center-service/medsciRole/getProjectIdAndRoleListDicList']
       // 判读生物谷路由，加参数
      if(window.location.href.includes('Bioon') && whiteList.indexOf(config.url) === -1) {
        config.data.projectId = config.url.includes('getRoleMenuPermissionsBtnList') ? 1 : 0
        config.data.tenant = 110
        config.data.projectIds = null
      }
      if(Object.prototype.toString.call(config.data) != '[object FormData]'){
        config.data = {
          projectId: store.getters.info.projectId,
          projectIds: projectIds,
          ...config.data
        }
      }else{ 
        //  config.data = {
        //   projectId: store.getters.info.projectId,
        //   projectIds: projectIds,
        //   ...config.data
        // }
        // config.data.append('projectId', store.getters.info.projectId)
        // config.data.append('projectIds', projectIds)
      }
    }
  }

  return config
}, error => {
  Promise.reject(error)
})

// respone拦截器
service.interceptors.response.use(
  response => {
    if (response.data.status === 401) {  //拦截登录页面
      store.dispatch('LogOut').then(() => {
        location.reload()
      })
      publicMethods.apiNotify(response.data.message, 'warning', response.data.status)
    }
    return response.data
  },
  error => {
    if (error.response) {
      if (error.response.status === 401) {  //拦截登录页面
        store.dispatch('LogOut').then(() => {
          location.reload()
        })
      }
      publicMethods.apiNotify(error.response.statusText, 'warning', error.response.status)
    }

    return Promise.reject(error)
  }
)

export default service
