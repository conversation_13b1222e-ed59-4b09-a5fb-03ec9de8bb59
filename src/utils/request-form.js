// axios 实例
import axios from 'axios'
import store from '../store/index'
import publicMethods from './public'

// 设定访问根地址
let baseUrl = window.location.origin;

const service = axios.create({
  baseURL: baseUrl,
  timeout: 500000
})

// request拦截器
service.interceptors.request.use(config => {
  config.headers['Cache-Control'] = 'no-cache'
  if (config.formData) {
    config.headers['content-type'] = 'application/x-www-form-urlencoded'
  } else {
    if (store.getters.token) {
      let data = `tenantId=112,channel=MEDSCI,userId=${store.getters.info.userId},userName=${encodeURIComponent(store.getters.info.userName)}`
      config.headers['Basic-Auth-Header'] = data
      // config.headers['Authorization'] = store.getters.token
      config.headers['Authorization'] = store.getters.ticket 
    }
  }

  return config
}, error => {
  Promise.reject(error)
})

// respone拦截器
service.interceptors.response.use(
  response => {
    if (response.data.status === 401) {  //拦截登录页面
      store.dispatch('LogOut').then(() => {
        location.reload()
      })
      publicMethods.apiNotify(response.data.message, 'warning', response.data.status)
    }
    return response.data
  },
  error => {
    if (error.response) {
      if (error.response.status === 401) {
        store.dispatch('LogOut').then(() => {
          location.reload()
        })
      }
      publicMethods.apiNotify(error.response.statusText, 'warning', error.response.status)
    }

    return Promise.reject(error)
  }
)

export default service
