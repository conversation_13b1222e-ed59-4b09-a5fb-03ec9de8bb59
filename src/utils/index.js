export function parseTime(time, cFormat) {
  if (arguments.length === 0) {
    return null
  }
  if (!time) {
    return '--:--'
  }
  const format = cFormat || '{y}-{m}-{d} {h}:{i}:{s}'
  let date
  if (typeof time === 'object') {
    date = time
  }
  else {
    if (('' + time).length === 10) {
      time = parseInt(time) * 1000
    }
    date = new Date(time)
  }

  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay()
  }
  const timeStr = format.replace(/{(y|m|d|h|i|s|a)+}/g, (result, key) => {
    let value = formatObj[key]
    if (key === 'a') {
      return ['一', '二', '三', '四', '五', '六', '日'][value - 1]
    }
    if (result.length > 0 && value < 10) {
      value = '0' + value
    }
    return value || 0
  })
  return timeStr
}

export function formatTime(time) {
  time = +time * 1000
  const d = new Date(time)
  const now = Date.now()

  const diff = (now - d) / 1000

  if (diff < 30) {
    return '刚刚'
  }
  else if (diff < 3600) { // less 1 hour
    return Math.ceil(diff / 60) + '分钟前'
  }
  else if (diff < 3600 * 24) {
    return Math.ceil(diff / 3600) + '小时前'
  }
  else if (diff < 3600 * 24 * 2) {
    return '1天前'
  }
  else {
    return d.getMonth() + 1 + '月' + d.getDate() + '日' + d.getHours() + '时' + d.getMinutes() + '分'
  }
}

export function tree_data(data, child, names) {
  let result = []
  for (let i = 0; i < data.length; i++) {
    let item = {}
    for (let j = 0; j < names.length; j++) {
      item[names[j]] = data[i][names[j]]
    }
    result.push(item)
    if (data[i][child] && Array.isArray(data[i][child]) && data[i][child].length) {
      const children = tree_data(data[i][child], child, names);
      result = [...children, ...result]
    }
  }
  return result
}

export function getEditContent(data) {
  let con;
  let getCon;
  con = data.replace(/[\r\n]/g, ""); // 去除html标签换行
  con = con.replace(/(<\/?a.*?>)|(<\/?span.*?>)/g, '')
  con = con.replace(/<[^>]+>/g, "??")
  let conArr = con.split("??")
  conArr.some(v => {
    if (v) {
      getCon = v
      return true;
    }
  })
  return getCon ? getCon.substr(0, 100) : ''
}
