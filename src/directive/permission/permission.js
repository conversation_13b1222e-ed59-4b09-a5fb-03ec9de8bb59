import store from '@/store'

export default {
  inserted (el, binding) {
    // value为数组, 第一个值为列表菜单path, 第二个值为按钮权限标识
    const { value } = binding
    const roles = store.getters && store.getters.permissionBtnName

    if ( value && value instanceof Array && value.length > 0 && roles) {
      const permissionRoles = value
      const hasPermission = roles[permissionRoles[0]].some( role => {
        return permissionRoles[1] === role
      })

      if (!hasPermission) {
        el.parentNode && el.parentNode.removeChild(el)
      }
    } else {
      throw new Error(`请确认您是否有当前操作的权限！`)
    }
  }
}
