import { Message } from 'element-ui';
export default {
  bind (el) {
    el.onclick = function () {
      let oInput = document.createElement('input')
      oInput.value = el.innerText || el.getAttribute("data-text")
      document.body.appendChild(oInput)
      oInput.select() // 选择对象
      document.execCommand("Copy") // 执行浏览器复制命令
      Message({
        message: '复制成功',
        type: 'success'
      });
      oInput.remove()
    }
  }
}

